const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const moment = require('moment');
const { getDashboardData } = require('./services/getDashboard.service');
const { postUploadCompareData, postMasterData, checkUploadedDataExists, checkUploadedDataPublished, deleteExistingData, unpublishMasterData } = require('../services/executeQuery.service');

async function uploadCompare(req) {
    log.debug(`>>>>>Entered uploadCompare.controller.uploadCompare`);
    return new Promise(async (resolve, reject) => {
        try {
            let data = req.body.data;
            let values = [];
            data.forEach(element => {
                values.push([moment().format("YYYY-MM-DD"), req.params.id, req.user.preferred_username, moment(element.OBS_DT).format("YYYY-MM-DD"), element.LABEL, element.VALUE, moment().format("YYYY-MM-DD"), "apiInput"]);
            });
            let obj = {
                "nodeId": req.params.id,
                "userId": req.user.preferred_username,
            }
            let isDataExists = await checkUploadedDataExists(obj);

            if (isDataExists) {
                let deleteData = await deleteExistingData(obj)
                log.info(`Successfully deleted existing data for node id ${req.params.id} and userid ${req.user.preferred_username} ${deleteData}`);
            }
            let results = await postUploadCompareData(values);
            if (results.rowsAffected > 0) {
                let inputData = {
                    "last_updated_date": moment().format("YYYY-MM-DD"),
                    "userId": req.user.preferred_username,
                    "insertDt": moment().format("YYYY-MM-DD"),
                    "insertUserId": "apiInput",
                    "nodeId": req.params.id
                };
                postMasterData(inputData).then(async response => {
                    if (response.rowsAffected > 0) {
                        let chartUploadData = await getDashboardData(req);
                        req.body["contentId"] = chartUploadData.id;
                        req.body["contentType"] = chartUploadData.type;
                        req.body["username"] = req.user.preferred_username;
                        resolve(chartUploadData);
                    } else {
                        log.error(`<<<<<Exited microservice-upload-compare.upload error unable to insert in master tables`);
                        reject([422, 'unable to insert in master tables']);
                    }
                })
            } else {
                log.error(`<<<<<Exited microservice-upload-compare.upload error unable to insert in master tables`);
                reject([422, 'unable to insert in data table']);
            }
        } catch (err) {
            
            log.error(`<<<<<Exited uploadCompare.controller.uploadCompare on getting CMS data with error ${err}`);
            reject(err);
        }
    })
}

async function unpublishData(req) {
    log.debug(`>>>>>Entered uploadCompare.controller.uploadCompare`);
    return new Promise(async (resolve, reject) => {
        try {
            let obj = {
                "nodeId": req.params.id,
                "userId": req.user.preferred_username
            }
            let isPublished = await checkUploadedDataPublished(obj);

            if (isPublished) {
                unpublishMasterData(obj)
                    .then( async response => {
                        let chartUploadData = await getDashboardData(req);
                        let unpublishedData = response.rowsAffected > 0 ? true : false;
                        log.info(`Successfully unpublished upload compare data for node id ${req.params.id} and userid ${req.user.preferred_username}`);
                        req.body["contentId"] = chartUploadData.id;
                        req.body["contentType"] = chartUploadData.type;
                        req.body["username"] = req.user.preferred_username;
                        log.info({
                            isUnpublished: unpublishedData,
                            Message: unpublishedData === true
                                ? `Successfully unpublished upload compare data for node id ${req.params.id} and userid ${req.user.preferred_username}`
                                : 'No Published upload compare data available/ Records does not exists in master table'
                        })
                        resolve(chartUploadData);
                    })
                    .catch( err => {
                        
                        log.error(`<<<<<Exited microservice-upload-compare.unpublishData error while unpublishing upload data`, err);
                    })
            } else {
                log.info(`No Published upload compare data available/ Records does not exists in master table`);
                resolve({
                    isUnpublished: false,
                    Message: 'No Published upload compare data available/ Records does not exists in master table'
                });
            }
        } catch (err) {
            
            log.error(`<<<<<Exited uploadCompare.controller.unpublishData on getting CMS data with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { uploadCompare, unpublishData }