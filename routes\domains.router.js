const express = require('express');
const router = new express.Router();
const domainsController = require('../microservice-domains/domains.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { validateDomainDetail } = require('./validators/domain.validator');

router.get('/', async (req, res, next) => {
    try {
      const data = await domainsController.getDomains(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for domains content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/navigation/', async (req, res, next) => {
    try {
      // API Versions for consumers- Default: 1, Mobile: 1, Web: 2
      const apiVersion = req.get('Accept-Version');
      if (!apiVersion) {
        req.apiVersion = 1;
      } else {
        req.apiVersion = +apiVersion;
      }
      const data = await domainsController.getDomainsNavigation(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for domains content-type, ERROR: ${err}`);
      next(err);
    }
  });
  
  router.get('/classifications/:id', async (req, res, next) => {
    try {
      const data = await domainsController.getDomainClassificationsV2(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for domains content-type, ERROR: ${err}`);
      next(err);
    }
  });
  
  router.get('/filters-old/:id', async (req, res, next) => {
    try {
      const data = await domainsController.getDomainClassificationsFilters(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for domains content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/filters/:id', async (req, res, next) => {
    try {
      const data = await domainsController.getDomainClassificationsFiltersV3(req);
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for domain content filter dropdown, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/detailv2-old/:id', validateDomainDetail, async (req, res, next) => {
    try {
      const data = await domainsController.getDomainNodes(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for domains content-type, ERROR: ${err}`);
      next(err);
    }
  });

  // This is a temporary route for the new version of the API.
  router.get('/detailv2/:id', validateDomainDetail, async (req, res, next) => {
    try {
      const data = await domainsController.getDomainNodesV3(req);
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for domains content-type, ERROR: ${err}`);
      next(err);
    }
  });
  

module.exports = router;
