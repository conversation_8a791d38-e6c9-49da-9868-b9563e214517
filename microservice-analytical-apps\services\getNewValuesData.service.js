const newValuesDataFunctions = require('scad-library').valuesDataAnalytical;
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getValuesDataFromQuery, getComboId } = require('./getGraphData.service');
const constants = require('../../config/constants.json');


async function isNewValuesMeta(valuesMetaArr) {
    return new Promise((resolve, reject) => {
        try {
            let newValuesId = [
                'scad-estimation',
                'latest-date',
                'previous-quarter-index',
                'latest-date-value',
                'quarter-date-value',
                'percentage-change']
            isExist = valuesMetaArr.filter(e => newValuesId.includes(e.id))
            if (isExist.length > 0) {
                resolve(true);
            } else {
                resolve(false);
            }
        }
        catch (err) {
            
            reject(err);
        }
    })
}

const getNewValuesData = (valuesMeta, userGroups, indicatorDrivers) => {
    return new Promise((resolve, reject) => {
        try {
            let iconIds = []; let valueWithData = []
            valuesMeta.forEach(value => {
                if (value.iconId && value.iconId.length > 0) {
                    iconIds.push(value.iconId);
                }
            })
            let counter = [];
            valuesMeta.forEach((value, index) => {
                if (indicatorDrivers) {
                    if (!Object.keys(indicatorDrivers).length > 0) {
                        if (!value.isScadProjection && !value.hasDefault) {
                            indicatorDrivers = constants.indicatorDrivers;
                        } else if(value.hasDefault) {
                            indicatorDrivers = constants.quarterlyIndicatorDrivers;
                        } else{
                            indicatorDrivers = constants.indicatorDriversPopulation;
                        }
                    }
                    if (value.comboIdTable) {
                        getComboId(value, indicatorDrivers).then(comboId => {
                            getEachValueData(value, comboId).then(data => {
                                valueWithData[index] = data;
                                counter.push(1);
                                if (valuesMeta.length === counter.length) {
                                    getIcons(iconIds, valueWithData, userGroups).then(result => {
                                        valueWithData = result;
                                        resolve(valueWithData);
                                    })
                                }
                            }).catch(err => {
                                
                                reject(err)
                            })
                        })
                    } else {
                        getEachValueData(value, '').then(data => {
                            valueWithData[index] = data;
                            counter.push(1);
                            if (valuesMeta.length === counter.length) {
                                getIcons(iconIds, valueWithData, userGroups).then(result => {
                                    valueWithData = result;
                                    resolve(valueWithData);
                                })
                            }
                        }).catch(err => {
                            
                            reject(err)
                        })
                    }
                } else {
                    reject([422, `Invalid request body`]);
                }
            });
        } catch (err) {
            
            reject(err);
        }
    })
}

const getIcons = (iconIds, valueWithData, userGroups) => {
    return new Promise((resolve, reject) => {
        try {
            if (iconIds.length > 0) {
                let iconCodes = iconIds.map(i => i).join(',');
                const cmsCompareUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_COUNTRY_LIST}${iconCodes}`
                const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
                getMetaFromCMS(req,cmsLoginUrl, cmsCompareUrl, userGroups).then(iconData => {
                    valueWithData.forEach(value => {
                        if (value.iconId && value.iconId.length > 0) {
                            iconData.forEach(data => {
                                if (data.country_id === value.iconId) {
                                    value.iconId = data.country_flag;
                                }
                            })
                        }
                    })
                    resolve(valueWithData);
                })
            } else {
                resolve(valueWithData);
            }
        } catch (err) {
            
            reject(err);
        }
    })

}

const getEachValueData = (valuesMetaObj, comboId) => {
    return new Promise((resolve, reject) => {
        try {
            getValuesDataFromQuery(valuesMetaObj, comboId).then(async (valuesData) => {
                let finalResults, scadEstimate;
                if (valuesMetaObj.isScadProjection && comboId !== valuesMetaObj.PARAMETER_COMBO_ID) {
                    scadEstimate = valuesData.filter(e => e.PARAMETER_COMBO_ID === valuesMetaObj.PARAMETER_COMBO_ID);
                }
                if (scadEstimate) {
                    finalResults = valuesData.filter(e => !scadEstimate.includes(e));
                }
                valuesData = finalResults ? finalResults : valuesData;
                switch (valuesMetaObj.id) {
                    case 'scad-estimation': {
                        const data = newValuesDataFunctions.scadEstimation(valuesMetaObj, valuesData, scadEstimate);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'latest-date': {
                        let data = newValuesDataFunctions.latestDate(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'latest-date-value': {
                        let data = newValuesDataFunctions.latestDateValue(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case "previous-from-last-index": {
                        const data = newValuesDataFunctions.previousFromLastIndex(valuesData);
                        valuesMeta = Object.assign(valuesMeta, data);
                        break;
                    }
                    case 'percentage-change': {
                        let data = newValuesDataFunctions.percentageChange(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    default: {
                        log.debug(`Values Function not available`);
                        break;
                    }
                }
                resolve(valuesMetaObj);
            })
        }
        catch (err) {
            
            reject([422, err]);
        }
    });
}

module.exports = { isNewValuesMeta, getNewValuesData };