const clkdb = require('../../services/clk-database.service');
const { getFilterOptionsQuery, getCorrelationVisualizationDataQuery } = require('./getQuery.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getFilterOptions(viewName, dimension, property) {
  try {
    let {query,binds} = getFilterOptionsQuery(viewName, dimension, property)
    let data = await getData(query,binds)
    log.info(`<<<<< Exit microservice-analytical-apps.services.executeQuery.getFilterOptions successfully`);
    return data
          
  }
  catch(err){
    log.error(`<<<<< Exited microservice-analytical-apps.services.executeQuery.getFilterOptions with error ${err}`);
    throw err;
  }
}

async function getCorrelationVisualizationData(visualization,filters,filterPanel){
    try{
        let {query,binds} = await getCorrelationVisualizationDataQuery(visualization,filters,filterPanel)
        let data = await getData(query,binds)
        log.info(`<<<<< Exit microservice-analytical-apps.services.executeQuery.getCorrelationVisualizationData successfully`);
        return data
    }
    catch(err){
        log.error(`<<<<< Exited microservice-analytical-apps.services.executeQuery.getCorrelationVisualizationData with error ${err}`);
        throw err;
    }
}


async function getData(query,binds={}) {
    try{
        log.info(`>>>>> Enter microservice-analytical-apps.services.executeQuery.getData`);
        let data = await clkdb.simpleExecute(query,binds)
        return data;
    }
    catch(exp){
        log.error("Error while fetching the data microservice-analytical-apps.services.executeQuery.getData")
        throw exp
    }
}


module.exports = {
    getFilterOptions,
    getCorrelationVisualizationData
}