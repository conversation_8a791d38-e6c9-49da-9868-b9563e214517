require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { getMetaFromCMS } = require('../services/common-service');
let constants = require('../config/constants.json');

/**
 * function to get survey content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function authBasket(req) {
    log.debug(`>>>>>Entered microservice.basket.controller.authBasket`);
    try {
        this.lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsAnalyticalAppsUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsGroupUrl.CMS_BASKET_APPS}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        this.cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsAnalyticalAppsUrl, req.user.groups);
        let auth_status = false
        if (this.cmsResponse){
            this.cmsResponse.forEach( element => {
                if (element.type == "Basket-Insights"){
                    auth_status = true
                    return {"message":"Authorized","status":auth_status}
                }

            })
            return {"message":"Unauthorized","status":auth_status}
        }
        else{
            return {"message":"Unauthorized","status":auth_status}
        }
        
    } catch (err) {
        log.error(`<<<<<Exited microservice.basket.controller.authBasket on getting CMS data with error ${err}`);
        if (err[0] == 404)
            return {"message":"Unauthorized","status":false}
        else{
            
            throw err;
        }
    }
}


module.exports = { authBasket };
