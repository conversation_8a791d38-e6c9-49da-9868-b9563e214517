const express = require('express');
const router = express.Router();
const approvalRequestController = require('../microservice-approval-request/approval-request.controller');

// Access check endpoint
router.post("/access-check", approvalRequestController.PermissionCheck);

// Apply permission middleware to all routes below
router.use(approvalRequestController.PermissionCheck);

// Dashboard routes (moved up to avoid conflict with /:id routes)
router.get("/dashboard/tab-count", approvalRequestController.getTabCount);

// Bulk operations (should come before /:id routes to avoid conflicts)
router.post("/bulk-actions", approvalRequestController.performBulkActions);

// Main approval request CRUD operations
router.post("/", approvalRequestController.createApprovalRequest);
router.get("/", approvalRequestController.getApprovalRequests);

// Specific approval request operations
router.get("/:id", approvalRequestController.getApprovalRequestDetails);
router.post("/:id/actions", approvalRequestController.performApprovalAction);
router.get("/:id/history", approvalRequestController.getRequestHistory);

// Comment operations for approval requests
router.route("/:id/comments")
  .get(approvalRequestController.getComments)
  .post(approvalRequestController.addComment);

router.route("/:id/comments/:commentId")
  .put(approvalRequestController.editComment)
  .delete(approvalRequestController.deleteComment);

module.exports = router;