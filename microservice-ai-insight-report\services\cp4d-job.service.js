const axios = require("axios").default;
require("dotenv").config();
const config = require("../ai-insight-report.constants.json")[process.env.NODE_ENV || "dev"];

// Temporarily create sanadkom tickets via datafabric jobs
// to bypass connectivity issue b/w bayaan and sanadkom
const cp4dClient = axios.create({
  baseURL: config.CP4D_BASE_URL,
});

cp4dClient.interceptors.request.use(async (reqConfig) => {
  const response = await axios.post(
    `${config.CP4D_BASE_URL}/icp4d-api/v1/authorize`,
    {
      username: config.CP4D_USERNAME,
      password: config.CP4D_PASSWORD,
    }
  );
  let token = response.data.token;
  
  reqConfig.headers = { Authorization: `Bearer ${token}` };

  return reqConfig;
});

async function runJob(report_id, ticket_create_body, report_type) {
  const jobBody = {
    job_run: {
      configuration: {
        env_variables: [
          `report_id=${report_id}`,
          `ticket_create_body=${JSON.stringify(ticket_create_body)}`,
          `report_type=${report_type}`,
          `S3_ACCESS_KEY_ID=${process.env.S3_ACCESS_KEY_ID}`,
          `S3_SECRET_KEY=${process.env.S3_SECRET_KEY}`,
          `S3_REGION=${process.env.S3_REGION}`,
          `S3_BUCKET=${config.AI_INSIGHT_REPORT_BUCKET}`,
          `S3_ENDPOINT=${process.env.S3_ENDPOINT}`,
          `SANADKOM_BASE_URL=${config.SMART_PUBLISHER_BASE_URL}`,
          `SANADKOM_CLIENT_ID=${config.SANADKOM_CLIENT_ID}`,
          `SANADKOM_CLIENT_SECRET=${config.SANADKOM_CLIENT_SECRET}`,
          `PLATFORM_BASEPATH=${process.env.PLATFORM_BASEPATH}`,
          `SANADKOM_IP=${config.SANADKOM_IP}`,
        ],
      },
    },
  };
  const response = await cp4dClient.post(
    `/v2/jobs/${config.CP4D_SANADKOM_TICKET_JOB_ID}/runs?project_id=${config.CP4D_PROJECT}`,
    jobBody
  );
  return response.data;
}

module.exports = {
  runJob,
};
