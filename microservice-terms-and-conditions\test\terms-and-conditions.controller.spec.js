const { getTermsAndConditions } = require('../terms-and-conditions.controller');
const axios = require('axios');
describe('controller', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    xtest('should get json response from Terms and Conditions controller - success', async () => {
        const mockGetResponse = {
            "data": [{
                "id": "WhatsNew", "title": "WhatsNew"
            }]
        };
        const expectedResponse = [{
            "id": "WhatsNew", "title": "WhatsNew"
        }];
        const mockPostResponse = {
            headers: {
                'set-cookie': ['default;default']
            }
        };

        jest.spyOn(axios, 'get').mockResolvedValue(mockGetResponse);
        jest.spyOn(axios, 'post').mockResolvedValue(mockPostResponse);
        let groupId = 0;
        result = await getTermsAndConditions({"params" : {
            userId : "<EMAIL>",
            organization: "accenture",
            tcVersion: "1"
        }});
       // expect(result).toEqual(expectedResponse);
    })
    xtest('should get error response from Terms and Conditions controller - failure', async () => {
        try {
            jest.spyOn(axios, 'get').mockRejectedValue(new Error('Mock Error'));
            jest.spyOn(axios, 'post').mockRejectedValue(new Error('Mock Error'));
            let groupId = 0;
            await getTermsAndConditions({"params" : {
                userId : "<EMAIL>",
                organization: "accenture",
                tcVersion: "1"
            }});
        } catch (err) {
            //expect(err).toEqual([401,new Error('Mock Error')]);
        }

    })
})