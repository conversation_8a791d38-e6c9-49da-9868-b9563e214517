const moment = require('moment');
const scadLib = require('scad-library');
const { util } = scadLib;
const Logger = scadLib.logger;
const log = new Logger().getInstance();

async function getScatterPlotSeries(visualization, data) {
    return new Promise((resolve, reject) => {
        try {
            visualization.seriesMeta.forEach(series => {
                series["data"] = [];
            });
            if (data.length > 0) {
                let topCategories = [];
                let limit = visualization.seriesMeta.length;
                topCategories = data.filter(e => moment(e.OBS_DT).format('YYYY-MM-DD') === moment(data[data.length - 1].OBS_DT).format('YYYY-MM-DD'));
                if (visualization.plotMeta) {
                    topCategories = topCategories.filter(e => e[visualization.plotMeta.plotColumn] === visualization.plotMeta.plotValue);
                    let categoryList = [];
                    topCategories = topCategories.sort((a, b) => (a.VALUE > b.VALUE) ? -1 : ((b.VALUE > a.VALUE) ? 1 : 0));
                    topCategories = topCategories.splice(0, limit);
                    topCategories.forEach(element => {
                        categoryList.push(element[visualization.plotMeta.mappingColumn]);
                    });
                    let filteredData = {};
                    let counter = [];

                    categoryList.forEach(category => {
                        filteredData[category] = [];
                        data.forEach(element => {
                            Object.entries(element).forEach(([key, value]) => {
                                if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                                    delete element[key];
                                }
                            });
                            if (element[visualization.plotMeta.mappingColumn] === category) {
                                filteredData[category].push(element);
                            }
                        });
                        counter.push(1);
                        const uniqueObj = {};
                        if (counter.length === categoryList.length) {
                            if (visualization.plotMeta.xAxisFilterColumn && visualization.plotMeta.xAxisFilterValue) {
                                Object.entries(filteredData).forEach(([column, value]) => {
                                    for (var i = value.length - 1; i >= 0; --i) {
                                        if (value[i][visualization.plotMeta.plotColumn] === visualization.plotMeta.xAxisColumn &&
                                            value[i][visualization.plotMeta.xAxisFilterColumn].toLowerCase() !== visualization.plotMeta.xAxisFilterValue.toLowerCase()) {
                                            value.splice(i, 1);
                                        }
                                    }
                                });
                            }
                            let counter1 = [];
                            Object.entries(filteredData).forEach(([column, value]) => {
                                let dateObj = []; const finalObj = [];
                                value.map((obj) => {
                                    dateObj.push(obj.OBS_DT);
                                });
                                const uniqueList = [...new Set(dateObj)]; // to get unique list of date
                                uniqueList.map((date) => {
                                    value.forEach((item) => {
                                        const obj = {};
                                        // grouping all indicator title : indicator value for each date
                                        if (date === item.OBS_DT) {
                                            const key = item.INDICATOR_ID;
                                            obj.OBS_DT = util.convertDate(`${item.OBS_DT}`);
                                            obj[key] = item.VALUE;
                                            obj[visualization.plotMeta.mappingColumn] = item[visualization.plotMeta.mappingColumn];
                                        }
                                        if (Object.keys(obj).length !== 0) finalObj.push(obj);
                                    });
                                });
                                finalObj.map((item) => {
                                    const date = item.OBS_DT;
                                    finalObj.map((obj, index) => {
                                        if (obj.OBS_DT === date && obj[visualization.plotMeta.mappingColumn] === item[visualization.plotMeta.mappingColumn]) {
                                            finalObj[index] = Object.assign(item, obj);
                                        }
                                    });
                                });
                                uniqueObj[column] = [...new Set(finalObj)]
                                counter1.push(1);
                                if (counter1.length === Object.keys(filteredData).length) {
                                    visualization.seriesMeta.forEach((series, index) => {
                                        series.label = topCategories[index][visualization.plotMeta.mappingColumn];
                                        series.data = uniqueObj[topCategories[index][visualization.plotMeta.mappingColumn]];
                                    })
                                    resolve(visualization);
                                }
                            });
                        }
                    });
                }
            }
        } catch (err) {
            log.err(`Error in microservice-statistic-insights.services.chart-services.single-scatterplot-chart.getScatterPlotSeries ${err}`)
            reject([422, err]);
        }
    });
}

module.exports = { getScatterPlotSeries }