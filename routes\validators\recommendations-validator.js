const { IFPError } = require("../../utils/error");

const validateRecommendations = (req, res, next) => {
    if (!Array.isArray(req.body))
        throw new IFPError(400,`Request body should be an array`)
    
    const domains = req.body
    domains.forEach(domain=>{
        if (!Number(domain))
            throw new IFPError(400,`Please provide valid domain ids`)
    })
    
    next()
  };

module.exports = {
    validateRecommendations
}