const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function listInsightDataQuery(nodeId) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_CHART_INSIGHTS WHERE NODE_ID = ${nodeId} ORDER BY ADD_DT DESC`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.addInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function addInsightDataQuery(insightData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_CHART_INSIGHTS ( EMAIL, USER_NAME, NODE_ID, NODE_TITLE, NODE_LINK, INSIGHT, STATUS, ADD_DT ) VALUES ( '${insightData.email}','${insightData.user}',${insightData.nodeId}, '${insightData.nodeTitle}', '${insightData.nodeLink}', '${insightData.insight}', 'APPROVED', TO_TIMESTAMP('${insightData.date}','DD/MM/YYYY HH24:MI:SS') )`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.addInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInsightDataQuery(id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_CHART_INSIGHTS WHERE ID = ${id}`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInsightsDataQuery(ids) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_CHART_INSIGHTS WHERE ID IN (${ids.toString()})`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateInsightDataQuery(insightData) {
    return new Promise((resolve, reject) => {
        try {
            let query
            query = `UPDATE IFP_CHART_INSIGHTS SET NODE_TITLE='${insightData.nodeTitle}', NODE_LINK='${insightData.nodeLink}', INSIGHT='${insightData.insight.replace(/'/g, "''")}', STATUS='APPROVED', ADD_DT=TO_TIMESTAMP('${insightData.date}','DD/MM/YYYY HH24:MI:SS'), EMAILSENT=0 WHERE ID='${insightData.id}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.updateInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateInsightStatusQuery(userEmail, setting, value) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_USER_SETTINGS ( EMAIL, SETTING, VALUE ) VALUES ( '${userEmail}', '${setting}', '${value}' )`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.updateInsightStatusQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function deleteInsightDataQuery(id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM IFP_CHART_INSIGHTS WHERE ID = ${id} `
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.deleteInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


module.exports = { listInsightDataQuery, addInsightDataQuery, getInsightDataQuery, updateInsightDataQuery, updateInsightStatusQuery, deleteInsightDataQuery, getInsightsDataQuery };
