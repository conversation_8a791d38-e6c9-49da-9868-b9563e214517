const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();

const Logger = require('scad-library').logger;
const encodeUrl = require('encodeurl');
const log = new Logger().getInstance();
let constants = require('../config/constants.json');
const { getScreenerCount, getSingleDimensionNodes } = require('./services/executeQuery.service')

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { getViewNameById } = require('../services/executeQuery.service');
const { IFPError } = require('../utils/error');
/**
 * function to get domains content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getDomains(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomains`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups);
    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomains successfully `);
    const domains = Object.values(data).map((domain) => {
      const domainObj = {
        id: domain.id,
        name: domain.name,
        icon: `${process.env.CMS_BASEPATH_URL}${domain.dark_icon}`,
        light_icon: `${process.env.CMS_BASEPATH_URL}${domain.light_icon}`
      }
      return domainObj;
    })

    return domains;
  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomains on getting CMS data with error ${err}`);
    throw err;
  }
}

/**
* function to get domains content from CMS
* @param {*} req 
* @param {*} res
* @param {*} next
*/
async function getDomainsNavigation(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomainsNavigation`);
  try {
    const { apiVersion } = req;
    const { search } = req.query
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    let cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V2;
    if (apiVersion == 2) {
      cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V3;
    }
    let cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${cmsDomainsUrlPath}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

    if (search && apiVersion == 2) {
      cmsDomainsUrl += `?keys=${search}`;
      cmsDomainsUrl = encodeUrl(cmsDomainsUrl);
    }

    const cmsResponse= await getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups)

    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomains successfully `)

    let cmsCacheKey = `cmsNavigation_${crypto.createHash('md5').update(JSON.stringify(cmsResponse)).digest("hex")}`
    const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
    if (cmsCacheResults) {
      log.info(`<<<<<Cache found for microservice-domains.controller.getDomainsNavigation`);
      return JSON.parse(cmsCacheResults);
    }

    let domainName;
    let subdomainName;
    let subthemeName;

    let AnalyticalApps = constants.classifications.analyticalApps
    let Reports = constants.classifications.reports
    let IFPIndicators = constants.classifications.innovativeStatistics
    let OfficialStatistics = constants.classifications.officialStatistics

    const result = await Promise.all(Object.values(cmsResponse.data).map(async classification => {
      const classificationObj = {
        id: classification.id,
        name: classification.classification,
        key: classification.key,
        light_icon: `${process.env.CMS_BASEPATH_URL}${classification.light_icon}`,
        dark_icon: `${process.env.CMS_BASEPATH_URL}${classification.dark_icon}`,
        nodeCount: 0,
        showTree: true
      }
      let nodeCount = 0
      if ([AnalyticalApps, Reports].includes(classification.key)) {
        let domains = Object.values(classification.domains).map((domain) => {
          let currentDomain;
          if (apiVersion == 2) {
            currentDomain = domain
          } else {
            currentDomain = Object.values(cmsResponse.domains).find(element => element.id == domain.id);
          }
          let domainName = domain.name.toLowerCase().split(' ').join('-');
          const domainObj = {
            id: domain.id,
            name: domain.name,
            nodeCount: domain.node_count,
            route: `/domain-exploration/${domainName}/${domain.id}`,
            light_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.light_icon}`,
            dark_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.dark_icon}`,
            showTree: false,
            nodes: domain.nodes
          }
          nodeCount += domain.node_count
          return domainObj
        }
        ).filter(domain => domain)
        classificationObj.nodeCount = nodeCount
        classificationObj.showTree = false
        classificationObj.domains = domains
      }

      else if ([IFPIndicators, OfficialStatistics].includes(classification.key)) {
        if (classification.domains) {
          let domains = await Promise.all(Object.values(classification.domains).map(async (domain) => {
            let currentDomain;
            if (apiVersion == 2) {
              currentDomain = domain;
            } else {
              currentDomain = Object.values(cmsResponse.domains).find(element => element.id == domain.id);
            }
            domain.name = domain.name.replace(/\s+/g, ' ');
            let domainName = domain.name.toLowerCase().split(' ').join('-');
            let domainNodeCount = domain.node_count?domain.node_count:1
            const domainObj = {
              id: domain.id,
              name: domain.name,
              light_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.light_icon}`,
              dark_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.dark_icon}`,
              route: `/domain-exploration/${domainName}/${domain.id}`,
              showTree: true,
              nodeCount: domain.node_count,
              subdomains: await Promise.all(Object.values(domain.subdomains).map(async (subdomain) => {
                subdomain.name = subdomain.name.replace(/\s+/g, ' ');
                let subdomainName = subdomain.name.toLowerCase().split(' ').join('-');
                const subDomainObj = {
                  id: subdomain.id,
                  name: subdomain.name,
                  route: `/domain-exploration/${domainName}/${subdomainName}/${subdomain.id}`,
                  showTree: true,
                  nodeCount: subdomain.node_count,
                  subthemes: await Promise.all(Object.values(subdomain.subthemes).map(async (subtheme) => {
                    subtheme.name = subtheme.name.replace(/\s+/g, ' ');
                    let subthemeName = subtheme.name.toLowerCase().split(' ').join('-');
                    const subThemeObj = {
                      id: subtheme.id,
                      name: subtheme.name,
                      showTree: true,
                      route: `/domain-exploration/${domainName}/${subdomainName}/${subthemeName}/${subtheme.id}`,
                      nodeCount: subtheme.node_count,
                    }
                    // if (classification.key == constants.classifications.officialStatistics && ['3193','3260'].includes(subtheme.id))
                    //   subThemeObj.screener = true
                    // else
                    //   subThemeObj.screener = false

                    try{
                      subThemeObj.products = Object.values(subtheme.products).map(product => {
                        return {
                          "name": product.name,
                          "showTree": true,
                          "nodes":product.nodes
                        }
                      })                       
                      
                    }
                    catch(e){

                    }
                    if (classification.key == constants.classifications.officialStatistics ){
                      subThemeObj.screener = subtheme.show_screener
                      subThemeObj.screenerConfiguration = subtheme.screener_configuration

                      if (subtheme.screener_configuration){
                        let dbCount=0;
                        let screenerConfiguration = {...subtheme.screener_configuration};
                        
                        if (Number(screenerConfiguration.screenerView)){
                          let viewName = await getViewNameById(screenerConfiguration.screenerView)
                          screenerConfiguration.screenerView = viewName
                        }
                        if (Object.keys(constants.censusViewMap).includes(screenerConfiguration.screenerView))
                          subThemeObj.products = []
                        dbCount = await getScreenerCount(screenerConfiguration)
                        
                        subThemeObj.count = dbCount?Number(dbCount):0
                        subThemeObj.screenerConfiguration = screenerConfiguration
                        domainNodeCount+=subThemeObj.count-1
                      }
                    }

                    return subThemeObj
                  }))
                }
                if (classification.key == constants.classifications.innovativeStatistics){
                  subDomainObj.screener = subdomain.show_screener
                  if (subdomain.screener_configuration){
                    let dbCount=0;
                    let screenerConfiguration = {...subdomain.screener_configuration};
                    
                    if (Number(screenerConfiguration.screenerView)){
                      let viewName = await getViewNameById(screenerConfiguration.screenerView)
                      screenerConfiguration.screenerView = viewName
                    }
                    
                    dbCount = await getScreenerCount(screenerConfiguration)
                    
                    subDomainObj.count = dbCount?Number(dbCount):0
                    subDomainObj.screenerConfiguration = screenerConfiguration
                    domainNodeCount+=subDomainObj.count-1
                  }
                }
                return subDomainObj;
              }))
            }
            nodeCount += domain.node_count;
            domainObj.nodeCount = domainNodeCount
            return domainObj
          }))
          classificationObj.showTree = [OfficialStatistics].includes(classification.key) ? true : false
          classificationObj.nodeCount = nodeCount
          classificationObj.domains = domains.sort((a,b) => b.nodeCount-a.nodeCount)
        }
      }

      return classificationObj;
    }))

    setRedis(cmsCacheKey, JSON.stringify(result), 8000, req.headers);
    return result

  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomainsNavigation on getting CMS data with error ${err}`);
    throw err;
  }
}

async function getDomainClassifications(req) {
  log.debug(
    `>>>>>Entered domains-microservice.domains.controller.getDomainClassifications`
  );
  try {
    const lang =
      req.headers["accept-language"] === "en"
        ? ""
        : `/${req.headers["accept-language"]}`;
    const cmsClassificationsByDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN}${req.params.id}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,
      cmsLoginUrl,
      cmsClassificationsByDomainsUrl,
      req.user.groups
    );
    log.debug(
      `<<<<<Exited domains-microservice.domains.controller.getDomainClassifications successfully `
    );

    let cmsClassificationDomainUrl = [];

    let screenerConfig = {
      experimental_statistics: [],
      official_statistics: [],
    };

    let classifications = [];
    for (classification of data.classification) {
      const classificationObj = {
        id: classification.id,
        key: classification.key,
        name: classification.name,
        light_icon: `${process.env.CMS_BASEPATH_URL}${classification.light_icon}`,
        dark_icon: `${process.env.CMS_BASEPATH_URL}${classification.dark_icon}`,
        count: parseInt(classification.count),
      };

      if (constants.screenerClassificationKeys.includes(classification.key)) {
        cmsClassificationDomainUrl.push(
          `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS}${req.params.id}?classification_id=${classification.id}`
        );
      }
      classifications.push(classificationObj);
    }

    const subDataPromises = cmsClassificationDomainUrl.map(
      async (url) => {
        const data = await getMetaFromCMS(req,
          cmsLoginUrl,
          url,
          req.user.groups
        );
        return data.subdomain;
      }
    );

    const dataCollection = await Promise.all(subDataPromises);
    dataCollection.flat().map(async (subDomain) => {
      if (subDomain.show_screener) {
        classification.count = classification.count - 1;
        screenerConfig.experimental_statistics.push(
          subDomain.screener_configuration
        );
      } else {
        if (subDomain.sub_theme && subDomain.sub_theme.length) {
          subDomain.sub_theme.map(async (subTheme) => {
            if (subTheme.show_screener) {
              classification.count = classification.count - 1;
              screenerConfig.official_statistics.push(
                subTheme.screener_configuration
              );
            }
          });
        }
      }
    });

    let count = {
      experimental_statistics: 0,
      official_statistics: 0,
    };
    let dataCountPromises = [];
    for (classificationKey of Object.keys(screenerConfig)){
      for (config of screenerConfig[classificationKey]) {
        if (Number(config.screenerView)){
          let viewName = await getViewNameById(config.screenerView)
          config.screenerView = viewName
        }
        const countData = await getScreenerCount(config);
        count[classificationKey] += countData;
      }
    }

    await Promise.all(dataCountPromises);
    classifications.map((classification) => {
      if (constants.screenerClassificationKeys.includes(classification.key)) {
        classification.count += count[classification.key];
      }
    });
    data.domain.light_icon = `${process.env.CMS_BASEPATH_URL}${data.domain.light_icon}`;
    data.domain.dark_icon = `${process.env.CMS_BASEPATH_URL}${data.domain.dark_icon}`;

    const classificationRespObj = {
      classification: classifications,
      domain: data.domain,
    };
    return classificationRespObj;
  } catch (err) {
    log.error(
      `<<<<<Exited domains-microservice.domains.controller.getDomainClassifications on getting CMS data with error ${err}`
    );
    throw err;
  }
}

async function getDomainClassificationsFilters(req) {
  log.debug(
    `>>>>>Entered domains-microservice.domains.controller.getDomainClassificationsFilters`
  );
  try {
    if (!req.params.id) {
      throw new IFPError(400,'No domain specified')
    }
    if (!req.query.classification) {
      throw new IFPError(400,'No classification specified')
    }

    const lang =
      req.headers["accept-language"] === "en"
        ? ""
        : `/${req.headers["accept-language"]}`;
    const cmsClassificationsByDomainsFilterUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS}${req.params.id}?classification_id=${req.query.classification}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,
      cmsLoginUrl,
      cmsClassificationsByDomainsFilterUrl,
      req.user.groups
    );
    log.debug(
      `<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsFilters successfully `
    );

    if (data.message)
      throw new IFPError(400,data.message)

    const filters = await Promise.all(Object.values(
      data.category ? data.category : data.subdomain
    ).map(async (theme) => {

      const themeObj = {
        id: theme.id,
        name: theme.title,
        showScreener: theme.show_screener,
        screenerConfiguration: theme.screener_configuration,
        subthemes: theme.sub_theme
          ? await Promise.all(Object.values(theme.sub_theme).map(async (subtheme) => {
              
              const subThemeObj = {
                id: subtheme.id,
                name: subtheme.title,
                showScreener: subtheme.show_screener,
                screenerConfiguration: subtheme.screener_configuration,
              };
              return subThemeObj;
            }))
          : [],
      };
      return themeObj;
    }));

    return filters;
  } catch (err) {
    log.error(
      `<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsFilters on getting CMS data with error ${err}`
    );
    throw err;
  }
}

async function getDomainNodes(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomainNodes`);
  try {

    if (!(req.params.id)){
      throw new IFPError(400,'No domain specified')
    }
    if (!req.query.classification){
      throw new IFPError(400,'No classification specified')
    }
    
    const queryMap = {
      classification: 'classification_id',
      category: 'category_id',
      subdomain: 'subdomain_id',
      subtheme: 'subtheme_id',
      product: 'product_id',
      search: 'search',
      entity_id: 'entity_id',
      smart_publisher: 'smart_publisher',
      from_date: 'from_date',
      to_date: 'to_date'
    };
    
    const queryParams = Object.entries(req.query)
      .map(([key, value]) => {
        if (queryMap[key]) {
          return `${queryMap[key]}=${value}`;
        }
        return null;
      })
      .filter(Boolean); 
    
    const { page = 1, limit = 10 } = req.query;
    
    queryParams.push(`page=${page}`);
    queryParams.push(`items_per_page=${limit}`);
    
    const queryString = queryParams.length ? `?${queryParams.join('&')}` : '';
    
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsDomainNodesUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAIN_NODES_LIST}${req.params.id}${queryString}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainNodesUrl, req.user.groups);
    try{
      if (req.baseUrl=='/api/mobile/content-type/domains')
        if (data.results.length){
          const singleDimensionNodes = await getSingleDimensionNodes(data.results.map(d=>d.id))
          let singleDimensionNodesMap = {}
          singleDimensionNodes.forEach(sNode=>{
            singleDimensionNodesMap[sNode.NODE_ID]=1
          })
          data.results.forEach(node=>{
            if (singleDimensionNodesMap[node.id])
              node.isMultiDimension = false
            else 
              node.isMultiDimension = true
          })
        }
    }
    catch(exp){

    }

    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomainNodes successfully `);
    return data;
  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomainNodes on getting CMS data with error ${err}`);
    throw err;
  }
}

module.exports = { getDomains, getDomainsNavigation, getDomainClassifications, getDomainClassificationsFilters, getDomainNodes };



