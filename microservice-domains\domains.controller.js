const { getMetaFromCMS} = require('../services/common-service');
require('dotenv').config();

const Logger = require('scad-library').logger;
const encodeUrl = require('encodeurl');
const log = new Logger().getInstance();
let constants = require('../config/constants.json');
let cms_mappings = require('../config/cms_mappings.json');
const {
	getScreenerCount,
	getSingleDimensionNodes,
  getSingleDimensionNodesV3,
	getIndicatorData,
	getScreenerData,
	getRankData,
	getDomainContentList,
	getDomainContentCount,
	getDomainContentProductList,
	getOfficialDomainFilteredData,
	getDomainContentFilters,
	getDomainOfficialStatisticsCount,
} = require("./services/executeQuery.service");

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { getViewNameById } = require('../services/executeQuery.service');
const { IFPError } = require('../utils/error');
const clkdb = require('../services/clk-database.service');
/**
 * function to get domains content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getDomains(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomains`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups);
    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomains successfully `);
    const domains = Object.values(data).map((domain) => {
      const domainObj = {
        id: domain.id,
        name: domain.name,
        icon: `${process.env.CMS_BASEPATH_URL}${domain.dark_icon}`,
        light_icon: `${process.env.CMS_BASEPATH_URL}${domain.light_icon}`
      }
      return domainObj;
    })

    return domains;
  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomains on getting CMS data with error ${err}`);
    throw err;
  }
}

/**
* function to get domains content from CMS
* @param {*} req 
* @param {*} res
* @param {*} next
*/
async function getDomainsNavigation(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomainsNavigation`);
  try {
    const { apiVersion } = req;
    const { search } = req.query
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    let cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V2;
    if (apiVersion == 2) {
      cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V3;
    }
    let cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${cmsDomainsUrlPath}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

    if (search && apiVersion == 2) {
      cmsDomainsUrl += `?keys=${search}`;
      cmsDomainsUrl = encodeUrl(cmsDomainsUrl);
    }

    const cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups)
    
    const cmsDomainDetailsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
    const cmsDomainData = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainDetailsUrl, req.user.groups); 

    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomains successfully `)

    let cmsCacheKey = `cmsNavigation_${crypto.createHash('md5').update(JSON.stringify(cmsResponse)).digest("hex")}`
    const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
    if (cmsCacheResults) {
      log.info(`<<<<<Cache found for microservice-domains.controller.getDomainsNavigation`);
      return JSON.parse(cmsCacheResults);
    }

    let domainName;
    let subdomainName;
    let subthemeName;
    let restructuredData={}

    let AnalyticalApps = constants.classifications.analyticalApps
    let Reports = constants.classifications.reports
    let IFPIndicators = constants.classifications.innovativeStatistics
    let OfficialStatistics = constants.classifications.officialStatistics

    let nodeData =await getIndicatorData(req);
    if (nodeData){
      restructuredData = await restructureNavigationData(nodeData,cmsDomainData);
    }

    const result = await Promise.all(Object.values(cmsResponse.data).map(async classification => {
      const classificationObj = {
        id: classification.id,
        name: classification.classification,
        key: classification.key,
        light_icon: `${process.env.CMS_BASEPATH_URL}${classification.light_icon}`,
        dark_icon: `${process.env.CMS_BASEPATH_URL}${classification.dark_icon}`,
        nodeCount: 0,
        showTree: true
      }
      let nodeCount = 0
      if ([AnalyticalApps, Reports].includes(classification.key)) {
        let domains = Object.values(classification.domains).map((domain) => {
          let currentDomain;
          if (apiVersion == 2) {
            currentDomain = domain
          } else {
            currentDomain = Object.values(cmsResponse.domains).find(element => element.id == domain.id);
          }
          let domainName = domain.name.toLowerCase().split(' ').join('-');
          const domainObj = {
            id: domain.id,
            name: domain.name,
            nodeCount: domain.node_count,
            route: `/domain-exploration/${domainName}/${domain.id}`,
            light_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.light_icon}`,
            dark_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.dark_icon}`,
            showTree: false,
            nodes: domain.nodes
          }
          nodeCount += domain.node_count
          return domainObj
        }
        ).filter(domain => domain)
        classificationObj.nodeCount = nodeCount
        classificationObj.showTree = false
        classificationObj.domains = domains
      }

      else if ([IFPIndicators].includes(classification.key)) {
        if (classification.domains) {
          let domains = await Promise.all(Object.values(classification.domains).map(async (domain) => {
            let currentDomain;
            if (apiVersion == 2) {
              currentDomain = domain;
            } else {
              currentDomain = Object.values(cmsResponse.domains).find(element => element.id == domain.id);
            }
            domain.name = domain.name.replace(/\s+/g, ' ');
            let domainName = domain.name.toLowerCase().split(' ').join('-');
            let domainNodeCount = domain.node_count?domain.node_count:1
            const domainObj = {
              id: domain.id,
              name: domain.name,
              light_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.light_icon}`,
              dark_icon: `${process.env.CMS_BASEPATH_URL}${currentDomain.dark_icon}`,
              route: `/domain-exploration/${domainName}/${domain.id}`,
              showTree: true,
              nodeCount: domain.node_count,
              subdomains: await Promise.all(Object.values(domain.subdomains).map(async (subdomain) => {
                subdomain.name = subdomain.name.replace(/\s+/g, ' ');
                let subdomainName = subdomain.name.toLowerCase().split(' ').join('-');
                const subDomainObj = {
                  id: subdomain.id,
                  name: subdomain.name,
                  route: `/domain-exploration/${domainName}/${subdomainName}/${subdomain.id}`,
                  showTree: true,
                  nodeCount: subdomain.node_count,
                  subthemes: await Promise.all(Object.values(subdomain.subthemes).map(async (subtheme) => {
                    subtheme.name = subtheme.name.replace(/\s+/g, ' ');
                    let subthemeName = subtheme.name.toLowerCase().split(' ').join('-');
                    const subThemeObj = {
                      id: subtheme.id,
                      name: subtheme.name,
                      showTree: true,
                      route: `/domain-exploration/${domainName}/${subdomainName}/${subthemeName}/${subtheme.id}`,
                      nodeCount: subtheme.node_count,
                    }
                    // if (classification.key == constants.classifications.officialStatistics && ['3193','3260'].includes(subtheme.id))
                    //   subThemeObj.screener = true
                    // else
                    //   subThemeObj.screener = false

                    try{
                      subThemeObj.products = Object.values(subtheme.products).map(product => {
                        return {
                          "name": product.name,
                          "showTree": true,
                          "nodes":product.nodes
                        }
                      })                       
                      
                    }
                    catch(e){

                    }
                    if (classification.key == constants.classifications.officialStatistics ){
                      subThemeObj.screener = subtheme.show_screener
                      subThemeObj.screenerConfiguration = subtheme.screener_configuration

                      if (subtheme.screener_configuration){
                        let dbCount=0;
                        let screenerConfiguration = {...subtheme.screener_configuration};
                        
                        if (Number(screenerConfiguration.screenerView)){
                          let viewName = await getViewNameById(screenerConfiguration.screenerView)
                          screenerConfiguration.screenerView = viewName
                        }
                        if (Object.keys(constants.censusViewMap).includes(screenerConfiguration.screenerView))
                          subThemeObj.products = []
                        dbCount = await getScreenerCount(screenerConfiguration)
                        
                        subThemeObj.count = dbCount?Number(dbCount):0
                        subThemeObj.screenerConfiguration = screenerConfiguration
                        domainNodeCount+=subThemeObj.count-1
                      }
                    }

                    return subThemeObj
                  }))
                }
                if (classification.key == constants.classifications.innovativeStatistics){
                  subDomainObj.screener = subdomain.show_screener
                  if (subdomain.screener_configuration){
                    let dbCount=0;
                    let screenerConfiguration = {...subdomain.screener_configuration};
                    
                    if (Number(screenerConfiguration.screenerView)){
                      let viewName = await getViewNameById(screenerConfiguration.screenerView)
                      screenerConfiguration.screenerView = viewName
                    }
                    
                    dbCount = await getScreenerCount(screenerConfiguration)
                    
                    subDomainObj.count = dbCount?Number(dbCount):0
                    subDomainObj.screenerConfiguration = screenerConfiguration
                    domainNodeCount+=subDomainObj.count-1
                  }
                }
                return subDomainObj;
              }))
            }
            nodeCount += domain.node_count;
            domainObj.nodeCount = domainNodeCount
            return domainObj
          }))
          classificationObj.showTree = [OfficialStatistics].includes(classification.key) ? true : false
          classificationObj.nodeCount = nodeCount
          classificationObj.domains = domains.sort((a,b) => b.nodeCount-a.nodeCount)
        }
      }
      else if ([OfficialStatistics].includes(classification.key)) {
        classificationObj.domains = restructuredData;
        classificationObj.nodeCount = nodeData.length;
      }

      return classificationObj;
    }))

    setRedis(cmsCacheKey, JSON.stringify(result), 8000, req.headers);
    return result

  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomainsNavigation on getting CMS data with error ${err}`);
    throw err;
  }
}


async function getDomainClassificationsV2(req) {
	log.debug(
		`>>>>>Entered domains-microservice.domains.controller.getDomainClassifications`
	);
	try {
		const lang = req.headers["accept-language"] === "en" ? "" : `/${req.headers["accept-language"]}`;
		let domainID = req.params.id;
    const userGroups = req.user.groups;

		const domainIdCMSMap =
			cms_mappings.domainIDCMSMap[process.env.NODE_ENV] || {};
		const domainIDCMS = domainIdCMSMap[domainID] || domainID;
		const domainIDBE =
			Object.keys(domainIdCMSMap).find(
				(key) => domainIdCMSMap[key].toString() === domainID
			) || domainID;

		const cmsClassificationsByDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN}${domainIDCMS}`;
		const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
		let data = await getMetaFromCMS(
			req,
			cmsLoginUrl,
			cmsClassificationsByDomainsUrl,
			
		);
		log.debug(
			`<<<<<Exited domains-microservice.domains.controller.getDomainClassifications successfully `
		);

		// Modify CMS response (data) to change count of official statistics from DB
    const officialStatisticsCount = await getDomainOfficialStatisticsCount(
			domainIDBE,
			userGroups
		);
    const officialStats = data.classification.find(
			(item) => item.key === "official_statistics"
		);
		if (officialStats) {
      officialStats.count = Number(officialStatisticsCount[0]?.count) || 0;
		}

		let cmsClassificationDomainUrl = [];

		let screenerConfig = {
			experimental_statistics: [],
			official_statistics: [],
		};

		let classifications = [];
		for (classification of data.classification) {
			const classificationObj = {
				id: classification.id,
				key: classification.key,
				name: classification.name,
        description: classification.description,
				light_icon: `${process.env.CMS_BASEPATH_URL}${classification.light_icon}`,
				dark_icon: `${process.env.CMS_BASEPATH_URL}${classification.dark_icon}`,
				count: classification.key === "official_statistics"
          ? Number(officialStats?.count) || 0
          : parseInt(classification.count),
			};

			if (constants.screenerClassificationKeys.includes(classification.key)) {
				cmsClassificationDomainUrl.push(
					`${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS}${req.params.id}?classification_id=${classification.id}`
				);
			}
			classifications.push(classificationObj);
		}

		const subDataPromises = cmsClassificationDomainUrl.map(async (url) => {
			const data = await getMetaFromCMS(req, cmsLoginUrl, url, req.user.groups);
			return data.subdomain;
		});

		const dataCollection = await Promise.all(subDataPromises);
		dataCollection.flat().map(async (subDomain) => {
			if (subDomain.show_screener) {
				classification.count = classification.count - 1;
				screenerConfig.experimental_statistics.push(
					subDomain.screener_configuration
				);
			} else {
				if (subDomain.sub_theme && subDomain.sub_theme.length) {
					subDomain.sub_theme.map(async (subTheme) => {
						if (subTheme.show_screener) {
							classification.count = classification.count - 1;
							screenerConfig.official_statistics.push(
								subTheme.screener_configuration
							);
						}
					});
				}
			}
		});

		let count = {
			experimental_statistics: 0,
			official_statistics: 0,
		};
		let dataCountPromises = [];
		for (classificationKey of Object.keys(screenerConfig)) {
			for (config of screenerConfig[classificationKey]) {
				if (Number(config.screenerView)) {
					let viewName = await getViewNameById(config.screenerView);
					config.screenerView = viewName;
				}
				const countData = await getScreenerCount(config);
				count[classificationKey] += Number(countData) || 0;
			}
		}

		await Promise.all(dataCountPromises);
		classifications.map((classification) => {
			if (constants.screenerClassificationKeys.includes(classification.key)) {
				classification.count += Number(count[classification.key]) || 0;
			}
		});
    
    if(data.domain && data.domain.light_icon && data.domain.dark_icon) {
  		data.domain.light_icon = `${process.env.CMS_BASEPATH_URL}${data.domain.light_icon}`;
	  	data.domain.dark_icon = `${process.env.CMS_BASEPATH_URL}${data.domain.dark_icon}`;
    }

		const classificationRespObj = {
			classification: classifications,
			domain: data.domain,
		};
		return classificationRespObj;
	} catch (err) {
		log.error(
			`<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsOld on getting CMS data with error ${err}`
		);
		throw err;
	}
}

async function getDomainClassificationsFilters(req) {
  log.debug(
    `>>>>>Entered domains-microservice.domains.controller.getDomainClassificationsFilters`
  );
  try {
    if (!req.params.id) {
      throw new IFPError(400,'No domain specified')
    }
    if (!req.query.classification) {
      throw new IFPError(400,'No classification specified')
    }

    const lang =
      req.headers["accept-language"] === "en"
        ? ""
        : `/${req.headers["accept-language"]}`;
    const cmsClassificationsByDomainsFilterUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS}${req.params.id}?classification_id=${req.query.classification}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,
      cmsLoginUrl,
      cmsClassificationsByDomainsFilterUrl,
      req.user.groups
    );
    log.debug(
      `<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsFilters successfully `
    );

    if (data.message)
      throw new IFPError(400,data.message)

    const filters = await Promise.all(Object.values(
      data.category ? data.category : data.subdomain
    ).map(async (theme) => {

      const themeObj = {
        id: theme.id,
        name: theme.title,
        showScreener: theme.show_screener,
        screenerConfiguration: theme.screener_configuration,
        subthemes: theme.sub_theme
          ? await Promise.all(Object.values(theme.sub_theme).map(async (subtheme) => {
              
              const subThemeObj = {
                id: subtheme.id,
                name: subtheme.title,
                showScreener: subtheme.show_screener,
                screenerConfiguration: subtheme.screener_configuration,
              };
              return subThemeObj;
            }))
          : [],
      };
      return themeObj;
    }));

    return filters;
  } catch (err) {
    log.error(
      `<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsFilters on getting CMS data with error ${err}`
    );
    throw err;
  }
}

async function getDomainClassificationsFiltersV3(req) {
	log.debug(
		`>>>>>Entered domains-microservice.domains.controller.getDomainClassificationsFiltersV3`
	);
	try {
		if (!req.params.id) {
			throw new IFPError(400, "No domain specified");
		}
		if (!req.query.classification) {
			throw new IFPError(400, "No classification specified");
		}
    const userGroups = req.user.groups;
		if (!userGroups || userGroups.length == 0) {
			throw new IFPError(400, "No user groups found in request");
		}

		const lang = req.headers["accept-language"] === "en" ? "" : `/${req.headers["accept-language"]}`;
    const env = process.env.NODE_ENV || "dev";

    // Domain ID and mapping
    const domainID = req.params.id;
    const domainIdCMSMap = cms_mappings.domainIDCMSMap[process.env.NODE_ENV] || {};
		const domainIDCMS = domainIdCMSMap[domainID] || domainID;
		const domainIDBE =
			Object.keys(domainIdCMSMap).find(
				(key) => domainIdCMSMap[key].toString() === domainID
			) || domainID;

    // Classification ID and mapping
		const classificationID = req.query.classification;
    const classificationIDCMSMap = cms_mappings.classificationIDCMSMap[env];
    const classificationKey = classificationIDCMSMap[classificationID] || classificationID;

    let response = null;
    let data = null;
    if (classificationKey == "official_statistics") {
      data = await getDomainContentFilters(
				domainIDBE,
				userGroups,
				req.headers["accept-language"]
			);
      // Transform data to match expected structure
      const themeMap = new Map();

      for (const item of data) {
        if (!themeMap.has(item.id)) {
          const themeObj = {
            id: item.id,
            name: item.name,
            showScreener: item.showScreener || false,
            subthemes: [],
          };
          if (item.showScreener && item.screenerConfiguration) {
            try {
              themeObj.screenerConfiguration = JSON.parse(item.screenerConfiguration);
            } catch (e) {
              themeObj.screenerConfiguration = item.screenerConfiguration;
            }
          }
          themeMap.set(item.id, themeObj);
        }
        const subthemeObj = {
          id: item.subthemeID,
          name: item.subthemeName,
          showScreener: item.subthemeShowScreener || false,
        };
        if (item.subthemeShowScreener && item.screenerConfiguration) {
          try {
            subthemeObj.screenerConfiguration = JSON.parse(item.screenerConfiguration);
          } catch (e) {
            subthemeObj.screenerConfiguration = item.screenerConfiguration;
          }
        }
        themeMap.get(item.id).subthemes.push(subthemeObj);
      }

			response = Array.from(themeMap.values());
    } else {

      const cmsClassificationsByDomainsFilterUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS}${domainIDCMS}?classification_id=${classificationID}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      data = await getMetaFromCMS(
        req,
        cmsLoginUrl,
        cmsClassificationsByDomainsFilterUrl,
        req.user.groups
      );
      log.debug(
        `<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsFiltersV3 successfully `
      );
  
      if (data.message) throw new IFPError(400, data.message);
  
      response = await Promise.all(
				Object.values(data.category ? data.category : data.subdomain).map(
					async (theme) => {
						const themeObj = {
							id: theme.id,
							name: theme.title,
							showScreener: theme.show_screener,
							screenerConfiguration: theme.screener_configuration,
							subthemes: theme.sub_theme
								? await Promise.all(
										Object.values(theme.sub_theme).map(async (subtheme) => {
											const subThemeObj = {
												id: subtheme.id,
												name: subtheme.title,
												showScreener: subtheme.show_screener,
												screenerConfiguration: subtheme.screener_configuration,
											};
											return subThemeObj;
										})
								  )
								: [],
						};
						return themeObj;
					}
				)
			);

    }

		return response;
	} catch (err) {
		log.error(
			`<<<<<Exited domains-microservice.domains.controller.getDomainClassificationsFilters on getting CMS data with error ${err}`
		);
		throw err;
	}
}

async function getDomainNodes(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomainNodes`);
  try {

    if (!(req.params.id)){
      throw new IFPError(400,'No domain specified')
    }
    if (!req.query.classification){
      throw new IFPError(400,'No classification specified')
    }
    
    const queryMap = {
      classification: 'classification_id',
      category: 'category_id',
      subdomain: 'subdomain_id',
      subtheme: 'subtheme_id',
      product: 'product_id',
      search: 'search',
      entity_id: 'entity_id'
    };
    
    const queryParams = Object.entries(req.query)
      .map(([key, value]) => {
        if (queryMap[key]) {
          return `${queryMap[key]}=${value}`;
        }
        return null;
      })
      .filter(Boolean); 
    
    const { page = 1, limit = 10 } = req.query;
    
    queryParams.push(`page=${page}`);
    queryParams.push(`items_per_page=${limit}`);
    
    const queryString = queryParams.length ? `?${queryParams.join('&')}` : '';
    
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsDomainNodesUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAIN_NODES_LIST}${req.params.id}${queryString}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainNodesUrl, req.user.groups);
    try{
      if (req.baseUrl=='/api/mobile/content-type/domains')
        if (data.results.length){
          const singleDimensionNodes = await getSingleDimensionNodes(data.results.map(d=>d.id))
          let singleDimensionNodesMap = {}
          singleDimensionNodes.forEach(sNode=>{
            singleDimensionNodesMap[sNode.NODE_ID]=1
          })
          data.results.forEach(node=>{
            if (singleDimensionNodesMap[node.id])
              node.isMultiDimension = false
            else 
              node.isMultiDimension = true
          })
        }
    }
    catch(exp){

    }

    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomainNodes successfully `);
    return data;
  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomainNodes on getting CMS data with error ${err}`);
    throw err;
  }
}

async function getDomainNodesV3(req) {
  log.debug(`>>>>>Entered domains-microservice.domains.controller.getDomainNodesV3`);
  try {

    if (!(req.params.id)){
      throw new IFPError(400,'No domain specified')
    }
    if (!req.query.classification){
      throw new IFPError(400,'No classification specified')
    }
    const userGroups = req.user.groups;
    if (!userGroups || userGroups.length == 0) {
      throw new IFPError(400, 'No user groups found in request');
    }
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const env = process.env.NODE_ENV || 'dev';
    
    // Domain ID and mapping
    let domainID = req.params.id;
    const domainIdCMSMap =cms_mappings.domainIDCMSMap[process.env.NODE_ENV] || {};
		const domainIDCMS = domainIdCMSMap[domainID] || domainID;

		const domainIDBE =
			Object.keys(domainIdCMSMap).find(
				(key) => domainIdCMSMap[key].toString() === domainID
			) || domainID;
    
    // Classification ID and mapping
    const classificationID = req.query.classification;
    const classificationIDCMSMap = cms_mappings.classificationIDCMSMap[env];

    const subdomainID = req.query.subdomain;
    const productID = req.query.product;
    const subthemeID = req.query.subtheme;
    const search = req.query.search;
    const classificationKey = classificationIDCMSMap[classificationID] || classificationID;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const offset = (page - 1) * limit;
    const data_security = req.query.data_security;

    let response = null;
    
    // Use classificationKey as needed
    if (classificationKey == "official_statistics"){
      // Use mapped CMS domain ID if available, else fallback to original param
      const [rowData, countRows, products] = await Promise.all([
				getDomainContentList(
					domainIDBE,
					subdomainID,
          subthemeID,
          productID,
          search,
					userGroups,
					limit,
					offset,
					req.headers["accept-language"],
          data_security
				),
				getDomainContentCount(
					domainIDBE,
					subdomainID,
          subthemeID,
          productID,
          search,
					userGroups,
					req.headers["accept-language"],
          data_security
				),
				getDomainContentProductList(
					domainIDBE,
					subdomainID,
          subthemeID,
					userGroups,
					req.headers["accept-language"]
				),
			]);

      // Final JSON response
      response = {
        results: rowData,
        total_count: countRows[0]?.total || 0,
        products
      };
    } else {
      const queryMap = {
        classification: 'classification_id',
        category: 'category_id',
        subdomain: 'subdomain_id',
        subtheme: 'subtheme_id',
        product: 'product_id',
        search: 'search',
        entity_id: 'entity_id',
        data_security: 'security_id',
        smart_publisher: 'smart_publisher',
        from_date: 'from_date',
        to_date: 'to_date'
      };
      
      const queryParams = Object.entries(req.query)
        .map(([key, value]) => {
          if (queryMap[key]) {
            return `${queryMap[key]}=${value}`;
          }
          return null;
        })
        .filter(Boolean);
      
      queryParams.push(`page=${page}`);
      queryParams.push(`items_per_page=${limit}`);
      
      const queryString = queryParams.length ? `?${queryParams.join('&')}` : '';
      // Use mapped CMS domain ID if available, else fallback to original param
      const cmsDomainNodesUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAIN_NODES_LIST}${domainIDCMS}${queryString}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      response = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainNodesUrl, req.user.groups);
    }
    
    // Process the response for mobile content-type.
    try{
      if (req.baseUrl=='/api/mobile/content-type/domains')
        if (response.results.length){
          const singleDimensionNodes = await getSingleDimensionNodes(response.results.map(d=>d.id))
          let singleDimensionNodesMap = {}
          singleDimensionNodes.forEach(sNode=>{
            singleDimensionNodesMap[sNode.NODE_ID]=1
          })
          response.results.forEach(node=>{
            if (singleDimensionNodesMap[node.id])
              node.isMultiDimension = false
            else 
              node.isMultiDimension = true
          })
        }
    } catch(exp){
      log.error(`Error processing domain nodes: ${exp}`);
    }

    log.debug(`<<<<<Exited domains-microservice.domains.controller.getDomainNodes successfully `);
    return response;
  } catch (err) {
    log.error(`<<<<<Exited domains-microservice.domains.controller.getDomainNodes with error ${err}`);
    throw err;
  }
}

/**
 * Restructures navigation data by organizing it into a hierarchical structure of domains, themes, subthemes, and products.
 * Each level contains metadata and routes for navigation, and nodes are added to products based on unique indicators.
 *
 * @async
 * @function restructureNavigationData
 * @param {Array<Object>} data - The raw navigation data containing domain, theme, subtheme, product, and node information.
 * @param {Array<Object>} cmsDomainData - The CMS domain data containing metadata such as icons for each domain.
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of domain objects, each containing hierarchical navigation data.
 *
 * @example
 * const data = [
 *   {
 *     domain_id: 1,
 *     theme_id: 101,
 *     subtheme_id: 1001,
 *     product_id: 2001,
 *     indicator_id: 3001,
 *     domain: "Health",
 *     theme: "Nutrition",
 *     subtheme: "Diet",
 *     product: "Food Survey",
 *     node_id: 4001,
 *     title: "Calorie Intake",
 *     data_source: "Survey Data",
 *   },
 *   // More data objects...
 * ];
 *
 * const cmsDomainData = [
 *   { id: 575, light_icon: "/icons/health-light.png", dark_icon: "/icons/health-dark.png" },
 *   // More CMS domain data objects...
 * ];
 *
 * const result = await restructureNavigationData(data, cmsDomainData);
 * console.log(result);
 */
async function restructureNavigationData(data, cmsDomainData) {
  const output = {};
  const domainIndicatorMap = {};
  const addedIndicators = new Set();
  const subthemes = [...new Set(data.map(obj => obj.subtheme_id))];
  const screenerData = await getScreenerData(subthemes);

  const domainMapper = cms_mappings.domainIDCMSMap[process.env.NODE_ENV]

  const cmsDomainDataMap = cmsDomainData.reduce((map, item) => {
    map[item.id] = item;
    return map;
  }, {});

  data.forEach(item => {
    const {
      domain_id: domainId,
      theme_id: themeId,
      subtheme_id: subthemeId,
      product_id: productId,
      indicator_id: indicatorId,
      domain,
      theme,
      subtheme,
      product,
      node_id: nodeId,
      title,
      data_source: dataSource,
    } = item;

    const domainName = domain.toLowerCase().replace(/\s+/g, '-');
    const themeName = theme.toLowerCase().replace(/\s+/g, '-');
    const subthemeName = subtheme.toLowerCase().replace(/\s+/g, '-');
    const cmsDomain = cmsDomainDataMap[domainMapper[parseInt(domainId)]];
    const env = process.env.NODE_ENV || 'dev';

    if (!output[domainId]) {
      output[domainId] = {
        id: domainId,
        name: domain,
        route: `/domain-exploration/${domainName}/${domainId}`,
        showTree: true,
        subdomains: [],
        light_icon: `${process.env.CMS_BASEPATH_URL}${cmsDomain.light_icon}`,
        dark_icon: `${process.env.CMS_BASEPATH_URL}${cmsDomain.dark_icon}`,
      };
      domainIndicatorMap[domainId] = new Set();
    }

    const domainObj = output[domainId];
    let themeObj = domainObj.subdomains.find(t => t.id === themeId);

    if (!themeObj) {
      themeObj = {
        id: themeId,
        name: theme,
        route: `/domain-exploration/${domainName}/${themeName}/${themeId}`,
        showTree: true,
        subthemes: [],
      };
      domainObj.subdomains.push(themeObj);
    }

    let subthemeObj = themeObj.subthemes.find(st => st.id === subthemeId);
    const screener = screenerData.find(item => item.subtheme_id === subthemeId);

    if (!subthemeObj) {
      subthemeObj = {
        id: subthemeId,
        name: subtheme,
        route: `/domain-exploration/${domainName}/${themeName}/${subthemeName}/${subthemeId}`,
        showTree: screener ? true : false,
        screener: !!screener,
        screenerConfiguration: screener ? JSON.parse(screener?.config) : [],
        products: [],
      };
      themeObj.subthemes.push(subthemeObj);
    }

    let productObj = subthemeObj.products.find(p => p.id === productId);

    if (!productObj && !screener) {
      productObj = {
        id: productId,
        name: product,
        nodes: [],
      };
      subthemeObj.products.push(productObj);
    }

    if (!addedIndicators.has(indicatorId) && !screener) {
      productObj.nodes.push({
        id: nodeId,
        note: indicatorId,
        title,
        data_source: dataSource,
        app_type: null,
        content_type: "scad_official_indicator",
        content_classification: {
          name: "Official Statistics",
          key: "official_statistics",
        },
        category: {
          id: Object.keys(cms_mappings.classificationIDCMSMap[env])
          .find(k => cms_mappings.classificationIDCMSMap[env][k] === "official_statistics"),
          name: "Official Statistics",
        },
      });

      addedIndicators.add(indicatorId);
      domainIndicatorMap[domainId].add(indicatorId);
    }
  });

  return Object.values(output).map(domain => ({
    ...domain,
    nodeCount: domainIndicatorMap[domain.id].size,
  }));
}


module.exports = {
	getDomains,
	getDomainsNavigation,
	getDomainClassificationsFilters,
	getDomainNodes,
	getDomainNodesV3,
	restructureNavigationData,
	getDomainClassificationsFiltersV3,
	getDomainClassificationsV2,
};
