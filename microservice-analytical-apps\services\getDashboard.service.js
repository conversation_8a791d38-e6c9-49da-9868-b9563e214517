const constants = require('../../config/constants.json');
const axios = require('axios');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getDashboardData(cmsResponse, req) {
    return new Promise((resolve, reject) => {
        let lang = `${req.headers["accept-language"]}`;
        const host = req.get('host');
        if (req.body.visualizations && req.body.visualizations.length > 0) {
            let responseObj = []; let counter = [];
            req.body.visualizations.forEach(visualization => {
                let url;
                if (req.baseUrl.includes('/api/mobile/'))
                    url = `http://${host}/api/mobile/content-type/statistics-insights/filter`;
                else
                    url = `http://${host}/api/content-type/statistics-insights/filter`;
                log.info(`API call to ${url} from getDashboard.service.js`);
                axios.post(`${url}`, visualization, {
                        headers: { Authorization: req.headers.authorization }
                    }
                ).then(response => {
                    responseObj.push(response.data);
                    counter.push(1);

                    if (counter.length === req.body.visualizations.length) {
                        resolve(responseObj);
                    }
                }).catch(err => {
                    
                    reject(err);
                })
            })
        } else {
            let indicatorList = cmsResponse.indicator_list.split(',').map(function (item) {
                return item.trim();
            });

            if (indicatorList.length > 0) {
                cmsResponse["sortVisualizations"] = true;
                cmsResponse.visualizations = [];
                let invalidData = [];
                indicatorList.forEach((id, index) => {
                    let url;
                    if (req.baseUrl.includes('/api/mobile/'))
                        url = `http://${host}/api/mobile/content-type/statistics-insights/${id}`;
                    else
                        url = `http://${host}/api/content-type/statistics-insights/${id}`;
                    log.info(`API call to ${url} from getDashboard.service.js`);
                    axios.get(`${url}`, {
                        headers: {
                            Authorization: req.headers.authorization,
                            appType: 'insights-discovery',
                            "Accept-Language":lang
                        }
                    }).then(response => {
                        let sortOrderCount = index + 1;
                        response.data["sortOrder"] = (invalidData.length > 0) ? sortOrderCount - invalidData.length : sortOrderCount;
                        cmsResponse.visualizations.push(response.data);
                        if (indicatorList.length === cmsResponse.visualizations.length + invalidData.length) {
                            resolve(cmsResponse);
                        }
                        
                    }).catch(err => {
                        
                        log.error(err.response.data);
                        if (err.response.status != 200 ) invalidData.push(1);
                        if ( invalidData.length >= indicatorList.length  ) return reject(err.response.data);
                    })
                });

            }

        }
    })
};

module.exports = { getDashboardData }