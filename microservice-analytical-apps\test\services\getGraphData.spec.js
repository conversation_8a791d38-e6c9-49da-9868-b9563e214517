const graphDataService = require('../../services/getGraphData.service');
const getQueryService = require('../../services/getQuery.service');
const databaseService = require('../../../services/database.service');

describe('controller', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    test('should get json response from newsLetters controller - success', async () => {
        await databaseService.initialize();
        const mockComboId = "E0000";
        const mockVisualization = {};
        
        jest.spyOn(getQueryService,'getQuery');
        jest.spyOn(graphDataService,'getData');
        jest.spyOn(databaseService,'simpleExecute');

    });
});