const express = require("express");
const router = new express.Router();
const aiInsightReportController = require("../microservice-ai-insight-report/ai-insight-report.controller");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const groupService = require("../microservice-ai-insight-report/services/SMEsGroups.service.json");
const insightConstants = require("../microservice-ai-insight-report/helper/constants.json");

function checkGroup(req, res, next) {
  if (process.env.NODE_ENV == "prod") {
    const userGroups = req.user.groups;
    // check if user belongs to the required group

    // if non of required groups in user grups, return 403 with {access: false }
    const requiredGroups = [...Object.values(groupService.SMEsGroups), groupService.consolidateGroup];
    if (!userGroups.some((group) => requiredGroups.includes(group))) {
      return res.status(403).json({ access: false });
    }

    // identify which group the sme is
    // eg: if user is eco sme then update req object with custom attribute

    const domainMatchedGroup = Object.entries(groupService.SMEsGroups).find(
      ([key, value]) => userGroups.includes(value)
    );
    const hasConsolidatedGroup = userGroups.includes(groupService.consolidateGroup.consolidate);

    if (domainMatchedGroup) {
      const [key, value] = domainMatchedGroup;
      if (userGroups.includes(value)) {
        req.user.domainSme = key;
      }
    }

    // if user group has consolidated group
    req.user.hasConsolidateGroup = hasConsolidatedGroup;
  }
  next();
}

// access check API
router.get("/access-check", checkGroup, async (req, res, next) => {
  try { 
    // access handled by checkGroup middleware
     const data = {
       "status":200,
       "access": true
     };
     res.set("Content-Type", "application/json");
     res.send(data);
     next();
   } catch (err) {
     log.error(`Error fetching data for access check ERROR: ${err}`);
     next(err);
   }
}); 

// Get Report Status List
router.get("/report-status", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.reportStatusDropdownList(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for report  status dropdown list ERROR: ${err}`
    );
    next(err);
  }
});

// Get Domain/consolidate Report List
router.get("/report", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.insightReportList(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for ai-insight-report ERROR: ${err}`);
    next(err);
  }
});

// Get Domain/consolidate Report
router.get("/report/:id", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.getKeyDrivers(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for ai-insight-report ERROR: ${err}`);
    next(err);
  }
});

// Section wise Edit Domain/consolidate Report
router.patch("/report/:id", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.updateGeneratedData(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for ai-insight-report ERROR: ${err}`);
    next(err);
  }
});

router.patch("/report/status/:sanadkomTicketId", async (req, res, next) => {
  try {
    const data = await aiInsightReportController.updateReportStatusByTicketId(
      req
    );
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error updating ai-insight-report ERROR: ${err}`);
    next(err);
  }
});

// API for regenerating Report
router.post("/report-regenerate", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.regenerateReport(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for ai-insight-report ERROR: ${err}`);
    next(err);
  }
});

//Smart Publisher API's

// Ticket create api for Domain insight Report Approval workflow
router.post("/domain-ticket", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.domainTicketCreate(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error creating ticket for doamin-insight-report ERROR:: ${err}`);
    next(err);
  }
});

// Ticket  create api for Consolidated Report Approval workflow
router.post("/consolidated-ticket", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.consolidateTicketCreate(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error creating ticket for consolidated-insight-report ERROR: ${err}`
    );
    next(err);
  }
});

// Download Report  PDF
router.get("/report-download", checkGroup, async (req, res, next) => {
  try {
    const data = await aiInsightReportController.downloadGeneratedPdf(req);
      res.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
      res
        .setHeader("Content-Type", "application/pdf")
        .setHeader(
          "Content-Disposition",
          `attachment; filename=${data.reportName}.pdf`
        )
        .end(data.pdfBuffer);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for ai-insight-report download ERROR: ${err}`
    );
    next(err);
  }
});

// Quarterly report creation email send  for Domain/consolidate
router.get("/report-email-send", async (req, res, next) => {
  try {
    const data = await aiInsightReportController.sendAiInsightReportEmail(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for ai-insight-report ERROR: ${err}`);
    next(err);
  }
});

// webhook api for ticket and report status update
router.post("/ticket-status-update-webhook", async (req, res, next) => {
  try {
    const data = await aiInsightReportController.ticketStatusUpdateWebhook(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for ai-insight-report report status update ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
