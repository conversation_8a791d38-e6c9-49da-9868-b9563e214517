const express = require('express');
const router = new express.Router();
const eciController = require('../microservice-eci/eci.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/auth', async (req, res, next) => {
    try {
      const data = await eciController.authEci(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

module.exports = router;