
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getPortfolioDynamicQueryWithDimension(visualization, isFilterPanelEnabled,filterPanel) {
  return new Promise((resolve, reject) => {
    try {
      let dbIndicatorIds = [];
      let dbIndicatorColumn = visualization.dbColumn;
      let query = "";
      let viewName = visualization.viewName ? visualization.viewName : "VW_STAT_INDICATORS";
      let dimensionColumns = []
      if (filterPanel){
        filterPanel.properties.forEach(property =>{
          dimensionColumns.push(`t.${property.path}`)
        })
      }
      if (visualization.seriesMeta.length > 0) {
        visualization.seriesMeta.forEach(series => {
          if ((series.keyCollection && series.keyCollection.length) > 0) {
            series.keyCollection.forEach(collection => {
              if (collection.dbIndicatorId) {
                dbIndicatorIds.push(collection.dbIndicatorId.toUpperCase());
              }
            });
          } else if (series.dbIndicatorId) {
            dbIndicatorIds.push(series.dbIndicatorId.toUpperCase());
          }
        });
        let indicatorIds = dbIndicatorIds.map(i => `'${i}'`).join(',');
        if (indicatorIds.length > 0) {
          query = `SELECT ${dimensionColumns.join(',')},
                    t.VALUE,
                    MAX(t.VALUE) OVER () AS MAX_VALUE,
                    MIN(t.VALUE) OVER () AS MIN_VALUE,
                    toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
                    toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
                    toString(toDate(toString(t.OBS_DT),	'yyyyMMdd')) AS OBS_DT,
                    substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR 
                  FROM ${viewName} t WHERE UPPER(${dbIndicatorColumn}) IN (${indicatorIds})`
        } else {
          query = `SELECT ${dimensionColumns.join(',')},
                    t.VALUE,
                    MAX(t.VALUE) OVER () AS MAX_VALUE,
                    MIN(t.VALUE) OVER () AS MIN_VALUE,
                    toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
                    toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
                    toString(toDate(toString(t.OBS_DT),	'yyyyMMdd')) AS OBS_DT,
                    substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR 
                  FROM ${viewName} t`
        }
        if (visualization.filterBy) {
          filterQuery(visualization.filterBy, query).then((result) => {
            query = `${result} ORDER BY OBS_DT`
            resolve(query);
          })
        } else {
          resolve(`${query} ORDER BY OBS_DT`);
        }
      }
    } catch (err) {
      
      log.error(`<<<<<< Exited microservice-official-insights.services.getQuery.service.getPortfolioDynamicQueryWithDimension with error ${err} `);
      reject([424, err]);
    }
  });
}


async function getPortfolioDynamicQuery(visualization) {
  return new Promise((resolve, reject) => {
    try {
      let dbIndicatorIds = [];
      let dbIndicatorColumn = visualization.dbColumn;
      let query = "";
      let viewName = visualization.viewName ? visualization.viewName : "VW_STAT_INDICATORS";
      if (visualization.seriesMeta.length > 0) {
        visualization.seriesMeta.forEach(series => {
          if ((series.keyCollection && series.keyCollection.length) > 0) {
            series.keyCollection.forEach(collection => {
              if (collection.dbIndicatorId) {
                dbIndicatorIds.push(collection.dbIndicatorId.toUpperCase());
              }
            });
          } else if (series.dbIndicatorId) {
            dbIndicatorIds.push(series.dbIndicatorId.toUpperCase());
          }
        });
        let indicatorIds = dbIndicatorIds.map(i => `'${i}'`).join(',');
        if (indicatorIds.length > 0) {
          query = `SELECT 
                    t.VALUE,
                    MAX(t.VALUE) OVER () AS MAX_VALUE,
                    MIN(t.VALUE) OVER () AS MIN_VALUE,
                    toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
                    toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
                    toString(toDate(toString(t.OBS_DT),	'yyyyMMdd')) AS OBS_DT,
                    substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR 
                  FROM ${viewName} t WHERE UPPER(${dbIndicatorColumn}) IN (${indicatorIds})`
        } else {
          query = `SELECT 
                    t.VALUE,
                    MAX(t.VALUE) OVER () AS MAX_VALUE,
                    MIN(t.VALUE) OVER () AS MIN_VALUE,
                    toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
                    toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
                    toString(toDate(toString(t.OBS_DT),	'yyyyMMdd')) AS OBS_DT,
                    substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR 
                  FROM ${viewName} t`
        }
        if (visualization.filterBy) {
          filterQuery(visualization.filterBy, query).then((result) => {
            query = `${result} ORDER BY OBS_DT`
            resolve(query);
          })
        } else {
          resolve(`${query} ORDER BY OBS_DT`);
        }
      }
    } catch (err) {
      
      log.error(`<<<<<< Exited microservice-official-insights.services.getQuery.service.getPortfolioDynamicQuery with error ${err} `);
      reject([424, err]);
    }
  });
}



async function filterQuery(filterByObj, query) {
  return new Promise((resolve, reject) => {
    try {
      if (Object.keys(filterByObj).length > 0) {
        Object.entries(filterByObj).forEach(([column, value]) => {
          if (Array.isArray(value)) {
            let values = value.map(i => `'${i}'`).join(',');
            if (query.includes('WHERE')) {
              query = `${query} AND UPPER ( REPLACE(${column},' ','')) IN (${values})`;
            } else {
              query = `${query} WHERE UPPER ( REPLACE(${column},' ','')) IN (${values})`;
            }
          } else {
            query = `${query} AND UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`
          }
        });
        resolve(query);
      } else {
        resolve(query);
      }
    } catch (err) {
      
      log.error(`<<<<<< Exited microservice-official-insights.services.getQuery.service.filterQuery with error ${err} `);
      reject([424, err])
    }
  })
}

async function getIndicatorMetaDataQuery(indicatorId,lang) {
  return new Promise((resolve, reject) => {
      try {

        let translatedKeys = {
          titleColumn : lang == 'EN'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR',
          topicColumn : lang == 'EN'?'TOPIC_NAME_ENGLISH':'TOPIC_NAME_ARABIC',
          themeColumn : lang == 'EN'?'THEME_NAME_ENGLISH':'THEME_NAME_ARABIC'
      }
      let binds ={
          
      }
      let query = `SELECT 
          INDICATOR_ID,
          ${translatedKeys.titleColumn} AS INDICATOR_NAME,
          ${translatedKeys.topicColumn} AS TOPIC_NAME,
          ${translatedKeys.themeColumn} AS THEME_NAME,
          SOURCE_TABLE FROM VW_INDICATOR_MAP WHERE INDICATOR_ID='${indicatorId}'`

          return resolve({ query: query, binds: binds });
          
      } catch (err) {
        
          log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeFilterDataQuery with error ${err} `);
          reject([424, err]);
      }
  });
}

module.exports = { 
  getPortfolioDynamicQuery,getPortfolioDynamicQueryWithDimension,getIndicatorMetaDataQuery }