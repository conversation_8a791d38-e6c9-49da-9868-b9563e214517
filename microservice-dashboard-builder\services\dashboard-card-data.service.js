const { randomUUID } = require("crypto");

const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const { PutObjectCommand, GetObjectCommand } = require("@aws-sdk/client-s3");

const { awsClient } = require("../../config/awsClient");

const CARD_DATA_FOLDER_KEY = process.env.S3_DASHBOARD_CARD_DATA_FOLDER_KEY;
const CARD_DATA_BUCKET = process.env.S3_DASHBOARD_CARD_DATA_BUCKET;

/**
 * Uploads given dashboard builder card data as .json file to S3
 * @param {Array} data
 * @returns {Promise<string | null>} Unique identifier for the
 * file (is also the file name) that can be stored in dashboard
 * card properties for retrieval via `getDashboardCardData`
 */
async function saveDashboardData(data) {
  const uniqueObjectId = randomUUID();
  const command = new PutObjectCommand({
    Bucket: CARD_DATA_BUCKET,
    Key: `${CARD_DATA_FOLDER_KEY}/${uniqueObjectId}.json`,
    Body: JSON.stringify(data),
  });
  try {
    await awsClient.send(command);
  } catch (error) {
    log.error(`Something went wrong when uploading card data: ${error}`);
    return null;
  }
  return uniqueObjectId;
}

/**
 * Allows updation of given custom data file
 * @param {*} dataId dataId (file name without extension)
 * of file to be updated
 * @param {*} data
 * @returns {Promise<string} data id (same as input)
 */
async function updateDashboardData(dataId, data) {
  const command = new PutObjectCommand({
    Bucket: CARD_DATA_BUCKET,
    Key: `${CARD_DATA_FOLDER_KEY}/${dataId}.json`,
    Body: JSON.stringify(data),
  });
  const response = await awsClient.send(command);
  return dataId;
}

/**
 * Fetches the custom dashboard card json data from S3 and returns the same
 * @param {string} dataId AWS ObjectKey / file name (without '.json')
 * that was created during data creation and saved to card properties
 * @returns {Promise<Array | undefined>}
 */
async function getDashboardCardData(dataId) {
  const command = new GetObjectCommand({
    Bucket: CARD_DATA_BUCKET,
    Key: `${CARD_DATA_FOLDER_KEY}/${dataId}.json`,
  });
  const response = await awsClient.send(command);
  if (response.Body) {
    const data = await response.Body.transformToString();
    return JSON.parse(data);
  }
}

module.exports = {
  saveDashboardDataService: saveDashboardData,
  updateDashboardDataService: updateDashboardData,
  getDashboardCardDataService: getDashboardCardData,
};
