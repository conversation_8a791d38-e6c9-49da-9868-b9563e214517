const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { IFPError } = require('../../utils/error');
let cms_mappings = require('../../config/cms_mappings.json');



async function getDomainOfficialStatisticsCountQuery(
	domainId = null,
	userGroups = []
) {
    
	// Build WHERE conditions
	let conditions = ["group_id IN ({userGroups:Array(String)})"];
	if (domainId) conditions.push("p.domain_id = {domainId:String}");
	let whereClause = conditions.length
		? `WHERE ${conditions.join(" AND ")}`
		: "";

	// Construct the query string
	const columns = {
		count: "COUNT(indicator_id)",
	};
	let query = `
        SELECT ${Object.entries(columns)
					.map(([alias, col]) => `${col} AS ${alias}`)
					.join(", ")}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS p
        ${whereClause}
    `;

	// Bind parameters for the query
	let binds = {
		domainId: domainId,
		userGroups: userGroups,
	};

	return { query, binds };
}

async function getDataClassificationCountQuery(
    domainId = null,
	label = null,
    userGroups = []
) {
    
	// Build WHERE conditions
	let conditions = ["group_id IN ({userGroups:Array(String)})"];
	if (label) conditions.push("p.label = {label:String}");
	if (domainId) conditions.push("p.domain_id = {domainId:String}");
	let whereClause = conditions.length
		? `WHERE ${conditions.join(" AND ")}`
		: "";

	// Construct the query string
	const columns = {
		count: "COUNT(indicator_id)",
		name: "p.name",
		label: "p.label"
	};
	let query = `
		SELECT ${Object.entries(columns)
					.map(([alias, col]) => `${col} AS ${alias}`)
					.join(", ")}
		FROM VW_DYNAMIC_OFFICIAL_INDICATORS p
		${whereClause}
		GROUP BY p.name, p.label
	`;

	// Bind parameters for the query
	let binds = {
        domainId: domainId,
		label: label,
		userGroups: userGroups,
	};

	return { query, binds };
}

async function getDataClassificationQuery(
) {
	const query = `
		SELECT DISTINCT label, name
		FROM VW_DYNAMIC_OFFICIAL_INDICATORS
		WHERE label IS NOT NULL AND label <> ''
	`;
	return { query };
}

async function getDomainExperimentalStatisticsCountQuery(
	screenerViews = []
) {
    
	// Build WHERE conditions
	// Assume screenerViews is passed as a parameter or defined elsewhere
	// Example: const screenerViews = ['VW_VIEW1', 'VW_VIEW2', ...];
	if (!Array.isArray(screenerViews) || screenerViews.length === 0) {
		throw new Error("screenerViews must be a non-empty array of view names");
	}

	let unionQueries = screenerViews.map(
		(view) => {
			//let viewConditions = [];
			//viewConditions.push("group_id IN ({userGroups:Array(String)})");
			//let viewWhereClause = viewConditions.length
				//? `WHERE ${viewConditions.join(" AND ")}`
				//: "";
			return `
				SELECT COUNT(DISTINCT INDICATOR_ID) AS count
				FROM ${view}
				
			`;
		}
	);

	let query = `
		SELECT SUM(count) AS count FROM (
			${unionQueries.join(" UNION ALL ")}
		)
	`;

	

	return { query };
}

async function getDomainOfficialScreenerCountQuery(
	screenerViews = [],
	domainName = null
) {
    
	// Build WHERE conditions
	// Assume screenerViews is passed as a parameter or defined elsewhere
	// Example: const screenerViews = ['VW_VIEW1', 'VW_VIEW2', ...];
	if (!Array.isArray(screenerViews) || screenerViews.length === 0) {
		throw new Error("screenerViews must be a non-empty array of view names");
	}

	let unionQueries = screenerViews.map(
		(view) => {
			let viewConditions = [];
			if (domainName) viewConditions.push("p.TOPIC_NAME_ENGLISH = {domainName:String}");
			
			let viewWhereClause = viewConditions.length
				? `WHERE ${viewConditions.join(" AND ")}`
				: "";
			return `
				SELECT COUNT(DISTINCT INDICATOR_ID) AS count
				FROM ${view}  p ${viewWhereClause}
				
			`;
		}
	);

	let query = `
		SELECT SUM(count) AS count FROM (
			${unionQueries.join(" UNION ALL ")}
		)
	`;
	let binds = {
		domainName: domainName,
	};
	

	return { query, binds };
}

async function getViewNameByIdQuery(id){
	try{
		let binds = {
			id:id
		}
		let query = `SELECT VIEWNAME FROM IFP_SCREENER_VIEW_MAP WHERE ID = {id:Int}`
		return {query:query,binds:binds}
	} catch (err) {
		log.error(`<<<<<< Exited services.getQuery.service.getViewNameById with error ${err} `);
		throw err;
	}
}

module.exports = {
	getDomainOfficialStatisticsCountQuery,
    getDataClassificationCountQuery,
	getDataClassificationQuery,
	getDomainExperimentalStatisticsCountQuery,
	getDomainOfficialScreenerCountQuery,
	getViewNameByIdQuery
};  