const { Sequelize, DataTypes } = require("sequelize");
const DropdownCategory = require("./drop-down-category.model");

/**
 * Existing model created for Bayaan UI used to store list of "DropdownOptions"
 * in Bayaan. DropdownOptions are the options that are grouped under a DropdownCategory.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const DropdownOption = sequelize.define(
    "DropdownOption",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      object_id: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        defaultValue: DataTypes.UUIDV4,
      },
      value: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      display_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      color: {
        type: DataTypes.STRING(7),
      },
    },
    {
      tableName: "common_dropdownoption",
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      deletedAt: 'deleted_at'
    }
  );

  DropdownOption.belongsTo(DropdownCategory(sequelize, DataTypes), {
    foreignKey: 'category_id',
    targetKey: 'id',
    as: 'category',
  });

  return DropdownOption;
}
module.exports = model;
