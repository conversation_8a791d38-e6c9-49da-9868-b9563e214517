const { IFPError } = require("../../utils/error");

const validateGlossaryList = (req, res, next) => {

  if (req.query.page)
      if (Number(req.query.page)){
          if (Number(req.query.page)<0)
              throw new IFPError(400,`${req.query.page} is not a valid page number`)
      }
      else
          throw new IFPError(400,`${req.query.page} is not a valid page number`)

  if (req.query.limit){
      if (Number(req.query.limit)){
          if (Number(req.query.limit)<1){
              throw new IFPError(400,`${req.query.limit} is not a valid limit number`)
          }
      }
      else
          throw new IFPError(400,`${req.query.limit} is not a valid limit number`)
  }


  const allowedFilterKeys = ['TOPIC_EN','TOPIC_AR','THEME_EN','THEME_AR','TITLE_EN','TITLE_AR'];

  if (req.body.sortBy) {
      const sortBy = req.body.sortBy
      const validValues = ['ASC', 'DESC'];
      if (!sortBy || !sortBy.alphabetical || !validValues.includes(sortBy.alphabetical)) {
        throw new IFPError(400,'Invalid sortBy value');
      }
  }

  if (req.body.filters) {
    const filters = req.body.filters
    const filterKeys = Object.keys(filters);
    const invalidFilterKeys = filterKeys.filter((key) => !allowedFilterKeys.includes(key));
    if (invalidFilterKeys.length > 0) {
      throw new IFPError(400,`Invalid filter key(s): ${invalidFilterKeys.join(', ')}.  Only allowed keys are TOPIC_EN, THEME_EN and TITLE_EN`);
    }

    filterKeys.forEach((key) => {
      if (!Array.isArray(filters[key]) && typeof filters[key] !== 'object') {
        throw new IFPError(400,`Invalid value for filter key ${key}`);
      }
    });

  }

  const allowedKeys = ['sortBy', 'filters'];
  const extraKeys = Object.keys(req.body).filter((key) => !allowedKeys.includes(key));
  if (extraKeys.length > 0) {
    throw new IFPError(400, `Invalid keys: ${extraKeys.join(', ')}. Only allowed keys are sortBy and filters`);
  }

  next()

};

module.exports = {
  validateGlossaryList
}