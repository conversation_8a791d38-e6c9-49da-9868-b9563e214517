function getUsersDataQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      formatDateTime(toDate(USER_CREATED_DT), '%b %Y') AS month,
      toInt32(COUNT(DISTINCT EMAIL)) AS count,
      ROW_NUMBER() OVER (ORDER BY min(toDate(USER_CREATED_DT))) AS rank
    FROM
      VW_IFP_USER_INFO
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`toDate(USER_CREATED_DT) >= {startDate: Date}`);
      binds.startDate = filter.startDate;
    }
    if (filter.endDate) {
      filters.push(`toDate(USER_CREATED_DT) <= {endDate: Date}`);
      binds.endDate = filter.endDate;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    query += filters.join(" AND ");
  }

  query += " GROUP BY month ORDER BY min(toDate(USER_CREATED_DT))";

  return { query, binds };
}

function getUsersByStatusQuery(filter) {
  let binds = {};
  let lastLoginCTE = `
    WITH LastLogin AS (
      SELECT
        userEmail,
        max(endTime) AS last_login
      FROM IFP_KPI_LOGS
      JOIN VW_IFP_USER_INFO ON VW_IFP_USER_INFO.EMAIL = IFP_KPI_LOGS.userEmail
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    lastLoginCTE += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`endTime >= {startDate: String}`);
      binds.startDate = `${filter.startDate} 00:00:00`;
    }
    if (filter.endDate) {
      filters.push(`endTime <= {endDate: String}`);
      binds.endDate = `${filter.endDate} 23:59:59`;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    lastLoginCTE += filters.join(" AND ");
  }

  lastLoginCTE += `
    GROUP BY userEmail
  ),`;

  const statusCTE = `
  UserStatus AS (
    SELECT
      userEmail,
      CASE
        WHEN last_login >= subtractMonths(now(), 3) THEN 'Active'
        ELSE 'Inactive'
      END AS status
    FROM LastLogin
  )

  SELECT
    status,
    count(*) AS user_count
  FROM UserStatus
  GROUP BY status
  `;

  const query = lastLoginCTE + statusCTE;
  return { query, binds };
}

function getTopUsersByUsageQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      VW_IFP_USER_INFO.NAME AS "name",
      IFP_KPI_LOGS.userEmail AS "email",
      VW_IFP_USER_INFO.ENTITY AS "entity",
      AVG(date_diff('second', startTime, endTime)) as "duration"
      FROM
      IFP_KPI_LOGS ikl
      JOIN VW_IFP_USER_INFO
      ON VW_IFP_USER_INFO.EMAIL = IFP_KPI_LOGS.userEmail
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`endTime >= {startDate: String}`);
      binds.startDate = `${filter.startDate} 00:00:00`;
    }
    if (filter.endDate) {
      filters.push(`endTime <= {endDate: String}`);
      binds.endDate = `${filter.endDate} 23:59:59`;
    }
    if (filter.entityNames?.length) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    filters.push(
      filter.mobile
        ? `startsWith(sessionType, 'mobile:')`
        : `NOT startsWith(sessionType, 'mobile:')`
    );

    query += filters.join(" AND ");
  }

  query += `
    GROUP BY userEmail, VW_IFP_USER_INFO.NAME, VW_IFP_USER_INFO.USER_TYPE, VW_IFP_USER_INFO.ENTITY 
    ORDER BY duration DESC
    LIMIT 5
  `;
  return { query, binds };
}

function getAverageUsageDurationQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      ifNull(ROUND(SUM(dateDiff('second', startTime, endTime)) / COUNT(DISTINCT userEmail), 2), 0) AS avg
    FROM
      IFP_KPI_LOGS ikl
  `;
  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`endTime >= {startDate: String}`);
      binds.startDate = `${filter.startDate} 00:00:00`;
    }
    if (filter.endDate) {
      filters.push(`endTime <= {endDate: String}`);
      binds.endDate = `${filter.endDate} 23:59:59`;
    }
    if (filter.entityNames && filter.entityNames.length > 0) {
      filters.push(
        `upper(arrayElement(splitByChar('.', arrayElement(splitByChar('@', userEmail), 2)), 1)) IN {entityNames: Array(String)}`
      );
      binds.entityNames = filter.entityNames.map((name) => name.toUpperCase());
    }

    filters.push(
      filter.mobile
        ? `startsWith(sessionType, 'mobile:')`
        : `NOT startsWith(sessionType, 'mobile:')`
    );

    query += filters.join(" AND ");
  }

  return { query, binds };
}

function getPlatformUsageByHourQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      toHour(toTimeZone(startTime, 'Asia/Dubai')) AS hour,
      toInt32(COUNT(DISTINCT userEmail)) AS count,
      toInt32(SUM(dateDiff('second', startTime, endTime))) AS totalSeconds,
      CONCAT(
        LPAD(toString(floor(SUM(dateDiff('second', startTime, endTime)) / 3600)), 2, '0'), ':',
        LPAD(toString(floor((SUM(dateDiff('second', startTime, endTime)) % 3600) / 60)), 2, '0'), ':',
        LPAD(toString(floor(SUM(dateDiff('second', startTime, endTime))) % 60), 2, '0')
      ) AS duration
    FROM
      IFP_KPI_LOGS ikl
    RIGHT JOIN VW_IFP_USER_INFO
      ON VW_IFP_USER_INFO.EMAIL = IFP_KPI_LOGS.userEmail
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`endTime >= {startDate: String}`);
      binds.startDate = `${filter.startDate} 00:00:00`;
    }
    if (filter.endDate) {
      filters.push(`endTime <= {endDate: String}`);
      binds.endDate = `${filter.endDate} 23:59:59`;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    filters.push(
      filter.mobile
        ? `startsWith(sessionType, 'mobile:')`
        : `NOT startsWith(sessionType, 'mobile:')`
    );

    query += filters.join(" AND ");
  }

  query += `
    GROUP BY hour
    ORDER BY hour WITH FILL FROM 10 TO 18 STEP 1
  `;
  return { query, binds };
}

function getUserWithGroupQuery(filter, limit, offset, isPaginated, selectedColumns) {
  let binds = {};
  let query = 'SELECT ';
  if (selectedColumns.length > 0) {
    selectedColumns.forEach(column => {
      switch (column) {
        case 'name':
          query += 'NAME AS name,';
          break;
        case 'group':
          query += `splitByChar(',', ifNull(replaceAll(AD_GROUP, ' ', ''),'')) AS group,`;
          break;
        default:
          break;
      }
    })
    query += `
        EMAIL AS email,
        COUNT(*) OVER() AS totalCount
      FROM 
        VW_IFP_USER_INFO
    `;
  } else {
    query += `
        NAME AS name,
        EMAIL AS email, 
        splitByChar(',', ifNull(replaceAll(AD_GROUP, ' ', ''),'')) AS group,
        COUNT(*) OVER() AS totalCount
      FROM 
        VW_IFP_USER_INFO
    `;
  }

  if (filter.startDate || filter.endDate || filter.entityNames?.length) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`toDate(USER_CREATED_DT) >= {startDate: Date}`);
      binds.startDate = filter.startDate;
    }
    if (filter.endDate) {
      filters.push(`toDate(USER_CREATED_DT) <= {endDate: Date}`);
      binds.endDate = filter.endDate;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    query += filters.join(" AND ");
  }
  if(isPaginated){
    query += ` LIMIT {limit: Int} OFFSET {offset: Int};`;
    binds.limit = limit;
    binds.offset = offset;
  }
  return { query, binds };
}

function generateUsersListQuery(entityId) {
  let query = `SELECT
	"classification",
	"name",
	"designation",
	"CLASSIFICATION_ID" ,
	"entity_id" ,
	"entity_name",
	LISTAGG("domain", ', ') WITHIN GROUP (
	ORDER BY "domain") AS "domain"
FROM
	(
	SELECT
		iua."DOMAIN" AS "domain",
		iua.ACCESS_LEVEL AS "classification",
		ifue.NAME AS "name" ,
		ifue.DESIGNATION AS "designation",
		IFUE."ROLE" AS "role",
		iel.NAME AS "entity_name",
		CASE
			WHEN UPPER(iua.ACCESS_LEVEL) = 'OPEN' THEN 1
			WHEN UPPER(iua.ACCESS_LEVEL) = 'CONFIDENTIAL' THEN 2
			WHEN UPPER(iua.ACCESS_LEVEL) = 'SENSITIVE' THEN 3
		END AS CLASSIFICATION_ID,
		IFUE.ENTITY_ID AS "entity_id"
	FROM
		IFP_USER_ACCESS_APPROVALS iuaa
	JOIN IFP_USER_ACCESS iua ON
		iuaa.USER_ACCESS_ID = iua.ACCESS_ID
	JOIN IFP_USER_ACCESS_REQUEST iuar ON
		iua.REQUEST_ID = iuar.REQUEST_ID
	JOIN IFP_FLOW_USERS_V2 ifue ON
		iuar.USER_ID = ifue.ID
	JOIN IFP_ENTITY_LOOKUP iel ON
		IFUE.ENTITY_ID = iel.ID
	WHERE
		iuaa.STATUS IN ('APPROVED', 'REVOKED')
		AND iuaa.STATUS IN ('APPROVED', 'REVOKED')
		AND IFUE."ROLE" = 'USER'`;

  if (entityId) {
    const entitySplit = entityId.split(",");
    const entities = entitySplit
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  AND IFUE.ENTITY_ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  query += `ORDER BY
		iuar.CREATED_DT)
GROUP BY
	"classification",
	"name",
	"designation",
	"CLASSIFICATION_ID" ,
	"entity_id",
	"entity_name"`;

  return { query, binds };
}

function generateUsersCountQuery() {
  let binds = {};
  let query = `SELECT
	COUNT(*) AS "total_users",
	SUM(CASE WHEN "ROLE" = 'PRIMARY_SUPERUSER' THEN 1 ELSE 0 END) AS "primary_super_users",
	SUM(CASE WHEN "ROLE" = 'SECONDARY_SUPERUSER' THEN 1 ELSE 0 END) AS "secondary_super_users",
	SUM(CASE WHEN "ROLE" IN ('DG', 'UNDER_SECRETARY') THEN 1 ELSE 0 END) AS "director_generals",
	SUM(CASE WHEN ENTITY_ID != 'E49' THEN 1 ELSE 0 END) AS "external_users",
	SUM(CASE WHEN ENTITY_ID = 'E49' THEN 1 ELSE 0 END) AS "internal_users"
FROM
	IFP_FLOW_USERS_V2 WHERE ACTIVATION_FLAG='ACTIVE' AND STATUS !='DELETED'`;

  return { query, binds };
}

function generateActiveAndNonActiveUsersQuery(entityId) {
  let binds = {};
  let query = `SELECT COUNT(DISTINCT CASE`;

  if (entityId) {
    query += ` WHEN multiSearchAny(userEmail, {entity: Array(String)}) > 0 THEN userEmail
    END) AS "active_count"`;

    binds.entity = entityId;
  }
  query += ` FROM IFP_KPI_LOGS
    WHERE startTime >= now() - INTERVAL 3 MONTH`;

  return { query, binds };
}

function generateEntityTotalCount(entityDomain) {
  let binds = {};
  let query = `SELECT COUNT(DISTINCT email) AS "total_user_count"
     FROM IFP_USER_INFO`;

  if (entityDomain) {
    const domains = entityDomain
      .map((_, index) => `:domain${index}`)
      .join(", ");
    query += `  WHERE LOWER(SUBSTR(email, INSTR(email, '@') + 1)) IN (${domains})`;
    entityDomain.forEach((domain, index) => {
      binds[`domain${index}`] = domain;
    });
  }
  return { query, binds };
}

function generateTotalEntitiesCountandSensitieve() {
  let binds = {};
  let query = `SELECT
	COUNT(DISTINCT ifuv.ENTITY_ID) AS "total_entity",
	COUNT(DISTINCT CASE WHEN IDAP.CLASSIFICATION = 'SENSITIVE' THEN ifuv.ENTITY_ID END) AS "sensitive_entity"
  FROM
	IFP_FLOW_USERS_V2 ifuv
  LEFT JOIN IFP_ENTITY_LOOKUP iel ON
	iel.ID = ifuv.ENTITY_ID
  LEFT JOIN IFP_DISS_ACCESS_POLICY idap ON
	IDAP.ENTITY_ID = ifuv.ENTITY_ID`;

  return { query, binds };
}

function getEntityDomains(entityId) {
  let binds = {};
  let query = `SELECT
	DOMAIN AS "domain",
	NAME AS "name"
  FROM
	IFP_ENTITY_LOOKUP iel`;

  if (entityId) {
    const entitySplit = entityId.split(",");
    const entities = entitySplit
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  WHERE ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  return { query, binds };
}

function generateUserQuery(limit, offset, filter) {
  let binds = {};
  let query = `SELECT
    viui.NAME AS "name",
    viui.EMAIL AS "email",
    iel.ID AS "entity_id",
    iel.NAME AS "entity_name",
    ifu.DESIGNATION AS "designation",
    LISTAGG(DISTINCT viui.CLASSIFICATION, ', ') WITHIN GROUP (ORDER BY viui.CLASSIFICATION) AS classifications,
    COUNT(*) OVER () AS total_count  
    FROM VW_IFP_USER_INFO viui
    LEFT JOIN IFP_FLOW_USERS_V2 ifu
    ON viui.ENC_EMAIL = ifu.EMAIL 
    JOIN IFP_ENTITY_LOOKUP iel
    ON SUBSTR(viui.EMAIL, INSTR(viui.EMAIL, '@') + 1) = iel.DOMAIN`;

  if (filter.entityId) {
    const entitySplit = filter.entityId.split(",");
    const entities = entitySplit
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  WHERE iel.ID IN (${entities})`;
    entitySplit.forEach((id, index) => {
      binds[`entityId${index}`] = id;
    });
  }

  if (filter.domain) {
    query += filter.entityId
      ? ` AND viui.DOMAIN= :domain`
      : ` WHERE viui.DOMAIN= :domain`;
    binds.domain = filter.domain.replace(/\s+/g, "").toUpperCase();
  }

  query += ` GROUP BY viui.NAME, viui.EMAIL, iel.ID, iel.NAME, ifu.DESIGNATION`;

  if (filter.classification) {
    query += ` HAVING LISTAGG(DISTINCT viui.CLASSIFICATION, ', ') WITHIN GROUP (ORDER BY viui.CLASSIFICATION) LIKE :classification`;
    binds.classification = `%${filter.classification}%`;
  }

  query += ` ORDER BY viui.NAME
  OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY`;

  binds.offset = offset;
  binds.limit = limit;

  return { query, binds };
}

function generateUserAccessQuery(filter) {
  let binds = {};
  let startDate = filter.startDate;
  let endDate = filter.endDate;

  let query = `
  WITH months AS (
  SELECT ADD_MONTHS(TRUNC(TO_DATE(:startDate, 'YYYY-MM-DD'), 'MM'), LEVEL - 1) AS MONTH_START
  FROM DUAL
  CONNECT BY LEVEL <= MONTHS_BETWEEN(TO_DATE(:endDate, 'YYYY-MM-DD'), TO_DATE(:startDate, 'YYYY-MM-DD')) + 1
  ),
  user_counts AS (
  SELECT
    TRUNC(USER_CREATED_DT, 'MM') AS MONTH_START,
    COUNT(DISTINCT ENC_EMAIL) AS DISTINCT_EMAIL_COUNT
  FROM IFP_USER_INFO
  WHERE USER_CREATED_DT >= TO_DATE(:startDate, 'YYYY-MM-DD')
    AND USER_CREATED_DT < ADD_MONTHS(TO_DATE(:endDate, 'YYYY-MM-DD'), 1)
`;

  binds.startDate = startDate;
  binds.endDate = endDate;

  // Domain filter
  if (filter.domain && filter.domain.length > 0) {
    const domainParams = filter.domain.map((_, i) => `:domain${i}`).join(", ");
    query += `
    AND LOWER(SUBSTR(EMAIL, INSTR(EMAIL, '@') + 1)) IN (${domainParams})`;

    filter.domain.forEach((d, i) => {
      binds[`domain${i}`] = d.toLowerCase();
    });
  }

  query += `GROUP BY TRUNC(USER_CREATED_DT, 'MM')
  ),
  pre_users AS (
  SELECT COUNT(DISTINCT ENC_EMAIL) AS PRE_COUNT
  FROM IFP_USER_INFO
  WHERE USER_CREATED_DT < TO_DATE(:startDate, 'YYYY-MM-DD')`;

  // Domain filter for pre-users
  if (filter.domain && filter.domain.length > 0) {
    const domainParams = filter.domain
      .map((_, i) => `:pre_domain${i}`)
      .join(", ");
    query += `
    AND LOWER(SUBSTR(EMAIL, INSTR(EMAIL, '@') + 1)) IN (${domainParams})`;

    filter.domain.forEach((d, i) => {
      binds[`pre_domain${i}`] = d.toLowerCase();
    });
  }

  query += `
),
final_result AS (
  SELECT
    m.MONTH_START,
    NVL(uc.DISTINCT_EMAIL_COUNT, 0) AS DISTINCT_EMAIL_COUNT
  FROM months m
  LEFT JOIN user_counts uc ON m.MONTH_START = uc.MONTH_START
),
final_with_cumulative AS (
  SELECT
    fr.MONTH_START,
    fr.DISTINCT_EMAIL_COUNT,
    SUM(fr.DISTINCT_EMAIL_COUNT) OVER (ORDER BY fr.MONTH_START) + pu.PRE_COUNT AS CUMULATIVE_COUNT
  FROM final_result fr
  CROSS JOIN pre_users pu
)
SELECT *
FROM final_with_cumulative
ORDER BY MONTH_START`;

  return { query, binds };
}

function generateActiveusersQuery(filter) {
  let binds = {
    startDate: filter.startDate,
    endDate: filter.endDate,
    domain: filter.domain,
  };

  let query = `WITH 
    months AS (
        SELECT 
            toStartOfMonth(addMonths({startDate:String}::Date, number)) AS month
        FROM 
            numbers(dateDiff('month', {startDate:String}::Date, {endDate:String}::Date) + 1)
    ),
    user_counts AS (
        SELECT 
            toStartOfMonth(startTime) AS month,
            COUNT(DISTINCT userEmail) AS usersCount,
            groupUniqArray(userEmail) AS userEmails
        FROM 
            IFP_KPI_LOGS
        WHERE 
            startTime BETWEEN {startDate:String} AND {endDate:String}
            AND has({domain:Array(String)}, splitByChar('@', userEmail)[2])`;

  query += filter.mobile
    ? ` AND startsWith(sessionType, 'mobile:')`
    : ` AND NOT startsWith(sessionType, 'mobile:')`;

  query += ` 
        GROUP BY 
            month
    )
    SELECT 
        formatDateTime(months.month, '%m/%d/%Y') AS month,
        ifNull(user_counts.usersCount, 0) AS usersCount,
        ifNull(user_counts.userEmails, []) AS userEmails
    FROM 
        months
    LEFT JOIN 
        user_counts 
    ON 
        months.month = user_counts.month
    ORDER BY 
        months.month`;

  return { query, binds };
}


function generateConsolidatedActiveUsers(filter) {
  let binds = {};

  let query = `SELECT 
    COUNT(DISTINCT userEmail) AS usersCount
    FROM 
    IFP_KPI_LOGS`;

  if (filter.startDate && filter.endDate) {
    query += ` WHERE 
    startTime BETWEEN {startDate:String} AND {endDate:String}`;
    binds.startDate = filter.startDate;
    binds.endDate = filter.endDate;
  }

  if (filter.domain) {
    query += ` AND has({domain:Array(String)}, splitByChar('@', userEmail)[2])`;
    binds.domain = filter.domain;
  }

  query += filter.mobile
    ? ` AND startsWith(sessionType, 'mobile:')`
    : ` AND NOT startsWith(sessionType, 'mobile:')`;

  return { query, binds };
}

function generateGeospatialOverviewQuery(filter) {
  let binds = {};
  let filters = [];

  let query = `
    SELECT 
        viui.ENTITY AS "entity",
        ikl.data2 AS district, 
        COUNT(DISTINCT ikl.userEmail) AS user_count
    FROM IFP_KPI_LOGS ikl 
    LEFT JOIN VW_IFP_USER_INFO viui 
      ON ikl.userEmail = viui.EMAIL
    WHERE 
        ikl.sessionType = 'geo_spatial'
  `;

  if (filter.entityNames && filter.entityNames.length > 0) {
    filters.push(`viui.ENTITY IN {district:Array(String)}`);
    binds.district = filter.entityNames;
  }

  if (filter.startDate && filter.endDate) {
    filters.push(`ikl.startTime BETWEEN {startDate: Date} AND {endDate: Date}`);
    binds.startDate = filter.startDate;
    binds.endDate = filter.endDate;
  }

  if (filter.userEmail) {
    filters.push(`ikl.userEmail = {userEmail: String}`);
    binds.userEmail = filter.userEmail;
  }

  filters.push(
    filter.mobile
      ? `startsWith(sessionType, 'mobile:')`
      : `NOT startsWith(sessionType, 'mobile:')`
  );

  if (filters.length > 0) {
    query += " AND " + filters.join(" AND ");
  }

  query += `
    GROUP BY viui.ENTITY, ikl.data2
    ORDER BY viui.ENTITY, ikl.data2
  `;

  return { query, binds };
}

function generateToolsOverviewQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      viui.ENTITY AS "entity",
      ikl.sessionType AS session,
      COUNT(DISTINCT ikl.userEmail) AS distinct_userEmail_count
      FROM
      IFP_KPI_LOGS ikl
      LEFT JOIN VW_IFP_USER_INFO viui 
      ON ikl.userEmail = viui.EMAIL
       WHERE
      ikl.sessionType IN ['self_service__my_bookmark',
      'self_service__Gen_AI',
      'self_service__dashboard',
      'self_service__auto_ml',
      'self_service__exploratory',
      'self_service__advance_prep',
      'self_service__basic_prep']
  `;

  if (filter.entityNames && filter.entityNames.length > 0) {
    query += ` AND viui.ENTITY IN {domains:Array(String)} `;
    binds.domains = filter.entityNames;
  }

  if (filter.startDate && filter.endDate) {
    query += ` AND ikl.startTime BETWEEN {startDate: Date} AND {endDate: Date} `;
    binds.startDate = filter.startDate;
    binds.endDate = filter.endDate;
  }

  if (filter.userEmail) {
    query += ` AND ikl.userEmail = {userEmail: String} `;
    binds.userEmail = filter.userEmail;
  }

  query += filter.mobile
    ? `AND startsWith(sessionType, 'mobile:')`
    : `AND NOT startsWith(sessionType, 'mobile:')`;

  query += `
      GROUP BY
      viui.ENTITY,
      ikl.sessionType
      ORDER BY
      viui.ENTITY,
      ikl.sessionType
  `;

  return { query, binds };
}

function generateUsersListQuery(offset, limit, entityId, external, selectedColumns, isPaginated) {
  let binds = {};
  let query = 'SELECT ';
  
  if (selectedColumns !== '' && !isPaginated) {
    let columns = selectedColumns.split(',');
    columns.forEach(column => {
      switch (column) {
        case 'email':
          query += 'DISTINCT iui.EMAIL AS "email",';
          break;
        case 'name':
          query += 'iui.NAME AS "name",';
          break;
        case 'entity_name':
          query += 'iel.NAME AS "entity_name",';
          break;
        case 'domains':
          query += 'LISTAGG(DISTINCT iui.DOMAIN, \', \') WITHIN GROUP (ORDER BY iui.DOMAIN) AS "domains",';
          break;
        case 'classifications':
          query += 'LISTAGG(DISTINCT iui.CLASSIFICATION, \', \') WITHIN GROUP (ORDER BY iui.CLASSIFICATION) AS "classifications",';
          break;
        default:
          break;
      }
    })
  } else {
    query += `DISTINCT iui.EMAIL AS "email",
                iui.NAME AS "name",
                iel.NAME AS "entity_name",
                LISTAGG(DISTINCT iui.DOMAIN, ', ') WITHIN GROUP (ORDER BY iui.DOMAIN) AS "domains",
                LISTAGG(DISTINCT iui.CLASSIFICATION, ', ') WITHIN GROUP (ORDER BY iui.CLASSIFICATION) AS "classifications",`;
  }

  query += `COUNT(*) OVER () AS "total_count"  
   FROM IFP_USER_INFO iui  
   LEFT JOIN IFP_FLOW_USERS_V2 ifu  
   ON iui.ENC_EMAIL = ifu.EMAIL 
   JOIN IFP_ENTITY_LOOKUP iel 
   ON iel.ID = ifu.ENTITY_ID`;

  query +=
    external != "false"
      ? ` WHERE iui.ENTITY != 'SCAD'`
      : ` WHERE iui.ENTITY = 'SCAD'`;

  if (entityId && external == "true") {
    const entitySplit = entityId.split(",");
    const entities = entitySplit
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  AND ifu.ENTITY_ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  query += ` GROUP BY iui.EMAIL, iui.NAME, iel.NAME`;
  
  if (isPaginated) {
    query += `
    OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;
  }
  return { query, binds };
}

function generateActiveUsersListBasedOnEntity(domain) {
  let binds = {};
  let query = `SELECT DISTINCT userEmail
      FROM IFP_KPI_LOGS
      WHERE userEmail LIKE {domain:String}`;
  binds.domain = "%" + domain + "%";

  return { query, binds };
}

module.exports = {
  getUsersDataQuery,
  getUsersByStatusQuery,
  getTopUsersByUsageQuery,
  getAverageUsageDurationQuery,
  getPlatformUsageByHourQuery,
  getUserWithGroupQuery,
  generateUsersCountQuery,
  generateUserQuery,
  generateTotalEntitiesCountandSensitieve,
  generateActiveAndNonActiveUsersQuery,
  getEntityDomains,
  generateUserAccessQuery,
  generateActiveusersQuery,
  generateConsolidatedActiveUsers,
  generateGeospatialOverviewQuery,
  generateToolsOverviewQuery,
  generateUsersListQuery,
  generateEntityTotalCount,
  generateActiveUsersListBasedOnEntity,
};
