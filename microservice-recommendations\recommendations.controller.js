const Logger = require('scad-library').logger;
require('dotenv').config();
const { getMetaFromCMS } = require('../services/common-service');
const log = new Logger().getInstance();
const constants = require('../config/constants.json');

const { getLatestRecommendationsData } = require('./services/executeQuery');
const { IFPError } = require('../utils/error');

async function getRecommendations(req) {
  log.debug(`>>>>>Entered microservice.recommendations.controller.getRecommendations`);
  try {

      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;         
      const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
      const cmsOfficialStatisticsList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_RECOMMENDATIONS_INDICATOR_URL}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      
      let domains = req.body;

      if (!domains.length)
        throw new IFPError(400,'Please input domain ids')

      let [
        domainList, 
        officialNodes
      ] = await Promise.all([
        getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups),
        getMetaFromCMS(req,cmsLoginUrl, cmsOfficialStatisticsList, req.user.groups)
      ]);

      domainNames = domains.map(d=>{
        let domain = domainList.find(dl=>dl.id==d)
        if (domain)
          return domain.name
      })

      domainNames = domainNames.filter(d=>d!=undefined)

      if (!domainNames.length)
        throw new IFPError(400,'Please provide valid domain ids')

      let nodeList = []
      let nodeIndicatorMap = {}
      officialNodes.forEach(node=>{
        if (domainNames.includes(node.parent_domain.title)){
          if (node.body){
            nodeList.push(node.body)
            nodeIndicatorMap[node.body]=node
          }
        }
      })
      
      const latestRecommendations = await getLatestRecommendationsData(nodeList)
      
      let results = latestRecommendations.map(node =>{
        indicatorId = Number(node.INDICATOR_ID)
        return {
          indicatorId: String(nodeIndicatorMap[indicatorId].nid),
          title: nodeIndicatorMap[indicatorId].title,
          contentType: "scad_official_indicator",
          appType: null,
          type: "official_statistics",
          domain: nodeIndicatorMap[indicatorId].parent_domain.title
        }
      })
      return results
    
                

  } catch (err) {
      log.error(`<<<<< Exited microservice.recommendations.controller.getRecommendations with error ${err} `)
      throw err;
  }
}



module.exports = {
  getRecommendations
};
