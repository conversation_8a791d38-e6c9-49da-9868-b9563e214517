const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const clkDatabaseService = require('../../services/clk-database.service');
async function getScadQueryWithDimension(visualization, isFilterPanelEnabled) {
  return new Promise((resolve, reject) => {
    try {
      let viewName = visualization.viewName ? visualization.viewName : "VW_STAT_INDICATORS";
      if (visualization.dimensionColumn) {
        let dbIndicatorId = visualization.dbIndicatorId.toUpperCase();
        let dbIndicatorColumn = visualization.dbColumn;
        let dbDimensionColumn = visualization.dimensionColumn;
        let dbDimensionValues = [];

        if (visualization.seriesMeta.length > 0) {
          visualization.seriesMeta.forEach(series => {
            if (Array.isArray(series.dimensionValue)) {
              series.dimensionValue.forEach(e => {
                dbDimensionValues.push(e.toUpperCase().replace(/ /g, ""))
              })
            } else {
              dbDimensionValues.push(series.dimensionValue.toUpperCase().replace(/ /g, ""));
            }
          });
        }

        let dimensionValues = dbDimensionValues.map(i => `'${i.replace(/'/g, "''")}'`).join(',');

        let query = `SELECT * FROM ${viewName} WHERE UPPER ( REPLACE(${dbIndicatorColumn},' ','')) IN ('${dbIndicatorId}') AND UPPER ( REPLACE(${dbDimensionColumn},' ','')) IN (${dimensionValues})`
        if (visualization.filterBy && !isFilterPanelEnabled) {
          filterQuery(visualization.filterBy, query).then((result) => {
            query = `${result} ORDER BY OBS_DT`
            resolve(query);
          })
        } else {
          resolve(`${query} ORDER BY OBS_DT`);
        }
      } else {
        let query = `SELECT * FROM ${viewName} ORDER BY OBS_DT`;
        resolve(query);
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getSacdQueryWithDimensions with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getPortfolioDynamicQueryWithDimension(visualization, isFilterPanelEnabled, filterPanel) {
  return new Promise(async (resolve, reject) => {
    try {
      let binds = {};
      let bindCounter = 1;
      let dbIndicatorIds = [];
      let dbIndicatorColumn = visualization.dbColumn;
      let query = "";
      let viewName = visualization.viewName ? visualization.viewName : "VW_STAT_INDICATORS";
      let dimensionColumns = []
      if (filterPanel) {
        filterPanel.properties.forEach(property => {
          dimensionColumns.push(`t.${property.path}`)
        })
      }
      if (visualization.seriesMeta.length > 0) {
        visualization.seriesMeta.forEach(series => {
          if ((series.keyCollection && series.keyCollection.length) > 0) {
            series.keyCollection.forEach(collection => {
              if (collection.dbIndicatorId) {
                // let bindVar = `bind${bindCounter++}`;
                // binds[bindVar] = collection.dbIndicatorId.toUpperCase();
                // dbIndicatorIds.push(`:${bindVar}`);
                dbIndicatorIds.push(`${collection.dbIndicatorId.toUpperCase()}`);
              }
            });
          } else if (series.dbIndicatorId) {
            // let bindVar = `bind${bindCounter++}`;
            // binds[bindVar] = series.dbIndicatorId.toUpperCase();
            // dbIndicatorIds.push(`:${bindVar}`);
            dbIndicatorIds.push(`${series.dbIndicatorId.toUpperCase()}`);
          }
        });
        let baseQuery;

        if (viewName == 'VW_STATISTICAL_INDICATORS') {
          baseQuery = `SELECT ${dimensionColumns.join(',')}, 
          t.VALUE,
          MAX(t.VALUE) OVER() AS MAX_VALUE,
            MIN(t.VALUE) OVER() AS MIN_VALUE,
            formatDateTime(toDate(toString(MAX(t.OBS_DT) OVER())), '%Y-%m-%d') AS MAX_OBS_DT,
            formatDateTime(toDate(toString(MIN(t.OBS_DT) OVER())), '%Y-%m-%d') AS MIN_OBS_DT,
            formatDateTime(toDate(toString(t.OBS_DT)), '%Y-%m-%d') AS OBS_DT,
            formatDateTime(toDate(toString(t.OBS_DT)), '%Y') AS YEAR
          FROM ${viewName} t`;
        }
        else {
          baseQuery = `SELECT ${dimensionColumns.join(',')}, t.VALUE, 
          MAX(t.VALUE) OVER () AS MAX_VALUE, MIN(t.VALUE) OVER () AS MIN_VALUE, 
          TO_CHAR(TO_DATE(MAX(t.OBS_DT) OVER (), 'YYYYMMDD'), 'YYYY-MM-DD') AS MAX_OBS_DT,
          TO_CHAR(TO_DATE(MIN(t.OBS_DT) OVER (), 'YYYYMMDD'), 'YYYY-MM-DD') AS MIN_OBS_DT,
          TO_CHAR(TO_DATE(t.OBS_DT, 'YYYYMMDD'), 'YYYY-MM-DD') AS OBS_DT, 
          TO_CHAR(TO_DATE(t.OBS_DT, 'YYYYMMDD'), 'YYYY') AS YEAR 
          FROM ${viewName} t`;
        }
        let yearClause = ''
        // if (viewName == 'VW_STATISTICAL_INDICATORS' && !visualization.isDuplicated){
        if (viewName == 'VW_STATISTICAL_INDICATORS') {
          let indicatorId = visualization.indicatorId
          try {
            let limitIndicatorsData = await getLimitIndicators()
            let limitIndicators = limitIndicatorsData.reduce((acc, obj) => {
              acc[obj.INDICATOR_ID] = obj.FINAL_DT;
              return acc;
            }, {});
            if (Object.keys(limitIndicators).includes(indicatorId))
              yearClause = `AND OBS_DT<'${limitIndicators[indicatorId]}'`
          }
          catch (err) {
            log.error(err)
          }

        }
        let query = dbIndicatorIds.length ? `${baseQuery} WHERE upper(${dbIndicatorColumn}) IN (${dbIndicatorIds.join(',')}) ${yearClause}` : baseQuery;

        if (visualization.filterBy) {
          filterQuery(visualization.filterBy, query).then((result) => {
            resolve({ query: `${result} ORDER BY OBS_DT`, binds: binds });
          })
        } else {
          resolve({ query: `${query} ORDER BY OBS_DT`, binds: binds });
        }
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getSacdQueryWithDimensions with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getCoiQuery(visualization) {
  return new Promise((resolve, reject) => {
    let viewName = '';
    try {
      if (visualization.viewName) {
        viewName = visualization.viewName.toUpperCase();
      }
      if (visualization.PARAMETER_COMBO_ID) {
        query = `SELECT * FROM ${viewName} WHERE ((upper(TYPE) ='NOWCAST') OR ( PARAMETER_COMBO_ID = '${visualization.PARAMETER_COMBO_ID}' AND  upper(TYPE) = 'FORECAST'))`;
      } else {
        query = `SELECT * FROM ${viewName}`;
      }
      if (visualization.filterBy) {
        filterQuery(visualization.filterBy, query).then((result) => {
          query = `${result} ORDER BY OBS_DT`
          resolve(query);
        })
      } else {
        resolve(`${query} ORDER BY OBS_DT`);
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getCoiQuery with error ${err} `);
      reject([424, err]);
    }
  })

}

async function getScadQuery(visualization) {
  return new Promise((resolve, reject) => {
    try {
      let dbIndicatorIds = [];
      let dbIndicatorColumn = visualization.dbColumn;
      let query = "";
      let viewName = visualization.viewName ? visualization.viewName : "VW_STAT_INDICATORS";
      if (visualization.seriesMeta.length > 0) {
        visualization.seriesMeta.forEach(series => {
          if ((series.keyCollection && series.keyCollection.length) > 0) {
            series.keyCollection.forEach(collection => {
              if (collection.dbIndicatorId) {
                dbIndicatorIds.push(collection.dbIndicatorId.toUpperCase());
              }
            });
          } else if (series.dbIndicatorId) {
            dbIndicatorIds.push(series.dbIndicatorId.toUpperCase());
          }
        });
        let indicatorIds = dbIndicatorIds.map(i => `'${i}'`).join(',');
        if (indicatorIds.length > 0) {
          query = `SELECT * FROM ${viewName} WHERE upper(${dbIndicatorColumn}) IN (${indicatorIds})`
        } else {
          query = `SELECT * FROM ${viewName}`
        }
        if (visualization.filterBy) {
          const viewsToExclude = [
            "VW_VISA_DOMESTIC_SPENDING",
            "VW_VISA_SPENDING_AD",
            "VW_VISA_SPENDING_NON_AD"
          ];
          if (viewsToExclude.includes(visualization.viewName)) { //hotfix. Remove this in the future
            filterInsightsDiscoveryQuery(visualization.filterBy, query).then((result) => {
              query = `${result} ORDER BY OBS_DT`
              resolve(query);
            })
          }
          else {
            filterQuery(visualization.filterBy, query).then((result) => {
              query = `${result} ORDER BY OBS_DT`
              resolve(query);
            })
          }
        } else {
          resolve(`${query} ORDER BY OBS_DT`);
        }
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getScadQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getPortfolioDynamicQuery(visualization) {
  return new Promise(async (resolve, reject) => {
    try {
      let binds = {};
      let bindCounter = 1;
      let dbIndicatorIds = [];
      let dbIndicatorColumn = visualization.dbColumn;

      let viewName = visualization.viewName ? visualization.viewName : "VW_STAT_INDICATORS";
      if (visualization.seriesMeta.length > 0) {
        if (viewName == 'VW_STATISTICAL_INDICATORS') {
          visualization.seriesMeta.forEach(series => {
            if ((series.keyCollection && series.keyCollection.length) > 0) {
              series.keyCollection.forEach(collection => {
                if (collection.dbIndicatorId) {
                  dbIndicatorIds.push(`${collection.dbIndicatorId}`);
                }
              });
            } else if (series.dbIndicatorId) {
              dbIndicatorIds.push(`${series.dbIndicatorId}`);
            }
          });
        }
        else {
          visualization.seriesMeta.forEach(series => {
            if ((series.keyCollection && series.keyCollection.length) > 0) {
              series.keyCollection.forEach(collection => {
                if (collection.dbIndicatorId) {
                  let bindVar = `bind${bindCounter++}`;
                  binds[bindVar] = collection.dbIndicatorId.toUpperCase();
                  dbIndicatorIds.push(`:${bindVar}`);
                }
              });
            } else if (series.dbIndicatorId) {
              let bindVar = `bind${bindCounter++}`;
              binds[bindVar] = series.dbIndicatorId.toUpperCase();
              dbIndicatorIds.push(`:${bindVar}`);
            }
          });
        }
        let baseQuery;
        if (viewName == 'VW_STATISTICAL_INDICATORS') {
          baseQuery = `SELECT
                t.VALUE,
              MAX(t.VALUE) OVER() AS MAX_VALUE,
                MIN(t.VALUE) OVER() AS MIN_VALUE,
                formatDateTime(toDate(toString(MAX(t.OBS_DT) OVER())), '%Y-%m-%d') AS MAX_OBS_DT,
                formatDateTime(toDate(toString(MIN(t.OBS_DT) OVER())), '%Y-%m-%d') AS MIN_OBS_DT,
                formatDateTime(toDate(toString(t.OBS_DT)), '%Y-%m-%d') AS OBS_DT,
                formatDateTime(toDate(toString(t.OBS_DT)), '%Y') AS YEAR
            FROM ${viewName} t
          `
        } else {
          baseQuery = `SELECT 
                t.VALUE,
                MAX(t.VALUE) OVER () AS MAX_VALUE,
                MIN(t.VALUE) OVER () AS MIN_VALUE,
                TO_CHAR(TO_DATE(MAX(t.OBS_DT) OVER (), 'YYYYMMDD'), 'YYYY-MM-DD') AS MAX_OBS_DT,
                TO_CHAR(TO_DATE(MIN(t.OBS_DT) OVER (), 'YYYYMMDD'), 'YYYY-MM-DD') AS MIN_OBS_DT,
                TO_CHAR(TO_DATE(t.OBS_DT, 'YYYYMMDD'), 'YYYY-MM-DD') AS OBS_DT,
                TO_CHAR(TO_DATE(t.OBS_DT, 'YYYYMMDD'), 'YYYY') AS YEAR
              FROM ${viewName} t`;
        }

        let yearClause = ''
        // if (viewName == 'VW_STATISTICAL_INDICATORS' && !visualization.isDuplicated){
        if (viewName == 'VW_STATISTICAL_INDICATORS') {
          let indicatorId = visualization.indicatorId
          try {
            let limitIndicatorsData = await getLimitIndicators()
            let limitIndicators = limitIndicatorsData.reduce((acc, obj) => {
              acc[obj.INDICATOR_ID] = obj.FINAL_DT;
              return acc;
            }, {});
            if (Object.keys(limitIndicators).includes(indicatorId))
              yearClause = `AND OBS_DT<'${limitIndicators[indicatorId]}'`
          }
          catch (err) {
            log.error(err)
          }

        }

        let query = dbIndicatorIds.length ? `${baseQuery} WHERE UPPER(${dbIndicatorColumn}) IN (${dbIndicatorIds.join(',')}) ${yearClause}` : baseQuery;

        if (visualization.filterBy) {
          filterQuery(visualization.filterBy, query).then((result) => {
            resolve({ query: `${result} ORDER BY OBS_DT`, binds: binds });
          })
        } else {
          resolve({ query: `${query} ORDER BY OBS_DT`, binds: binds });
        }
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getScadQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getCompareQuery(compareMeta) {
  return new Promise((resolve, reject) => {
    try {
      if (compareMeta.dimension) {
        let dimensionValues = [];
        let query = '';
        if (compareMeta.seriesMeta.length > 0) {
          compareMeta.seriesMeta.forEach(series => {
            if (series.dimensionValue) {
              dimensionValues.push(series.dimensionValue.toUpperCase());
            }
          });
          let dimensionArr = dimensionValues.map(i => `'${i}'`).join(',');
          if (dimensionArr.length > 0) {
            query = `SELECT * FROM ${compareMeta.compareTable} WHERE ${compareMeta.dbColumn} = '${compareMeta.dbIndicatorId}' AND UPPER(${compareMeta.comparedbColumn}) IN (${dimensionArr}) ORDER BY OBS_DT`
          } else {
            query = `SELECT * FROM ${compareMeta.compareTable} ORDER BY OBS_DT`
          }
          resolve(query);
        }
      } else {
        let dbIndicatorIds = [];
        let dbIndicatorColumn = compareMeta.comparedbColumn;
        let query = "";
        let tableName = compareMeta.compareTable;
        if (compareMeta.seriesMeta.length > 0) {
          compareMeta.seriesMeta.forEach(series => {
            if (series.dbIndicatorId) {
              dbIndicatorIds.push(series.dbIndicatorId.toUpperCase());
            }
          });
          let indicatorIds = dbIndicatorIds.map(i => `'${i}'`).join(',');
          if (indicatorIds.length > 0) {
            query = `SELECT * FROM ${tableName} WHERE UPPER(${dbIndicatorColumn}) IN (${indicatorIds}) ORDER BY OBS_DT`
          } else {
            query = `SELECT * FROM ${tableName} ORDER BY OBS_DT`
          }
        }
        resolve(query);
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getCompareQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function filterQuery(filterByObj, query) {
  return new Promise((resolve, reject) => {
    try {
      if (Object.keys(filterByObj).length > 0) {
        const viewsToExclude = [
          "VW_VISA_DOMESTIC_SPENDING",
          "VW_VISA_SPENDING_AD",
          "VW_VISA_SPENDING_NON_AD"
        ];
        Object.entries(filterByObj).forEach(([column, value]) => {
          const isFound = viewsToExclude.filter(item => query.includes(item)).length
          if (isFound) {
            if (Array.isArray(value)) {
              let values = value.map(i => `'${i}'`).join(',');
              if (query.includes('WHERE')) {
                query = `${query} AND ${column} IN (${values})`;
              } else {
                query = `${query} WHERE ${column} IN (${values})`;
              }
            } else {
              query = `${query} AND ${column}= '${value}'`
            }
          }
          else {
            if (Array.isArray(value)) {
              let values = value.map(i => `'${i}'`).join(',');
              if (query.includes('WHERE')) {
                query = `${query} AND UPPER ( REPLACE(${column},' ','')) IN (${values})`;
              } else {
                query = `${query} WHERE UPPER ( REPLACE(${column},' ','')) IN (${values})`;
              }
            } else {
              const condition = `UPPER(REPLACE(${column}, ' ', '')) = '${value.toUpperCase().replace(/ /g, '')}'`;
              query += query.includes('WHERE') ? ` AND ${condition}` : ` WHERE ${condition}`;
            }
          }

        });
        resolve(query);
      } else {
        resolve(query);
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.filterQuery with error ${err} `);
      reject([424, err])
    }
  })
}

async function filterInsightsDiscoveryQuery(filterByObj, query) {
  return new Promise((resolve, reject) => {
    try {
      if (Object.keys(filterByObj).length > 0) {
        Object.entries(filterByObj).forEach(([column, value]) => {
          if (Array.isArray(value)) {
            let values = value.map(i => `'${i}'`).join(',');
            if (query.includes('WHERE')) {
              query = `${query} AND ${column} IN (${values})`;
            } else {
              query = `${query} WHERE ${column} IN (${values})`;
            }
          } else {
            query = `${query} AND ${column}= '${value}'`
          }
        });
        resolve(query);
      } else {
        resolve(query);
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.filterQuery with error ${err} `);
      reject([424, err])
    }
  })
}

async function getPercentageChangeQuery(obj, list) {
  return new Promise((resolve, reject) => {
    try {
      let query = `SELECT * FROM ${obj.viewName} where ${obj.dbColumn} = '${obj.dbValue}'`
      let dimensionValues = list.map(i => `'${i}'`).join(',');
      if (dimensionValues.length > 0) {
        query = `${query} AND ${obj.mappingColumn} IN (${dimensionValues}) ORDER BY OBS_DT`;
      } else {
        query = `${query} ORDER BY OBS_DT`
      }
      resolve(query);
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getPercentageChangeQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getFilterOptionsQuery(filterObj) {
  return new Promise((resolve, reject) => {
    try {
      let query = `SELECT * FROM ${filterObj.viewName}`
      if (filterObj.dimension) {
        let counter = [];
        let queryWhereClause = `${query} WHERE`
        Object.entries(filterObj.dimension).forEach(([column, value]) => {
          if (query.includes('WHERE')) {
            query = `${query} AND ${column} = '${value}'`;
          } else {
            query = `${queryWhereClause} ${column} = '${value}'`;
          }
          counter.push(1);
        })
        if (Object.keys(filterObj.dimension).length === counter.length) {
          query = `${query}`;
        }
      } else {
        query = `${query}`;
      }
      query = `${query} ORDER BY RANK`
      resolve(query);
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getFilterOptionsQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getAllFilterDataQuery(meta, columns, yearly) {
  return new Promise((resolve, reject) => {
    try {
      if (!columns.length) {
        reject([424, 'No columns available for table']);
      }
      columns = columns.map(column => column.NAME).join(',')

      if (yearly) {
        query = `SELECT ${columns},
        'ALL' AS YEAR,
        toString(toDate (toDate (OBS_DT, 'YYYYMMDD'), 'YYYY-MM-DD')) AS OBS_DT 
        FROM ${meta.viewName} WHERE ${meta.dbColumn} = '${meta.dbIndicatorId}'`;
      } else {
        query = `SELECT ${columns},
          toString(toYear (toDate (OBS_DT, 'YYYYMMDD'))) AS YEAR,
          toString(toDate (toDate (OBS_DT, 'YYYYMMDD'), 'YYYY-MM-DD')) AS OBS_DT 
          FROM ${meta.viewName} WHERE ${meta.dbColumn} = '${meta.dbIndicatorId}'`;
      }

      if (meta.filterBy) {
        filterInsightsDiscoveryQuery(meta.filterBy, query).then((result) => {

          resolve({ query: `${result} ORDER BY OBS_DT`, binds: {} });
        })
      } else {
        resolve({ query: `${query} ORDER BY OBS_DT`, binds: {} });
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getFilterOptionsQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getAccuracyMetricsDataQuery(meta, id) {
  return new Promise((resolve, reject) => {
    try {
      let query = `SELECT * FROM ${meta.viewName} WHERE ${meta.dbColumn} = '${id}'`
      resolve(query);
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getAccuracyMetricsDataQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getValuesDataQuery(meta) {
  return new Promise((resolve, reject) => {
    try {
      if (meta.viewName) {
        let query = `SELECT * FROM ${meta.viewName}`
        const viewsToExclude = [
          "VW_VISA_DOMESTIC_SPENDING",
          "VW_VISA_SPENDING_AD",
          "VW_VISA_SPENDING_NON_AD"
        ];
        if (viewsToExclude.includes(meta.viewName)) {
          if (meta.dimension && Object.keys(meta.dimension).length > 0) {
            let counter = [];
            Object.entries(meta.dimension).forEach(([column, value]) => {
              if (query.includes('WHERE')) {
                query = `${query} AND ${column}= '${value}'`;
              } else {
                query = `${query} WHERE ${column}= '${value}'`;
              }
              counter.push(1);
            })
            if (Object.keys(meta.dimension).length === counter.length) {
              query = `${query}`;
            }
          }
        }
        else {
          if (meta.dimension && Object.keys(meta.dimension).length > 0) {
            let counter = [];
            Object.entries(meta.dimension).forEach(([column, value]) => {
              if (query.includes('WHERE')) {
                query = `${query} AND UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`;
              } else {
                query = `${query} WHERE UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`;
              }
              counter.push(1);
            })
            if (Object.keys(meta.dimension).length === counter.length) {
              query = `${query}`;
            }
          }
        }
        query = `${query} ORDER BY OBS_DT`;
        resolve(query);
      }
      else {
        resolve('SELECT * FROM VW_STAT_INDICATORS');
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getValuesDataDataQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getSeriesDataQuery(meta) {
  return new Promise((resolve, reject) => {
    try {
      if (meta.viewName) {
        let query = `SELECT DISTINCT ${meta.dimensionColumn} FROM ${meta.viewName}`
        if (meta.dynamicSeriesFilters && Object.keys(meta.dynamicSeriesFilters).length > 0) {
          let counter = [];
          Object.entries(meta.dynamicSeriesFilters).forEach(([column, value]) => {
            if (query.includes('WHERE')) {
              query = `${query} AND UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`;
            } else {
              query = `${query} WHERE UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`;
            }
            counter.push(1);
          })
          if (Object.keys(meta.dynamicSeriesFilters).length === counter.length) {
            query = `${query}`;
          }
        }
        resolve(query);
      }
      else {
        resolve('SELECT * FROM VW_STAT_INDICATORS');
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getValuesDataDataQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getCategoricalDimensionsQuery(meta) {
  return new Promise((resolve, reject) => {
    try {
      if (meta.viewName) {
        let query = `SELECT DISTINCT ${meta.dimensionColumn} FROM ${meta.viewName} WHERE ${meta.dbColumn} = '${meta.dbIndicatorId}' AND ${meta.dimensionColumn} IS NOT NULL`
        if (meta.dynamicSeriesFilters && Object.keys(meta.dynamicSeriesFilters).length > 0) {
          let counter = [];
          Object.entries(meta.dynamicSeriesFilters).forEach(([column, value]) => {
            if (query.includes('WHERE')) {
              query = `${query} AND UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`;
            } else {
              query = `${query} WHERE UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`;
            }
            counter.push(1);
          })
          if (Object.keys(meta.dynamicSeriesFilters).length === counter.length) {
            query = `${query}`;
          }
        }
        resolve(query);
      }
      else {
        resolve('SELECT * FROM VW_STAT_INDICATORS');
      }
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getValuesDataDataQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getPopStatsDataQuery(nodeIds) {
  return new Promise((resolve, reject) => {
    try {
      let query;
      if (nodeIds.length)
        query = `SELECT * FROM IFP_POPULAR_STATS WHERE NODE_ID IN (${nodeIds.join(',')}) ORDER BY HIT_COUNT DESC`
      else
        query = `SELECT * FROM IFP_POPULAR_STATS WHERE NODE_ID IN ('') ORDER BY HIT_COUNT DESC`
      return resolve(query);
    } catch (err) {

      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getPopStatsDataQuery with error ${err} `);
      reject([424, err]);
    }
  });
}

async function getLimitIndicators() {
  try {
    let data = await clkDatabaseService.simpleExecute("SELECT INDICATOR_ID,FINAL_DT FROM LIMIT_INDICATOR_DATA")
    return data;
  }
  catch (err) {
    log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getLimitIndicators with error ${err} `);
    return []
  }
}

module.exports = {
  getCoiQuery, getValuesDataQuery, getAccuracyMetricsDataQuery,
  getScadQuery, getScadQueryWithDimension, getCompareQuery,
  getPercentageChangeQuery, getFilterOptionsQuery, getAllFilterDataQuery,
  getSeriesDataQuery, getCategoricalDimensionsQuery, getPortfolioDynamicQuery, getPortfolioDynamicQueryWithDimension, getPopStatsDataQuery, filterInsightsDiscoveryQuery
}