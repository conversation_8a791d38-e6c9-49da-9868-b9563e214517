const { Sequelize, DataTypes } = require("sequelize");

/**
 * Model for storing approval requests in the Bayaan approval system.
 * Each request represents an item that needs to be reviewed and approved.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const ApprovalRequest = sequelize.define(
    "ApprovalRequest",
    {
      id: {
        type: DataTypes.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      object_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      object_type: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      requestor_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      assignee_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: 'pending',
      },
      last_action_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      approver_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSONB,
        allowNull: true,
      }
    },
    {
      tableName: "bayaan_approval_requests",
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      deletedAt: 'deleted_at',
      paranoid: true,
    }
  );
  
  return ApprovalRequest;
}

module.exports = model;