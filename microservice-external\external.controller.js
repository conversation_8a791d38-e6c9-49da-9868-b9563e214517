const Logger = require('scad-library').logger;
require('dotenv').config();
const log = new Logger().getInstance();
const { IFPError } = require('../utils/error');
const { getEntityUserCountData } = require('./services/executeQuery');

async function getEntityUserCount(req) {
    log.debug(`>>>>>Entered microservice.external.controller.getEntityUserCount`);
    try {

        const entityId = req.params.id;
        if (isNaN(entityId)) {
            throw new IFPError(400,'Invalid entity ID');
        }
        const data = await getEntityUserCountData(entityId);
        if (data.length) {
            return {
                'status': 'success',
                'data': data[0].USER_COUNT
            }
        } else {
            return {
                'status': 'failed',
                'message': 'No data found'
            }
        }

    } catch (err) {
        log.error(`<<<<< Exited microservice.external.controller.getEntityUserCount with error ${err} `)
        throw err;
    }
}



module.exports = {
    getEntityUserCount
};
