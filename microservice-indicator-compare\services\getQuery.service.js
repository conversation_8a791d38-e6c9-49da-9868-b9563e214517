const Logger = require('scad-library').logger;

const log = new Logger().getInstance();

async function getIndicatorDataQuery(indicatorId, viewName, lang) {
    try {
        let binds = {
            indicatorId: indicatorId
        };
        const unitColumn = viewName === "VW_STATISTICAL_INDICATORS" ? `t.UNIT_${lang}` : (lang === 'EN' ? `UNIT` : `t.UNIT_${lang}`);

        const query = `
            SELECT 
                t.VALUE,
                ${unitColumn} AS UNIT,
                MAX(t.VALUE) OVER () AS MAX_VALUE,
                MIN(t.VALUE) OVER () AS MIN_VALUE,	
                toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
                toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
                toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')) AS OBS_DT,
                substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR 
            FROM 
                ${viewName} t 
            WHERE 
                INDICATOR_ID = {indicatorId:String}
            ORDER BY 
                OBS_DT
            `;
            
        return { query: query, binds: binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getIndicatorDataQuery with error ${err} `);
        throw err;
    }
}


async function createCompareAppDataQuery(myAppsData) {
    try {
        let insertParts = [];
        let binds = {};

        myAppsData.forEach((app, index) => {
            insertParts.push(`INTO IFP_COMPARE_NODES ( ID, TITLE, NODE, USER_EMAIL) VALUES (:id${index}, :title${index}, :indicator${index}, :email${index})`);
            binds[`id${index}`]= app.id
            binds[`title${index}`]=app.title
            binds[`indicator${index}`]= app.indicator
            binds[`email${index}`]= app.email                
        });

        const query = `INSERT ALL ${insertParts.join(' ')} SELECT 1 FROM DUAL`;

        return { query:query, binds:binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.createCompareAppDataQuery with error ${err} `);
        throw err;
    }
}

async function listMyAppsDataQuery(email){
    try {
        const binds = {
            email: email
        };

        const query = `SELECT DISTINCT ID AS ID, TITLE AS TITLE, TO_CHAR(TO_TIMESTAMP(INSERT_DT, 'DD/MM/RR HH:MI:SS.FF AM'), 'DD-MM-YYYY') AS PUBLISH_DATE FROM IFP_COMPARE_NODES WHERE USER_EMAIL = :email`;

        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.addAppToMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getCompareDataQuery(id,userEmail){
    try {
        const binds = {
            id: id,
            userEmail: userEmail
        };
        const query = `SELECT * FROM IFP_COMPARE_NODES WHERE ID = :id AND USER_EMAIL = :userEmail`;

        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.addAppToMyAppsDataQuery with error ${err} `);
        throw err
    }
}

async function addAppToMyAppsDataQuery(node,userEmail) {
    try {
        const binds = {
            nodeId: node.id,
            nodeTitle: node.title,
            userEmail: userEmail
        };
        const query = `INSERT INTO IFP_MY_APPS (NODE_ID, TITLE, CONTENT_TYPE, USER_EMAIL, STATUS) 
                        VALUES (:nodeId, :nodeTitle, 'compare-statistics', :userEmail, 'SAVE')`;

        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.addAppToMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getCompareNodesInfoQuery(nodes){
    try {

        const binds = {};
        let bindCounter = 0;

        const placeholders = nodes.map(node => {
            bindCounter++;
            const bindVarName = `node${bindCounter}`;
            binds[bindVarName] = node;
            return `{${bindVarName}:String}`;
        });

        const query = `SELECT * FROM VW_INDICATOR_MAP WHERE INDICATOR_ID IN (${placeholders.join(',')})`;

        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.addAppToMyAppsDataQuery with error ${err} `);
        throw err;
    }
}
module.exports = { 
    getIndicatorDataQuery,
    createCompareAppDataQuery,
    listMyAppsDataQuery,
    getCompareDataQuery,
    addAppToMyAppsDataQuery,
    getCompareNodesInfoQuery
 };
