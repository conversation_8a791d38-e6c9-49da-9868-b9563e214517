const express = require('express');
const router = new express.Router();
const {<PERSON>user<PERSON><PERSON>roller, EntityController, ProductEngagementController, UserController, ApprovalController, RoleController, DGController, InvitationController, EIDRequestMappingController, downloadUserGuideController, listDeletedUsers } = require('../microservice-users-v3/user.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const passport = require('passport');
const { validatePEUser, validateUAEPassToken, validateSuperUserInvite, validateSUUser, validateUserInvite, validateUserRegister, validateSuperUserRegister, validateOtpRequest, validateDGInvite, validateDGRegister, validateDGUser, validateInvitationDelete, validateInvitationUpdate, validatePEorSUUser, validateUserGuideDownload, validateSUDeletedUsersListRequest } = require('./validators/userv3.validator');
const { authStrategySelector } = require('../services/authorization.service');

router.get('/role', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new RoleController().getUserRole(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching role, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/list', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new EntityController().listEntity(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/access-policy', authStrategySelector, validateSUUser,async (req, res, next) => {
    try {
        const data = await new EntityController().accessPolicy(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching access policy, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/requests/:type', authStrategySelector,validateSUUser, async (req, res, next) => {
    try {
        const data = await new SuperuserController().getUserAccessRequests(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

//Superuser Onboarding
router.post('/entity/superuser/make-primary', authStrategySelector, validateSUUser,async (req, res, next) => {
    try {
        const data = await new SuperuserController().makePrimary(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/superuser/verify', async (req, res, next) => {
    try {
        const data = await new SuperuserController().verifyToken(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error verifying invitation token, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/superuser/request/otp', async (req, res, next) => {
    try {
        const data = await new SuperuserController().requestOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error requesting otp for superuser, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/superuser/verify/otp', validateOtpRequest, async (req, res, next) => {
    try {
        const data = await new SuperuserController().verifySUOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error verifying otp for superuser, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/superuser/register',validateUAEPassToken,validateSuperUserRegister, async (req, res, next) => {
    try {
        const data = await new SuperuserController().register(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

//Superuser Management
router.post('/entity/set/dg',authStrategySelector, async (req, res, next) => {
    try {
        const data = await new SuperuserController().setDG(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error inviting entity users, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/invitations',authStrategySelector, validateSUUser, async (req, res, next) => {
    try {
        const data = await new SuperuserController().listInvitations(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error listing invitations for entity, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/invitations/create',authStrategySelector, validateSUUser,validateUserInvite, async (req, res, next) => {
    try {
        const data = await new SuperuserController().inviteUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error inviting entity users, ERROR: ${err}`);
        next(err);
    }
});

router.post('/invitations/validate', async (req, res, next) => {
    try {
        const data = await new InvitationController().validateInvitation(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error validating invitation, ERROR: ${err}`);
        next(err);
    }
});

router.post('/invitations/:invitationId/resend',authStrategySelector, async (req, res, next) => {
    try {
        const data = await new SuperuserController().resendInvitation(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error resending invite to entity users, ERROR: ${err}`);
        next(err);
    }
});

router.delete('/invitations/:invitationId',authStrategySelector, validateInvitationDelete, async (req, res, next) => {
    try {
        const data = await new InvitationController().deleteInvitationRecord(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error deleting invite, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/superuser/manage-access', authStrategySelector,validateSUUser,async (req, res, next) => {
// router.post('/entity/superuser/manage-access',async (req, res, next) => {
    try {
        const data = await new ApprovalController().approveUserAccess(req,req.superuser.ROLE);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

router.delete('/entity/users/:id', authStrategySelector,validateSUUser,async (req, res, next) => {
    // router.post('/entity/superuser/manage-access',async (req, res, next) => {
        try {
            const data = await new SuperuserController().deleteUser(req);
            res.set('Content-Type', 'application/json');
            res.send(data);
            next();
        } catch (err) {
            log.error(`Error managing user access, ERROR: ${err}`);
            next(err);
        }
    });

router.post('/entity/superuser/edit-access', authStrategySelector,validateSUUser,async (req, res, next) => {
    // router.post('/entity/superuser/manage-access',async (req, res, next) => {
        try {
            const data = await new SuperuserController().editAccess(req);
            res.set('Content-Type', 'application/json');
            res.send(data);
            next();
        } catch (err) {
            log.error(`Error managing user access, ERROR: ${err}`);
            next(err);
        }
    });

router.get('/entity/superuser/users-export', authStrategySelector, validateSUUser, async (req, res, next) => {
    try {
        const { buffer, entityName } = await new SuperuserController().exportUsers(req)
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', `attachment; filename="${entityName}_Users_Export.xlsx"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

//DG Management
router.post('/entity/dg/manage-access', authStrategySelector,validateDGUser, async (req, res, next) => {
    try {
        const data = await new ApprovalController().approveUserAccess(req,'DG');
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/dg/requests/:type', authStrategySelector, validateDGUser, async (req, res, next) => {
    try {
        const data = await new DGController().getUserAccessRequests(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/dg/register',validateUAEPassToken, validateDGRegister, async (req, res, next) => {
    try {
        const data = await new DGController().register(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error inviting entity users, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/dg/verify/otp', validateOtpRequest, async (req, res, next) => {
    try {
        const data = await new DGController().verifyDGOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/dg/request/otp', async (req, res, next) => {
    try {
        const data = await new DGController().requestOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

//Entity User Management
router.get('/entity/user/verify', async (req, res, next) => {
    try {
        const data = await new UserController().verifyToken(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error verifying invitation token, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/user/register',validateUAEPassToken, validateUserRegister, async (req, res, next) => {
    try {
        const data = await new UserController().register(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error inviting entity users, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/user/request/otp', async (req, res, next) => {
    try {
        const data = await new UserController().requestOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/user/verify/otp', validateOtpRequest, async (req, res, next) => {
    try {
        const data = await new UserController().verifyUserOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.patch('/entity/user/:userId', authStrategySelector, validateSUUser, async (req, res, next) => {
    try {
        const status = await new UserController().update(req);
        res.set('Content-Type', 'application/json');
        res.status(status).send();
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/user/nda', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new UserController().getNdaAcceptanceStatus(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error getting nda status for user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/user/nda', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new UserController().acceptNda(req);
        res.set("Content-Type", "application/json");
        res.status(204).send();
        next();
    } catch (err) {
        log.error(`Error getting nda status for user, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/deleted-users', authStrategySelector, validateSUUser, validateSUDeletedUsersListRequest, async (req, res, next) => {
    try {
        const data = await listDeletedUsers(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error listing deleted users, ERROR: ${err}`);
        next(err);
    }
});

router.get('/product-engagement/invitations',authStrategySelector, validatePEUser, async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().listInvitations(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error listing invitations for entity, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/invitations/create',authStrategySelector, validatePEUser,validateSuperUserInvite , async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().createInvitations(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error creating invitations for entity, ERROR: ${err}`);
        next(err);
    }
});

router.patch('/invitations/:invitationId', authStrategySelector, validatePEorSUUser, validateInvitationUpdate, async (req, res, next) => {
    try {
        if (req.peUser)
            await new InvitationController().updateSUInvitation(req);
        else
            await new InvitationController().updateUserInvitation(req);
        res.set('Content-Type', 'application/json');
        res.status(204).send();
        next();
    } catch (err) {
        log.error(`Error updating invitations, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/make-primary',authStrategySelector, validatePEUser , async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().makePrimary(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error creating invitations for entity, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/user/activate',authStrategySelector, async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().activateUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error activating user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/user/deactivate',authStrategySelector, async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().deActivateUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error activating user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/user/deactivate',authStrategySelector, async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().deActivateUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error activating user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/manage-access', authStrategySelector,validatePEUser,async (req, res, next) => {
    try {
        const data = await new ApprovalController().approveUserAccess(req,"PRIMARY_PE_USER");
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

router.get('/product-engagement/requests/:type', authStrategySelector,validatePEUser, async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().getUserAccessRequests(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/mappings', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new EIDRequestMappingController().getAllMappings(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching all mappings, ERROR: ${err}`);
        next(err);
    }
});

router.post('/mappings/by-request-ids', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new EIDRequestMappingController().getMappingByRequestIds(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching mappings by request IDs, ERROR: ${err}`);
        next(err);
    }
});

router.post('/mappings/by-emails', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new EIDRequestMappingController().getMappingByEmails(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching mappings by emails, ERROR: ${err}`);
        next(err);
    }
});

router.delete('/mappings', authStrategySelector, async (req, res, next) => {
    try {
        const data = await new EIDRequestMappingController().bulkDeleteMappings(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error deleting mappings, ERROR: ${err}`);
        next(err);
    }
});

router.get('/product-engagement/users-export', authStrategySelector, validatePEUser, async (req, res, next) => {
    try {
        const { buffer, entityName } = await new ProductEngagementController().exportUsers(req);
        const fileName = entityName ? `${entityName}_Bayaan_Users_Export` : 'Bayaan_Users_Export';
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', `attachment; filename="${fileName}.xlsx"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.send(buffer);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

router.get('/product-engagement/deleted-users', authStrategySelector, validatePEUser, async (req, res, next) => {
    try {
        const data = await listDeletedUsers(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error listing deleted users, ERROR: ${err}`);
        next(err);
    }
});

router.get('/user-guide', authStrategySelector, validateUserGuideDownload, async (req, res, next) => {
    try {
        await downloadUserGuideController(req, res);
    } catch (err) {
        log.error(`Error downloading user guide, ERROR: ${err}`);
        next(err);
    }
});

module.exports = router;
