const Logger = require('scad-library').logger;
const axios = require("axios");
const { setRedis, getRedis } = require("../../services/redis.service");
const clkdb = require("../../services/clk-database.service");
const db = require('../../services/database.service');
const constants = require("../../config/constants.json");
const { decryptEmail } = require("../../services/encryption.service");
const log = new Logger().getInstance();
const {
	getOverallDomainsQuery,
	getEntityListQuery,
	getSelfServiceToolDataQuery,
	getselfServiceToolUserDataQuery,
	getGeoSpatialDataQuery,
	getGeoSpatialUserDataQuery,
	generateEntityList,
	generateSuperUsersQuery,
	generateClassificationUserQuery,
	generateMostNotificationQuery,
	generateExperimentalIndicatorName,
	getBENodeDataQuery,
} = require("./getQuery.service");
const {
	formatSessionType,
} = require("./kpi.service");

async function getEntityList() {
	try {
		const EntityListQuery = await getEntityListQuery();
		const entityList = await getOracleData(EntityListQuery.query);

		return entityList;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getEntityList with error ${err}`
		);
		throw err;
	}
}

async function getOverallDomainsData(filter) {
	try {
		const { query, query_params } = await getOverallDomainsQuery(filter);
		const [cmsData, nodeCountData] = await Promise.all([
			getCMSData(),
			getData(query, query_params),
		]);

		const countMap = nodeCountData.reduce((map, item) => {
			map[item.nid] = parseInt(item.count, 10);
			return map;
		}, {});

		// Aggregate counts by domain and compute total count
		const domainAggregation = cmsData.reduce(
			(acc, { nid, domain }) => {
				const count = countMap[nid] || 0;
				if (domain) {
					acc.total += count;
					acc.domains[domain] = (acc.domains[domain] || 0) + count;
				}
				return acc;
			},
			{ total: 0, domains: {} }
		);

		// Format the result
		const formattedResponse = Object.entries(domainAggregation.domains).map(
			([domain, count]) => {
				const percentage =
					domainAggregation.total === 0
						? 0
						: ((count / domainAggregation.total) * 100).toFixed(2);
				return {
					domain: domain.replace(/&amp;/g, '&'),
					count,
					percentage: parseFloat(percentage),
				};
			}
		);

		const sortedResponse = formattedResponse.sort(
			(a, b) => b.count - a.count
		);

		return sortedResponse;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getOverallDomainsData with error ${err}`
		);
		throw err;
	}
}

async function getCommonStatisticsData(filter) {
	try {
		const nodeCountQuery = await getOverallDomainsQuery(filter);
		const [cmsData, nodeCountData] = await Promise.all([
			getCMSData(),
			getData(nodeCountQuery.query, nodeCountQuery.query_params),
		]);

		const countMap = nodeCountData.reduce((map, item) => {
			map[item.nid] = parseInt(item.count, 10);
			return map;
		}, {});

		// Filter nodes that have contentClassification with key contentClassificationKey
		const officialNodes = cmsData.filter(
			({ contentClassification }) =>
				contentClassification &&
				contentClassification.key === filter.contentClassificationKey
		);

		// Aggregate counts by domain for official statistics nodes
		const domainAggregation = officialNodes.reduce((acc, { nid, title, domain }) => {
			const count = countMap[nid];
			if (domain && count) {
				acc[domain] = acc[domain] || { count: 0, nodes: [] };
				acc[domain].count += count;
				acc[domain].nodes.push({ nid, title, count });
			}
			return acc;
		}, {});

		// Calculate total counts for percentage calculation
		const totalCount = Object.values(domainAggregation).reduce(
			(sum, domain) => sum + domain.count,
			0
		);

		// Format the result
		const formattedResponse = Object.entries(domainAggregation).map(
			([domain, { count, nodes }]) => {
				const domainPercentage =
					totalCount === 0 ? 0 : ((count / totalCount) * 100).toFixed(2);
				// Sort nodes by count and get top 6
				const topNodes = nodes
					.filter((node) => node.count > 0)
					.sort((a, b) => b.count - a.count)
					.slice(0, 6)
					.map((node) => {
						const nodePercentage =
							count === 0 ? 0 : ((node.count / count) * 100).toFixed(2);
						return {
							id: node.nid,
							name: node.title,
							count: node.count,
							percentage: parseFloat(nodePercentage),
						};
					});

				return {
					domain: domain.replace(/&amp;/g, '&'),
					count,
					percentage: parseFloat(domainPercentage),
					nodes: topNodes,
				};
			}
		);
		const sortedResponse = formattedResponse.sort(
			(a, b) => b.count - a.count
		);
		return sortedResponse;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getCommonStatisticsData with error ${err}`
		);
		throw err;
	}
}

async function getExperimentalStatisticsData(filter) {
	try {
		const nodeCountQuery = await getOverallDomainsQuery(filter);
		const [cmsData, nodeCountData] = await Promise.all([
			getCMSData(),
			getData(nodeCountQuery.query, nodeCountQuery.query_params),
		]);

		const countMap = nodeCountData.reduce((map, item) => {
			map[item.nid] = parseInt(item.count, 10);
			return map;
		}, {});

		// Filter nodes that have contentClassification with key "experimental_statistics"
		const cmsNodes = cmsData.filter(
			({ contentClassification }) =>
				contentClassification &&
				contentClassification.key === "experimental_statistics"
		);

		// Aggregate counts by domain for experimental nodes
		// NOTE: Here, intead of nid, it will be themeId
		const domainAggregation = cmsNodes.reduce(
			(acc, { themeId, theme, domain }) => {
				const count = countMap[themeId];
				if (domain && count) {
					// Ensure count exists
					acc[domain] = acc[domain] || { count: 0, nodes: [] };
					acc[domain].count += count;
					acc[domain].nodes.push({ themeId, theme, count });
				}
				return acc;
			},
			{}
		);

		// Calculate total counts for percentage calculation
		const totalCount = Object.values(domainAggregation).reduce(
			(sum, domain) => sum + domain.count,
			0
		);

		// Format the result
		const formattedResponse = Object.entries(domainAggregation).map(
			([domain, { count, nodes }]) => {
				const domainPercentage =
					totalCount === 0 ? 0 : ((count / totalCount) * 100).toFixed(2);
				// Sort nodes by count and get top 6
				const topNodes = nodes
					.filter((node) => node.count > 0)
					.sort((a, b) => b.count - a.count)
					.slice(0, 6)
					.map((node) => {
						const nodePercentage =
							count === 0 ? 0 : ((node.count / count) * 100).toFixed(2);
						return {
							id: node.themeId,
							name: node.theme,
							count: node.count,
							percentage: parseFloat(nodePercentage),
						};
					});

				return {
					domain: domain.replace(/&amp;/g, "&"),
					count,
					percentage: parseFloat(domainPercentage),
					nodes: topNodes,
				};
			}
		);
		const sortedResponse = formattedResponse.sort((a, b) => b.count - a.count);
		return sortedResponse;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getExperimentalStatisticsData with error ${err}`
		);
		throw err;
	}
}

async function getAnalyticalAppsData(filter) {
	try {
		filter.contentClassificationKey = "analytical_apps"
		const nodeCountQuery = await getOverallDomainsQuery(filter);
		const [cmsData, nodeCountData] = await Promise.all([
			getCMSData(),
			getData(nodeCountQuery.query, nodeCountQuery.query_params),
		]);

		const countMap = nodeCountData.reduce((map, item) => {
			map[item.nid] = parseInt(item.count, 10);
			return map;
		}, {});

		// Filter nodes that have contentClassification with key contentClassificationKey
		const analyticalNodes = cmsData.filter(
			({ contentClassification }) =>
				contentClassification &&
				contentClassification.key === filter.contentClassificationKey
		);

		// Aggregate counts by domain and category for nodes
		const domainAggregation = analyticalNodes.reduce(
			(acc, { nid, title, domain, category }) => {
				const count = countMap[nid];
				if (domain && count) {
					acc[domain] = acc[domain] || { count: 0, categories: {} };
					acc[domain].count += count;

					if (category) {
						acc[domain].categories[category] = acc[domain].categories[
							category
						] || { nodes: [] };
						acc[domain].categories[category].nodes.push({ nid, title, count });
					}
				}
				return acc;
			},
			{}
		);

		// Calculate total counts for percentage calculation
		const totalCount = Object.values(domainAggregation).reduce(
			(sum, domain) => sum + domain.count,
			0
		);

		// Format the result
		const formattedResponse = Object.entries(domainAggregation).map(
			([domain, { count, categories }]) => {
				const domainPercentage =
					totalCount === 0 ? 0 : ((count / totalCount) * 100).toFixed(2);

				// Format categories
				const formattedCategories = Object.entries(categories).map(
					([categoryName, { nodes }]) => {
						const totalCategoryCount = nodes.reduce(
							(sum, node) => sum + node.count,
							0
						);
						// Sort nodes by count and get top 6
						const topNodes = nodes
							.filter((node) => node.count > 0)
							.sort((a, b) => b.count - a.count)
							.slice(0, 6)
							.map((node) => {
								const nodePercentage =
									totalCategoryCount === 0
										? 0
										: ((node.count / totalCategoryCount) * 100).toFixed(2);
								return {
									id: node.nid,
									name: node.title,
									count: node.count,
									percentage: parseFloat(nodePercentage),
								};
							});
						return {
							name: categoryName,
							nodes: topNodes,
						};
					}
				);

				return {
					domain: domain.replace(/&amp;/g, "&"),
					count,
					percentage: parseFloat(domainPercentage),
					category: formattedCategories,
				};
			}
		);
		const sortedResponse = formattedResponse.sort((a, b) => b.count - a.count);
		return sortedResponse;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getAnalyticalAppsData with error ${err}`
		);
		throw err;
	}
}

async function getData(query, binds = {}) {
	return new Promise((resolve, reject) => {
		log.debug(
			`>>>>> Enter microservice-kpi.services.executeQuery.service.getData`
		);
		clkdb
			.simpleExecute(query, binds, false)
			.then((data) => {
				log.debug(
					`<<<<< Exit microservice-kpi.services.executeQuery.service.getData successfully`
				);
				resolve(data);
			})
			.catch((err) => {
				log.error(
					`<<<<< Exit microservice-kpi.services.executeQuery.service.getData with error ${err}`
				);
				log.error(`Error Executing Query:- ${query}`);
				reject([423, err]);
			});
	});
}

async function getCMSData() {
	try {
		// Define cache key and check Redis cache
		const cmsCacheKey = "responseService_cms_nodeList";
		const cmsCacheResults = await getRedis(cmsCacheKey);
		if (cmsCacheResults) {
			return JSON.parse(cmsCacheResults);
		}

		// Define CMS URLs and credentials
		const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
		const cmsNodeListUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_NODE_LIST_URL}`;
		const cmsCredentials = {
			username: "<EMAIL>",
			password: "5Iu88Q5JP07hLP8",
		};

		// Log in to CMS and get authentication cookie
		const postResponse = await axios.post(cmsLoginUrl, {
			name: cmsCredentials.username,
			pass: cmsCredentials.password,
		});
		const cookie = postResponse?.headers["set-cookie"][0].split(";")[0];

		// Fetch CMS node list using the authentication cookie
		let response = await axios.get(cmsNodeListUrl, {
			headers: { Cookie: cookie },
		});

		// Change / append official statistics from DB to the response
		// Delete the existing official statistics from the response
		response.data = response.data.filter(
			(item) => item.contentClassification && item.contentClassification.key != "official_statistics"
		);
		// Add the official statistics from DB to the response
		const { query, query_params } = await getBENodeDataQuery();
		const beNodeData = await getData(query, query_params);

		// Transform beNodeData to match the CMS node structure
		const transformedBeNodeData = beNodeData.map(item => ({
			nid: item.id,
			title: item.title,
			contentClassification: {
				country_flag: null,
				country_flag_light: null,
				key: item.contentClassification_key,
				name: item.contentClassification_name,
			},
			domain: item.domain,
			themeId: item.theme_id,
			theme: item.theme,
			category: item.category,
		}));

		// Append the transformed official statistics to the response
		response.data = response.data.concat(transformedBeNodeData);

		if (response) {
			// Cache the response
			await setRedis(
				cmsCacheKey,
				JSON.stringify(response.data),
				constants.redis.cmsResponseTTL
			);
		}

		return response.data;
	} catch (err) {
		log.error(`<<<<< Exited getCMSData with error ${err}`);
		throw err;
	}
}

async function getselfServiceToolData(filter) {
	try {
		const SelfServiceListQuery = await getSelfServiceToolDataQuery(filter);

		// Execute the query and get the result data
		const rawData = await getData(
			SelfServiceListQuery.query,
			SelfServiceListQuery.query_params
		);

		// Calculate total duration for percentage calculation
		const totalDuration = rawData.reduce(
			(sum, row) => sum + row.avgUsageHours * row.sessionCount,
			0
		);

		// Map rawData to format required by dataList
		const dataList = rawData.map((row) => {
			const sessionDuration = row.avgUsageHours * row.sessionCount;
			const percentage = totalDuration > 0
				? ((sessionDuration / totalDuration) * 100).toFixed(2)
				: 0;

			return {
				sessionType: row.sessionType,
				name: formatSessionType(row.sessionType), // Friendly name for sessionType
				averageUsageHrsValue: row.avgUsageHours, // Average session duration in hours
				averageUsageValueUnit: "hrs",
				percentageShare: parseFloat(percentage), // Time-based usage percentage
				sessionCount: row.sessionCount, // Total count of sessions
			};
		});

		return dataList;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getselfServiceToolData with error ${err}`
		);
		throw err;
	}
}

async function getselfServiceToolUserData(
	filter,
	limit,
	offset,
	isPaginated
) {
	try {
		// Build query for fetching user-specific session data
		const { query, query_params } = await getselfServiceToolUserDataQuery(
			filter,
			limit,
			offset,
			isPaginated
		);
		// Execute query and fetch results
		const dataList = await getData(query, query_params);


		// Return the response
		return {
			count: dataList[0]?.total_count || 0,
			results: dataList.map((row) => ({
				email: row.email,
				duration: row.duration
			})),
		};

	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getselfServiceToolUserData with error ${err}`
		);
		throw err;
	}
}

async function getGeoSpatialData(filter, limit = 10, offset = 0) {
	try {
		// Execute the query
		const { query, query_params } = await getGeoSpatialDataQuery(filter, limit, offset);
		const dataList = await getData(query, query_params);

		// Return the response
		return {
			count: dataList[0]?.total_count || 0,
			results: dataList.map((item) => ({
				[filter.category]: item[filter.category],
				duration: item.duration,
				user_count: item.user_count
			})),
		};
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getGeoSpatialData with error ${err}`
		);
		throw err;
	}
}

async function getGeoSpatialUserData(
	filter,
	limit = 10,
	offset = 0
) {
	try {
		// Get the SQL query and parameters
		const { query, query_params } = await getGeoSpatialUserDataQuery(
			filter,
			limit,
			offset
		);

		// Execute the query and fetch data
		const dataList = await getData(query, query_params);
		return {
			count: dataList[0]?.total_count || 0,
			results: dataList.map((item) => ({
				email: item.email,
			})),
		};
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-kpi.executeQuery.service.getGeoSpatialUserData with error ${err}`
		);
		throw err;
	}
}


// new one dashboard functions 
async function getEntityDetails(entityId, status) {
	try {
		// Fetch entity data
		const entityDataQuery = generateEntityList(entityId);
		const entityData = await getOracleData(entityDataQuery.query, entityDataQuery.binds);
		if (!entityData.length) return [];

		const classificationQuery = generateClassificationUserQuery(entityId);
		const usersQuery = generateSuperUsersQuery(entityId, status);
		const [entityClassificationData, superUsersData] = await Promise.all([
			getOracleData(classificationQuery.query, classificationQuery.binds),
			getOracleData(usersQuery.query, usersQuery.binds)
		]);
		return entityData.map((element, index) => {
			const users = superUsersData || [];
			users.forEach(user => {
				user.email = decryptEmail(user.email),
				user.permission = constants.dataClassifications[entityClassificationData[index]?.CLASSIFICATION_ID] || []
			});  // Decrypt emails in-place

			return {
				...element,
				user: users
			};
		});
	} catch (err) {
		log.error(`Error in getEntityDetails: ${err}`);
		throw err;
	}
}


async function getEnabledIndicators(filter) {
	const { query, binds } = generateMostNotificationQuery(filter);
	try {
		const [cmsData, nodeCountData] = await Promise.all([
			getCMSData(),
			getOracleData(query, binds),
		]);
		return { nodesList: cmsData, countList: nodeCountData };
	} catch (err) {
		log.error(
			`<<<<< Exit microservice-kpi.services.entity.service.getEntityActiveList with error ${err}`
		);
		log.error(`Error Executing Query:- ${query}`);
		throw err;
	}
}


async function getOracleData(query, binds = {}) {
	try {
		log.debug(`>>>>> microservice.data-governance.executeQuery.service.executeQuery.service.getData`);
		let data = await db.simpleExecute(query, binds);
		log.debug(`<<<<< Exit microservice.data-governance.executeQuery.service.executeQuery.service.getData successfully`);
		return data;
	} catch (err) {
		log.error(`<<<<< Exit microservice.data-governance.executeQuery.service.executeQuery.service.getData with error ${err}`);
		log.error(`Error Executing Query:- ${query}`);
		throw err;
	}
}



async function getExperimentalIndicatorName(id) {
	const { query, binds } = generateExperimentalIndicatorName(id);
	try {
		const data = await clkdb.simpleExecute(query, binds);
		return data;
	} catch (err) {
		log.error(
			`<<<<< Exit microservice-kpi.services.entity.service.getEntityActiveList with error ${err}`
		);
		log.error(`Error Executing Query:- ${query}`);
		throw err;
	}
}






module.exports = {
	getOverallDomainsData,
	getCommonStatisticsData,
	getExperimentalStatisticsData,
	getAnalyticalAppsData,
	getEntityList,
	getselfServiceToolData,
	getselfServiceToolUserData,
	getGeoSpatialData,
	getGeoSpatialUserData,
	getEntityDetails,
	getEnabledIndicators,
	getExperimentalIndicatorName,
	getCMSData
};
