const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const approvalRequestService = require('./services/approvalRequest.service');
const approvalRequestActionService = require('./services/approvalRequestAction.service');
const commentService = require('./services/approvalRequestComment.service');
const { userRoleMapping, getUserDetails } = require('./helper/approvalRequestObject');

// Middleware: Permission check
exports.PermissionCheck = async (req, res, next) => {
    try {
        // Check if user is authenticated (assuming JWT or similar auth mechanism)
        if (!req.user || !req.user.preferred_username) {
            return res.status(401).json({ 
            error: 'Unauthorized',
            message: 'Authentication required' 
            });
        }
        
        // Get user details
        const currentUserEmail = req.user.preferred_username;
        const currentUser = await getUserDetails(currentUserEmail);
        
        if (!currentUser) {
            return res.status(401).json({ 
                error: 'Unauthorized',
                message: 'User not found' 
            });
        }

        req.currentUserRole = currentUser.role;

        // Determine user role category
        const userApproverRoleCategory = Object.keys(userRoleMapping).find(category => 
            userRoleMapping[category].includes(currentUser.role)
        );

        if (!userApproverRoleCategory) {
            return res.status(403).json({ 
                error: 'Forbidden',
                message: 'Invalid user role' 
            });
        }
        req.userApproverRoleCategory = userApproverRoleCategory;

        // If permission is granted:
        return next();

        // If not authorized:
        // return res.status(403).json({ message: 'Forbidden' });
    } catch (err) {
        console.error('PermissionCheck error:', err);
        return res.status(500).json({ message: 'Internal Server Error' });
    }
}

/**
* Controller for creating a new approval request
*/
exports.createApprovalRequest = async (req, res, next) => {
    try {
        const data = await approvalRequestService.createApprovalRequest(req);
        res.set("Content-Type", "application/json");
        res.status(201).send(data);
        next();
    } catch (err) {
        log.error(`Error creating approval request: ${err}`);
        next(err);
    }
};

/**
* Controller for getting approval requests with filtering
*/
exports.getApprovalRequests = async (req, res, next) => {
    try {
        const data = await approvalRequestService.getApprovalRequests(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching approval requests: ${err}`);
        next(err);
    }
};

/**
* Controller for getting detailed information about a specific approval request
*/
exports.getApprovalRequestDetails = async (req, res, next) => {
    try {
        const data = await approvalRequestService.getApprovalRequestDetails(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching approval request details: ${err}`);
        next(err);
    }
};


/**
* Controller for getting action history information about a specific approval request
*/
exports.getRequestHistory = async (req, res, next) => {
    try {
        const data = await approvalRequestService.getRequestHistory(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching approval request history details: ${err}`);
        next(err);
    }
};

/**
 * Controller for getting tab count for the current user
 */
exports.getTabCount = async (req, res, next) => {
    try {
        const data = await approvalRequestService.getTabCount(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching tab count: ${err}`);
        next(err);
    }
}

/**
* Controller for performing actions on approval requests
* Handles the HTTP request/response and delegates business logic to the service layer
*/
exports.performApprovalAction = async (req, res, next) => {
    try {
        const requestId = req.params.id;
        const { action, assigneeId, comment, metadata } = req.body;
        const currentUserEmail = req.user.preferred_username;

        // Validate required fields based on action
        if (action === 'assign' && !assigneeId) {
            return res.status(400).json({
                error: 'assigneeId is required for assign action'
            });
        }
        
        // Validate that action is one of the allowed values
        const validActions = ['assign', 'approve', 'reject', 'revert', 'delete', 'restore', 'claim', 'unpublish'];
        if (!validActions.includes(action)) {
            return res.status(400).json({
                error: `Invalid action. Must be one of: ${validActions.join(', ')}`
            });
        }

        // Validate request ID format (basic UUID validation)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(requestId)) {
            return res.status(400).json({
                error: `Invalid request ID format: ${requestId}`
            });
        }

        // Check user role and permissions
        console.log("Current User Role:", req.currentUserRole);
        console.log("User Approver Role Category:", req.userApproverRoleCategory);

        if (!req.currentUserRole || !req.userApproverRoleCategory) {
            return res.status(403).json({
                error: 'Forbidden',
                message: 'User role or approver role category not found'
            });
        }
        if (req.userApproverRoleCategory === "REQUESTOR") {
            const approverActions = ['approve', 'reject', 'revert', 'restore', 'unpublish'];
            if (approverActions.includes(action)) {
                return res.status(403).json({
                    error: 'Forbidden',
                    message: 'Requestor cannot perform this action'
                });
            }
        }
        
        const result = await approvalRequestActionService.performApprovalAction(
            requestId,
            action,
            currentUserEmail,
            { assigneeId, comment, metadata }
        );
        
        res.status(200).json(result);
        next();
    } catch (err) {
        log.error(`Error performing approval action: ${err}`);
        
        // Handle specific error types with appropriate status codes
        if (err.name === 'NotFoundError') {
            return res.status(404).json({ error: err.message });
        } else if (err.name === 'ValidationError') {
            return res.status(400).json({ error: err.message });
        } else if (err.name === 'UnauthorizedError') {
            return res.status(403).json({ error: err.message });
        }
        
        res.status(500).json({ error: 'An unexpected error occurred' });
        next(err);
    }
};

/**
 * Controller for performing bulk actions on approval requests
 */
exports.performBulkActions = async (req, res, next) => {
    try {
        const { action, requestIds, comment } = req.body;
        const currentUserEmail = req.user.preferred_username;

        // Validate required fields
        if (!action) {
            return res.status(400).json({
                error: 'action is required'
            });
        }

        if (!requestIds || !Array.isArray(requestIds) || requestIds.length === 0) {
            return res.status(400).json({
                error: 'requestIds is required and must be a non-empty array'
            });
        }

        // Validate that action is one of the allowed values
        const validActions = ['assign', 'approve', 'reject', 'revert', 'delete', 'restore', 'claim', 'unpublish'];
        if (!validActions.includes(action)) {
            return res.status(400).json({
                error: `Invalid action. Must be one of: ${validActions.join(', ')}`
            });
        }

        // Validate request IDs format (basic UUID validation)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        const invalidIds = requestIds.filter(id => !uuidRegex.test(id));
        if (invalidIds.length > 0) {
            return res.status(400).json({
                error: `Invalid request ID format: ${invalidIds.join(', ')}`
            });
        }

        // Prepare bulk operation results
        const results = {
            successful: [],
            failed: [],
            totalProcessed: requestIds.length,
            successCount: 0,
            failureCount: 0
        };

        // Process each request ID using the existing single action service
        for (const requestId of requestIds) {
            try {
                // Reuse the existing single action service with comment if provided
                const actionOptions = {};
                if (comment) actionOptions.comment = comment;

                const result = await approvalRequestActionService.performApprovalAction(
                    requestId,
                    action,
                    currentUserEmail,
                    actionOptions
                );

                results.successful.push({
                    requestId,
                    result
                });
                results.successCount++;

            } catch (error) {
                results.failed.push({
                    requestId,
                    error: error.message || 'Unknown error occurred',
                    errorType: error.name || 'Error'
                });
                results.failureCount++;
            }
        }

        // Determine response status based on results
        let statusCode = 200;
        if (results.failureCount > 0 && results.successCount === 0) {
            // All failed
            statusCode = 400;
        } else if (results.failureCount > 0) {
            // Partial success
            statusCode = 207; // Multi-Status
        }

        res.status(statusCode).json({
            message: `Bulk ${action} operation completed`,
            summary: {
                total: results.totalProcessed,
                successful: results.successCount,
                failed: results.failureCount
            },
            results: {
                successful: results.successful,
                failed: results.failed
            }
        });

        next();

    } catch (err) {
        log.error(`Error performing bulk approval action: ${err}`);
        
        res.status(500).json({ 
            error: 'An unexpected error occurred during bulk operation',
            message: process.env.NODE_ENV === 'development' ? err.message : undefined
        });
        next(err);
    }
};

/**
 * Controller for handling approval request comments
 */
exports.addComment = async (req, res, next) => {
    try {
        const data = await commentService.addComment(req);
        res.set("Content-Type", "application/json");
        res.status(201).send(data);
        next();
    } catch (err) {
        log.error(`Error adding comment: ${err}`);
        next(err);
    }
};

/**
 * Controller for getting comments for a specific approval request
 */
exports.getComments = async (req, res, next) => {
    try {
        const data = await commentService.getComments(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching comments: ${err}`);
        next(err);
    }
}

/**
 * Controller for edit comments for a specific approval request
 */
exports.editComment = async (req, res, next) => {
    try {
        const data = await commentService.editComment(req);
        res.set("Content-Type", "application/json");
        res.status(200).send(data);
        next();
    } catch (err) {
        log.error(`Error editing comment: ${err}`);
        next(err);
    }
};

/**
 * Controller for delete comments for a specific approval request
 */
exports.deleteComment = async (req, res, next) => {
    try {
        const data = await commentService.deleteComment(req);
        res.set("Content-Type", "application/json");
        res.status(200).send(data);
        next();
    } catch (err) {
        log.error(`Error deleting comment: ${err}`);
        next(err);
    }
};