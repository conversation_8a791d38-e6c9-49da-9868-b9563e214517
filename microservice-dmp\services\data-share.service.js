const crypto = require("crypto");
const fs = require("fs");
const path = require("path");

const axios = require("axios").default;
const { getDXPData } = require("./api.service");
const { dxpUrls } = require("../../config/constants.json");

async function createProductDataShare(
  req,
  shareName,
  productId,
  activationTokenLifeTime = 3600
) {
  const body = {
    name: shareName,
    productId: productId,
    target: {
      definition: {
        type: "databricks-open",
        ipAccessList: {},
        recipientTokenLifetimeInSeconds: activationTokenLifeTime,
      },
    },
  };
  try {
    const productDataShare = await getDXPData(
      req,
      "POST",
      dxpUrls.PRODUCT.CREATE_DATA_SHARE,
      body
    );
    const share = productDataShare["dataShares"][0]; // QUESTION: can one create call return multiple data shares
    return share.id;
  } catch (error) {
    throw error;
  }
}

async function getDataShare(req, shareId) {
  const url = `${dxpUrls.DATA_SHARE.DETAIL}/${shareId}`;
  try {
    const dataShare = await getDXPData(req, "GET", url);
    return dataShare;
  } catch (error) {
    throw error;
  }
}

/**
 * Programmatic activation of the Delta Share
 * See: https://docs.databricks.com/api/azure/workspace/recipientactivation/retrievetoken
 * @param {*} req
 * @param {string} activationUrl
 */
async function activateDeltaShare(activationUrl) {
  const activationDomain = `https://${activationUrl.split("/")[2]}`;
  const activationToken = activationUrl.split("/").at(-1).split("?")[1];
  const activationApiUrl = `${activationDomain}/${dxpUrls.DELTA_SHARE.ACTIVATE_DELTA_SHARE}/${activationToken}`;
  try {
    const response = await axios.get(activationApiUrl, {
      headers: { origin: activationDomain, accept: "application/json" },
    });
    const data = response.data;
    return data;
  } catch (error) {
    throw error;
  }
}

function generateTokenHash(userId, productId) {
  const combined = `${userId}_${productId}`;
  const hash = crypto.createHash("sha256").update(combined).digest("hex");
  return hash;
}

function saveDeltaShareToken(tokenHash, token) {
  const filename = `${tokenHash}.json`;
  const filePath = path.join(__dirname, "/shares/", filename);

  fs.writeFileSync(filePath, JSON.stringify(token));
  console.log(`Token saved to ${filename}`);
}

function loadDeltaShareToken(tokenHash) {
  const filename = `${tokenHash}.json`;
  const filePath = path.join(__dirname, "/shares/", filename);

  if (fs.existsSync(filePath)) {
    const token = JSON.parse(fs.readFileSync(filePath, "utf8"));
    console.log(`Token loaded from ${filename}`);
    return token;
  } else {
    console.log(`No token found for ${filename}`);
    return null;
  }
}

module.exports = {
  createProductDataShare,
  getDataShare,
  activateDeltaShare,
  generateTokenHash,
  saveDeltaShareToken,
  loadDeltaShareToken,
};
