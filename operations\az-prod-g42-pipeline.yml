trigger: none

variables:
  app_name: 'scad-insights-be'
  registry: '$(OpenShift.Registry)/azure-devops'
  ouser: '$(OpenShift.Registry.Username)'
  opassword: '$(OpenShift.SA.Token)'
  openshift_url: '$(OpenShift.API.Server)'
  application_labels: "ifp,be"
jobs:
- job: Init
  pool: 'oc-light-agent'
  steps:
  - script: |
        echo "##vso[task.setvariable variable=version;isOutput=true]$(Build.SourceVersion)"
        echo "##vso[build.updatebuildnumber]$(Build.SourceVersion)"
    name: setvarStep
    displayName: 'Generating version'
    failOnStderr: "true"

- job: DockerBuild
  pool: 'oc-light-agent'
  dependsOn: Init
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'

  - task: Npm@1
    inputs:
      command: 'install'
      verbose: false
      customRegistry: 'useFeed'
      customFeed: '4ac6f6d3-9411-4182-b105-79854fc1587c'
    displayName: 'npm install'

  - script: |
      tar -czvf node_modules.tar.gz node_modules
    displayName: 'TAR node_modules'

  - script: |
      buildah login --tls-verify=false $(registry) --username $(ouser) --password $(opassword)
    failOnStderr: "true"
    displayName: 'login to podman registry'

  - script: |
      buildah build --tls-verify=false -t $(registry)/$(app_name):$(version) \
      --build-arg NPM_FEED_USERNAME=$(NPM_FEED_USERNAME) \
      --build-arg NPM_FEED_opassword=$(NPM_FEED_opassword)  -f operations/dockerfiles/dockerfile .
      buildah push --tls-verify=false $(registry)/$(app_name):$(version)
    displayName: 'Building and pushing image to podman registry $(registry)/$(app_name):$(version)'
