const scadLib = require('scad-library');
const util = scadLib.util;
const Logger = scadLib.logger;
const log = new Logger().getInstance();

/**
 * Function to process data for sunburst chart according to how the data structure provided by UI
 * @param {*} seriesData object
 * @param {*} visualization object
 */
const getSunBurstSeries = async (visualization, seriesData) => {
    return new Promise((resolve, reject) => {
        try {
            if (seriesData && seriesData.length > 0) {
                /*formatting data received from db by mapping id to values 
                based on dimension object provided within json through CMS*/
                visualization.seriesMeta.forEach( series => {
                    //adding data array inside each series
                    series.data = [];
                    let matchingValueObject = series.dimension;
                    let result = seriesData.filter(function(item) {
                        item.OBS_DT = util.convertDate(item.OBS_DT.toString());
                        for (var key in matchingValueObject) {
                            let itemColumn = item[key] === null ? '': item[key].toUpperCase();
                            let dimensionValue = matchingValueObject[key] === null ? '' : matchingValueObject[key].toUpperCase();
                            if ( item[key] === undefined || itemColumn != dimensionValue){
                                return false;
                            }
                        }
                        Object.keys(item).forEach((k) => item[k] == null && delete item[k]);
                        return true;
                      });

                    if( result.length >= 1 ) series.data = result ;

                })
            }
            else {
                log.error(`Data not available in DB for visualization ${visualization}`);
                reject([404, `Data not available in DB for sunburst-with-change-chart`]);
            }

            resolve(visualization);
        } catch (err) {
            
            log.error(`Error processing sunburst-with-change-chart ${err}`);
            reject([422, err]);
        }
    })
}


module.exports = { getSunBurstSeries }
