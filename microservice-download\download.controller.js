const Logger = require('scad-library').logger;
require('dotenv').config();
const log = new Logger().getInstance();
const axiosInstance = require('axios');
const http = require('http');
const https = require('https');
const axios = axiosInstance.create({
  httpAgent: new http.Agent({ keepAlive: true }),
  httpsAgent: new https.Agent({ keepAlive: true })
});

async function downloadCMSFile(req, res) {
  log.debug(`>>>>>Entered microservice.download.controller.downloadCMSFile`);
  try {
    const filename = req.query.filename;

    const cmsEndpoint = `${process.env.CMS_BASEPATH}/sites/${filename}`;

    const cmsResponse = await axios.get(cmsEndpoint, { responseType: 'stream' });

    res.set(cmsResponse.headers);

    cmsResponse.data.pipe(res);

  } catch (err) {
    log.error(`<<<<< Exited microservice.download.controller.downloadCMSFile with error ${err} `)
    throw err;
  }
}

module.exports = {
  downloadCMSFile
};
