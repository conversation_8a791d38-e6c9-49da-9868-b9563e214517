const { Sequelize, DataTypes, Model } = require("sequelize");
const ApprovalRequestComment = require("./approval-request-comment.model");

/**
 * Model for storing file attachments linked to comments in the Bayaan approval system.
 * Attachments can be supporting documents, screenshots, or other files.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const ApprovalRequestCommentAttachment = sequelize.define(
    "ApprovalRequestCommentAttachment",
    {
      id: {
        type: DataTypes.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      comment_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      filename: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      content_type: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      file_path: {
        type: DataTypes.STRING(500),
        allowNull: false,
      },
      file_size: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      tableName: "bayaan_approval_request_comment_attachments",
      createdAt: 'created_at',
      updatedAt: null,
      timestamps: false,
    }
  );
  
  ApprovalRequestCommentAttachment.belongsTo(ApprovalRequestComment(sequelize, DataTypes), {
    foreignKey: 'comment_id',
    targetKey: 'id',
    as: 'comment',
  });
  
  ApprovalRequestCommentAttachment.addHook('beforeCreate', (instance) => {
    instance.created_at = new Date();
  });
  
  return ApprovalRequestCommentAttachment;
}

module.exports = model;