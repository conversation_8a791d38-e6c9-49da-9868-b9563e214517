const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

// Define the table name for the livability data
const tableName = "VW_LIVABILITY_DATA";

/**
 * Constructs query to fetch Livability dashboard data based on the provided parameters.
 * @param {string} lang - The language for which data is fetched.
 * @param {string|null} parentCategory - The parent category for filtering data.
 * @param {string|null} category - The category for filtering data.
 * @param {number|null} ismap - Indicator whether to fetch map data.
 * @returns {Object} - An object containing the constructed query and bindings.
 * @throws {Array} - Throws an array with error code and message if an error occurs.
 */
async function getLivabilityDataQuery(lang, parentCategory = null, category = null, ismap = null) {
    try {
        lang = lang.toUpperCase();

        // Initialize bindings and conditions arrays
        let binds = {};
        let conditions = [
            `"REGION_${lang}" = 'All UAE'`,
            `"IS_ACTIVE" = 1`,
        ];

        if (parentCategory !== null) {
            conditions.push(`CATEGORY_${lang} = :category`);
            if (category !== null) {
                binds.category = category;
            } else {
                binds.category = parentCategory;
            }
        }

        // If ismap is provided, add condition for is map
        if (ismap === 1) {
            conditions.push(`"IS_MAP" = :ismap`);
            binds.ismap = ismap;
        }

        // Construct the SQL query using the conditions
        let query = `
            SELECT 
                "ITEM_GROUP_ID", 
                "ITEM_GROUP_NAME_${lang}" AS "GROUP_NAME",
                "VALUE",
                "COUNT",
                "ITEM_${lang}" AS "ITEM",
                "REGION_${lang}" AS "REGION",
                "PARENT_CATEGORY_${lang}" AS "PARENT_CATEGORY",
                "CATEGORY_${lang}" AS "CATEGORY",
                "IS_ACTIVE"
            FROM
                ${tableName}
            WHERE 
                ${conditions.join(" AND ")}
            ORDER BY 
                "ITEM_GROUP_ID",
                "ITEM_ID"
        `;

        return { query, binds };
    } catch (err) {
        log.error(
            `<<<<<< Exited microservice-dashboard.services.getQuery.service.getLivabilityDataQuery with error ${err} `
        );
        throw [424, err];
    }
}

/**
 * Constructs a query to fetch dashboard categories based on the provided language and parent label.
 * @param {string} lang - The language for which categories are fetched.
 * @param {string|null} parentCategory - The parent label for filtering categories.
 * @returns {Object} - An object containing the constructed query and bindings.
 * @throws {Array} - Throws an array with error code and message if an error occurs.
 */
async function getLivabilityCategoryQuery(lang, parentCategory = null) {
	try {
		lang = lang.toUpperCase();

		let binds = {};
        let conditions = [
            `"ITEM_EN" = 'All respondents'`,
            `"REGION_EN" = 'All UAE'`,
        ];

		// Define condition based on whether parentCategory is provided or not
		if (parentCategory !== null) {
			conditions.push(`"CATEGORY_DEPTH" = 2`);
			conditions.push(`"PARENT_CATEGORY_${lang}" = :parentCategory`);
			binds.parentCategory = parentCategory;
		} else {
			conditions.push(`"CATEGORY_DEPTH" IN (0, 1)`);
		}

        // Construct the SQL query using the condition created.
		let query = `
            SELECT 
                "CATEGORY_ID", "CATEGORY_${lang}" AS CATEGORY, "VALUE", "COUNT", "IS_ACTIVE"
            FROM
                ${tableName}
            WHERE
                ${conditions.join(" AND ")}
            ORDER BY CATEGORY_ID
        `;

		return { query, binds };
	} catch (err) {
		log.error(
			`<<<<<< Exited microservice-dashboard.services.getQuery.service.getLivabilityCategoryQuery with error ${err} `
		);
		throw [424, err];
	}
}


/**
 * Constructs query to fetch Livability dashboard composition data based on the provided parameters.
 * @param {string} lang - The language for which data is fetched.
 * @param {string|null} parentCategory - The parent category for filtering data.
 * @param {string|null} category - The category for filtering data.
 * @param {number|null} ismap - Indicator whether to fetch map data.
 * @returns {Object} - An object containing the constructed query and bindings.
 * @throws {Array} - Throws an array with error code and message if an error occurs.
 */
async function getLivabilityCompositionQuery(
	lang,
	parentCategory = null,
	category = null,
) {
	try {
		lang = lang.toUpperCase();

		let binds = {};
		let conditions = [
			`"ITEM_EN" = 'All respondents'`,
			`"REGION_EN" = 'All UAE'`,
			`"IS_COMPOSITION" = 1`,
			`"IS_ACTIVE" = 1`,
		];

		// Define condition based on whether parentCategory is provided or not
		if (parentCategory !== null) {
            conditions.push(`"PARENT_CATEGORY_${lang}" = :parentCategory`);

            if (category !== null) {
                binds.parentCategory = category;
            } else {
				binds.parentCategory = parentCategory;
            }
		}

		// Construct the SQL query using the condition created.
		let query = `
            SELECT 
                "CATEGORY_ID",
                "CATEGORY_${lang}" AS CATEGORY,
                "VALUE",
                "COUNT",
                "IN_LIVEABILITY_INDEX",
                "ACTIVE_PERCENT",
                "IS_ACTIVE"
            FROM
                ${tableName}
            WHERE
                ${conditions.join(" AND ")}
            ORDER BY
                CATEGORY_ID,
                ITEM_ID
        `;

		return { query, binds };
	} catch (err) {
		log.error(
			`<<<<<< Exited microservice-dashboard.services.getQuery.service.getLivabilityCompositionQuery with error ${err} `
		);
		throw [424, err];
	}
}

/**
 * Constructs query to fetch Livability dashboard composition description based on the category.
 * @param {string} lang - The language for which data is fetched.
 * @param {string|null} category - The category for filtering data.
 * @returns {Object} - An object containing the constructed query and bindings.
 * @throws {Array} - Throws an array with error code and message if an error occurs.
 */
async function getLivabilityCompositionDescriptionQuery(lang, category) {
	try {
		lang = lang.toUpperCase();

		let binds = {};
		let conditions = [
			`"ITEM_EN" = 'All respondents'`,
			`"REGION_EN" = 'All UAE'`,
			`"IS_COMPOSITION" = 1`,
			`"IS_ACTIVE" = 1`,
			`"CATEGORY_${lang}" = :category`,
			`ROWNUM <= 1`, // Only one row
		];
        binds.category = category;

		// Construct the SQL query using the condition created.
		let query = `
            SELECT 
                "CATEGORY_${lang}" AS CATEGORY,
                "COMPOSITION_DESCRIPTION_${lang}" AS COMPOSITION_DESCRIPTION,
                "IS_ACTIVE"
            FROM
                ${tableName}
            WHERE
                ${conditions.join(" AND ")}
        `;

		return { query, binds };
	} catch (err) {
		log.error(
			`<<<<<< Exited microservice-dashboard.services.getQuery.service.getLivabilityCompositionDescriptionQuery with error ${err} `
		);
		throw [424, err];
	}
}

/**
 * Constructs query to fetch Livability map title and description based on the category.
 * @param {string} lang - The language for which data is fetched.
 * @param {string|null} category - The category for filtering data.
 * @returns {Object} - An object containing the constructed query and bindings.
 * @throws {Array} - Throws an array with error code and message if an error occurs.
 */
async function getLivabilityMapMetaQuery(lang, category) {
	try {
		lang = lang.toUpperCase();

		let binds = {};
		let conditions = [
			`"ITEM_EN" = 'All respondents'`,
			`"REGION_EN" = 'All UAE'`,
			`"IS_MAP" = 1`,
			`"IS_ACTIVE" = 1`,
			`"CATEGORY_${lang}" = :category`,
			`ROWNUM <= 1`, // Only one row
		];
        binds.category = category;

		// Construct the SQL query using the condition created.
		let query = `
            SELECT 
                "CATEGORY_${lang}" AS CATEGORY,
                "TITLE_${lang}" AS TITLE,
                "SUBTITLE_${lang}" AS SUBTITLE,
                "IS_ACTIVE"
            FROM
                ${tableName}
            WHERE
                ${conditions.join(" AND ")}
        `;

		return { query, binds };
	} catch (err) {
		log.error(
			`<<<<<< Exited microservice-dashboard.services.getQuery.service.getLivabilityMapMetaQuery with error ${err} `
		);
		throw [424, err];
	}
}

module.exports = {
	getLivabilityDataQuery,
	getLivabilityCategoryQuery,
	getLivabilityCompositionQuery,
	getLivabilityCompositionDescriptionQuery,
	getLivabilityMapMetaQuery,
};
