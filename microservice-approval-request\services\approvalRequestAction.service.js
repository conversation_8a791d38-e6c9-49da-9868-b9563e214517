const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const pgModels = require('../../database/postgres/models'); // PostgreSQL models
const { IFPError } = require("../../utils/error");
const { notificationService } = require("../helper/notification");
const { formatApprovalRequest } = require("../helper/approvalRequestObject");

const ApprovalRequest = pgModels.ApprovalRequest;
const ApprovalRequestComment = pgModels.ApprovalRequestComment;
const ApprovalRequestHistory = pgModels.ApprovalRequestHistory;

/**
* Service for handling approval request actions
* Contains the business logic for processing various approval actions
*/
const approvalRequestActionService = {
    /**
    * Perform an action on an approval request
    * @param {string} requestId - ID of the approval request
    * @param {string} action - Type of action to perform (assign|approve|reject|revert|delete|restore|claim|unpublish)
    * @param {string} currentUserEmail - ID of the user performing the action
    * @param {Object} options - Additional options
    * @param {string} [options.assigneeId] - ID of user to assign (for assign action)
    * @param {string} [options.comment] - Optional comment about the action
    * @param {Object} [options.metadata] - Additional metadata
    * @returns {Promise<Object>} Updated approval request data
    */
    performApprovalAction: async (requestId, action, currentUserEmail, options = {}) => {
        const { assigneeId, comment, metadata } = options;
        
        // Start a transaction to ensure all database operations succeed or fail together
        const transaction = await pgModels.sequelize.transaction();
        
        try {
            // Find the approval request
            const approvalRequest = await ApprovalRequest.findByPk(requestId, { transaction });
            
            if (!approvalRequest) {
                throw new IFPError(400, `Approval request with ID ${requestId} not found`);
            }
            
            // Store the original values for history tracking
            const previousStatus = approvalRequest.status;
            const previousAssignee = approvalRequest.assignee_id;
            
            // Determine the new status and assignee based on the action
            const { newStatus, newAssignee, shouldNotify } = approvalRequestActionService._processActionEffects(
                action, 
                approvalRequest, 
                currentUserEmail, 
                assigneeId
            );
            
            // Update the approval request
            approvalRequest.status = newStatus;
            approvalRequest.assignee_id = newAssignee;
            approvalRequest.last_action_at = new Date();
            
            // Set the approver_id for approve action
            if (action === 'approve') {
                approvalRequest.approver_id = currentUserEmail;
            }
            
            // Save any additional metadata if provided
            if (metadata) {
                approvalRequest.metadata = {
                    ...(approvalRequest.metadata || {}),
                    ...metadata
                };
            }
            
            // Save the updated approval request
            await approvalRequest.save({ transaction });
            
            // Create a comment if provided or if it's a system action
            let commentRecord = null;
            if (comment || ['assign', 'approve', 'reject', 'revert', 'unpublish', 'claim'].includes(action)) {
                const commentContent = comment || approvalRequestActionService._generateSystemComment(action, currentUserEmail);
                const commentType = comment ? 'user' : 'system';
                // Create the comment record
                commentRecord = await ApprovalRequestComment.create({
                    request_id: requestId,
                    user_id: currentUserEmail,
                    content: commentContent,
                    comment_type: commentType
                }, { transaction });
            }

            // Record the history of this action
            await ApprovalRequestHistory.create({
                request_id: requestId,
                previous_status: previousStatus,
                new_status: newStatus,
                previous_assignee: previousAssignee,
                new_assignee: newAssignee,
                changed_by: currentUserEmail,
                changed_at: new Date(),
                comment_id: commentRecord ? commentRecord.id : null
            }, { transaction });

            // Commit the transaction
            await transaction.commit();

            // Send notification if required (after transaction commit)
            if (shouldNotify) {
                await approvalRequestActionService._sendActionNotification(
                    action, 
                    approvalRequest.requestor_id, 
                    currentUserEmail,
                    requestId,
                    approvalRequest.object_type
                );
            }
            
            // Return the updated approval request in the expected format
            return formatApprovalRequest(approvalRequest, {
                action,
                currentUserEmail,
                commentRecord
            });
        } catch (error) {
            // Rollback the transaction on error
            await transaction.rollback();
            throw error;
        }
    },
    
    /**
    * Process the effects of an action (status and assignee changes)
    * @private
    */
    _processActionEffects: (action, approvalRequest, currentUserEmail, assigneeId) => {
        let newStatus = approvalRequest.status;
        let newAssignee = approvalRequest.assignee_id;
        let shouldNotify = false;
        
        switch (action) {
            case 'assign':
                newAssignee = assigneeId;
                break;
            
            case 'approve':
                if (approvalRequest.status !== 'pending') {
                    throw new IFPError(400, `Only pending requests can be approved`);
                }
                newStatus = 'approved';
                newAssignee = approvalRequest.requestor_id; // Set back to requestor
                shouldNotify = true;
                break;
            
            case 'reject':
                if (approvalRequest.status !== 'pending') {
                    throw new IFPError(400, `Only pending requests can be rejected`);
                }
                newStatus = 'rejected';
                newAssignee = approvalRequest.requestor_id; // Set back to requestor
                break;
            
            case 'revert':
                if (approvalRequest.status !== 'pending') {
                    throw new IFPError(400, `Only pending requests can be reverted`);
                }
                newStatus = 'pending'; // Status remains pending
                newAssignee = approvalRequest.requestor_id; // Set back to requestor
                shouldNotify = true;
            break;
            
            case 'delete':
                newStatus = 'deleted';
                break;
                
            case 'restore':
                if (approvalRequest.status !== 'deleted') {
                    throw new IFPError(400, `Only deleted requests can be restored`);
                }
                newStatus = 'unpublished';
            break;
            
            case 'claim':
                // Just update the assignee to the current user
                newAssignee = currentUserEmail;
                break;
                
            case 'unpublish':
                if (approvalRequest.status !== 'approved') {
                    throw new IFPError(400, `Only approved requests can be unpublished`);
                }
                newStatus = 'unpublished';
                shouldNotify = true;
                break;
            
            default:
                throw new IFPError(400, `Invalid action: ${action}`);
        }
        
        return { newStatus, newAssignee, shouldNotify };
    },
    
    /**
    * Generate a system comment based on the action
    * @private
    */
    _generateSystemComment: (action, currentUserEmail) => {
        // Map each action to a default system comment, including the user who performed it
        const commentMap = {
            approve: `Request has been approved by ${currentUserEmail}.`,
            reject: `Request has been rejected by ${currentUserEmail}.`,
            revert: `Request has been reverted for changes by ${currentUserEmail}.`,
            unpublish: `The approved item has been unpublished by ${currentUserEmail}.`,
            assign: `Request has been assigned to a user by ${currentUserEmail}.`,
            delete: `Request has been deleted by ${currentUserEmail}.`,
            restore: `Request has been restored by ${currentUserEmail}.`,
            claim: `Request has been claimed by ${currentUserEmail}.`
        };

        // Return the mapped comment or a generic system action comment
        return commentMap[action] || `System action performed: ${action} by ${currentUserEmail}.`;
    },
    
    /**
    * Send notification for certain actions
    * @private
    */
    _sendActionNotification: async (action, requestorId, currentUserEmail, requestId, objectType) => {
        try {
            const notificationType = {
                approve: 'approval_request_approved',
                revert: 'approval_request_reverted',
                unpublish: 'approval_item_unpublished'
            }[action];
            
            if (notificationType) {
                await notificationService.sendNotification({
                    type: notificationType,
                    recipientId: requestorId,
                    senderId: currentUserEmail,
                    data: {
                        requestId,
                        objectType
                    }
                });
            }
        } catch (error) {
            // Log but don't fail the request if notification fails
            log.error(`Failed to send notification for ${action} action: ${error}`);
        }
    }
};

module.exports = approvalRequestActionService;