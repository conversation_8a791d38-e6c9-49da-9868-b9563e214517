const express = require('express');
const router = new express.Router();
const gisController = require('../microservice-gis/gis.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/map-districts', async (req, res, next) => {
    try {
      const data = await gisController.getMapDistricts(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

router.get('/map-districtsv2', async (req, res, next) => {
  try {
    const data = await gisController.getMapDistrictsV2(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.get('/map-districtsv3', async (req, res, next) => {
  try {
    const data = await gisController.getMapDistrictsV3(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.get('/auth', async (req, res, next) => {
  try {
    const data = await gisController.getArcGISToken(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;