const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const oracledb = require('oracledb');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {  getWhatNewDataQuery, getLatestDataQuery } = require('./getQuery.service');


async function getWhatNewData(type) {
  return new Promise((resolve, reject) => {
    getWhatNewDataQuery(type).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery successfully`);
        resolve(data);
      })
      .catch((err) => {
        log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getIndicatorMetaDataQuery with error ${err}`);
      reject(err);
    })
  })
}

async function getLatestData(type) {
  return new Promise((resolve, reject) => {
    getLatestDataQuery(type).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getLatestDataQuery`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getLatestDataQuery successfully`);
        resolve(data);
      })
      .catch((err) => {
        log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getLatestDataQuery with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getLatestDataQuery with error ${err}`);
      reject(err);
    })
  })
}

module.exports = {
  getWhatNewData, getLatestData
}