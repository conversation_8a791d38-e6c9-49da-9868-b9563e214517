const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const multer = require('multer');
class IFPError extends Error {
    constructor(status=500, message, data={}) {
        super();
        this.status = status;
        this.message = message;
        if (Object.keys(data).length)
            this.data = data
    }

    setAccess(access){
        this.access = access
    }
}

const ifpErrorMiddleware = (err, req, res, next) => {
    if (err instanceof IFPError) {
        log.error(`Error on ${req.path}`, err);
        if ('access' in err)
            return res.status(err.status).send({
                message: err.message,
                status: err.status,
                access: err.access
            });
        else{
            if (err.data)
                return res.status(err.status).send({
                    message: err.message,
                    status: err.status,
                    ...err.data
                });
            else
                return res.status(err.status).send({
                    message: err.message,
                    status: err.status,
                });
        }
            
    }
    else if (err instanceof multer.MulterError){
        if (err.code == 'LIMIT_FILE_SIZE')
            res.status(400).send({ message: `${err.message}, field: ${err.field}`});
        else
            res.status(400).send({ message: `Bad file request: ${err.field}`});
    }

    if (err.type == 'entity.parse.failed') {
        res.status(400).send({ message: 'Bad request body', error: err.message, body: err.body });
    }

    log.error(`Unhandled Error on ${req.path} ${err}`);
    res.status(500).send({
        message: "Internal Server Error",
        status: 500,
    });
};

module.exports = {
    IFPError,
    ifpErrorMiddleware
}