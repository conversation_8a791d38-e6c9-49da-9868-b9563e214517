require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getMetaFromCMS } = require('../services/common-service');

const constants = require('../config/constants.json')
const { getAcceptedTermsAndConditions, acceptUserTermsAndConditions, updateUserLangPreferrence } = require('./services/executeQuery.service'); 
const { IFPError } = require('../utils/error');


async function getTermsAndConditionsContent(req){
    return new Promise(async (resolve, reject) => {
        try {
            const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
            const cmsTCUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_TC_URL}`;
            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
            const data = await getMetaFromCMS(req,cmsLoginUrl, cmsTCUrl, req.user.groups);
            if (data.length){
                let tcData = data[0]
                cleanedTcData = {
                    title: tcData.title,
                    tcVersion: "1",
                    pageType: "tcpage",
                    sections: []
                }
                tcData.content_data.items.forEach(item=>{
                    sectionData = {
                        sectionOrder: item.section_order,
                        sectionTitle: item.section_title,
                        subsections:[]
                    }
                    item.subsections.forEach(subsection=>{
                        let subsectionItem={
                            subsectionContent: subsection.content,
                            subsectionOrder: subsection.order,
                            subsectionTitle: subsection.title
                        }
                        sectionData.subsections.push(subsectionItem)
                    })

                    cleanedTcData.sections.push(sectionData)
                    
                })

            }
            resolve([cleanedTcData])
        } catch (err) {
            
            log.error(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.acceptTermsAndConditions on getting CMS data with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
 async function acceptTermsAndConditions( req ) {
    log.debug(`>>>>>Entered terms-and-conditions-microservice.terms-and-conditions.controller.acceptTermsAndConditions`);
    return new Promise(async (resolve, reject) => {
        try {
            let body = req.body;
            body.language = req.headers["accept-language"];
            acceptUserTermsAndConditions(body)
            .then(results => {
                log.debug(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.acceptTermsAndConditions successfully `);
                resolve({
                    "message":'User successfully accepted Terms and Conditions!!!',
                    "tcAcceptStatus": results? true : false,
                    "tcVersion": body.tcVersion
                });
            })
            .catch((err) => {
                
                log.debug(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.acceptTermsAndConditions not successfully `);
                reject(err);
            });
        } catch (err) {
            
            log.error(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.acceptTermsAndConditions on getting CMS data with error ${err}`);
            reject(err);
        }
    })
}

async function getTermsAndConditions( req ) {
    log.debug(`>>>>>Entered terms-and-conditions-microservice.terms-and-conditions.controller.getAcceptedTermsAndConditions`);
    return new Promise(async (resolve, reject) => {
        try {
            const organization = req.params.organization;
            const userId = req.user.preferred_username;
            if (userId != req.params.userId)
                throw new IFPError(400,'Invalid user id')
            const tcVersion = req.params.tcVersion;
            const data = await getAcceptedTermsAndConditions(userId, organization, tcVersion);
            if ( data.length > 0 ){
                log.debug(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.getAcceptedTermsAndConditions successfully `);
                resolve({
                    "message":'User already accepted Terms and Conditions!!!',
                    "tcAcceptStatus": true,
                    "tcVersion": tcVersion,
                    // "result":data[0]
                });
            } else {
                resolve({
                    "message":'User has not accepted Terms and Conditions!!!',
                    "tcAcceptStatus": false,
                    "tcVersion": tcVersion,
                    // "result":data[0]
                });
            }
        } catch (err) {
            
            log.error(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.getAcceptedTermsAndConditions on getting CMS data with error ${err}`);
            reject(err);
        }
    })
}


async function updateLangPreferrence( req ) {
    log.debug(`>>>>>Entered terms-and-conditions-microservice.terms-and-conditions.controller.updateLangPreferrence`);
    return new Promise(async (resolve, reject) => {
        try {
            const body = req.body;
            updateUserLangPreferrence(body)
            .then(results => {
                log.debug(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.updateLangPreferrence successfully `);
                resolve({
                    "message":'User successfully updated preferred language!!!',
                    "Status": results? true : false,
                    "language": body.language
                });
            })
            .catch((err) => {
                log.debug(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.updateLangPreferrence not successfully `);
                reject(err);
            });
        } catch (err) {
            
            log.error(`<<<<<Exited terms-and-conditions-microservice.terms-and-conditions.controller.updateLangPreferrence on getting CMS data with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { acceptTermsAndConditions, getTermsAndConditions, updateLangPreferrence, getTermsAndConditionsContent };
