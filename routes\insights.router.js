const express = require('express');
const router = new express.Router();
const insightsController = require('../microservice-insights/insightsv2.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/list/:id', async (req, res, next) => {
    try {
      const data = await insightsController.listInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/add', async (req, res, next) => {
    try {
      const data = await insightsController.addInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/update/:id', async (req, res, next) => {
    try {
      const data = await insightsController.updateInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/delete/:id', async (req, res, next) => {
    try {
      const data = await insightsController.deleteInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/approve', async (req, res, next) => {
    try {
      const data = await insightsController.approveInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/reject', async (req, res, next) => {
    try {
      const data = await insightsController.rejectInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/request-approval', async (req, res, next) => {
    try {
      const data = await insightsController.sendInsightsForApproval(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/request-edit', async (req, res, next) => {
    try {
      const data = await insightsController.requestEditInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });
  


module.exports = router;
