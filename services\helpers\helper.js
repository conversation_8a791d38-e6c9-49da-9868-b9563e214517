const cacheExcludeTables = require('../../config/constants.json').redis.cacheExcludeTables
const crypto = require('crypto');
const sanitizeHtml = require('sanitize-html');
const { IFPError } = require('../../utils/error');

function shouldCache(statement) {
    return ![
      'INSERT INTO', 'UPDATE ', 'MERGE '
    ].some(op => statement.includes(op)) && !cacheExcludeTables.some(table => statement.includes(table));
  }

function extractViewPrefix(statement) {
    if (!statement.includes('* FROM')) return '';
    const querySplit = statement.split("* FROM");
    const viewNameSplit = (querySplit[1] || '').trim().split(' ');
    return viewNameSplit[0] || '';
  }

function generateCacheKey(statement, binds) {
    const serializedBinds = JSON.stringify(binds);
    const combinedData = statement + serializedBinds;
    const viewPrefix = extractViewPrefix(statement);
    return 'data_' + viewPrefix + crypto.createHash('md5').update(combinedData).digest("hex");
}

function generateClkCacheKey(statement, binds = {}) {
  const serializedBinds = JSON.stringify(binds);
  if (Object.keys(binds).length > 0) {
		statement = statement + serializedBinds;
	}
  const viewPrefix = extractViewPrefix(statement);
  return 'clk_data_' + viewPrefix + crypto.createHash('md5').update(statement).digest("hex");
}

async function validateAPIKey(key) {
  const binds = {key:key}
  const query = `SELECT KEY FROM IFP_ADMIN_KEY WHERE KEY=:key`
  const data = await db.simpleExecute(query,binds)
  if (data.length)
    return true
  else
    return false
}

function clearCacheUser(key){
  let user;
  switch (key) {
    case '1d8e552f-6727-4dda-b953-1f7f3f2bc580':
        user = '<EMAIL>'
        break;
    case 'cfa1dbe5-528a-4918-8463-5d4ff4a3eecd':
        user = '<EMAIL>'
        break;
    case '8da4b49d-aaba-4b08-9242-1ee1d4d324bd':
        user = '<EMAIL>'
        break;
    case 'efb04232-46dd-463b-acca-93819f48a15f':
        user = '<EMAIL>'
        break;
    case '13c25b6c-5ed1-4a53-8676-7cc9c5786b2d':
        user= '<EMAIL>'
        break;
    case '8da30475-96bb-436d-9f28-49780ddb3fda':
        user = '<EMAIL>'
        break;
    case '6a8db918-b4cf-4c41-bfea-25c246c0a283':
        user = '<EMAIL>'
        break;
    default:
        
  }

  return user;
}

const toTitleCase = (str) => {
  return str.toLowerCase().split(' ').map(function (word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
  }).join(' ');
}

function validateEmailContent(content){
  const sanitizedContent = sanitizeHtml(content, {
      allowedTags: [], // Disallow all tags
      allowedAttributes: {} // Disallow all attributes
  });

  if (content !== sanitizedContent) {
      throw new IFPError(400,"HTML content is not allowed.");
  }

  return sanitizedContent;
}

module.exports = {
    shouldCache,
    generateCacheKey,
    generateClkCacheKey,
    clearCacheUser,
    validateAPIKey,
    toTitleCase,
    validateEmailContent
}