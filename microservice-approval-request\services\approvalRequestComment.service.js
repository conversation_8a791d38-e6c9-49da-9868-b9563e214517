const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const { get } = require("superagent");
const pgModels = require('../../database/postgres/models'); // PostgreSQL models
const { IFPError } = require("../../utils/error");
const { formatApprovalRequestComment } = require("../helper/approvalRequestObject");
const { getUserDetails } = require('../helper/approvalRequestObject');
// TODO: Need to implement image upload service
// const imageService = require('../services/image.service');

/**
 * Service for handling approval request comments
 */
const commentService = {
    /**
     * Add a comment to an approval request
     * @param {Object} req - Express request object
     * @returns {Promise<Object>} - Comment data with attachments and user details
     */
    addComment: async (req) => {
        const { id: requestId } = req.params;
        const currentUserEmail = req.user.preferred_username;
        
        // Check if approval request exists
        const approvalRequest = await pgModels.ApprovalRequest.findByPk(requestId);
        if (!approvalRequest) {
            throw new IFPError(400, `Approval request not found`);
        }

        // Check if user is authorized to comment
        if (approvalRequest.requestor_id !== currentUserEmail && approvalRequest.assignee_id !== currentUserEmail && approvalRequest.approver_id !== currentUserEmail) {
            throw new IFPError(403, `User is not authorized to comment on this request`);
        }
        // Check if request is in a valid state for commenting
        if (approvalRequest.status !== 'pending') {
            throw new IFPError(400, `Cannot comment on request in status: ${approvalRequest.status}`);
        }
        
        let content, metadata, attachmentData = [];

        // Handle multipart/form-data
        // TODO: Image upload logic
        if (req.files && req.files.length > 0) {
            content = req.body.content;
            metadata = req.body.metadata ? JSON.parse(req.body.metadata) : {};
            
            // TODO: Process uploaded files
            // attachmentData = req.files.map(file => ({
            //     filename: file.filename,
            //     originalFilename: file.originalname,
            //     contentType: file.mimetype,
            //     filePath: file.path,
            //     fileSize: file.size
            // }));
        }
        // Handle JSON payload with base64 images
        else if (req.is('application/json')) {
            const { content: jsonContent, attachments = [], metadata: jsonMetadata = {} } = req.body;
            content = jsonContent;
            metadata = jsonMetadata;
            
            // TODO: Process base64 images
            // if (attachments && attachments.length > 0) {
            //     for (const attachment of attachments) {
            //         if (!attachment.imageData) {
            //             throw new IFPError(`Image data is required for attachments`);
            //         }
            //         const savedImage = await imageService.saveBase64Image(
            //             attachment.imageData,
            //             attachment.contentType,
            //             attachment.filename
            //         );
            //         attachmentData.push(savedImage);
            //     }
            // }
        } else {
            // Plain form data without files
            content = req.body.content;
            metadata = req.body.metadata || {};
        }

        // Validate content
        if (!content) {
            throw new IFPError(400, `Comment content is required`);
        }

        // Create comment record in database
        const comment = await pgModels.ApprovalRequestComment.create({
            request_id: requestId,
            user_id: currentUserEmail,
            content: content,
            comment_type: 'comment'
        });

        // Create attachment records for each file
        const attachments = [];
        for (const attachment of attachmentData) {
            const commentAttachment = await pgModels.ApprovalRequestCommentAttachment.create({
                comment_id: comment.id,
                filename: attachment.originalFilename,
                content_type: attachment.contentType,
                file_path: attachment.filePath,
                file_size: attachment.fileSize
            });
            
            // Get the CDN URL for the attachment
            const url = imageService.getAttachmentUrl(attachment.filename);
            
            attachments.push({
                id: commentAttachment.id,
                url: url,
                filename: attachment.originalFilename,
                contentType: attachment.contentType
            });
        }
        
        // Return response
        return formatApprovalRequestComment(comment, {
            attachments,
            fetchUserDetails: true,
        });
    },

    /**
     * Get comments for an approval request
     * @param {Object} req - Express request object
     * @returns {Promise<Object>} - Paginated comments data
     */
    getComments: async (req) => {
        const { id: requestId } = req.params;
        const { commentType } = req.query;
        const currentUserEmail = req.user.preferred_username;
        
        // Check if approval request exists
        const approvalRequest = await pgModels.ApprovalRequest.findByPk(requestId);
        if (!approvalRequest) {
            throw new IFPError(400, `Approval request not found`);
        }

        // Check if user is authorized to comment
        if (approvalRequest.requestor_id !== currentUserEmail && approvalRequest.assignee_id !== currentUserEmail && approvalRequest.approver_id !== currentUserEmail) {
            throw new IFPError(403, `User is not authorized to get comment on this request`);
        }
        
        // Set up query filters
        const filter = { request_id: requestId };
        if (commentType) filter.comment_type = commentType;

        const comments = await pgModels.ApprovalRequestComment.findAll({
            where: filter,
            order: [['created_at', 'ASC']],
        });

        // Cache user details to avoid repeated calls
        const userDetailsCache = {};
        return Promise.all(comments.map(async (comment) => {
            let userDetails = userDetailsCache[comment.user_id];
            if (!userDetails) {
                userDetails = await getUserDetails(comment.user_id);
                userDetailsCache[comment.user_id] = userDetails;
            }
            return {
                id: comment.id,
                requestId: comment.request_id,
                content: comment.content,
                commentType: comment.comment_type,
                createdAt: comment.created_at,
                updatedAt: comment.updated_at,
                userDetails,
            };
        }));
    },

    /**
     * Edit an existing comment
     * @param {Object} req - Express request object
     * @returns {Promise<Object>} Updated comment data
     */
    editComment: async (req) => {
        const { id: requestId, commentId } = req.params;
        const currentUserEmail = req.user.preferred_username;
        
        // Check if comment exists and belongs to the request
        const comment = await pgModels.ApprovalRequestComment.findOne({
            where: {
                id: commentId,
                request_id: requestId
            }
        });

        if (!comment) {
            throw new IFPError(400, `Comment not found. It may have been deleted or does not belong to this request`);
        }
        
        // Check if user is the comment author
        if (comment.user_id !== currentUserEmail) {
            throw new IFPError(403, `You can only edit your own comments.`);
        }
        
        // Update comment content
        if (req.body.content !== undefined) {
            comment.content = req.body.content;
        }
        
        // Save updated comment
        await comment.save();
        
        // Return response
        return formatApprovalRequestComment(comment, {
            fetchUserDetails: true,
        });
    },

    /**
     * Delete a comment
     * @param {Object} req - Express request object
     * @returns {Promise<Object>} Deletion confirmation
     */
    deleteComment: async (req) => {
        const { id: requestId, commentId } = req.params;
        const currentUserEmail = req.user.preferred_username;
        
        // Delete the comment
        await pgModels.ApprovalRequestComment.destroy({
            where: {
                id: commentId,
                user_id: currentUserEmail,
                request_id: requestId
            }
        });
        
        // Return success response
        return {
            success: true,
            message: 'Comment deleted successfully',
            id: commentId
        };
    }
};

module.exports = commentService;