const express = require('express');

const router = new express.Router();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../config/constants.json')


const analyticalAppsRouter = require('./analytical-app.router')
const statisticsInsightsRouter = require('./statistics-insights.router')
const innovativeInsightsRouter = require('./innovative-insights.router')
const officialInsightsRouter = require('./official-insights.router')
const domainsRouter = require('./domains.router')
const surveyRouter = require('./survey.router')
const tcRouter = require('./terms-and-conditions.router')
const pagesRouter = require('./pages.router')
const settingsRouter = require('./settings.router')
const insightsRouter = require('./insights.router')
const notificationsRouter = require('./notifications.router')
const infographicReportsRouter = require('./infographic-reports.router')
const publicationsRouter = require('./publications.router')
const slaRouter = require('./sla.router')
const glossaryRouter = require('./glossary.router')
const geospatialRevampRouter = require('./geospatial-revamp.router')
const headerRouter = require('./header.router')
const categoriesRouter = require('./categories.router')
const classificationRouter = require('./classification.router')
const footerRouter = require('./footer.router')
const myAppsRouter = require('./myapps.router')
const compareRouter = require('./indicator-compare.router')
const spatialAnalyticsRouter = require('./spatial-analytics.router')
const eciRouter = require('./eci.router')
const basketRouter = require('./basket.router')
const whatsNewRouter = require('./whatsnew.router')
const userJourneyRouter = require('./user-journey.router')
const gisRouter = require('./gis.router')
const recommendationsRouter = require('./recommendations.router')
const computeRouter = require('./compute.router')
const reportRouter = require('./infographic-reports.router')
const livabilityDashboardRouter = require("./livability-dashboard.router");
const dashboardBuilderRouter = require("./dashboard-builder.router");
const censusRouter = require("./census.router");
const newslettersRouter = require("./newsletters.router");
const inflationRouter = require("./inflation.router");
const accessCheckRouter = require("./accesscheck.router");
const chartInsightsRouter = require('./chart-insights.router')
const aiInsightReportRouter = require('./ai-insight-report.router')
const dmpRouter = require("./dmp.router")
const bayaanProductLibraryRouter = require('./bayaan-product-library.router')

try {
	log.debug(">>>>> Enter content-type router");

	router.use("/analytical-apps", analyticalAppsRouter);
	router.use("/statistics-insights", statisticsInsightsRouter);
	router.use("/innovative-insights", innovativeInsightsRouter);
	router.use("/official-insights", officialInsightsRouter);
	router.use("/domains", domainsRouter);
	router.use("/survey", surveyRouter);
	router.use("/terms-and-conditions", tcRouter);
	router.use("/pages/whatsnewpage", whatsNewRouter);
	router.use("/whatsnew", whatsNewRouter);
	router.use("/pages", pagesRouter);
	router.use("/settings", settingsRouter);
	router.use("/insights", insightsRouter);
	router.use("/chart-insights", chartInsightsRouter);
	router.use("/notifications", notificationsRouter);
	router.use("/newsletters", infographicReportsRouter);
	router.use("/publications", publicationsRouter);
	router.use("/sla", slaRouter);
	router.use("/glossary", glossaryRouter);
	router.use("/geospatial-revamp", geospatialRevampRouter);
	router.use("/header", headerRouter);
	router.use("/categories", categoriesRouter);
	router.use("/classifications", classificationRouter);
	router.use("/footer", footerRouter);
	router.use("/myapps", myAppsRouter);
	router.use("/indicator-compare", compareRouter);
	router.use("/spatial-analytics", spatialAnalyticsRouter);
	router.use("/eci", eciRouter);
	router.use("/basket", basketRouter);
	router.use("/user-journey", userJourneyRouter);
	router.use("/gis", gisRouter);
	router.use("/recommendations", recommendationsRouter);
	router.use("/compute", computeRouter);
	router.use("/reports", reportRouter);
	router.use("/inflation", inflationRouter);

	router.use("/dashboards/livability", livabilityDashboardRouter);
	router.use("/dashboard-builder", dashboardBuilderRouter);
	router.use("/census", censusRouter);
	router.use("/newsletter-publications", newslettersRouter);
	router.use("/dmp", dmpRouter)

	// Mobile - Access check
	router.use("/access-check", accessCheckRouter);
	router.use("/ai-insight-report", aiInsightReportRouter)
	router.use("/bayaan-product-library", bayaanProductLibraryRouter);

	log.debug("<<<<< Exit content-type router successfully");
} catch (err) {
  log.error(`<<<<< Exit content-type router with error ${err}`);
  
}

module.exports = router;
