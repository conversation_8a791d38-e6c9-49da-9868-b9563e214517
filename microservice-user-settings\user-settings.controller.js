require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getUserSettingsData, updateUserSettingsData, deleteUserSettingsData } = require('./services/executeQuery.service'); 

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
 async function getUserSettings( req ) {
    log.info(`>>>>>Entered user-settings-microservice.user-settings.controller.getUserSettings`);
    try {
        const userEmail = req.user.preferred_username;
        let results = await getUserSettingsData(userEmail)
        let settingsData = {}
        if (results.length){
            results.forEach( settingRecord =>{
                log.info(`Setting ${settingRecord.SETTING} has value ${settingRecord.VALUE}`);
                settingsData[settingRecord.SETTING] = settingRecord.VALUE
            })
        }
        else{
            settingsData = {
                "fontSize": "md",
                "theme": "light",
                "cursor": "type1",
                "lang" : "en"
            }
        }

        log.info(`<<<<<Exited user-settings-microservice.user-settings.controller.getUserSettingsData successfully `);
        return settingsData;
    } catch (err) {
        log.error(`<<<<<Exited user-settings-microservice.user-settings.controller.getUserSettings with error ${err}`);
        throw err;
    }
}

async function updateUserSettings( req ) {
    log.debug(`>>>>>Entered user-settings-microservice.user-settings.controller.updateUserSettings`);

    try {
        const userEmail = req.user.preferred_username;
        const settings = req.body.settings;
        log.info(`Settings to be updated: ${JSON.stringify(settings)}`);
        if (settings){
            await deleteUserSettingsData(userEmail);
            settings.forEach( async setting => {
                log.info(`Updating setting ${setting.name} with value ${setting.value}`);
                await updateUserSettingsData(userEmail, setting.name, setting.value);
            })
            return {
                "message":'User settings has been updated',
                "status": "success"
            };
        }
        else{
            return {
                "message":'Error updating settings. No configuration data has been provided',
                "status": "failed"
            };
        }
    } catch (err) {
        log.error(`<<<<<Exited user-settings-microservice.user-settings.controller.updateUserSettings on getting CMS data with error ${err}`);
        throw err;
    }
}


module.exports = { getUserSettings, updateUserSettings };
