const axios = require('axios');
const Logger = require('scad-library').logger;

const log = new Logger().getInstance();

async function getDashboardData(req) {
    return new Promise((resolve, reject) => {
        const host = req.get('host');
        let url = `http://${host}/api/content-type/statistics-insights/${req.params.id}`;
        log.info(`API call to ${url} from getDashboard.service.js`);
        axios.get(`${url}`, {
            headers: {
                Authorization: req.headers.authorization,
                "Accept-language": req.headers["accept-language"]
            }
        }).then(response => {
            resolve(response.data);
        }).catch(err => {
            
            log.error(`<<<<<Exited microservice-upload-compare.getStatisticsInsightsById error at API call to ${url} from getDashboard.service.js`);
            reject(err);
        })
    })
};

module.exports = { getDashboardData }