let data = JSON.stringify([
    {
        "id": "3188",
        "type": "Tableau-Internal",
        "component_subtitle": "National Account Dashboard",
        "component_title": "National Account",
        "domains": [

        ],
        "application_url": "",
        "imgSrc": "\/sites\/default\/files\/uploads\/images\/Tnhumb-img-3.png",
        "attachment": "",
        "policy_guide": "",
        "note": "",
        "search_tags": [

        ],
        "narrative": "",
        "Indicator": " Empty Default ",
        "json": " {\u003Cbr \/\u003E\r\n `language`: `EN`,\u003Cbr \/\u003E\r\n `indicatorTools`: [\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `export-png`,\u003Cbr \/\u003E\r\n `disabled`: true,\u003Cbr \/\u003E\r\n `label`: `ExportPNG`\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `export-csv`,\u003Cbr \/\u003E\r\n `label`: `ExportCSV`\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `indicatorFilters`: [\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `period-filter`,\u003Cbr \/\u003E\r\n `options`: [\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `1-year`,\u003Cbr \/\u003E\r\n `label`: `1YEAR`,\u003Cbr \/\u003E\r\n `value`: 1,\u003Cbr \/\u003E\r\n `unit`: `year`\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `3-years`,\u003Cbr \/\u003E\r\n `label`: `3YEARS`,\u003Cbr \/\u003E\r\n `value`: 3,\u003Cbr \/\u003E\r\n `unit`: `years`\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `All`,\u003Cbr \/\u003E\r\n `label`: `ALL`,\u003Cbr \/\u003E\r\n `value`: null,\u003Cbr \/\u003E\r\n `unit`: null,\u003Cbr \/\u003E\r\n `isSelected`: true\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n ]\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `indicatorDrivers`: [\u003Cbr \/\u003E\r\n \u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `indicatorValues`: [\u003Cbr \/\u003E\r\n \u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `indicatorVisualizations`: {\u003Cbr \/\u003E\r\n `visualizationsMeta`: [\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `line-chart-non-oil-trade-indicator`,\u003Cbr \/\u003E\r\n `type`: `line-chart`,\u003Cbr \/\u003E\r\n `dimension`: false,\u003Cbr \/\u003E\r\n `dbColumn`: `INDICATOR_ID`,\u003Cbr \/\u003E\r\n `seriesMeta`: [\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `non-oil-imports`,\u003Cbr \/\u003E\r\n `label`: `Non-OilImports`,\u003Cbr \/\u003E\r\n `color`: `#3267FF`,\u003Cbr \/\u003E\r\n `type`: `solid`,\u003Cbr \/\u003E\r\n `dbIndicatorId`: `45290`,\u003Cbr \/\u003E\r\n `xAccessor`: {\u003Cbr \/\u003E\r\n `type`: `date`,\u003Cbr \/\u003E\r\n `path`: `OBS_DT`,\u003Cbr \/\u003E\r\n `specifier`: `%Y-%m-%d`\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n `yAccessor`: {\u003Cbr \/\u003E\r\n `type`: `value`,\u003Cbr \/\u003E\r\n `path`: `VALUE`\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `non-oil-exports`,\u003Cbr \/\u003E\r\n `label`: `Non-OilExports`,\u003Cbr \/\u003E\r\n `color`: `#26A6BF`,\u003Cbr \/\u003E\r\n `type`: `solid`,\u003Cbr \/\u003E\r\n `dbIndicatorId`: `8430`,\u003Cbr \/\u003E\r\n `xAccessor`: {\u003Cbr \/\u003E\r\n `type`: `date`,\u003Cbr \/\u003E\r\n `path`: `OBS_DT`,\u003Cbr \/\u003E\r\n `specifier`: `%Y-%m-%d`\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n `yAccessor`: {\u003Cbr \/\u003E\r\n `type`: `value`,\u003Cbr \/\u003E\r\n `path`: `VALUE`\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n {\u003Cbr \/\u003E\r\n `id`: `non-oil-re-export`,\u003Cbr \/\u003E\r\n `label`: `Non-OilRe-exports`,\u003Cbr \/\u003E\r\n `color`: `#983EFF`,\u003Cbr \/\u003E\r\n `type`: `solid`,\u003Cbr \/\u003E\r\n `dbIndicatorId`: `45283`,\u003Cbr \/\u003E\r\n `xAccessor`: {\u003Cbr \/\u003E\r\n `type`: `date`,\u003Cbr \/\u003E\r\n `path`: `OBS_DT`,\u003Cbr \/\u003E\r\n `specifier`: `%Y-%m-%d`\u003Cbr \/\u003E\r\n },\u003Cbr \/\u003E\r\n `yAccessor`: {\u003Cbr \/\u003E\r\n `type`: `value`,\u003Cbr \/\u003E\r\n `path`: `VALUE`\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `markersMeta`: [\u003Cbr \/\u003E\r\n \u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `showLegend`: true,\u003Cbr \/\u003E\r\n `showPointLabels`: true,\u003Cbr \/\u003E\r\n `xAxisLabel`: null,\u003Cbr \/\u003E\r\n `yAxisLabel`: `MillionAED`,\u003Cbr \/\u003E\r\n `xAxisFormat`: `date_y`,\u003Cbr \/\u003E\r\n `yAxisFormat`: `d3-number`,\u003Cbr \/\u003E\r\n `tooltipTitleFormat`: `date_MMMy`,\u003Cbr \/\u003E\r\n `tooltipValueFormat`: `number_1.0-0`\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n ],\u003Cbr \/\u003E\r\n `visualizationDefault`: `line-chart-non-oil-trade-indicator`\u003Cbr \/\u003E\r\n }\u003Cbr \/\u003E\r\n} ",
        "indicator_list": "",
        "indicatorValues_subtitle": "",
        "indicatorValues_title": "",
        "visualization_subtitle": "",
        "visualization_title": "",
        "enableDynamicPanel": "False",
        "listofDyanmicPanelContent": [

        ],
        "highlightsMeta": "",
        "infogramUrl": "",
        "confidenceIntervalMeta": "",
        "enableConfidenceInterval": "False",
        "endpoint_label": "",
        "endpoint_title": "",
        "endpoint_url": "",
        "default_layer": "False",
        "show_on_legend": "False",
        "defaultDistrictId": "",
        "endpointType": "",
        "nodeId": "",
        "summaryCardId": "",
        "endpoint_icon_id": "",
        "cardDate": "",
        "dashboardUrl": "",
        "enablePointToggle": "False",
        "maxPointLimit": "",
        "minLimitYAxis": "",
        "publication_date": "2023-05-10",
        "tagName": "Dashboard",
        "tagColorCode": "#168EAA",
        "showInsights": "False",
        "height": "883",
        "host_url": "https:\/\/bi.scad.gov.ae\/",
        "embedded_code_version": "3",
        "site_root": "",
        "external_name": "NationalAccountsDashboard_28Sep2021\/NationalAccounts_Summary",
        "tabs": "No",
        "toolbar": "Yes",
        "showAppBanner": "False"
    },

    {
        id: "598",
        type: "Insights-Discovery",
        component_subtitle: "The analytical app is based on Hotel Industry data. It provides an important performance indicators and metrics on hotels across selected cities.\n\nSTR stands for Smith Travel Research, a hospitality analytics firm founded in 1985. They collect the data from hotels who subscribed to STR service to observe the performance metrics of their competitors. The genius part of STR's model is that these hotel customers also contribute their own performance metrics with STR which then packages and resells them to other competitors and investors. So readers of this dashboard should bear in mind that the following data doesn't cover the whole scope of the hotels in the listed cities.\n\n \n\n \n\nPlease visit for more information: https://hoteltechreport.com/news/str-report",
        component_title: "STR - Insights Discovery",
        domains: [
            "Economy",
        ],
        application_url: "",
        imgSrc: "/sites/default/files/uploads/images/Tnhumb-img-1.png",
        attachment: "",
        policy_guide: "",
        note: "",
        search_tags: [
        ],
        narrative: "",
        Indicator: " Real Estate - Highlights Section Sale ",
        json: " {<br />\r\n  &quot;language&quot;: &quot;EN&quot;,<br />\r\n  &quot;type&quot;: &quot;insights-discovery&quot;,<br />\r\n  &quot;sortVisualizations&quot;: true,<br />\r\n  &quot;indicatorValues&quot;: {<br />\r\n    &quot;overviewValueMeta&quot;: [<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;year-to-date-value&quot;,<br />\r\n        &quot;title&quot;: &quot;Occupancy&quot;,<br />\r\n        &quot;aggregation&quot;: &quot;Year to Date&quot;,<br />\r\n        &quot;hideYearDate&quot;: true,<br />\r\n        &quot;viewName&quot;: &quot;VW_STR_INDICATORS&quot;,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;INDICATOR_ID&quot;: [<br />\r\n            &quot;OCCUPANCY_YTD&quot;,<br />\r\n            &quot;OCCUPANCY_MOM&quot;,<br />\r\n            &quot;OCCUPANCY_YOY&quot;,<br />\r\n            &quot;CH_OCCUPANCY_YTD&quot;,<br />\r\n            &quot;CH_OCCUPANCY_MOM&quot;,<br />\r\n            &quot;CH_OCCUPANCY_YOY&quot;<br />\r\n          ]<br />\r\n        }<br />\r\n      },<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;percentage-change&quot;,<br />\r\n        &quot;title&quot;: &quot;Y/Y&quot;,<br />\r\n        &quot;type&quot;: &quot;dynamic-with-title-template&quot;,<br />\r\n        &quot;valueFormat&quot;: &quot;percentage_1.1-1&quot;,<br />\r\n        &quot;color&quot;: &quot;#3667ff&quot;,<br />\r\n        &quot;templateFormat&quot;: &quot;dateQuarterYYYY&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_STR_MONTHLY_KPIS_CITY&quot;,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;INDICATOR_ID&quot;: &quot;KPIS_BY_CITY_MONTHLY&quot;<br />\r\n          <br />\r\n        },<br />\r\n        &quot;filterBy&quot;: {<br />\r\n          &quot;CATEGORY_NAME&quot;: &quot;OCCUPANCY&quot;,<br />\r\n          &quot;CITY_NAME&quot;: &quot;ABU DHABI&quot;<br />\r\n        },<br />\r\n        &quot;unit&quot;: &quot;years&quot;,<br />\r\n        &quot;period&quot;: 1<br />\r\n      }<br />\r\n    ]<br />\r\n  },<br />\r\n  &quot;indicatorTools&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-png&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;label&quot;: &quot;Export PNG&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-csv-all&quot;,<br />\r\n      &quot;label&quot;: &quot;Extract All&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-csv&quot;,<br />\r\n      &quot;label&quot;: &quot;Extract Current Selection&quot;<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorActions&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;toggle-fullscreen&quot;,<br />\r\n      &quot;type&quot;: &quot;simple&quot;,<br />\r\n      &quot;label&quot;: &quot;Toggle Fullscreen&quot;<br />\r\n    }<br />\r\n  ]<br />\r\n} ",
        indicator_list: "599",
        indicatorValues_subtitle: "",
        indicatorValues_title: "",
        visualization_subtitle: "",
        visualization_title: "",
        enableDynamicPanel: "True",
        listofDyanmicPanelContent: [
            "Highlights",
        ],
        highlightsMeta: "{\n  \"indicatorValues\": {\n    \"selectAggregation\": {\n      \"label\": \"Time Period\",\n      \"options\": [\n        \"Year to Date\",\n        \"Year on Year\",\n        \"Month on Month\"\n      ],\n      \"default\": \"Year to Date\"\n    },\n    \"valuesMeta\": [\n      {\n        \"id\": \"year-to-date\",\n        \"title\": \"\",\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"additionalText\": \"Jan to\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"OCCUPANCY_YTD\"\n          ]\n        },\n        \"dateFormat\": \"MMM YYYY\"\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Occupancy\",\n        \"subtitle\": \"\",\n        \"hideYearDate\": true,\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"iconId\": \"RENT\",\n        \"iconLightId\": \"RENT_LIGHT\",\n        \"note\": \"Represent the percentage of available rooms sold during a specified time period. Occupancy rate is calculated by dividing the number of rooms sold by rooms available. @br@Occupancy rate = Rooms Sold / Rooms available\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"OCCUPANCY_YTD\",\n            \"OCCUPANCY_MOM\",\n            \"OCCUPANCY_YOY\",\n            \"CH_OCCUPANCY_YTD\",\n            \"CH_OCCUPANCY_MOM\",\n            \"CH_OCCUPANCY_YOY\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Available rooms\",\n        \"subtitle\": \"(Room's Supply)\",\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"iconId\": \"VILLA\",\n        \"note\": \"The number of rooms in a hotel or set of hotels multiplied by the number of days in a specified time period. @br@Example: 100 rooms in subject hotel x 31 days in the month = Room Supply of 3,100 for the month.\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"SUPPLY_YTD\",\n            \"SUPPLY_MOM\",\n            \"SUPPLY_YOY\",\n            \"CH_SUPPLY_YTD\",\n            \"CH_SUPPLY_MOM\",\n            \"CH_SUPPLY_YOY\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Occupied/ Sold rooms\",\n        \"subtitle\": \"(Room's Demand)\",\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"iconId\": \"APARTMENT\",\n        \"note\": \"The number of rooms sold in a specified time period (excludes complimentary rooms)\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"DEMAND_YTD\",\n            \"DEMAND_MOM\",\n            \"DEMAND_YOY\",\n            \"CH_DEMAND_YTD\",\n            \"CH_DEMAND_MOM\",\n            \"CH_DEMAND_YOY\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Revenue\",\n        \"subtitle\": \"(Room's Revenue)\",\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"iconId\": \"NUM_LISTING\",\n        \"note\": \"Total room revenue generated from the guestroom rentals or sales\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"REVENUE_YTD\",\n            \"REVENUE_MOM\",\n            \"REVENUE_YOY\",\n            \"CH_REVENUE_YTD\",\n            \"CH_REVENUE_MOM\",\n            \"CH_REVENUE_YOY\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Revenue Per Available Room\",\n        \"subtitle\": \"(RevPAR)\",\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"iconId\": \"COMMERCIAL\",\n        \"iconLightId\": \"COMMERCIAL_LIGHT\",\n        \"note\": \"Total room revenue divided by the total number of available rooms. @br@RevPAR= Room Revenue/Rooms Available\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"REVPAR_YTD\",\n            \"REVPAR_MOM\",\n            \"REVPAR_YOY\",\n            \"CH_REVPAR_YTD\",\n            \"CH_REVPAR_MOM\",\n            \"CH_REVPAR_YOY\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Average Daily Rate\",\n        \"subtitle\": \"(ADR)\",\n        \"format\": \"USD\",\n        \"viewName\": \"VW_STR_INDICATORS\",\n        \"iconId\": \"LAND\",\n        \"iconLightId\": \"LAND_LIGHT\",\n        \"note\": \"A measure of the average rate paid for rooms sold, calculated by dividing room revenue by rooms sold. @br@ADR = Room Revenue/Rooms Sold\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"ADR_YTD\",\n            \"ADR_MOM\",\n            \"ADR_YOY\",\n            \"CH_ADR_YTD\",\n            \"CH_ADR_MOM\",\n            \"CH_ADR_YOY\"\n          ]\n        }\n      }\n    ]\n  }\n}",
        infogramUrl: "https://coi.infogram.com/1pkr1dzrkdvx7dh9x7yn7vvxgyu3dp27qep",
        confidenceIntervalMeta: "",
        enableConfidenceInterval: "False",
        endpoint_label: "",
        endpoint_title: "",
        endpoint_url: "",
        default_layer: "False",
        show_on_legend: "False",
        defaultDistrictId: "",
        endpointType: "",
        nodeId: "",
        summaryCardId: "",
        endpoint_icon_id: "",
        cardDate: "",
        dashboardUrl: "",
        enablePointToggle: "True",
        maxPointLimit: "",
        minLimitYAxis: "",
        publication_date: "2023-05-25",
        tagName: "Insights Discovery",
        tagColorCode: "#7401F8",
        showInsights: "False",
        height: "",
        host_url: "",
        embedded_code_version: "",
        site_root: "",
        external_name: "",
        tabs: "No",
        toolbar: "No",
        showAppBanner: "False",
    },

    {
        id: "17",
        type: "Internal",
        component_subtitle: "Population projection estimates projections and scenarios for the Emirate of Abu Dhabi based on select socio-economic drivers.",
        component_title: "What If Analysis: Abu Dhabi Population",
        domains: [
            "Labour Force",
        ],
        application_url: "",
        imgSrc: "/sites/default/files/uploads/images/dashboard_colored.svg",
        attachment: "",
        policy_guide: "",
        note: "<p>See the attached document for detailed information around projection model components, assumptions, and additional technical details. Sources: SCAD Official &amp; External Data</p>",
        search_tags: [
            "Constant Prices",
        ],
        narrative: "<p>This Analytical App leverages regression analysis to estimate the 5-year forecast for the population of Abu Dhabi and Al Ain. The total population of Abu Dhabi is composed of a large Non-Citizen or expatriate population and a smaller local population of Emirati Citizens. Change in the Citizen population reflects the fertility of rate, as more births result in a higher population, while an influx of expatriates, driven by more economic factors contributes to the Non-Citizen Population.</p>",
        Indicator: " PopulationProjection_HomeDomainpage_Results_Feb2021 ",
        json: " {<br />\r\n  &quot;language&quot;: &quot;EN&quot;,<br />\r\n  &quot;indicatorTools&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-png&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;label&quot;: &quot;Export PNG&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-csv&quot;,<br />\r\n      &quot;label&quot;: &quot;Export CSV&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;download-pdf&quot;,<br />\r\n      &quot;label&quot;: &quot;Download PDF&quot;<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorFilters&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;period-filter&quot;,<br />\r\n      &quot;options&quot;: [<br />\r\n        {<br />\r\n          &quot;id&quot;: &quot;5-years&quot;,<br />\r\n          &quot;label&quot;: &quot;5 YEARS&quot;,<br />\r\n          &quot;value&quot;: 5,<br />\r\n          &quot;unit&quot;: &quot;years&quot;,<br />\r\n          &quot;isSelected&quot;: true<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;id&quot;: &quot;10-years&quot;,<br />\r\n          &quot;label&quot;: &quot;10 YEARS&quot;,<br />\r\n          &quot;value&quot;: 10,<br />\r\n          &quot;unit&quot;: &quot;years&quot;<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;id&quot;: &quot;All&quot;,<br />\r\n          &quot;label&quot;: &quot;ALL&quot;,<br />\r\n          &quot;value&quot;: null,<br />\r\n          &quot;unit&quot;: null<br />\r\n        }<br />\r\n      ]<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorDrivers&quot;: [<br />\r\n    {<br />\r\n      &quot;title&quot;: &quot;SCAD Projection&quot;,<br />\r\n      &quot;id&quot;: &quot;scad-p0000&quot;,<br />\r\n      &quot;type&quot;: &quot;radio&quot;,<br />\r\n      &quot;viewOnly&quot;: true,<br />\r\n      &quot;disabled&quot;: false,<br />\r\n      &quot;subtitle&quot;: &quot;Official estimate&quot;,<br />\r\n      &quot;note&quot;: null,<br />\r\n      &quot;options&quot;: [<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;OFF&quot;,<br />\r\n          &quot;value&quot;: [<br />\r\n            {<br />\r\n              &quot;title&quot;: &quot;Inflation Rate&quot;,<br />\r\n              &quot;id&quot;: &quot;parameter_1_range&quot;,<br />\r\n              &quot;type&quot;: &quot;radio&quot;,<br />\r\n              &quot;disabled&quot;: true,<br />\r\n              &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n              &quot;note&quot;: &quot;null&quot;,<br />\r\n              &quot;options&quot;: [<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-5%&quot;,<br />\r\n                  &quot;value&quot;: &quot;low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;0%&quot;,<br />\r\n                  &quot;value&quot;: &quot;medium&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+5%&quot;,<br />\r\n                  &quot;value&quot;: &quot;high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                }<br />\r\n              ]<br />\r\n            },<br />\r\n            {<br />\r\n              &quot;title&quot;: &quot;Crude Oil Production&quot;,<br />\r\n              &quot;type&quot;: &quot;radio&quot;,<br />\r\n              &quot;disabled&quot;: true,<br />\r\n              &quot;id&quot;: &quot;parameter_2_range&quot;,<br />\r\n              &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n              &quot;options&quot;: [<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;0%&quot;,<br />\r\n                  &quot;value&quot;: &quot;medium&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                }<br />\r\n              ]<br />\r\n            },<br />\r\n            {<br />\r\n              &quot;title&quot;: &quot;UK Industrial Production&quot;,<br />\r\n              &quot;type&quot;: &quot;radio&quot;,<br />\r\n              &quot;disabled&quot;: true,<br />\r\n              &quot;id&quot;: &quot;parameter_4_range&quot;,<br />\r\n              &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n              &quot;options&quot;: [<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;0%&quot;,<br />\r\n                  &quot;value&quot;: &quot;medium&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                }<br />\r\n              ]<br />\r\n            }<br />\r\n          ],<br />\r\n          &quot;isSelected&quot;: true<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;ON&quot;,<br />\r\n          &quot;value&quot;: [<br />\r\n            {<br />\r\n              &quot;title&quot;: &quot;Inflation Rate&quot;,<br />\r\n              &quot;id&quot;: &quot;parameter_1_range&quot;,<br />\r\n              &quot;type&quot;: &quot;radio&quot;,<br />\r\n              &quot;disabled&quot;: false,<br />\r\n              &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n              &quot;note&quot;: &quot;null&quot;,<br />\r\n              &quot;options&quot;: [<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-5%&quot;,<br />\r\n                  &quot;value&quot;: &quot;low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;0%&quot;,<br />\r\n                  &quot;value&quot;: &quot;medium&quot;,<br />\r\n                  &quot;isSelected&quot;: true<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+5%&quot;,<br />\r\n                  &quot;value&quot;: &quot;high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                }<br />\r\n              ]<br />\r\n            },<br />\r\n            {<br />\r\n              &quot;title&quot;: &quot;Crude Oil Production&quot;,<br />\r\n              &quot;type&quot;: &quot;radio&quot;,<br />\r\n              &quot;disabled&quot;: false,<br />\r\n              &quot;id&quot;: &quot;parameter_2_range&quot;,<br />\r\n              &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n              &quot;options&quot;: [<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;0%&quot;,<br />\r\n                  &quot;value&quot;: &quot;medium&quot;,<br />\r\n                  &quot;isSelected&quot;: true<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                }<br />\r\n              ]<br />\r\n            },<br />\r\n            {<br />\r\n              &quot;title&quot;: &quot;UK Industrial Production&quot;,<br />\r\n              &quot;type&quot;: &quot;radio&quot;,<br />\r\n              &quot;disabled&quot;: false,<br />\r\n              &quot;id&quot;: &quot;parameter_4_range&quot;,<br />\r\n              &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n              &quot;options&quot;: [<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;-10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;low&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;0%&quot;,<br />\r\n                  &quot;value&quot;: &quot;medium&quot;,<br />\r\n                  &quot;isSelected&quot;: true<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+10%&quot;,<br />\r\n                  &quot;value&quot;: &quot;high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                },<br />\r\n                {<br />\r\n                  &quot;label&quot;: &quot;+20%&quot;,<br />\r\n                  &quot;value&quot;: &quot;very high&quot;,<br />\r\n                  &quot;isSelected&quot;: false<br />\r\n                }<br />\r\n              ]<br />\r\n            }<br />\r\n          ],<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        }<br />\r\n      ]<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;title&quot;: &quot;Inflation Rate&quot;,<br />\r\n      &quot;id&quot;: &quot;parameter_1_range&quot;,<br />\r\n      &quot;type&quot;: &quot;radio&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n      &quot;note&quot;: &quot;null&quot;,<br />\r\n      &quot;options&quot;: [<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;-10%&quot;,<br />\r\n          &quot;value&quot;: &quot;very low&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;-5%&quot;,<br />\r\n          &quot;value&quot;: &quot;low&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;0%&quot;,<br />\r\n          &quot;value&quot;: &quot;medium&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;+5%&quot;,<br />\r\n          &quot;value&quot;: &quot;high&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;+10%&quot;,<br />\r\n          &quot;value&quot;: &quot;very high&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        }<br />\r\n      ]<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;title&quot;: &quot;Crude Oil Production&quot;,<br />\r\n      &quot;type&quot;: &quot;radio&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;id&quot;: &quot;parameter_2_range&quot;,<br />\r\n      &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n      &quot;options&quot;: [<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;-20%&quot;,<br />\r\n          &quot;value&quot;: &quot;very low&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;-10%&quot;,<br />\r\n          &quot;value&quot;: &quot;low&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;0%&quot;,<br />\r\n          &quot;value&quot;: &quot;medium&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;+10%&quot;,<br />\r\n          &quot;value&quot;: &quot;high&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;+20%&quot;,<br />\r\n          &quot;value&quot;: &quot;very high&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        }<br />\r\n      ]<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;title&quot;: &quot;UK Industrial Production&quot;,<br />\r\n      &quot;type&quot;: &quot;radio&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;id&quot;: &quot;parameter_4_range&quot;,<br />\r\n      &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n      &quot;options&quot;: [<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;-20%&quot;,<br />\r\n          &quot;value&quot;: &quot;very low&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;-10%&quot;,<br />\r\n          &quot;value&quot;: &quot;low&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;0%&quot;,<br />\r\n          &quot;value&quot;: &quot;medium&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;+10%&quot;,<br />\r\n          &quot;value&quot;: &quot;high&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;+20%&quot;,<br />\r\n          &quot;value&quot;: &quot;very high&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        }<br />\r\n      ]<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorValues&quot;: {<br />\r\n    &quot;overviewValueMeta&quot;: [<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;latest-date-value&quot;,<br />\r\n        &quot;title&quot;: &quot;Total Population Projection in {dateStart}&quot;,<br />\r\n        &quot;type&quot;: &quot;static-with-title-template&quot;,<br />\r\n        &quot;valueFormat&quot;: &quot;number_1.0-0&quot;,<br />\r\n        &quot;templateFormat&quot;: &quot;date_MMM y&quot;,<br />\r\n        &quot;isScadProjection&quot;: &quot;true&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_PP_PROJECTION_V2&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DIM_PP_PARAM_COMBO&quot;,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;NATIONALITY_TYPE&quot;: &quot;TOTAL-POPULATION&quot;,<br />\r\n          &quot;TYPE&quot;: &quot;PROJECTION&quot;<br />\r\n        },<br />\r\n        &quot;dateFormat&quot;: &quot;YYYY-MM-DD&quot;<br />\r\n      },<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;percentage-change&quot;,<br />\r\n        &quot;title&quot;: &quot;Y/Y&quot;,<br />\r\n        &quot;type&quot;: &quot;dynamic-with-title-template&quot;,<br />\r\n        &quot;valueFormat&quot;: &quot;percentage_1.1-1&quot;,<br />\r\n        &quot;color&quot;: &quot;#3667ff&quot;,<br />\r\n        &quot;templateFormat&quot;: &quot;dateQuarterYYYY&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_PP_PROJECTION_V2&quot;,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;NATIONALITY_TYPE&quot;: &quot;TOTAL-POPULATION&quot;,<br />\r\n          &quot;TYPE&quot;: &quot;ESTIMATION&quot;<br />\r\n        },<br />\r\n        &quot;unit&quot;: &quot;years&quot;,<br />\r\n        &quot;period&quot;: 1<br />\r\n      }<br />\r\n    ],<br />\r\n    &quot;valuesMeta&quot;: [<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;latest-date-value&quot;,<br />\r\n        &quot;title&quot;: &quot;Total Population Projection in {dateStart}&quot;,<br />\r\n        &quot;type&quot;: &quot;static-with-title-template&quot;,<br />\r\n        &quot;valueFormat&quot;: &quot;number_1.0-0&quot;,<br />\r\n        &quot;templateFormat&quot;: &quot;date_MMM y&quot;,<br />\r\n        &quot;isScadProjection&quot;: &quot;true&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_PP_PROJECTION_V2&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DIM_PP_PARAM_COMBO&quot;,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;NATIONALITY_TYPE&quot;: &quot;TOTAL-POPULATION&quot;,<br />\r\n          &quot;TYPE&quot;: &quot;PROJECTION&quot;<br />\r\n        },<br />\r\n        &quot;dateFormat&quot;: &quot;YYYY-MM-DD&quot;<br />\r\n      },<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;scad-estimation&quot;,<br />\r\n        &quot;title&quot;: &quot;Change from SCAD projection&quot;,<br />\r\n        &quot;type&quot;: &quot;dynamic&quot;,<br />\r\n        &quot;valueFormat&quot;: &quot;percentage_1.1-1&quot;,<br />\r\n        &quot;isScadProjection&quot;: &quot;true&quot;,<br />\r\n        &quot;PARAMETER_COMBO_ID&quot;: &quot;P0000&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DIM_PP_PARAM_COMBO&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_PP_PROJECTION_V2&quot;,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;NATIONALITY_TYPE&quot;: &quot;TOTAL-POPULATION&quot;,<br />\r\n          &quot;TYPE&quot;: &quot;PROJECTION&quot;<br />\r\n        }<br />\r\n      }<br />\r\n    ]<br />\r\n  },<br />\r\n  &quot;indicatorVisualizations&quot;: {<br />\r\n    &quot;visualizationsMeta&quot;: [<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;line-chart-population-indicator&quot;,<br />\r\n        &quot;type&quot;: &quot;line-chart&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_PP_PROJECTION_V2&quot;,<br />\r\n        &quot;isScadProjection&quot;: &quot;true&quot;,<br />\r\n        &quot;scadComboId&quot;: &quot;P0000&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DIM_PP_PARAM_COMBO&quot;,<br />\r\n        &quot;seriesMeta&quot;: [<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;citizen&quot;,<br />\r\n            &quot;label&quot;: &quot;Citizen&quot;,<br />\r\n            &quot;color&quot;: &quot;#26A6BF&quot;,<br />\r\n            &quot;type&quot;: &quot;solid&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;NATIONALITY_TYPE&quot;: &quot;CITIZEN&quot;,<br />\r\n              &quot;TYPE&quot;: &quot;ESTIMATION&quot;<br />\r\n            },<br />\r\n            &quot;xAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;OBS_DT&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            },<br />\r\n            &quot;yAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;non-citizen&quot;,<br />\r\n            &quot;label&quot;: &quot;Non-citizen&quot;,<br />\r\n            &quot;color&quot;: &quot;#983EFF&quot;,<br />\r\n            &quot;type&quot;: &quot;solid&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;NATIONALITY_TYPE&quot;: &quot;NON-CITIZEN&quot;,<br />\r\n              &quot;TYPE&quot;: &quot;ESTIMATION&quot;<br />\r\n            },<br />\r\n            &quot;xAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;OBS_DT&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            },<br />\r\n            &quot;yAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;total-population&quot;,<br />\r\n            &quot;label&quot;: &quot;Total population&quot;,<br />\r\n            &quot;color&quot;: &quot;#3267FF&quot;,<br />\r\n            &quot;type&quot;: &quot;solid&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;NATIONALITY_TYPE&quot;: &quot;TOTAL-POPULATION&quot;,<br />\r\n              &quot;TYPE&quot;: &quot;ESTIMATION&quot;<br />\r\n            },<br />\r\n            &quot;xAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;OBS_DT&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            },<br />\r\n            &quot;yAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;citizen-forecast&quot;,<br />\r\n            &quot;label&quot;: &quot;Citizen&quot;,<br />\r\n            &quot;color&quot;: &quot;#26A6BF&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;NATIONALITY_TYPE&quot;: &quot;CITIZEN&quot;,<br />\r\n              &quot;TYPE&quot;: &quot;PROJECTION&quot;<br />\r\n            },<br />\r\n            &quot;xAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;OBS_DT&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            },<br />\r\n            &quot;yAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;non-citizen-forecast&quot;,<br />\r\n            &quot;label&quot;: &quot;Non-citizen&quot;,<br />\r\n            &quot;color&quot;: &quot;#983EFF&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;NATIONALITY_TYPE&quot;: &quot;NON-CITIZEN&quot;,<br />\r\n              &quot;TYPE&quot;: &quot;PROJECTION&quot;<br />\r\n            },<br />\r\n            &quot;xAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;OBS_DT&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            },<br />\r\n            &quot;yAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;total-population-forecast&quot;,<br />\r\n            &quot;label&quot;: &quot;Total population&quot;,<br />\r\n            &quot;color&quot;: &quot;#3267FF&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;NATIONALITY_TYPE&quot;: &quot;TOTAL-POPULATION&quot;,<br />\r\n              &quot;TYPE&quot;: &quot;PROJECTION&quot;<br />\r\n            },<br />\r\n            &quot;xAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;OBS_DT&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            },<br />\r\n            &quot;yAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE&quot;<br />\r\n            }<br />\r\n          }<br />\r\n        ],<br />\r\n        &quot;markersMeta&quot;: [<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;population-projection_real-vs-forecast&quot;,<br />\r\n            &quot;color&quot;: &quot;#ffffff&quot;,<br />\r\n            &quot;type&quot;: &quot;line-with-label&quot;,<br />\r\n            &quot;labelText&quot;: &quot;Forecast&quot;,<br />\r\n            &quot;axis&quot;: &quot;x&quot;,<br />\r\n            &quot;accessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;date&quot;,<br />\r\n              &quot;path&quot;: &quot;DATE&quot;,<br />\r\n              &quot;specifier&quot;: &quot;%Y-%m-%d&quot;<br />\r\n            }<br />\r\n          }<br />\r\n        ],<br />\r\n        &quot;xAxisLabel&quot;: null,<br />\r\n        &quot;yAxisLabel&quot;: &quot;Population by million&quot;,<br />\r\n        &quot;showLegend&quot;: true,<br />\r\n        &quot;yAxisExtraStepMin&quot;: 0.2,<br />\r\n        &quot;yAxisExtraStepMax&quot;: 0.05,<br />\r\n        &quot;xAxisFormat&quot;: &quot;date_y&quot;,<br />\r\n        &quot;yAxisFormat&quot;: &quot;d3-number&quot;,<br />\r\n        &quot;tooltipTitleFormat&quot;: &quot;date_MMM y&quot;,<br />\r\n        &quot;tooltipValueFormat&quot;: &quot;number_1.0-0&quot;<br />\r\n      }<br />\r\n    ],<br />\r\n    &quot;visualizationDefault&quot;: &quot;line-chart-population-indicator&quot;<br />\r\n  }<br />\r\n} ",
        indicator_list: "",
        indicatorValues_subtitle: "",
        indicatorValues_title: "",
        visualization_subtitle: "",
        visualization_title: "",
        enableDynamicPanel: "False",
        listofDyanmicPanelContent: [
        ],
        highlightsMeta: "",
        infogramUrl: "",
        confidenceIntervalMeta: "",
        enableConfidenceInterval: "False",
        endpoint_label: "",
        endpoint_title: "",
        endpoint_url: "",
        default_layer: "",
        show_on_legend: "",
        defaultDistrictId: "",
        endpointType: "",
        nodeId: "",
        summaryCardId: "",
        endpoint_icon_id: "",
        cardDate: "",
        dashboardUrl: "",
        enablePointToggle: "False",
        maxPointLimit: "",
        minLimitYAxis: "",
        publication_date: "2023-05-10",
        tagName: "",
        tagColorCode: "",
        showInsights: "True",
        height: "",
        host_url: "",
        embedded_code_version: "",
        site_root: "",
        external_name: "",
        tabs: "No",
        toolbar: "No",
        showAppBanner: "False",
    },
    {
        id: "409",
        type: "Insights-Discovery",
        component_subtitle: "The analytical app is based on IHS Markit data for Abu Dhabi’s Economy adjusted PMI index. The tool is intended as an insight discovery app to analyze the market conditions and economic trends in the manufacturing and service sectors.",
        component_title: "Abu Dhabi Purchasing Managers Index",
        domains: [
            "Economy",
        ],
        application_url: "",
        imgSrc: "/sites/default/files/uploads/images/dashboard_colored.svg",
        attachment: "",
        policy_guide: "",
        note: "",
        search_tags: [
        ],
        narrative: "",
        Indicator: " Spending Trend Analysis - Insights Discovery ",
        json: " {<br />\r\n  &quot;language&quot;: &quot;EN&quot;,<br />\r\n  &quot;type&quot;: &quot;insights-discovery&quot;,<br />\r\n  &quot;sortVisualizations&quot;: true,<br />\r\n  &quot;indicatorTools&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-png&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;label&quot;: &quot;Export PNG&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-csv-all&quot;,<br />\r\n      &quot;label&quot;: &quot;Extract All&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-csv&quot;,<br />\r\n      &quot;label&quot;: &quot;Extract Current Selection&quot;<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorActions&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;toggle-fullscreen&quot;,<br />\r\n      &quot;type&quot;: &quot;simple&quot;,<br />\r\n      &quot;label&quot;: &quot;Toggle Fullscreen&quot;<br />\r\n    }<br />\r\n  ]<br />\r\n} ",
        indicator_list: "398, 416, 417, 1509",
        indicatorValues_subtitle: "",
        indicatorValues_title: "",
        visualization_subtitle: "",
        visualization_title: "PMI INSIGHTS DISCOVERY",
        enableDynamicPanel: "True",
        listofDyanmicPanelContent: [
            "Highlights",
        ],
        highlightsMeta: "{\n  \"indicatorValues\": {\n    \"selectAggregation\": {\n      \"label\": \"Time Period\",\n      \"options\": [\n        \"Year on Year\",\n        \"Month on Month\"\n      ],\n      \"default\": \"Month on Month\"\n    },\n    \"valuesMeta\": [\n      {\n        \"id\": \"year-to-date\",\n        \"title\": \"\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"additionalText\": \"Additional text\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_AD_PMI_MOM\",\n            \"COI_AD_PMI_MOM_CH\",\n            \"COI_AD_PMI_YOY\",\n            \"COI_AD_PMI_YOY_CH\"\n          ]\n        },\n        \"dateFormat\": \"MMM YYYY\"\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"Abu Dhabi PMI Index\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"iconId\": \"Total-Spend\",\n        \"iconLightId\": \"Total-Spend_LIGHT\",\n        \"note\": \"PMI Index for Abu Dhabi Average Value for All industries\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_AD_PMI_MOM\",\n            \"COI_AD_PMI_MOM_CH\",\n            \"COI_AD_PMI_YOY\",\n            \"COI_AD_PMI_YOY_CH\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"UAE PMI Index\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"iconId\": \"transactions\",\n        \"iconLightId\": \"transactions_LIGHT\",\n        \"note\": \"PMI Index for UAE Average Value for All industries\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_UAE_PMI_MOM\",\n            \"COI_UAE_PMI_MOM_CH\",\n            \"COI_UAE_PMI_YOY\",\n            \"COI_UAE_PMI_YOY_CH\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"{dimension} (Top Sub-Indicator)\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"iconId\": \"NUM_LISTING\",\n        \"iconLightId\": \"NUM_LISTING_LIGHT\",\n        \"note\": \"Top Sub-Indicator Value for Average Value over Industries in Abu Dhabi\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_AD_TOP_SUBIND_MOM\",\n            \"COI_AD_TOP_SUBIND_MOM_CH\",\n            \"COI_AD_TOP_SUBIND_YOY\",\n            \"COI_AD_TOP_SUBIND_YOY_CH\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"{dimension} (Lowest Sub-Indicator)\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"iconId\": \"Average-Ticket-Size\",\n        \"iconLightId\": \"Average-Ticket-Size_LIGHT\",\n        \"note\": \"Lowest Sub-Indicator Value for Average Value over Industries in Abu Dhabi\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_AD_LOW_SUBIND_MOM\",\n            \"COI_AD_LOW_SUBIND_MOM_CH\",\n            \"COI_AD_LOW_SUBIND_YOY\",\n            \"COI_AD_LOW_SUBIND_YOY_CH\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"{dimension} (Top Industry)\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"iconId\": \"APARTMENT\",\n        \"iconLightId\": \"APARTMENT_LIGHT\",\n        \"note\": \"Top Industry Value for PMI Index in Abu Dhabi\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_AD_TOP_INDUSTRY_MOM\",\n            \"COI_AD_TOP_INDUSTRY_MOM_CH\",\n            \"COI_AD_TOP_INDUSTRY_YOY\",\n            \"COI_AD_TOP_INDUSTRY_YOY_CH\"\n          ]\n        }\n      },\n      {\n        \"id\": \"year-to-date-value\",\n        \"title\": \"{dimension} (Lowest Industry)\",\n        \"viewName\": \"VW_PMI_TRENDS\",\n        \"iconId\": \"COMMERCIAL\",\n        \"iconLightId\": \"COMMERCIAL_LIGHT\",\n        \"note\": \"Lowest Industry Value for PMI Index in Abu Dhabi\",\n        \"dimension\": {\n          \"INDICATOR_ID\": [\n            \"COI_AD_LOW_INDUSTRY_MOM\",\n            \"COI_AD_LOW_INDUSTRY_MOM_CH\",\n            \"COI_AD_LOW_INDUSTRY_YOY\",\n            \"COI_AD_LOW_INDUSTRY_YOY_CH\"\n          ]\n        }\n      }\n    ]\n  }\n}",
        infogramUrl: "",
        confidenceIntervalMeta: "",
        enableConfidenceInterval: "False",
        endpoint_label: "",
        endpoint_title: "",
        endpoint_url: "",
        default_layer: "",
        show_on_legend: "",
        defaultDistrictId: "",
        endpointType: "",
        nodeId: "",
        summaryCardId: "",
        endpoint_icon_id: "",
        cardDate: "",
        dashboardUrl: "",
        enablePointToggle: "False",
        maxPointLimit: "",
        minLimitYAxis: "",
        publication_date: "2023-05-10",
        tagName: "Insights Discovery",
        tagColorCode: "#7401F8",
        showInsights: "False",
        height: "",
        host_url: "",
        embedded_code_version: "",
        site_root: "",
        external_name: "",
        tabs: "No",
        toolbar: "No",
        showAppBanner: "False",
    },

    {
        id: "617",
        type: "Internal",
        component_subtitle: "Index based on 2014 as a reference year (2014=100)",
        component_title: "Economic Activity Index Forecasting Model by Sector (Constant GDP)",
        domains: [
            "Economy",
        ],
        application_url: "",
        imgSrc: "",
        attachment: "",
        policy_guide: "",
        note: "Data science methodology",
        search_tags: [
        ],
        narrative: "",
        Indicator: " FORECAST CONSTANT CONF ",
        json: " {<br />\r\n  &quot;language&quot;: &quot;EN&quot;,<br />\r\n  &quot;sortVisualizations&quot;: true,<br />\r\n  &quot;indicatorTools&quot;: [<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-png&quot;,<br />\r\n      &quot;disabled&quot;: true,<br />\r\n      &quot;label&quot;: &quot;Export PNG&quot;<br />\r\n    },<br />\r\n    {<br />\r\n      &quot;id&quot;: &quot;export-csv&quot;,<br />\r\n      &quot;label&quot;: &quot;Export CSV&quot;<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorActions&quot;: [<br />\r\n    <br />\r\n  ],<br />\r\n  &quot;driverTitle&quot;: &quot;Time Selector&quot;,<br />\r\n  &quot;driverSubtitle&quot;: &quot;Modify below to view changes in the projection&quot;,<br />\r\n  &quot;indicatorDrivers&quot;: [<br />\r\n    {<br />\r\n      &quot;title&quot;: &quot;Forecast&quot;,<br />\r\n      &quot;id&quot;: &quot;parameter_1_range&quot;,<br />\r\n      &quot;type&quot;: &quot;radio&quot;,<br />\r\n      &quot;subtitle&quot;: &quot;Range&quot;,<br />\r\n      &quot;note&quot;: &quot;1 quarter forecast&quot;,<br />\r\n      &quot;options&quot;: [<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;1 Quarter&quot;,<br />\r\n          &quot;value&quot;: &quot;1q&quot;,<br />\r\n          &quot;isSelected&quot;: true<br />\r\n        },<br />\r\n        {<br />\r\n          &quot;label&quot;: &quot;2 Quarter&quot;,<br />\r\n          &quot;value&quot;: &quot;2q&quot;,<br />\r\n          &quot;isSelected&quot;: false<br />\r\n        }<br />\r\n      ]<br />\r\n    }<br />\r\n  ],<br />\r\n  &quot;indicatorValues&quot;: {<br />\r\n    &quot;valuesMeta&quot;: [<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;latest-date&quot;,<br />\r\n        &quot;title&quot;: &quot;Forecast Period&quot;,<br />\r\n        &quot;type&quot;: &quot;static-with-title-template&quot;,<br />\r\n        &quot;valueFormat&quot;: &quot;dateQuarterYYYY&quot;,<br />\r\n        &quot;viewName&quot;: &quot;VW_RI_GDP_SECTOR_CNST_IND_PCT&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DS_GDP_SECTORS_COMBO_CNST&quot;,<br />\r\n        &quot;hasDefault&quot;: true,<br />\r\n        &quot;dimension&quot;: {<br />\r\n          &quot;SECTOR&quot;: &quot;MANUFACTURING&quot;<br />\r\n        },<br />\r\n        &quot;dateFormat&quot;: &quot;YYYY-MM-DD&quot;<br />\r\n      }<br />\r\n    ]<br />\r\n  },<br />\r\n  &quot;indicatorVisualizations&quot;: {<br />\r\n    &quot;visualizationsMeta&quot;: [<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;tree-chart-economy-sector-indicator&quot;,<br />\r\n        &quot;type&quot;: &quot;tree-map-with-change-chart&quot;,<br />\r\n        &quot;sortOrder&quot;: 1,<br />\r\n        &quot;viewName&quot;: &quot;VW_RI_GDP_SECTOR_CNST_IND_PCT&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DS_GDP_SECTORS_COMBO_CNST&quot;,<br />\r\n        &quot;xAxisLabel&quot;: &quot;Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.&quot;,<br />\r\n        &quot;hasDefault&quot;: true,<br />\r\n        &quot;tooltips&quot;: [<br />\r\n          {<br />\r\n            &quot;type&quot;: &quot;text&quot;,<br />\r\n            &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;type&quot;: &quot;value&quot;,<br />\r\n            &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n            &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;type&quot;: &quot;value&quot;,<br />\r\n            &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n            &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;type&quot;: &quot;percentage&quot;,<br />\r\n            &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n            &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;type&quot;: &quot;percentage&quot;,<br />\r\n            &quot;label&quot;: &quot;Y/Y Change&quot;,<br />\r\n            &quot;path&quot;: &quot;CHANGE_PY&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;type&quot;: &quot;value&quot;,<br />\r\n            &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n            &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n          }<br />\r\n        ],<br />\r\n        &quot;colorCodes&quot;: {<br />\r\n          &quot;positive&quot;: {<br />\r\n            &quot;1_5&quot;: &quot;#19AA2B&quot;,<br />\r\n            &quot;5_10&quot;: &quot;#0e931f&quot;,<br />\r\n            &quot;10_20&quot;: &quot;#007506&quot;,<br />\r\n            &quot;20_60&quot;: &quot;#005700&quot;,<br />\r\n            &quot;60+&quot;: &quot;#003b00&quot;<br />\r\n          },<br />\r\n          &quot;negetive&quot;: {<br />\r\n            &quot;1_5&quot;: &quot;#e54d35&quot;,<br />\r\n            &quot;5_10&quot;: &quot;#c83220&quot;,<br />\r\n            &quot;10_20&quot;: &quot;#ac0e0a&quot;,<br />\r\n            &quot;20_60&quot;: &quot;#870101&quot;,<br />\r\n            &quot;60+&quot;: &quot;#610000&quot;<br />\r\n          },<br />\r\n          &quot;neutral&quot;: &quot;#666878&quot;<br />\r\n        },<br />\r\n        &quot;selectRangeBy&quot;: &quot;CHANGE&quot;,<br />\r\n        &quot;colorsRange&quot;: [<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;1_5&quot;,<br />\r\n            &quot;valueLL&quot;: 1,<br />\r\n            &quot;valueUL&quot;: 5<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;5_10&quot;,<br />\r\n            &quot;valueLL&quot;: 5,<br />\r\n            &quot;valueUL&quot;: 10<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;10_20&quot;,<br />\r\n            &quot;valueLL&quot;: 10,<br />\r\n            &quot;valueUL&quot;: 20<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;20_60&quot;,<br />\r\n            &quot;valueLL&quot;: 20,<br />\r\n            &quot;valueUL&quot;: 60<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;60+&quot;,<br />\r\n            &quot;valueLL&quot;: 60,<br />\r\n            &quot;valueUL&quot;: &quot;Infinity&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;neutral&quot;,<br />\r\n            &quot;valueLL&quot;: 0,<br />\r\n            &quot;valueUL&quot;: 1<br />\r\n          }<br />\r\n        ],<br />\r\n        &quot;seriesMeta&quot;: [<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;construction&quot;,<br />\r\n            &quot;label&quot;: &quot;Construction&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;CONSTRUCTION&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;financial-and-insurance&quot;,<br />\r\n            &quot;label&quot;: &quot;Financial and insurance&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Financial and insurance&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;manufacturing&quot;,<br />\r\n            &quot;label&quot;: &quot;Manufacturing&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Manufacturing&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;publi-administration-and-defence-compulsory-social-security&quot;,<br />\r\n            &quot;label&quot;: &quot;Public admin &amp; Defence; Compulsory Social Sec.&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Public admin &amp; Defence; Compulsory Social Sec.&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles&quot;,<br />\r\n            &quot;label&quot;: &quot;Wholesale &amp; Retail Trade; Repair of vehicles &amp; Motorcycles&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Wholesale &amp; Retail Trade; Repair of vehicles &amp; Motorcycles&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;information-and-communication&quot;,<br />\r\n            &quot;label&quot;: &quot;Information and communication&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;INFORMATION AND COMMUNICATION&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;electricity-gas-and-water-supply-waste-management&quot;,<br />\r\n            &quot;label&quot;: &quot;Electricity, Gas, &amp; Water supply; Waste Mgmt&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Electricity, Gas, &amp; Water supply; Waste Mgmt&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;professional-scientific-and-technical-administrative-and-support-services&quot;,<br />\r\n            &quot;label&quot;: &quot;Professional, scientific and technical&amp; Administrative and support services&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Professional, scientific and technical&amp; Administrative and support services&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;real-estate&quot;,<br />\r\n            &quot;label&quot;: &quot;Real estate&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Real estate&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          }<br />\r\n        ]<br />\r\n      },<br />\r\n      {<br />\r\n        &quot;id&quot;: &quot;tree-chart-economy-sector-indicator-2&quot;,<br />\r\n        &quot;type&quot;: &quot;tree-map-with-change-chart&quot;,<br />\r\n        &quot;sortOrder&quot;: 2,<br />\r\n        &quot;viewName&quot;: &quot;VW_RI_GDP_SECTOR_CNST_IND_PCT&quot;,<br />\r\n        &quot;comboIdTable&quot;: &quot;DS_GDP_SECTORS_COMBO_CNST&quot;,<br />\r\n        &quot;xAxisLabel&quot;: &quot;Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.&quot;,<br />\r\n        &quot;hasDefault&quot;: true,<br />\r\n        &quot;colorCodes&quot;: {<br />\r\n          &quot;positive&quot;: {<br />\r\n            &quot;1_5&quot;: &quot;#19AA2B&quot;,<br />\r\n            &quot;5_10&quot;: &quot;#0e931f&quot;,<br />\r\n            &quot;10_20&quot;: &quot;#007506&quot;,<br />\r\n            &quot;20_60&quot;: &quot;#005700&quot;,<br />\r\n            &quot;60+&quot;: &quot;#003b00&quot;<br />\r\n          },<br />\r\n          &quot;negetive&quot;: {<br />\r\n            &quot;1_5&quot;: &quot;#e54d35&quot;,<br />\r\n            &quot;5_10&quot;: &quot;#c83220&quot;,<br />\r\n            &quot;10_20&quot;: &quot;#ac0e0a&quot;,<br />\r\n            &quot;20_60&quot;: &quot;#870101&quot;,<br />\r\n            &quot;60+&quot;: &quot;#610000&quot;<br />\r\n          },<br />\r\n          &quot;neutral&quot;: &quot;#666878&quot;<br />\r\n        },<br />\r\n        &quot;selectRangeBy&quot;: &quot;CHANGE&quot;,<br />\r\n        &quot;colorsRange&quot;: [<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;1_5&quot;,<br />\r\n            &quot;valueLL&quot;: 1,<br />\r\n            &quot;valueUL&quot;: 5<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;5_10&quot;,<br />\r\n            &quot;valueLL&quot;: 5,<br />\r\n            &quot;valueUL&quot;: 10<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;10_20&quot;,<br />\r\n            &quot;valueLL&quot;: 10,<br />\r\n            &quot;valueUL&quot;: 20<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;20_60&quot;,<br />\r\n            &quot;valueLL&quot;: 20,<br />\r\n            &quot;valueUL&quot;: 60<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;60+&quot;,<br />\r\n            &quot;valueLL&quot;: 60,<br />\r\n            &quot;valueUL&quot;: &quot;Infinity&quot;<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;range&quot;: &quot;neutral&quot;,<br />\r\n            &quot;valueLL&quot;: 0,<br />\r\n            &quot;valueUL&quot;: 1<br />\r\n          }<br />\r\n        ],<br />\r\n        &quot;seriesMeta&quot;: [<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;construction&quot;,<br />\r\n            &quot;label&quot;: &quot;Construction&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;CONSTRUCTION&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;financial-and-insurance&quot;,<br />\r\n            &quot;label&quot;: &quot;Financial and insurance&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Financial and insurance&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;manufacturing&quot;,<br />\r\n            &quot;label&quot;: &quot;Manufacturing&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Manufacturing&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;publi-administration-and-defence-compulsory-social-security&quot;,<br />\r\n            &quot;label&quot;: &quot;Public admin &amp; Defence; Compulsory Social Sec.&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Public admin &amp; Defence; Compulsory Social Sec.&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;wholesale-and-retail-trade-repair-of-motor-vehicles-and-motorcycles&quot;,<br />\r\n            &quot;label&quot;: &quot;Wholesale &amp; Retail Trade; Repair of vehicles &amp; Motorcycles&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Wholesale &amp; Retail Trade; Repair of vehicles &amp; Motorcycles&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;information-and-communication&quot;,<br />\r\n            &quot;label&quot;: &quot;Information and communication&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;INFORMATION AND COMMUNICATION&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;electricity-gas-and-water-supply-waste-management&quot;,<br />\r\n            &quot;label&quot;: &quot;Electricity, Gas, &amp; Water supply; Waste Mgmt&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Electricity, Gas, &amp; Water supply; Waste Mgmt&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;professional-scientific-and-technical-administrative-and-support-services&quot;,<br />\r\n            &quot;label&quot;: &quot;Professional, scientific and technical&amp; Administrative and support services&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Professional, scientific and technical&amp; Administrative and support services&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          },<br />\r\n          {<br />\r\n            &quot;id&quot;: &quot;real-estate&quot;,<br />\r\n            &quot;label&quot;: &quot;Real estate&quot;,<br />\r\n            &quot;color&quot;: &quot;rgba(56, 101, 255, 0.3)&quot;,<br />\r\n            &quot;type&quot;: &quot;forecast&quot;,<br />\r\n            &quot;dimension&quot;: {<br />\r\n              &quot;SECTOR&quot;: &quot;Real estate&quot;<br />\r\n            },<br />\r\n            &quot;valueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;changeAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Q/Q Change&quot;,<br />\r\n              &quot;path&quot;: &quot;CHANGE&quot;<br />\r\n            },<br />\r\n            &quot;sectorNameAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;path&quot;: &quot;SECTOR&quot;<br />\r\n            },<br />\r\n            &quot;proportionAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Proportion of Total Economy&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_PERC_ECO&quot;<br />\r\n            },<br />\r\n            &quot;currentValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Current Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_CURRENT&quot;<br />\r\n            },<br />\r\n            &quot;forecastedValueAccessor&quot;: {<br />\r\n              &quot;type&quot;: &quot;value&quot;,<br />\r\n              &quot;label&quot;: &quot;Forecasted Value&quot;,<br />\r\n              &quot;path&quot;: &quot;VALUE_FORECAST&quot;<br />\r\n            }<br />\r\n          }<br />\r\n        ]<br />\r\n      }<br />\r\n    ],<br />\r\n    &quot;visualizationDefault&quot;: &quot;tree-chart-economy-sector-indicator&quot;<br />\r\n  },<br />\r\n  &quot;country_flag&quot;: &quot;&quot;,<br />\r\n  &quot;page_icon&quot;: &quot;&quot;,<br />\r\n  &quot;page_menu_icon&quot;: &quot;&quot;,<br />\r\n  &quot;isFavorite&quot;: &quot;false&quot;<br />\r\n} ",
        indicator_list: "",
        indicatorValues_subtitle: "",
        indicatorValues_title: "",
        visualization_subtitle: "",
        visualization_title: "",
        enableDynamicPanel: "False",
        listofDyanmicPanelContent: [
        ],
        highlightsMeta: "",
        infogramUrl: "",
        confidenceIntervalMeta: "",
        enableConfidenceInterval: "False",
        endpoint_label: "",
        endpoint_title: "",
        endpoint_url: "",
        default_layer: "False",
        show_on_legend: "False",
        defaultDistrictId: "",
        endpointType: "",
        nodeId: "",
        summaryCardId: "",
        endpoint_icon_id: "",
        cardDate: "",
        dashboardUrl: "",
        enablePointToggle: "True",
        maxPointLimit: "",
        minLimitYAxis: "",
        publication_date: "2023-05-12",
        tagName: "Scenario Drivers",
        tagColorCode: "",
        showInsights: "False",
        height: "",
        host_url: "",
        embedded_code_version: "",
        site_root: "",
        external_name: "",
        tabs: "No",
        toolbar: "No",
        showAppBanner: "False",
    },

]
)
module.exports = { data }