const Logger = require('scad-library').logger;
const {filterQuery} = require('../services/helper')

const log = new Logger().getInstance();

async function getComboIdQuery(indicatorDrivers, comboIdTable) {
    try {
        let query = `SELECT PARAMETER_COMBO_ID FROM ${comboIdTable} WHERE `;
        if(indicatorDrivers){
            const indicatorCount = Object.keys(indicatorDrivers).length;
            let counter = [];
            Object.keys(indicatorDrivers).forEach((key) => {
                query = `${query} ${counter.length === 0 ? '':'AND '}LOWER(${key.toUpperCase()})='${indicatorDrivers[key]}'` 
                counter.push('1');
            });
        }
        return query;
    } catch (err) {
        log.error(`Error building getComboIdQuery: ERROR ${err}`)
        throw err;
    }
}

async function getTreeMapQuery(visualization) {
    try {
        let query = `SELECT VALUE AS SECTOR_SIZE, SECTOR, OBS_DT FROM ${visualization.sectorSizeViewName} WHERE INDICATOR_ID = '${visualization.sectorSizeIndicatorId}' AND OBS_DT = (SELECT MAX(OBS_DT) FROM ${visualization.sectorSizeViewName} WHERE INDICATOR_ID = '${visualization.sectorSizeIndicatorId}')`;
        return query;
    } catch (err) {
        log.error(`Error building getTreeMapQuery: ERROR ${err}`)
        throw err;
    }

}
async function getQuery(visualization, comboId) {
    try {
        let query = `SELECT * FROM ${visualization.viewName} WHERE (UPPER(PARAMETER_COMBO_ID) = '${comboId}'  OR PARAMETER_COMBO_ID IS NULL) `
        if (visualization.filterBy)
            query = filterQuery(visualization.filterBy, query)
        if (visualization.isScadProjection) {
            query = `${query} OR UPPER(PARAMETER_COMBO_ID) = '${visualization.scadComboId}' ORDER BY OBS_DT`
        } else {
            query = `${query} ORDER BY OBS_DT`
        }
        return query;
    } catch (err) {
        log.error(`Error building getTreeMapQuery: ERROR ${err}`)
        throw err;
    }

}

async function getValuesDataFromDBQuery(value) {
    try {
        let query = '';
        querywithWhere = `SELECT * FROM ${value.viewName} WHERE`
        if (value.dimension) {
            Object.entries(value.dimension).forEach(([column, value]) => {
                if (Array.isArray(value) && value.length > 1) {
                    let dimensionValues = [];
                    value.forEach(element => {
                        dimensionValues.push(element);
                    });
                    let dimensionValue = dimensionValues.map(i => `'${i}'`).join(',');
                    query = `${querywithWhere} UPPER(${column}) IN (${dimensionValue})`
                } else {
                    query = `${querywithWhere} UPPER(${column}) = '${value}'`
                }
            })
        } else {
            query = `SELECT * FROM ${value.viewName}`;
        }
        if (value.filterBy && Object.keys(value.filterBy).length > 0) {
            Object.entries(value.filterBy).forEach(([column, columnValue]) => {
                if (query.includes('WHERE')) {
                    query = `${query} AND UPPER(${column}) = '${columnValue}'`
                } else {
                    query = `${querywithWhere} UPPER(${column}) = '${value}'`
                }
            })
        }
        query = `${query} ORDER BY OBS_DT`;
        return query
    } catch (err) {
        throw err
    }
}

async function getAccuracyMetricsDataQuery(meta, id) {
    try {
        let query = `SELECT * FROM ${meta.viewName} WHERE ${meta.dbColumn} = '${id}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getAccuracyMetricsDataQuery with error ${err} `);
        throw err;
    }
}

async function getValuesQuery(valuesMeta,comboId) {
    try {
        let viewName = valuesMeta.viewName;
        let query = `SELECT * FROM ${viewName}`;
        if ((Object.keys(valuesMeta.dimension)).length > 0) {
            Object.entries(valuesMeta.dimension).forEach(([columnValue, value]) => {
                if(query.includes('WHERE')){
                    query = `${query} AND UPPER(${columnValue}) = '${value}'`
                } else {
                    query = `${query} WHERE UPPER(${columnValue}) = '${value}'`
                }
            });
            if(comboId && valuesMeta.unit !== 'nowcast-forecast'){
                if(query.includes('WHERE')){
                    query = `${query} AND UPPER(PARAMETER_COMBO_ID) = '${comboId}'`
                } else {
                    query = `${query} WHERE UPPER(PARAMETER_COMBO_ID) = '${comboId}'`
                } 
            }
            if(valuesMeta.unit === 'nowcast-forecast'){
                if(query.includes('WHERE')){
                    query = `${query} AND (UPPER(PARAMETER_COMBO_ID) = '${comboId}' OR UPPER(PARAMETER_COMBO_ID) is null)`
                } else {
                    query = `${query} WHERE (UPPER(PARAMETER_COMBO_ID) = '${comboId}' OR UPPER(PARAMETER_COMBO_ID) is null)`
                } 
            }

            if (valuesMeta.isScadProjection && valuesMeta.PARAMETER_COMBO_ID) {
                query = `${query} OR UPPER(PARAMETER_COMBO_ID) = '${valuesMeta.PARAMETER_COMBO_ID}'`
            }
            query = `${query} ORDER BY OBS_DT`;
        } else {
            query = `SELECT * from ${viewName} ORDER BY OBS_DT`;
        }
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getValuesQuery with error ${err} `);
        throw err
    }
}

function getFilterOptionsQuery(viewName, dimension, property) {
    const binds = { dimension: property.path };
    const query = `SELECT ${dimension.value} AS "VALUE" FROM ${viewName} WHERE ${dimension.path} = {dimension:String} ORDER BY RANK`;
    return { query, binds };
}

function getCorrelationVisualizationDataQuery(visualization,filters,filterPanel) {
    const { viewName, filterBy, seriesMeta, type } = visualization;
    const binds = {};
    let series;

    if (type === 'correlation-chart') {
        series = seriesMeta[0];
    } else {
        // For other types, you may handle seriesMeta differently
        // series = seriesMeta;

        // For now, just use the first element of seriesMeta
    }

    const { xAccessor, yAccessor, tooltipAccessor, dimension } = series;

    const selectColumns = [
        xAccessor.path,
        yAccessor.path,
        tooltipAccessor.path,
        tooltipAccessor.description
    ];

    const filteredSelectColumns = selectColumns.filter(column => column !== null && column !== undefined);
    let query = `SELECT ${filteredSelectColumns.join(', ')} FROM ${viewName}`;

    let whereConditions = [];

    if (!(filters && Object.keys(filters).length > 0) && (filterPanel && Object.keys(filterPanel).length > 0)) {
        filterPanel.properties.forEach((property) => {
            const key = property.path;
            const value = property.default
            const paramName = `filterPanel_${key}`;
            if (Array.isArray(value) && value.length > 1) {
                binds[paramName] = value.map(v => v.toString().toUpperCase());
                whereConditions.push(`upper(${key}) IN {${paramName}:Array(String)}`);
            } else {
                binds[paramName] = value.toString().toUpperCase();
                whereConditions.push(`upper(${key}) = {${paramName}:String}`);
            }
        });
    }

    if (filters && Object.keys(filters).length > 0) {
        Object.entries(filters).forEach(([key, value]) => {
            const paramName = `filters_${key}`;
            if (Array.isArray(value) && value.length > 1) {
                binds[paramName] = value.map(v => v.toString().toUpperCase());
                whereConditions.push(`upper(${key}) IN {${paramName}:Array(String)}`);
            } else {
                binds[paramName] = value.toString().toUpperCase();
                whereConditions.push(`upper(${key}) = {${paramName}:String}`);
            }
        });
    }

    if (filterBy && Object.keys(filterBy).length > 0) {
        Object.entries(filterBy).forEach(([key, value]) => {
            const paramName = `filterBy_${key}`;
            if (Array.isArray(value)) {
                binds[paramName] = value.map(v => v.toString().toUpperCase());
            } else {
                binds[paramName] = value.toString().toUpperCase();
            }
            whereConditions.push(`upper(${key}) = {${paramName}:String}`);
        });
    }

    if (dimension && Object.keys(dimension).length > 0) {
        Object.entries(dimension).forEach(([key, value]) => {
            const paramName = `dimension_${key.toLowerCase()}`;
            if (Array.isArray(value) && value.length > 1) {
                binds[paramName] = value.map(v => v.toString().toUpperCase());
                whereConditions.push(`upper(${key}) IN {${paramName}:Array(String)}`);
            } else {
                binds[paramName] = value.toString().toUpperCase();
                whereConditions.push(`upper(${key}) = {${paramName}:String}`);
            }
        });
    }

    if (whereConditions.length > 0) {
        query += ' WHERE ' + whereConditions.join(' AND ');
    }

    if (type === 'correlation-chart') {
        query += ` ORDER BY ${yAccessor.path}`;
    }
    else{
        query += ' ORDER BY OBS_DT';
    }

    return { query, binds };
}



module.exports = { getQuery, getComboIdQuery, getTreeMapQuery, getValuesDataFromDBQuery,
     getAccuracyMetricsDataQuery, getValuesQuery, getFilterOptionsQuery, getCorrelationVisualizationDataQuery }