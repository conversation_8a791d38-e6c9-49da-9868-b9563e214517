{"paths": {"/content-type/eci/auth": {"get": {"tags": ["Economic Complexity Index"], "summary": "Checks for authorization for Economic Complexity Index", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "The status message of the authorization attempt."}, "token": {"type": "string", "description": "The authorization token provided upon successful authorization."}, "status": {"type": "boolean", "description": "The boolean status indicating the success of the authorization attempt."}}, "required": ["message", "token", "status"]}}}}}}}, "/content-type/basket/auth": {"get": {"tags": ["Basket of Goods"], "summary": "Checks for authorization for Basket of Goods", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "The status message of the authorization attempt."}, "status": {"type": "boolean", "description": "The boolean status indicating the success of the authorization attempt."}}, "required": ["message", "token", "status"]}}}}}}}}}