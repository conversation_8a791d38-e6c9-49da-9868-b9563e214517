const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getQuery, getComboIdQuery, getValuesQuery, getValuesDataFromDBQuery, getAccuracyMetricsDataQuery } = require('./getQuery.service');
const constants = require('../../config/constants.json');
const clkTables = constants.clickhouseTables

/**
 * Function to get data from db
 * @param {*} id - content id 
 * @param {*} visualization - metadata 
 */
async function getGraphData(comboId, visualization) {
  try {
    let query = await getQuery(visualization, comboId)
    let data = await getData(query)
    return data;
  } catch (err) {
    throw err;
  }
}

/**
 * Function to get parameter_combo_id from db based on values provided in request body indicatorDrivers
 * @param {*} indicatorDrivers - request body object 
 * @param {*} visualization - metadata 
 */
async function getComboId(visualization, indicatorDrivers) {
  try{
    let comboIdQuery = await getComboIdQuery(indicatorDrivers, visualization.comboIdTable)
    let comboId = await getData(comboIdQuery)
    let id = comboId[0].PARAMETER_COMBO_ID;
    return id;
  }catch(err){
    throw err;
  }
}

/**
 * Function to get tree chart data
 * @param {*} indicatorDrivers - request body object 
 * @param {*} visualization - metadata 
 */

async function getData(query) {
  try{
    log.debug(`>>>>> Enter microservice-analytical-apps.services.getGraphData.service.getData`);
    const containsClView = clkTables.some(view => query.includes(view));
    const executeQuery = containsClView? clkdb.simpleExecute:db.simpleExecute
    let data = await executeQuery(query)
    log.debug(`<<<<< Exit microservice-analytical-apps.services.getGraphData.service.getData successfully`);
    return data;
  }
  catch(err){
    log.error(`<<<<< Exit microservice-analytical-apps.services.getGraphData.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err
  }
}

async function getValuesDataFromDB(value) {
  try{
    let query = await getValuesDataFromDBQuery(value)
    let data = await getData(query)
    return data;
  }
  catch(err) {
    throw err;
  }
}

async function getAccuracyMetricsData(accuracyMetricsMeta, contentId){
    try{
      let query = await getAccuracyMetricsDataQuery(accuracyMetricsMeta, contentId)
      let data = await getData(query)
      return data;
    }catch(err){
      throw err;
    }
}

async function getValuesDataFromQuery(valuesMeta, comboId) {
    try{
      let query = await getValuesQuery(valuesMeta, comboId)
      let data = await getData(query)
      return data;
    }catch(err) {
      throw err;
    }
}
module.exports = { getGraphData, getComboId, getData, getValuesDataFromDB, getAccuracyMetricsData, getValuesDataFromQuery }