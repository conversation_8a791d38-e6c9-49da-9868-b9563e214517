require('dotenv').config();

const Logger = require('scad-library').logger;
const moment = require('moment')
const log = new Logger().getInstance();
const { listInsightData, addInsightData, getInsightData, updateInsightData, deleteInsightData, updateSubmitRequestInsightsData, getInsightsData } = require('./servicesv2/executeQuery.service');
const { sendInsightsEmail } = require('./servicesv2/sendEmail.service');
const { validateEmailContent } = require('../services/helpers/helper');

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function listInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.listInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const nodeId = req.params.id           
            const insightUsers = process.env.INSIGHT_USERS.split(',')
            let isInsightUser = insightUsers.includes(req.user.preferred_username)?true:false
            listInsightData(nodeId)
                .then(results => {
                    results = results.filter(insight=>insight.EMAIL==req.user.preferred_username) // For temp setup, will be changed in the query later
                    results.forEach(insight => {
                            insight.isEdit = insight.EMAIL == req.user.preferred_username?true:false
                            insight.showDelete = insight.EMAIL == req.user.preferred_username?true:false                       
                        }
                    )
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.listInsights successfully `);
                    
                    return resolve(
                        {
                            "data":results,
                            "isInsightUser":isInsightUser
                        }
                    );
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.listInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.listInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function addInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.addInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const reqBody = req.body
            
            // if (!process.env.INSIGHT_USERS.split(',').includes(req.user.preferred_username)){
            //     log.debug(`Add Insights: Request user is a regular user`);
            //     return reject("You don't have permission to add insights")
            // }

            log.debug(`Add Insights: Request user is an SME`);

            insightData = {}
            insightData.email = req.user.preferred_username;
            insightData.user = req.user.name;
            insightData.insight = reqBody.insight;
            insightData.nodeId = reqBody.nodeId;
            insightData.nodeTitle = reqBody.nodeTitle;
            insightData.nodeLink = reqBody.nodeLink;
            insightData.date = moment().format('DD/MM/YYYY HH:mm:ss')

            validateEmailContent(reqBody.insight)
            addInsightData(insightData)
                .then(results => {
                    // sendInsightsEmail(req,'ADD')
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.addInsights successfully `);
                    return resolve({
                        "message": "Insights added successfully.",
                        "status": "success"
                    });
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.addInsights not successfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.addInsights with error ${err}`);
            reject(err);
        }
    })
}


/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function updateInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.updateInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const id = req.params.id
            const reqBody = req.body
            let insight = await getInsightData(id)
            insight = insight.length?insight[0]:resolve("No insight found with given id")
            if (!insight.EMAIL == req.user.preferred_username)
                return resolve("You don't have permission to update this insight")
            
            const date = moment().format('DD/MM/YYYY HH:mm:ss')

            insightData = {}
            insightData.id = insight.ID
            insightData.email = insight.EMAIL;
            insightData.insight = reqBody.insight;
            insightData.nodeId = reqBody.nodeId;
            insightData.nodeTitle = reqBody.nodeTitle;
            insightData.nodeLink = reqBody.nodeLink;
            insightData.prevInsight = insight.INSIGHT;
            insightData.date = date
            insightData.status = "APPROVED"
            validateEmailContent(reqBody.insight)

            updateInsightData(insightData)
                .then(async results => {
                    sendInsightsEmail(req,'UPDATE',insightData)
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.updateInsights successfully `);
                    return resolve({"message":"Insight update","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.updateInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.updateInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function deleteInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.approveInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            
            const id = req.params.id
            let insight = await getInsightData(id)
            insight = insight[0]
            if (![insight.EMAIL,process.env.INSIGHT_APPROVER].includes(req.user.preferred_username))
                return resolve("You don't have permission to delete insights")

            deleteInsightData(id)
                .then(async results => {
                    sendInsightsEmail(req,'DELETE',insight)
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.deleteInsights successfully `);
                    return resolve({"message":"Insight deleted","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.deleteInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.deleteInsights with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { listInsights, addInsights, updateInsights, deleteInsights};
