const { IFPError } = require("../../utils/error");
const db = require('../../services/database.service');

const validateCommonAuth = async (req, res, next) => {
    try{
        apiKey = req.headers['x-api-key']

        if (!apiKey)
            throw new IFPError(400,'Please provide an API key');
        let query = `SELECT * FROM IFP_ADMIN_KEYS WHERE KEY = :apiKey`
        let binds = {apiKey: apiKey}
        let data = await db.simpleExecute(query, binds)
        if (data.length < 1)
            throw new IFPError(401,'Invalid API key');
        next()
    }
    catch(err){
        next(err)
    }
}     


module.exports = {
    validateCommonAuth
}