// echo "export HR_USER=hr" >> ~/.bashrc
// echo "export HR_CONNECTIONSTRING=0.0.0.0/orcl" >> ~/.bashrc
// source ~/.bashrc
require('dotenv').config();
//db connection configuration details. the terms starts with process.env comes from .env file
module.exports = {
  hrPool: {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    connectString: process.env.DB_CONNECTIONSTRING,
    poolMin: 1,
    poolMax: 10,
    poolIncrement: 1,
    poolPingInterval: 30,
    poolTimeout: 60,
    _enableStats  : true
  }
};
