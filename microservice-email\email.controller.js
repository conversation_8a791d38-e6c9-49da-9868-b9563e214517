const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../config/constants.json')

require('dotenv').config();

async function sendEmail(req) {
    new Promise((resolve, reject) => {
        let reqBody = req.body;
        reqBody.userName = req.user.preferred_username
        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, // true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });
        let organization = ''
        if (reqBody.organization) {
            organization = `Organization: ${reqBody.organization} \n`;
        }
        var mailOptions = {
            from: process.env.SYSTEM_MAILID,
            to: constants.supportEmailId,
            cc: constants.supportCCEmailIds,
            subject: reqBody.subject,
            text: `Username: ${reqBody.userName} \n ${organization} ${reqBody.myConcern}`
        };

        let fullname = reqBody.userName.toLowerCase().split('@')[0];
        let setName = fullname.split('.');
        if (setName.length > 1) {
            setName = setName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
        }

        var mailOptionsResponse = {
            from: process.env.SYSTEM_MAILID,
            to: reqBody.userName,
            subject: `Thank you for contacting us!`,
            attachments: [
                {
                    filename: 'IFP_logo.png',
                    path: __dirname + '/images/IFP_logo.png',
                    cid: 'ifp-logo'
                },
                {
                    filename: 'SCAD_logo.png',
                    path: __dirname + '/images/SCAD_logo.png',
                    cid: 'scad-logo'
                },
            ],
            html: `
            <html>
                <body>
                    <main>
                        <div class="contact-us" style="background-color: #1E1F26;">
                            <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                                <tr>
                                    <div>
                                        <td align="left">
                                            <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                        </td>
                                        <td align="right">
                                            <img src="cid:scad-logo" alt="" width={102} height={44} />
                                        </td>
                                    </div>
                                </tr>
                            </table>
                            <table style="background-color: #1E1F26; width:100%;">
                                <tr>
                                    <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                                        Salam ${setName},<br /><br /><br />${constants.emailResponseUser}
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </main>
                </body>
            </html>`
        };

        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                reject(error);
            }
            if (info) {
                transporter.sendMail(mailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    log.info(`Email sent successfully to user ${reqBody.userName} \n${info.response}`);
                    resolve();
                })
            }
            log.info(`Email sent successfully to COI Support \n${info.response}`);
            resolve();
        })
    });
}


module.exports = { sendEmail }
