const mailer = require("nodemailer");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const nunjucks = require("nunjucks");

async function sendReportEmail(data) {
  try {
    const transporter = mailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: false, //true for 465, false for other ports
      logger: true,
      debug: true,
      tls: {
        // do not fail on invalid certs
        rejectUnauthorized: false,
      },
    });

    var sendAiInsightReportEmailOptionsResponse = {
      from: process.env.SYSTEM_MAILID,
      to: data.recepients,
      subject: "Bayaan Insight Publication",
      attachments: [
        {
          filename: "new-message-banner.png",
          path: process.cwd() + "/emails/email-new-message-banner.png",
          cid: "new-message-banner",
        },
      ],
      html: nunjucks.render(
        "microservice-ai-insight-report/new-domain-quarter-report.njk",
        { ...data, emailBanner: "new-message-banner" }
      ),
    };

    transporter.sendMail(
      sendAiInsightReportEmailOptionsResponse,
      function (error, info) {
        if (error) {
          log.error(error);
        }

        log.info(`Email sent successfully to ${data.recepients}`);
        return `Email sent successfully to ${data.recepients}`;
      }
    );
  } catch (err) {
    log.error(err);
  }
}

module.exports = { sendReportEmail };
