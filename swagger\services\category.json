{"paths": {"/content-type/categories/": {"get": {"tags": ["Categories"], "summary": "Retrieve categories information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the category or section."}, "name": {"type": "string", "description": "The name of the category or section."}, "icon_path": {"type": "string", "format": "uri", "description": "The URL to the icon representing the category or section in dark mode."}, "light_icon_path": {"type": "string", "format": "uri", "description": "The URL to the icon representing the category or section in light mode."}, "node_count": {"type": "integer", "description": "The number of nodes or items within the category or section."}, "isSelected": {"type": "boolean", "description": "Indicates whether the category or section is currently selected."}}, "required": ["id", "name", "icon_path", "light_icon_path", "node_count", "isSelected"], "additionalProperties": false}, "description": "Schema for a response containing an array of categories or sections with their details."}}}}}}}}}