const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  getSuperUserStatusQuery,
  getUserFeatureFlagQuery,
  getMaintenanceStatusQuery
 } = require('./getQuery.service');


/**
 * Function to get superuser status
 * @param {*} userEmail - user email 
 */
async function getSuperUserStatusData(userEmail) {
  try {
    const query = await getSuperUserStatusQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-sla.executeQuery.service.getSuperUserStatusData with error ${err}`);
    throw err;
  }
}

async function getUserFeatureFlagData(userEmail) {
  try {
    const query = await getUserFeatureFlagQuery(userEmail);
    const data = await getData(query);
    if (data.length === 0) {
      return null;
    }
    return data[0];
  } catch (err) {
    log.error(`<<<<< Exited microservice-sla.executeQuery.service.getUserFeatureFlag with error ${err}`);
    throw err;
  }
}

async function getMaintenanceStatus(){
  try {
    const query = await getMaintenanceStatusQuery();
    const data = await getClkData(query);
    if (data.length === 0) {
      return null;
    }
    return data[0];
  } catch (err) {
    log.error(`<<<<< Exited microservice-sla.executeQuery.service.getMaintenanceStatus with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-sla.services.executeQuery.service.getData`);
    let data = await db.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-sla.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-sla.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getClkData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-sla.services.executeQuery.service.getData`);
    let data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-sla.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-sla.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getSuperUserStatusData,
  getUserFeatureFlagData,
  getMaintenanceStatus
};
