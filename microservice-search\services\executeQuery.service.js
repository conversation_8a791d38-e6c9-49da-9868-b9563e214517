const clkdb = require('../../services/clk-database.service');
const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  getGlossaryDataQuery,
  getScreenerSearchQuery,
  officialNodeSearchQuery,
  officialNodeSearchQueryAdvanced
} = require('./getQuery.service');

/**
 * Function to get popular stats
 * @param {*} nodeIds - Node IDs 
 */
async function getGlossaryData(lang,page=1,limit=10,filters={},sortBy={},searchTerm=null) {
  return new Promise((resolve, reject) => {
    getGlossaryDataQuery(lang,page,limit,filters,sortBy,searchTerm).then((result) => {
      getClkData(result.query,result.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-search.executeQuery.service.getGlossaryData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-search.executeQuery.service.getGlossaryData with error ${err}`);
      reject(err);
    })
  })
}

async function getExperimentalIndicatorsData(searchTerm='',screenerConfigurations,lang='EN') {

  return new Promise((resolve, reject) => {
    getScreenerSearchQuery(searchTerm,screenerConfigurations,lang).then((result) => {
      getClkData(result.query,result.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-search.executeQuery.service.getExperimentalIndicatorsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-search.executeQuery.service.getExperimentalIndicatorsData with error ${err}`);
      reject(err);
    })
  })
}


async function getClkData(query,binds={}) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    clkdb.simpleExecute(query,binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

async function officialIndicatorSearch(req,searchQuery){
  try {
    // let {query, binds}= await officialNodeSearchQuery(req,searchQuery);
    let {query, binds}= await officialNodeSearchQueryAdvanced(req, searchQuery);
    let data = await clkdb.simpleExecute(query, binds);

    return data.map(row => {
      let jsonConfig = row;  // Assuming JSON_CONFIG is an array, taking the first element

      return {
          id: jsonConfig.node_id,
          type: "scad_official_indicator",  // This is static, but you can change it if needed
          title:  jsonConfig.title,
          subTitle: jsonConfig.subtitle,
          contentClassification: "Official Statistics",  // This is static, adjust if necessary
          pageCategory: "Official Statistics",  // This is static, adjust if necessary
          topic: {
              id: jsonConfig.domain_id,  // This is static, replace with dynamic data if required
              name: jsonConfig.domain
          },
          theme: jsonConfig.theme,
          subtheme: jsonConfig.subtheme,
          product: jsonConfig.product,
          body: jsonConfig.note,  // Assuming 'body' is mapped from 'note'
          note: jsonConfig.note,  // If there's a note field
          appType: null,  // Static for now
          relevanceScore: jsonConfig.relevance_score,
      };
    });
    // return data;
    
  }catch (err) {
    log.error(`<<<<< Exited services.executeQuery.service.revokeRefreshToken with error ${err}`);
    throw err;
  }
}


module.exports = {
  getGlossaryData,getExperimentalIndicatorsData,officialIndicatorSearch
};
