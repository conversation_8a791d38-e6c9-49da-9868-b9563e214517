const clkdb = require('../../services/clk-database.service');
const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  getGlossaryDataQuery, getScreenerSearchQuery
} = require('./getQuery.service');

/**
 * Function to get popular stats
 * @param {*} nodeIds - Node IDs 
 */
async function getGlossaryData(lang,page=1,limit=10,filters={},sortBy={},searchTerm=null) {
  return new Promise((resolve, reject) => {
    getGlossaryDataQuery(lang,page,limit,filters,sortBy,searchTerm).then((result) => {
      getClkData(result.query,result.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-search.executeQuery.service.getGlossaryData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-search.executeQuery.service.getGlossaryData with error ${err}`);
      reject(err);
    })
  })
}

async function getExperimentalIndicatorsData(searchTerm='',screenerConfigurations,lang='EN') {

  return new Promise((resolve, reject) => {
    getScreenerSearchQuery(searchTerm,screenerConfigurations,lang).then((result) => {
      getClkData(result.query,result.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-search.executeQuery.service.getExperimentalIndicatorsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-search.executeQuery.service.getExperimentalIndicatorsData with error ${err}`);
      reject(err);
    })
  })
}


async function getClkData(query,binds={}) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    clkdb.simpleExecute(query,binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = {
  getGlossaryData,getExperimentalIndicatorsData
};
