function businessAddInsightsMessage(name,chartTitle,chartLink,insight){
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${name},<br /><br />
                            We would like to express our sincere appreciation for your valuable insights added to <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a>. Your contribution is highly valuable to us!<br/><br/>

                            Insight Content: </br>
                            ${insight}</br><br/>

                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>`
}

function businessSubmitApprovalInsightsMessage(name,insight,approverName){
    let insightsList = ""
    insight.forEach(element => {
        insightsList += `<li>${element.INSIGHT}</li>`;
    });
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${name},<br /><br />
                            Your approval request for the <a style="color: #3e8fff;" href="${insight[0].NODE_LINK}">${insight[0].NODE_TITLE}</a> has been sent to ${approverName} for review and approval. We appreciate your valuable insights and eagerly await feedback from the approver within the next 3 business days.<br/><br/>
                            Please find below the insights for approval:
                            <ul>
                            ${insightsList}
                            </ul>
                            <br/>Thank you for your contribution and professional collaboration. We wish you the best of luck!<br/>
                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>

`
}
function approverAddInsightsMessage(businessName,approverName,chartTitle,chartLink){
    return `
    <html>
        <body>
            <main>
                <div class="contact-us" style="background-color: #1E1F26;">
                    <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                        <tr>
                            <div>
                                <td align="left">
                                    <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                </td>
                                <td align="right">
                                    <img src="cid:scad-logo" alt="" width={102} height={44} />
                                </td>
                            </div>
                        </tr>
                    </table>
                    <table style="background-color: #1E1F26; width:100%;">
                        <tr>
                            <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                                Salam ${approverName},<br /><br /><br />${businessName} has added a new insight to ${chartTitle}!.<br/>Please review the insight.<br/>Here's the link: <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a><br/><br/>All the Best!<br/>
                            </td>
                        </tr>
                    </table>
                </div>
            </main>
        </body>
    </html>`
}

function approverSubmitRequestInsightsMessage(businessName,approverName,insight){
    let insightsList = ""
    insight.forEach(element => {
        insightsList += `<li>${element.INSIGHT}</li>`;
    });
    return `
    <html>
        <body>
            <main>
                <div class="contact-us" style="background-color: #1E1F26;">
                    <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                        <tr>
                            <div>
                                <td align="left">
                                    <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                </td>
                                <td align="right">
                                    <img src="cid:scad-logo" alt="" width={102} height={44} />
                                </td>
                            </div>
                        </tr>
                    </table>
                    <table style="background-color: #1E1F26; width:100%;">
                        <tr>
                            <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                                Dear ${approverName},
                                <br /><br />${businessName} has submitted insights for review regarding <a style="color: #3e8fff;" href="${insight[0].NODE_LINK}">${insight[0].NODE_TITLE}</a>. Your attention and approval are kindly requested.<br/>
                                Requested Insights:<br/>
                                <ul>
                                ${insightsList}
                                </ul>
                                <br/>Thank you for your prompt attention and collaboration.<br/>
                            </td>
                        </tr>
                    </table>
                </div>
            </main>
        </body>
    </html>`
}

function businessApproveInsightsMessage(businessName,approverName,chartTitle,chartLink,insights){
    let insightsList = ""   
    insights.forEach(element => {
        insightsList += `<li>${element.INSIGHT}</li>`;
    });
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${businessName},<br /><br /><br />We are pleased to inform you that your insight for ${chartTitle} has been approved by ${approverName}.<br/><br/>
                            Approved Insights:
                            <ul>${insightsList}</ul>
                            You can view your approved insight by clicking on the following link: <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a>.<br/><br/>
                            Thank you for your valuable contribution.<br/>
                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>`
}

function businessRejectInsightsMessage(businessName,approverName,chartTitle,chartLink,insights){
    let insightsList = ""
    insights.forEach(element => {
        insightsList += `<li>${element.INSIGHT}</li>`;
    });
    return `
    <html>
        <body>
            <main>
                <div class="contact-us" style="background-color: #1E1F26;">
                    <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                        <tr>
                            <div>
                                <td align="left">
                                    <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                </td>
                                <td align="right">
                                    <img src="cid:scad-logo" alt="" width={102} height={44} />
                                </td>
                            </div>
                        </tr>
                    </table>
                    <table style="background-color: #1E1F26; width:100%;">
                        <tr>
                            <td style="color: #D8D8D8; font-size: 16px; font-family: Lato Regular; line-height: 19px; padding: 20px;">
                            Dear ${businessName},<br /><br /><br />
                            We would like to inform you that insights for <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a> has been rejected by ${approverName}.</br>
                            </br>
                            Rejected Insights:
                            <ul>${insightsList}</ul>

                            </td>
                        </tr>
                    </table>
                </div>
            </main>
        </body>
    </html>`
}

function businessRequestEditInsightsMessage(businessName,approverName,chartTitle,chartLink,insights,setComments){
    let insightsList = ""
    insights.forEach(element => {
        insightsList += `<li>${element.INSIGHT}</li>`;
    });
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${businessName},<br /><br />
                            We kindly inform you that ${approverName} has requested your attention to make necessary edits to the insights for ${chartTitle}. Your cooperation in this matter is greatly appreciated.<br/><br/>
                            Please access the following link to review: <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a><br/>
                            <br/>
                            Insights for Edit:<br/>
                            <ul>${insightsList}</ul>
                            <br/>
                            Approver's Feedback:<br/>
                            ${setComments}
                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>
`
}

function businessUpdateInsightsMessage(name,chartTitle,chartLink,insight,prevInsight){
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${name},<br /><br /><br />We would like to inform you that you have made a revision to your insights for <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a>.<br/></br>
                            
                            Previous Insight:</br>
                            ${prevInsight}<br/><br/>
                            Revised Insight:</br>
                            ${insight}<br/><br/>
                            Thank you for your attention to detail and commitment to improving your insights.<br/>
                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>
    `
}

function businessUpdateByApproverInsightsMessage(name,chart_title){
    return `
    <html>
        <body>
            <main>
                <div class="contact-us" style="background-color: #1E1F26;">
                    <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                        <tr>
                            <div>
                                <td align="left">
                                    <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                </td>
                                <td align="right">
                                    <img src="cid:scad-logo" alt="" width={102} height={44} />
                                </td>
                            </div>
                        </tr>
                    </table>
                    <table style="background-color: #1E1F26; width:100%;">
                        <tr>
                            <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                                Salam ${name},<br /><br /><br />Your insights for ${chart_title} has been edited by approver!.<br/><br/>
                            </td>
                        </tr>
                    </table>
                </div>
            </main>
        </body>
    </html>`
}

function approverUpdateInsightsMessage(businessName,approverName,chartTitle,chartLink){
    return `
    <html>
        <body>
            <main>
                <div class="contact-us" style="background-color: #1E1F26;">
                    <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                        <tr>
                            <div>
                                <td align="left">
                                    <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                </td>
                                <td align="right">
                                    <img src="cid:scad-logo" alt="" width={102} height={44} />
                                </td>
                            </div>
                        </tr>
                    </table>
                    <table style="background-color: #1E1F26; width:100%;">
                        <tr>
                            <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                                Salam ${approverName},<br /><br /><br />${businessName} has updated the insight for ${chartTitle}!.<br/>Please review the insight.<br/>Here's the link: <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a><br/><br/>All the Best!<br/>
                            </td>
                        </tr>
                    </table>
                </div>
            </main>
        </body>
    </html>`
}

function businessDeleteInsightsMessage(name,chartTitle,chartLink,insight){
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${name},<br /><br />We would like to inform you that you have successfully deleted the following insight from <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a>.<br/><br/>
                            Deleted Insight:<br/>
                            ${insight}<br/><br/>

                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>`
}
function businessDeleteByApproverInsightsMessage(name,chartTitle,chartLink,insight,approverName){
    return `
    <html>
    <body>
        <main>
            <div class="contact-us" style="background-color: #1E1F26;">
                <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                    <tr>
                        <div>
                            <td align="left">
                                <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                            </td>
                            <td align="right">
                                <img src="cid:scad-logo" alt="" width={102} height={44} />
                            </td>
                        </div>
                    </tr>
                </table>
                <table style="background-color: #1E1F26; width:100%;">
                    <tr>
                        <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                            Salam ${name},<br /><br />
                            We would like to inform you that the insights you submitted for <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a> have been deleted by ${approverName}.<br/><br/>
 
                            Deleted Insight: </br>
                            ${insight}
                            <br/><br/>
                            
                        </td>
                    </tr>
                </table>
            </div>
        </main>
    </body>
</html>`
}
function approverDeleteInsightsMessage(businessName,approverName,chartTitle,chartLink,insight){
    return `
    <html>
        <body>
            <main>
                <div class="contact-us" style="background-color: #1E1F26;">
                    <table style="background-color: #1E1F26; width:100%;border-bottom: 2px solid #A0947E;">
                        <tr>
                            <div>
                                <td align="left">
                                    <img src="cid:ifp-logo" alt="" width={100} height={40}/>
                                </td>
                                <td align="right">
                                    <img src="cid:scad-logo" alt="" width={102} height={44} />
                                </td>
                            </div>
                        </tr>
                    </table>
                    <table style="background-color: #1E1F26; width:100%;">
                        <tr>
                            <td style="color: #D8D8D8; font-size: 16px;font-family: Lato Regular;line-height: 19px; padding: 20px;">
                                Salam ${approverName},<br /><br />${businessName} has deleted an insight from <a style="color: #3e8fff;" href="${chartLink}">${chartTitle}</a><br/>
                                </br>Deleted Insight: </br>
                            ${insight}
                            <br/><br/>
                            </td>
                        </tr>
                    </table>
                </div>
            </main>
        </body>
    </html>`

}
module.exports = {businessAddInsightsMessage, approverAddInsightsMessage, businessApproveInsightsMessage,businessRejectInsightsMessage, businessUpdateInsightsMessage,businessUpdateByApproverInsightsMessage, approverUpdateInsightsMessage, businessDeleteInsightsMessage,businessDeleteByApproverInsightsMessage, approverDeleteInsightsMessage,businessSubmitApprovalInsightsMessage,approverSubmitRequestInsightsMessage, businessRequestEditInsightsMessage}