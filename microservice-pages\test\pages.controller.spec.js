const controller = require('../pages.controller');
const axios = require('axios');
describe('controller', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    xtest('should get json response from Pages controller - success', async () => {
        const mockGetResponse = {
            data: [
                {
                    "id": "428",
                    "pageType": "tcpage",
                    "title": "Terms and Conditions",
                    "page_description": "",
                    "page_menu_icon": "",
                    "menuLabel": ""
                },
                {
                    "id": "424",
                    "pageType": "homepage",
                    "title": "Insights &amp; Foresights Platform",
                    "page_description": "Insights &amp; Foresights Platform",
                    "page_menu_icon": "",
                    "menuLabel": "Home"
                },
                {
                    "id": "415",
                    "pageType": "listingpage",
                    "title": "Agriculture",
                    "page_description": "This page contains Agriculture related indicators ",
                    "page_menu_icon": "/sites/default/files/2021-12/Agriculture%20menu%20icon_0.jpg",
                    "menuLabel": ""
                },
                {
                    "id": "380",
                    "pageType": "domainpage",
                    "title": "Economy Insights",
                    "page_description": "What If Scenarios, Forecasts, and Indicators for Economic sectors",
                    "page_menu_icon": "/sites/default/files/2021-11/icon-economy_0.svg",
                    "menuLabel": ""
                }
            ]
        };

        const expectedResponse = [
            {
                "label": "",
                "pageList": [
                    {
                        "title": "Insights & Foresights Platform",
                        "id": "424",
                        "pageDescription": "Insights & Foresights Platform",
                        "pageMenuIcon": "",
                        "pageIcon": "https://scad-insights-cms-dev.scad.gov.ae/sites/default/files/2022-01/illustration-home.png",
                        "pageType": "homepage",
                        "publication_date": "2021-12-15",
                        "subsections": [
                            {
                                "sectionOrder": "1",
                                "sectionType": "listingpage",
                                "indicatorList": [],
                                "listingPages": {
                                    "listingType": "scad_what_s_new",
                                    "listingLimit": "3",
                                    "listingLabel": "Whats New Section"
                                }
                            },
                            {
                                "sectionOrder": "2",
                                "sectionType": "indicatorlist",
                                "indicatorList": [
                                    {
                                        "indicatorId": "343",
                                        "contentType": "geospatial_analysis",
                                        "appType": "",
                                        "type": ""
                                    },
                                    {
                                        "indicatorId": "17",
                                        "contentType": "analytical_apps",
                                        "appType": "internal",
                                        "type": ""
                                    },
                                    {
                                        "indicatorId": "367",
                                        "contentType": "analytical_apps",
                                        "appType": "internal",
                                        "type": ""
                                    },
                                    {
                                        "indicatorId": "73",
                                        "contentType": "scad_official_indicator",
                                        "appType": "",
                                        "type": "Scad"
                                    }
                                ],
                                "listingPages": {
                                    "listingType": "",
                                    "listingLimit": "",
                                    "listingLabel": "Main Indicators"
                                }
                            },
                            {
                                "sectionOrder": "3",
                                "sectionType": "listingpage",
                                "indicatorList": [],
                                "listingPages": {
                                    "listingType": "analytical_apps",
                                    "listingLimit": "4",
                                    "listingLabel": "Analytical Apps"
                                }
                            },
                            {
                                "sectionOrder": "4",
                                "sectionType": "listingpage",
                                "indicatorList": [],
                                "listingPages": {
                                    "listingType": "newsletters",
                                    "listingLimit": "4",
                                    "listingLabel": "Infographic Reports"
                                }
                            }
                        ],
                        "route": "/home",
                        "menuLabel": "Home",
                        "disabled": false,
                        "navigable": true
                    }
                ]
            },
            {
                "label": "Explore Domain",
                "pageList": [
                    {
                        "id": "380",
                        "pageType": "domainpage",
                        "title": "Economy Insights",
                        "page_description": "What If Scenarios, Forecasts, and Indicators for Economic sectors",
                        "page_menu_icon": "/sites/default/files/2021-11/icon-economy_0.svg",
                        "menuLabel": "",
                        "template": "domains",
                        "route": "/domain-exploration/economy-insights",
                        "disabled": false,
                        "navigable": true
                    }
                ]
            },
            {
                "label": "Explore Data",
                "pageList": [
                    {
                        "id": "415",
                        "pageType": "listingpage",
                        "title": "Agriculture",
                        "page_description": "This page contains Agriculture related indicators ",
                        "page_menu_icon": "/sites/default/files/2021-12/Agriculture%20menu%20icon_0.jpg",
                        "menuLabel": "",
                        "route": "/data-exploration/agriculture",
                        "disabled": false,
                        "navigable": true
                    }
                ]
            }
        ];
        const mockPostResponse = {
            headers: {
                'set-cookie': ['default;default']
            }
        };

        jest.spyOn(axios, 'get').mockResolvedValue(mockGetResponse);
        jest.spyOn(axios, 'post').mockResolvedValue(mockPostResponse);
        const mockReq = {
            "body": {},
            "user": {
                "preferred_username": "<EMAIL>",
                "groups": [
                    "6dde8c2c-1eff-4581-aa4b-84b2443cae32",
                    "e46138dd-347a-4908-8424-51ea4322e331"
                ]
            },
            "headers": {
                "accept-language": "en",
                "host": "localhost:3000"
            },
            "params": {}
        };
        result = await controller.getPages(mockReq);
        // console.log(result);
        // console.log('------->', result, expectedResponse);
        //expect(result).toEqual(expectedResponse);
    })
    test('should get error response from Pages controller - failure', async () => {
        try {
            jest.spyOn(axios, 'get').mockRejectedValue(new Error('Mock Error'));
            jest.spyOn(axios, 'post').mockRejectedValue(new Error('Mock Error'));
            let groupId = ['6dde8c2c-1eff-4581-aa4b-84b2443cae32'];
            //await controller.getPages(groupId);
        } catch (err) {
            // expect(err).toEqual([401, new Error('Mock Error')]);
        }

    })
});
