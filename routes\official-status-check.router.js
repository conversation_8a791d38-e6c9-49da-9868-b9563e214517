const express = require('express');
const router = new express.Router();
const statisticsInsightsController = require('../microservice-statistics-insights/statistics-insights.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../config/constants.json');

  router.get('/:id', async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getStatisticsInsightsById(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.use((err,req, res, next) => {
    try {
      if (err) {
          if (constants.errorMessage.hasOwnProperty(err[0])) {
              const errorMessage = constants.errorMessage[err[0]];
              log.error(`Handling Error ${JSON.stringify(err[1])} ${err[1].stack}`);
              let reason = err[1] ? err[1] : '';
              res.status(err[0]).send({ message: errorMessage, error: err[0], reason: reason.message });
              next();
          }
          else {
            log.error(`Error handling error message ${err}`)
            res.status(500).send({ message: "Internal Server Error", error:500, reason:err.message });
              next();
          }
      }
    } catch (err) {
        log.error(`Error handling error message ${err}`)
        res.status(500).send({ message: "Internal Server Error", error:500, reason:err.message });
          next();
    }

  })

  

module.exports = router;
