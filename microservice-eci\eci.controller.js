require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getMetaFromCMS } = require('../services/common-service');
let constants = require('../config/constants.json');
const jsonwebtoken = require('jsonwebtoken')

/**
 * function to get survey content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function authEci(req) {
    log.debug(`>>>>>Entered eci-microservice.eci.controller.authEci`);
    try {
        let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsAnalyticalAppsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_ECI_APPS}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        let cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsAnalyticalAppsUrl, req.user.groups);
        let auth_status = false
        if (cmsResponse){
            let eciCount = cmsResponse.filter(element => element.type == "ECI-Insights").length
            if (eciCount){
                auth_status = true
                let token = jsonwebtoken.sign(
                    {
                        auth_level: 2,
                        sub: "server",
                        status: "valid",
                    },
                    process.env.TESSERACT_JWT_SECRET.trim(),
                    {expiresIn: "8h"},
                    );
                return {"message":"Authorized","token":token,"status":auth_status}
            }
            return {"message":"Unauthorized","status":auth_status}
        }
        else{
            return {"message":"Unauthorized","status":auth_status}
        }
        
    } catch (err) {
        log.error(`<<<<<Exited eci-microservice.eci.controller.authEci on getting CMS data with error ${err}`);
        if (err[0] == 404)
            return resolve({"message":"Unauthorized","status":false})
        else{
            throw err;
        }
    }
}

module.exports = { authEci };
