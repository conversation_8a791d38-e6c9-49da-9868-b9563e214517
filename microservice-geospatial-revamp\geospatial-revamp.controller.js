require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getregionsData, getAllFiltersData, getSummaryData, getPopulationSummaryData, getLaborForceSummaryData, getRealEstateSummaryData } = require('./services/executeQuery.service');


async function getRegionsFilters(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getRegionsFilters`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const regions = await getregionsData(req);
        const returnRegion = regions.map(region => {
            return {
                "POPULATION": region.POPULATION,
                "CITIZEN_CODE": region.CITIZEN_CODE,
                "CITIZEN_AR": region.CITIZEN_AR,
                "CITIZEN_EN": region.CITIZEN_EN
            };
        });
        return {
            "regions":returnRegion
        }
    } catch (err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getRegionsFilters on getting data with error ${err}`);
        throw err;
    }
}

async function getAllFilters(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getAllFilters`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const allFilters = await getAllFiltersData(req)
        return {
            "regions": getDistinctRegions(allFilters.geo),
            "districts": getDistinctDistricts(allFilters.geo),
            "communities": getDistinctCommunities(allFilters.geo),  
            "years": getDistinctYears(allFilters.geo),
            "quarters": getDistinctQuarters(allFilters.geo),
            "citizens": allFilters.citizens,
            "gender": allFilters.gender,
            "marital": allFilters.marital,
            "attainment": allFilters.attainment,  
            // "houseHold": allFilters.houseHold,
            "buildingsType": allFilters.buildingsType, 
            "buildingsUse": allFilters.buildingsUse,
            "unitsUse": allFilters.unitsUse, 
            "unitsType": allFilters.unitsType
        }
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getAllFilters on getting data with error ${err}`);
        throw err;
    }
}

async function getSummary(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getSummary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const summary = await getSummaryData(req)
        log.info(summary);
        return {
            "summary":summary
        }
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getSummary on getting data with error ${err}`);
        throw err;
    }
}

async function getPopulationSummary(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getPopulationSummary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const summary = await getPopulationSummaryData(req)
        log.info(summary);
        return {
            "summary":summary
        }
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getPopulationSummary on getting data with error ${err}`);
        throw err;
    }
} 

async function getLaborForceSummary(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getLaborForceSummary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const summary = await getLaborForceSummaryData(req)
        log.info(summary);
        return {
            "summary":summary
        }
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getLaborForceSummary on getting data with error ${err}`);
        throw err;
    }
}


async function getRealEstateSummary(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getRealEstateSummary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const summary = await getRealEstateSummaryData(req)
        log.info(summary);
        return {
            "summary":summary
        }
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getRealEstateSummary on getting data with error ${err}`);
        throw err;
    }
}

function getDistinctRegions(data) {
    const uniqueRegions = new Map();
    
    data.forEach(item => {
        if (item.REGION_CODE) {
            uniqueRegions.set(item.REGION_CODE, {
                REGION_CODE: item.REGION_CODE,
                REGION_AR: item.REGION_AR,
                REGION_EN: item.REGION_EN
            });
        }
    });

    return Array.from(uniqueRegions.values())
    .sort((a, b) => {
        if (a.REGION_CODE == null) return 1; 
        if (b.REGION_CODE == null) return -1;

        return a.REGION_CODE - b.REGION_CODE;
    });
}

function getDistinctDistricts(data) {
    const uniqueDistricts = new Map();
    
    data.forEach(item => {
        if (item.DISTRICT_CODE) {
            uniqueDistricts.set(item.DISTRICT_CODE, {
                REGION_CODE: item.REGION_CODE,
                DISTRICT_CODE: item.DISTRICT_CODE,
                DISTRICT_AR: item.DISTRICT_AR,
                DISTRICT_EN: item.DISTRICT_EN
            });
        }
    });

    return Array.from(uniqueDistricts.values())
    .sort((a, b) => {
        if (!a.DISTRICT_CODE) return 1;
        if (!b.DISTRICT_CODE) return -1;
    
        return a.DISTRICT_CODE.localeCompare(b.DISTRICT_CODE);
    });
}

function getDistinctCommunities(data) {
    const uniqueCommunities = new Map();
    
    data.forEach(item => {
        if (item.COMMUNITY_CODE) {
            uniqueCommunities.set(item.COMMUNITY_CODE, {
                REGION_CODE: item.REGION_CODE,
                DISTRICT_CODE: item.DISTRICT_CODE,
                COMMUNITY_CODE: item.COMMUNITY_CODE,
                COMMUNITY_AR: item.COMMUNITY_AR,
                COMMUNITY_EN: item.COMMUNITY_EN
            });
        }
    });

    return Array.from(uniqueCommunities.values())
    .sort((a, b) => {
        if (!a.COMMUNITY_CODE) return 1;
        if (!b.COMMUNITY_CODE) return -1;
    
        return a.COMMUNITY_CODE.localeCompare(b.COMMUNITY_CODE);
    });
}

function getDistinctYears(data) {
    const uniqueYears = new Map();
    
    data.forEach(item => {
        if (item.YEAR) {
            uniqueYears.set(item.YEAR, {
                YEAR_CODE: item.YEAR,
                YEAR_AR: item.YEAR,
                YEAR_EN: item.YEAR
            });
        }
    });

    return Array.from(uniqueYears.values())
    .sort((a, b) => {
        if (a.YEAR == null) return 1; 
        if (b.YEAR == null) return -1;

        return a.YEAR - b.YEAR;
    });
}

function getDistinctQuarters(data) {
    const uniqueQuarters = new Map();
    
    data.forEach(item => {
        if (item.QUARTER && item.YEAR) {
            const key = `${item.YEAR}-${item.QUARTER}`; // Use YEAR + QUARTER as key
            uniqueQuarters.set(key, {
                YEAR_CODE: item.YEAR,
                QUARTER_CODE: item.QUARTER,
                QUARTER_AR: item.QUARTER,
                QUARTER_EN: item.QUARTER
            });
        }
    });

    return Array.from(uniqueQuarters.values())
    .sort((a, b) => {
        if (a.YEAR_CODE !== b.YEAR_CODE) {
            return b.YEAR_CODE - a.YEAR_CODE; // Sort by year descending (latest first)
        }
        return a.QUARTER_CODE.localeCompare(b.QUARTER_CODE, undefined, { numeric: true });
    });
}



module.exports = { getRegionsFilters, getAllFilters, getSummary, getPopulationSummary, getLaborForceSummary, getRealEstateSummary };
