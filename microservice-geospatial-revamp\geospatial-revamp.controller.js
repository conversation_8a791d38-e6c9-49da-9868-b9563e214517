require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getregionsData, getAllFiltersData, getSummaryData, getPopulationSummaryData, getLaborForceSummaryData, getRealEstateSummaryData, getCensusAccessData } = require('./services/executeQuery.service');
const constants = require('../config/constants.json');
async function getGeospatialDomains(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getRegionsFilters`);
    try {

        let populationAccess = false;
        let realEstateAccess = false;
        let labourForceAccess = false;
        let communityAccess = false;
        let nationalityAccess = false;
        let religionAccess = false;

        let userGroups = req.user.groups
        const populationGroups = constants.geospatialAccessGroups.population
        const realEstateGroups = constants.geospatialAccessGroups.realEstate
        const labourForceGroups = constants.geospatialAccessGroups.labourForce

        populationAccess = userGroups.some(group => populationGroups.includes(group));
        realEstateAccess = userGroups.some(group => realEstateGroups.includes(group));
        labourForceAccess = userGroups.some(group => labourForceGroups.includes(group));
        communityAccess = userGroups.includes(constants.geospatialAccessGroups.community)

        let censusModuleAccessData = await getCensusAccessData(req.user.preferred_username, 1);
        if (censusModuleAccessData && censusModuleAccessData.length > 0) {
            censusModuleAccessData.forEach(access => {
                if (access.MODULE === 'NATIONALITY') {
                    nationalityAccess = true;
                }
                if (access.MODULE === 'RELIGION') {
                    religionAccess = true;
                }
            })
        }
     
        const results = {
            populationAccess,
            realEstateAccess,
            labourForceAccess,
            communityAccess,
            nationalityAccess,
            religionAccess
        };
        return results
    } catch (err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getRegionsFilters on getting data with error ${err}`);
        throw err;
    }
}

async function getRegionsFilters(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getRegionsFilters`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const regions = await getregionsData(req);
        const returnRegion = regions.map(region => {
            return {
                "POPULATION": region.POPULATION,
                "CITIZEN_CODE": region.CITIZEN_CODE,
                "CITIZEN_AR": region.CITIZEN_AR,
                "CITIZEN_EN": region.CITIZEN_EN
            };
        });
        return {
            "regions":returnRegion
        }
    } catch (err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getRegionsFilters on getting data with error ${err}`);
        throw err;
    }
}

async function getAllFilters(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getAllFilters`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const allFilters = await getAllFiltersData(req)
        let results = {
            "regions": getDistinctRegions(allFilters.geo),
            "districts": getDistinctDistricts(allFilters.geo),
            "years": getDistinctYears(allFilters.geo),
            "quarters": getDistinctQuarters(allFilters.geo),
            "citizens": allFilters.citizens,
            "gender": allFilters.gender,
            "marital": allFilters.marital,
            "attainment": allFilters.attainment,  
            // "houseHold": allFilters.houseHold,
            "buildingsType": allFilters.buildingsType, 
            "buildingsUse": allFilters.buildingsUse,
            "unitsUse": allFilters.unitsUse, 
            "unitsType": allFilters.unitsType,
            "communities": []
        }

        if (req.geospatialAccess.communityAccess){ //For checking community access. Only a temp solution. To be migrated to CMS for unified access control.
            results["communities"] = getDistinctCommunities(allFilters.geo)
        }
        return results;     
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getAllFilters on getting data with error ${err}`);
        throw err;
    }
}

async function getSummary(req) {
    log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.getSummary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const summary = await getSummaryData(req)
        log.info(summary);
        return {
            "summary":summary
        }
    } catch(err) {
        log.error(`<<<<<Exited microservice.geospatial-revamp.controller.getSummary on getting data with error ${err}`);
        throw err;
    }
}

async function createSummaryHandler(summaryDataFunction, functionName) {
    return async function(req) {
        log.debug(`>>>>>Entered microservice.geospatial-revamp.controller.${functionName}`);
        try {
            const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
            const summary = await summaryDataFunction(req);
            log.info(summary);
            return { "summary": summary };
        } catch(err) {
            log.error(`<<<<<Exited microservice.geospatial-revamp.controller.${functionName} on getting data with error ${err}`);
            throw err;
        }
    };
}

const getPopulationSummary = createSummaryHandler(getPopulationSummaryData, 'getPopulationSummary');
const getLaborForceSummary = createSummaryHandler(getLaborForceSummaryData, 'getLaborForceSummary');
const getRealEstateSummary = createSummaryHandler(getRealEstateSummaryData, 'getRealEstateSummary');


function getDistinctItems(data, config) {
    const uniqueItems = new Map();
    
    data.forEach(item => {
        if (item[config.codeField]) {
            const itemData = {};
            config.fields.forEach(field => {
                itemData[field] = item[field];
            });
            uniqueItems.set(item[config.codeField], itemData);
        }
    });

    return Array.from(uniqueItems.values()).sort(config.sortFunction);
}

// Usage:
const regions = getDistinctItems(data, {
    codeField: 'REGION_CODE',
    fields: ['REGION_CODE', 'REGION_AR', 'REGION_EN'],
    sortFunction: (a, b) => a.REGION_CODE - b.REGION_CODE
});

const districts = getDistinctItems(data, {
    codeField: 'DISTRICT_CODE',
    fields: ['REGION_CODE', 'DISTRICT_CODE', 'DISTRICT_AR', 'DISTRICT_EN'],
    sortFunction: (a, b) => a.DISTRICT_CODE.localeCompare(b.DISTRICT_CODE)
});

const communities = getDistinctItems(data, {
    codeField: 'COMMUNITY_CODE',
    fields: ['REGION_CODE', 'DISTRICT_CODE', 'COMMUNITY_CODE', 'COMMUNITY_AR', 'COMMUNITY_EN'],
    sortFunction: (a, b) => a.COMMUNITY_CODE.localeCompare(b.COMMUNITY_CODE)
});

const years = getDistinctItems(data, {
    codeField: 'YEAR',
    fields: ['YEAR'],
    sortFunction: (a, b) => a.YEAR - b.YEAR
});

const quarters = getDistinctItems(data, {
    codeField: 'QUARTER',
    fields: ['YEAR', 'QUARTER'],
    sortFunction: (a, b) => {
        if (a.YEAR !== b.YEAR) {
            return a.YEAR - b.YEAR;
        }
        return a.QUARTER - b.QUARTER;
    }
});

module.exports = { getGeospatialDomains, getRegionsFilters, getAllFilters, getSummary, getPopulationSummary, getLaborForceSummary, getRealEstateSummary };
