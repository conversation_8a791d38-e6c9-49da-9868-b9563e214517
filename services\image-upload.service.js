const { PutObjectCommand } = require("@aws-sdk/client-s3");
const { awsClient } = require("../config/awsClient");
const { randomUUID } = require("crypto");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

// Configuration - you can move these to environment variables or config file
const IMAGE_BUCKET = process.env.S3_IMAGE_BUCKET || "your-image-bucket";
const IMAGE_FOLDER = "publication-images";

/**
 * Uploads an image to S3 and returns the public URL
 * @param {Buffer} imageBuffer - The image file buffer
 * @param {string} originalName - Original filename
 * @param {string} mimeType - MIME type of the image
 * @param {string} publicationId - Publication ID to associate with the image
 * @returns {Promise<string|null>} - Returns the public URL of the uploaded image or null if failed
 */
async function uploadImage(imageBuffer, originalName, mimeType, publicationId) {
  try {
    // Generate unique filename
    const fileExtension = originalName.split('.').pop();
    const uniqueFileName = `${publicationId}_${randomUUID()}.${fileExtension}`;
    const key = `${IMAGE_FOLDER}/${uniqueFileName}`;

    const command = new PutObjectCommand({
      Bucket: IMAGE_BUCKET,
      Key: key,
      Body: imageBuffer,
      ContentType: mimeType,
      // Make the object publicly readable (optional, depending on your bucket policy)
      ACL: 'public-read'
    });

    await awsClient.send(command);
    
    // Construct the public URL
    const imageUrl = constructPublicUrl(key);
    
    log.info(`Image uploaded successfully: ${imageUrl}`);
    return imageUrl;

  } catch (error) {
    log.error(`Error uploading image: ${error}`);
    return null;
  }
}

/**
 * Constructs the public URL for the uploaded image
 * @param {string} key - S3 object key
 * @returns {string} - Public URL
 */
function constructPublicUrl(key) {
  const endpoint = process.env.S3_ENDPOINT;
  const region = process.env.S3_REGION;
  
  if (endpoint) {
    // For custom S3-compatible storage (like MinIO)
    return `${endpoint}/${IMAGE_BUCKET}/${key}`;
  } else {
    // For AWS S3
    return `https://${IMAGE_BUCKET}.s3.${region}.amazonaws.com/${key}`;
  }
}

/**
 * Validates if the uploaded file is a valid image
 * @param {string} mimeType - MIME type to validate
 * @returns {boolean} - True if valid image type
 */
function isValidImageType(mimeType) {
  const allowedTypes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];
  return allowedTypes.includes(mimeType);
}

/**
 * Validates image file size
 * @param {Buffer} buffer - Image buffer
 * @param {number} maxSizeInMB - Maximum size in MB (default: 5MB)
 * @returns {boolean} - True if size is valid
 */
function isValidImageSize(buffer, maxSizeInMB = 5) {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return buffer.length <= maxSizeInBytes;
}

module.exports = {
  uploadImage,
  isValidImageType,
  isValidImageSize
};
