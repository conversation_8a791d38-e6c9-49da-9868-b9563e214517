const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const constants = require('../../config/constants.json')
const log = new Logger().getInstance();
const { getBaselineForecastDataQuery, getCombinationDataQuery, getForecastCombinationDataQuery, getMetricsModelDataQuery, getMetricsTrainTestDataQuery } = require('./getQuery.service');

async function getBaselineForecastData() {
  try {
    const query = await getBaselineForecastDataQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-inflation.executeQuery.service.getBaselineForecastData with error ${err}`);
    throw err;
  }
}

async function getCombinationData(drivers) {
  try {
    const {query,binds} = await getCombinationDataQuery(drivers);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-inflation.executeQuery.service.getCombinationData with error ${err}`);
    throw err;
  }
}

async function getForecastCombinationData(combinationId) {
  try {
    const {query,binds} = await getForecastCombinationDataQuery(combinationId);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-inflation.executeQuery.service.getForecastCombinationData with error ${err}`);
    throw err;
  }
}

async function getMetricsModelData() {
  try {
    const query = await getMetricsModelDataQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-inflation.executeQuery.service.getMetricsModelData with error ${err}`);
    throw err;
  }
}

async function getMetricsTrainTestData() {
  try {
    const query = await getMetricsTrainTestDataQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-inflation.executeQuery.service.getMetricsTrainTestData with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-inflation.services.executeQuery.service.getData`);
    let data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-inflation.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-inflation.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getBaselineForecastData,
  getCombinationData,
  getForecastCombinationData,
  getMetricsModelData,
  getMetricsTrainTestData
}