const express = require('express');
const router = new express.Router();
const headerController = require('../microservice-header/header.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
    try {
      const data = await headerController.getHeader(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for header content-type, ERROR: ${err}`);
      next(err);
    }
  });

router.get('/dyanamic', async (req, res, next) => {
  try {
    const data = await headerController.getDyanamicHeader(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for dyanamic header content-type, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
