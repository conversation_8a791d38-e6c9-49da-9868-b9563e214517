const {
  assignConfigurationValuesResponse,
} = require("../microservice-statistics-insights/services/chart-configuration-services/helper");
const { IFPError } = require("../utils/error");
const { processVisualization } = require("./functions");
const {
  getProductDetail,
  listOrganizations,
  listSubscribedProducts,
} = require("./services/dmp.service");
const { createProductDataShare, getDataShare, activateDeltaShare, generateTokenHash, loadDeltaShareToken, saveDeltaShareToken } = require("./services/data-share.service")
const { getAssetTableData, getAsset } = require("./services/asset.service");
const { getAssetTableDataWrapper } = require("./services/asset.service");

async function getProductData(req, res, next) {
  const { productId } = req.params;
  // const { id } = req.params;
  const lang =
    req.headers["accept-language"] === "en"
      ? ""
      : `/${req.headers["accept-language"]}`;
  try {
    let product = await getProductDetail(req, productId);

    // Get table mapping
    const { data_map, data_share_id } = product.integrationMetadata;
    const { json_config_sf, json_config, data_table, indicator_map, overview } =
      data_map;

    const shareTokenHash = generateTokenHash(
      "FocfvtVURUmSMWQUFPlCjQ",
      productId
    );
    const shareToken = loadDeltaShareToken(shareTokenHash);
    if (!shareToken) {
      throw new IFPError(404, "Share token not found for asset");
    }

    // Get configuration and metadata
    const configData = await getAssetTableDataWrapper(req, shareToken, json_config);
    if (!configData) {
      throw new IFPError(400, "Config data not found");
    }

    const { CONFIGURATION, META_DATA } = configData[0];

    const configuration = JSON.parse(CONFIGURATION);
    const metaData = JSON.parse(
      META_DATA.replace(/\\/g, "")
        .replace(/\n/g, "\\n")
        .replace(/\t/g, "\\t")
        .replace(/_/g, " ")
    )["EN"];

    let response = {};
    response = assignConfigurationValuesResponse({}, configuration);
    response.type = "official_statistics";
    response.indicatorVisualizations = {};
    response.indicatorVisualizations.visualizationsMeta = configuration;
    response.indicatorVisualizations.visualizationDefault = configuration[0].id;
    response.tableFields = [
      {
        label: lang == "en" ? "INDICATOR ID" : "معرف المؤشر",
        path: "INDICATOR_ID",
      },
      { label: lang == "en" ? "VALUE" : "قيمة", path: "VALUE" },
      { label: lang == "en" ? "DATE" : "تاريخ", path: "OBS_DT" },
    ];

    response = await processVisualization(
      req,
      response,
      response.indicatorVisualizations.visualizationsMeta,
      data_map,
      shareToken
    );
    res.send(response);
    next();
  } catch (error) {
    next(error);
  }
}

async function navigationListController(req, res, next) {
  try {
    let organizations = [];
    const navigation = [];
    const productsList = [];
    const entityList = [];

    const classifications = [
      { key: 'official_statistics', name: 'Official Statistics' },
      { key: 'experimental_statistics', name: 'Experimental Statistics' },
      { key: 'analytical_apps', name: 'Analytical Apps' },
      { key: 'reports', name: 'Reports' }
    ]
    const [subscribedProductsData, organizationList] = await Promise.all([
      listSubscribedProducts(req),
      listOrganizations(req)
    ]);

    if (subscribedProductsData?.length) {
      subscribedProductsData.forEach((item) => {
        if (!productsList.includes(item.productId)) {
          productsList.push({productId: item.productId, organizationId: item.organizationId});
        }
        if (!entityList.includes(item.organizationId)) {
          entityList.push(item.organizationId); 
        }
      });
    }
    
    if (organizationList?.length) {
      organizations = organizationList.filter((entity) => entityList.includes(entity.id)).map((item) => {
        return {id: item.id, name: item.title}
      });
    }

    for(const classification of classifications) {
      const data = {
        key: classification.key,
        name: classification.name,
        showTree: false,
        entities: organizations.length && classification.key === 'official_statistics' ? await setEntityDetails(req, organizations, productsList) : []
      }
      navigation.push(data);
    }

    res.set("Content-Type", "application/json").send(navigation)
    next()
  } catch(error) {
    next(`Error in navigationListController: ${error}`)
  }
}

async function setEntityDetails(req, entityList, products) {
  try {
    let entityData = [];
    for(const entity of entityList) {
      const data = {
        id: entity.id,
        name: entity.name,
        showTree: false,
        nodes: await getNodeList(req, entity.id, products),
        // route: `/domain-exploration/${entity.name}/${entity.id}`
      }
      entityData.push(data);
    }
    return entityData;
  } catch (error) {
    next(`Error in setEntityDetails: ${error}`)
  }
}

async function getNodeList(req, id, products) {
  try {
    let nodeData = products.filter((product) => product.organizationId === id);
    let result = [];
    for(const product of nodeData) {
      const data = await getProductDetail(req, product.productId);
      const item = {
        id: product.productId,
        // id: 6834,
        content_type: 'dmp',
        app_type: 'dmp',
        title: data.displayName,
        route: `/statistics-insights/scad_official_indicator/6834`,
      }
      result.push(item)
    }
    return result;
  } catch (error) {
    next(`Error in getNodeList: ${error}`)
  }
}

async function getOrCreateDeltaShareToken(req, res, next) {
  try {
    const { productId } = req.params;

    // check and return if exists
    const tokenHash = generateTokenHash("FocfvtVURUmSMWQUFPlCjQ", productId);
    let token = loadDeltaShareToken(tokenHash);

    // if not exists, create new of user-product combo, activate and return token
    if (!token) {
      const productShareId = await createProductDataShare(
        req,
        `${productId}_share`,
        productId
      );
      const share = await getDataShare(req, productShareId);
      const activationUrl = share.details.tokens[0].activationUrl; // QUESTION: can one share have multiple tokens?
      const { shareName } = share.details;
      token = await activateDeltaShare(activationUrl);
      // save share token
      saveDeltaShareToken(tokenHash, { shareName, ...token });
    }

    res.send(token);
    next();
  } catch (error) {
    next(error);
  }
}

async function getAssetData(req, res, next) {
  try {
    const { productId, assetId } = req.params;

    const shareTokenHash = generateTokenHash(
      "FocfvtVURUmSMWQUFPlCjQ",
      productId
    );
    const shareToken = loadDeltaShareToken(shareTokenHash);
    if (!shareToken) {
      throw new IFPError(404, "Share token not found for asset");
    }
    const { shareName, ...shareCredentials } = shareToken;

    // Get asset details
    const asset = await getAsset(req, assetId);
    const assetCategory = asset.integrationMetadata.bayaan_category.value;
    let tableName = asset.edges[0].definition.resource_id.fullName;
    const tablePath = `${JSON.stringify(
      shareCredentials
    )}#${shareName}.${tableName.split(".").slice(1).join(".")}`;

    const tableData = await getAssetTableData(
      assetId,
      assetCategory,
      tablePath
    );
    res.send(tableData);
    next();
  } catch (error) {
    next(error);
  }
}

module.exports = {
  getProductData,
  navigationListController,
  getOrCreateDeltaShareToken,
  getAssetData
};
