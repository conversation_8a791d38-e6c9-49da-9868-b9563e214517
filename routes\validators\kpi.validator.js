const { IFPError } = require('../../utils/error');

const validateQueryStrings = (req,res,next)=>{
    const { limit = 10, offset = 0, isPaginated = 'true' } = req.query;
    let numbers = [limit, offset];

    if (limit == 0) {
        throw new IFPError(400, 'Limit cannot be zero');
    }

    if (isPaginated.toLowerCase() !== 'true' && isPaginated.toLowerCase() !== 'false') {
        throw new IFPError(400, '"isPaginated" value must be "true" or "false"');
    }

    let isValidNumber = numbers.every(num => num >= 0);
    if(!isValidNumber)
        throw new IFPError(400, 'All numbers must be non-negative');

    req.query.isPaginated = JSON.parse(isPaginated);

    next()
}

module.exports = {
    validateQueryStrings
}