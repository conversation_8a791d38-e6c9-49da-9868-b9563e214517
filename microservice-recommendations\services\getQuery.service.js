const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json');


async function getLatestRecommendationsDataQuery(nodes){
  return new Promise((resolve, reject) => {
    try {
        let nodeChunks = [];
        const chunkSize = 1000;
        for (let i = 0; i < nodes.length; i += chunkSize) {
            const chunk = nodes.slice(i, i + chunkSize);
            nodeChunks.push(chunk.map(node => `'${node}'`).join(','));
        }

        nodeChunks = nodeChunks.map(nodeChunk => ` INDICATOR_ID IN (${nodeChunk})`).join(' OR')

        // let query = `  SELECT TOPIC_NAME_ENGLISH,
        //                       INDICATOR_ID,
        //                       MAX_OBS_DT,
        //                       rnk2
        //                 FROM (SELECT TOPIC_NAME_ENGLISH,
        //                               INDICATOR_ID,
        //                               MAX_OBS_DT,
        //                               ROW_NUMBER ()
        //                                   OVER (PARTITION BY TOPIC_NAME_ENGLISH
        //                                         ORDER BY MAX_OBS_DT DESC)            AS rnk,
        //                               ROW_NUMBER ()
        //                                   OVER (PARTITION BY TOPIC_NAME_ENGLISH
        //                                         ORDER BY TOPIC_NAME_ENGLISH DESC)    rnk2
        //                         FROM (SELECT T.TOPIC_NAME_ENGLISH, S.INDICATOR_ID, S.MAX_OBS_DT
        //                                 FROM STATISTICAL_IND_HIERARCHY T
        //                                       RIGHT JOIN
        //                                       (  SELECT INDICATOR_ID, MAX (OBS_DT) AS MAX_OBS_DT
        //                                           FROM VW_STATISTICAL_INDICATORS
        //                                           WHERE ${nodeChunks}
        //                                       GROUP BY INDICATOR_ID
        //                                       ORDER BY MAX (OBS_DT) DESC) S
        //                                           ON T.STAT_VALUE_ID = S.INDICATOR_ID)) ranked
        //                 WHERE rnk <= 7
        //               ORDER BY rnk2
        //                 FETCH FIRST 7 ROWS ONLY`

        let query = `WITH ranked AS (
 
          SELECT 
              T.TOPIC_NAME_ENGLISH,
              S.INDICATOR_ID,
              S.MAX_OBS_DT,
              ROW_NUMBER() OVER (PARTITION BY T.TOPIC_NAME_ENGLISH ORDER BY S.MAX_OBS_DT DESC) AS rnk,
              ROW_NUMBER() OVER (PARTITION BY T.TOPIC_NAME_ENGLISH ORDER BY T.TOPIC_NAME_ENGLISH DESC) AS rnk2
          FROM STATISTICAL_IND_HIERARCHY T
          RIGHT JOIN (
              SELECT 
                  INDICATOR_ID, 
                  MAX(OBS_DT) AS MAX_OBS_DT
              FROM VW_STATISTICAL_INDICATORS
              WHERE ${nodeChunks}
              GROUP BY INDICATOR_ID
              ORDER BY MAX(OBS_DT) DESC
          ) S ON T.STAT_VALUE_ID = S.INDICATOR_ID
      )
      SELECT 
          TOPIC_NAME_ENGLISH,
          INDICATOR_ID,
          MAX_OBS_DT,
          rnk2
      FROM ranked
      WHERE rnk <= 7
      ORDER BY rnk2
      LIMIT 7;
      `

        return resolve({ query: query, binds: {} });
        
    } catch (err) {
      
        log.error(`<<<<<< Exited microservice-recommendations.services.getQuery.service.getLatestRecommendationsData with error ${err} `);
        reject([424, err]);
    }
});
}
module.exports = { 
  getLatestRecommendationsDataQuery }