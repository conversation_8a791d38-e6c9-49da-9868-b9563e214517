const express = require('express');
const router = new express.Router();
const classificationController = require('../microservice-classification/classification.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
    try {
      const data = await classificationController.getClassification(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for classification content-type, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
