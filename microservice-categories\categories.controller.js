const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const Logger = require('scad-library').logger;

const log = new Logger().getInstance();
let constants = require('../config/constants.json');
/**
 * function to get categories content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getCategories(req) {
    log.debug(`>>>>>Entered domains-microservice.domains.controller.getCategories`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CATEGORY_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups);
        log.debug(`<<<<<Exited domains-microservice.domains.controller.getCategories successfully `);
        const categories = Object.values(data.categories).map((category) => {
            const categoryObj = {
                id: category.id,
                name: category.name,
                icon_path: `${process.env.CMS_BASEPATH_URL}${category.icon_path}`,
                light_icon_path: `${process.env.CMS_BASEPATH_URL}${category.light_icon_path}`,
                node_count: category.nodes_count,
                isSelected: false
            }
            return categoryObj;
        })


        return categories;
    } catch (err) {
        log.error(`<<<<<Exited domains-microservice.domains.controller.getCategories on getting CMS data with error ${err}`);
        throw err;
    }
}
module.exports = { getCategories };
