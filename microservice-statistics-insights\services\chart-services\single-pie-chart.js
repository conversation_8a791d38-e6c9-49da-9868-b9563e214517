const scadLib = require('scad-library');
const { util } = scadLib;
const Logger = scadLib.logger;
const log = new Logger().getInstance();
const moment = require('moment');
const { getPercentageChange } = require('../getGraphData');


async function getSinglePieSeries(visualization, results, type) {
    log.debug('>>>>> Enter services.chart-services.single-pie-chart.getStackedVerticalBarData');
    if (type == 'geospatial') {
        return visualization;
    } else {
        return new Promise((resolve, reject) => {
            try {
                visualization.seriesMeta.forEach(series => {
                    series["data"] = [];
                });
                let limit = visualization.seriesMeta.length;
                results = results.filter(e => moment(e.OBS_DT).format('YYYY-MM-DD') === moment(results[results.length - 1].OBS_DT).format('YYYY-MM-DD'));
                results = results.sort((a, b) => (a.VALUE > b.VALUE) ? -1 : ((b.VALUE > a.VALUE) ? 1 : 0));
                let topCategories = results.splice(0, limit);
                if (visualization.dbPercentageChange) {
                    let categoryList = [];
                    topCategories.forEach(element => {
                        categoryList.push(element[visualization.dbPercentageChange.mappingColumn]);
                    });
                    getPercentageChange(visualization.dbPercentageChange, categoryList).then(percentageData => {
                        percentageData = percentageData.filter(e => moment(e.OBS_DT).format('YYYY-MM-DD') === moment(percentageData[percentageData.length - 1].OBS_DT).format('YYYY-MM-DD'))
                        topCategories.forEach(category => {
                            percentageData.forEach(element => {
                                if (category[visualization.dbPercentageChange.mappingColumn] === element[visualization.dbPercentageChange.mappingColumn]) {
                                    category["PERCENTAGE"] = element.VALUE;
                                }
                            })
                        });
                        if (limit === topCategories.length) {
                            visualization.seriesMeta.forEach((series, index) => {
                                topCategories[index].OBS_DT = util.convertDate(topCategories[index].OBS_DT);
                                Object.entries(topCategories[index]).forEach(([key, value]) => {
                                    if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                                        delete topCategories[index][key];
                                    }
                                });
                                series.data.push(topCategories[index]);
                            })
                            resolve(visualization);
                        }
                    })
                } else {
                    if (limit === topCategories.length) {
                        visualization.seriesMeta.forEach((series, index) => {
                            topCategories[index].OBS_DT = util.convertDate(topCategories[index].OBS_DT);
                            visualization.seriesMeta[index].data.push(topCategories[index]);
                        })
                        resolve(visualization);
                    }
                }
            } catch (err) {
                reject(err);
            }
        });
    }
};

async function getSinglePieSeriesWithFilter(visualization, results, type) {
    log.debug('>>>>> Enter services.chart-services.single-pie-chart.getStackedVerticalBarData');
    if (type == 'geospatial') {
        return visualization;
    } else {
        return new Promise((resolve, reject) => {
            try {
                let categoryAccessorPath = ''
                visualization.seriesMeta.forEach(series => {
                    series["data"] = [];
                    categoryAccessorPath = series.categoryAccessor.path
                });

                results = results.filter(e => moment(e.OBS_DT).format('YYYY-MM-DD') === moment(results[results.length - 1].OBS_DT).format('YYYY-MM-DD'));
                results = results.sort((a, b) => (a.VALUE > b.VALUE) ? -1 : ((b.VALUE > a.VALUE) ? 1 : 0));

                let categoryTypes = new Set();
                results.forEach(element => {
                    categoryTypes.add(element[categoryAccessorPath])
                });
                categoryTypes = Array.from(categoryTypes)
                let topCategories = results
                if (visualization.dbPercentageChange) {
                    let categoryList = [];
                    topCategories.forEach(element => {
                        categoryList.push(element[visualization.dbPercentageChange.mappingColumn]);
                    });
                    getPercentageChange(visualization.dbPercentageChange, categoryList).then(percentageData => {
                        percentageData = percentageData.filter(e => moment(e.OBS_DT).format('YYYY-MM-DD') === moment(percentageData[percentageData.length - 1].OBS_DT).format('YYYY-MM-DD'))
                        matchKeys = visualization.matchKeys
                        topCategories.forEach(category => {
                            percentageData.forEach(element => {
                                let isMatched = true
                                matchKeys.forEach(mKey => {
                                    isMatched = isMatched && (category[mKey] == element[mKey])
                                });
                                if (category[visualization.dbPercentageChange.mappingColumn] === element[visualization.dbPercentageChange.mappingColumn] && isMatched) {
                                    category["PERCENTAGE"] = element.VALUE;
                                }
                            })
                        });

                        visualization.seriesMeta.forEach((series, index) => {
                            topCategories[index].OBS_DT = util.convertDate(topCategories[index].OBS_DT);
                            Object.entries(topCategories[index]).forEach(([key, value]) => {
                                if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                                    delete topCategories[index][key];
                                }
                            });
                            topCategories.forEach(element => {
                                if (categoryTypes[index] == element[series.categoryAccessor.path])
                                    series.data.push(element);
                            });
                        })
                        resolve(visualization);
                    })
                } else {
                    if (limit === topCategories.length) {
                        visualization.seriesMeta.forEach((series, index) => {
                            topCategories[index].OBS_DT = util.convertDate(topCategories[index].OBS_DT);
                            visualization.seriesMeta[index].data.push(topCategories[index]);
                        })
                        resolve(visualization);
                    }
                }
            } catch (err) {
                reject(err);
            }
        });
    }
};

module.exports = {
    getSinglePieSeriesWithFilter,
    getSinglePieSeries
};
