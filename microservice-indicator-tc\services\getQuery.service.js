const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function setIndicatorTCDataQuery(nodeId,userEmail) {
    try {
        let binds = {
            nodeId: nodeId,
            userEmail: userEmail
        }
        let query = `INSERT INTO IFP_INDICATOR_TC (NODE_ID, EMAIL) VALUES (:nodeId, :userEmail)`;
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-indicator-tc.services.getQuery.service.setIndicatorTCDataQuery with error ${err} `);
        throw err;
    }
}

async function getIndicatorTCStatusDataQuery(nodeId, userEmail) {
    try {
        let binds = {
            nodeId: nodeId,
            userEmail: userEmail
        };

        let query = `SELECT * FROM IFP_INDICATOR_TC WHERE EMAIL = :userEmail AND NODE_ID = :nodeId`;
        
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-indicator-tc.services.getQuery.service.getIndicatorTCStatusDataQuery with error ${err} `);
        throw err;
    }
}

module.exports = { 
    setIndicatorTCDataQuery,
    getIndicatorTCStatusDataQuery
 };
