const { IFPError } = require("../../utils/error");
const { getRoleGroupCode } = require("../helper");

const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

async function getEntityListQuery() {
  try {
    let query = `
    SELECT
      A.ID,
      A.NAME,
      A.DOMAIN AS "DOMAINS",
      A.CREATED_AT
    FROM
        IFP_ENTITY_LOOKUP A
    JOIN IFP_DISS_ACCESS_POLICY B ON
        A.ID = B.ENTITY_ID
    GROUP BY
        A.ID, A.NAME, A.DOMAIN, A.CREATED_AT
    ORDER BY
        A.NAME
    `;

    return { query: query, binds: {} };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getEntityListQuery with error ${err} `
    );
    throw err;
  }
}

async function getEntityDetailsQuery(entityId) {
  try {
    let binds = {
      entityId: entityId,
    };
    let query = `SELECT * FROM IFP_ENTITY_LOOKUP WHERE ID = :entityId`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getEntityDetailsQuery with error ${err} `
    );
    throw err;
  }
}

async function createInvitationQuery(
  requestId,
  phone,
  userEmail,
  emailMasked,
  entityID,
  inviteType,
  designation,
  platformAccess,
  requestedAccess
) {
  try {
    const query = `
          INSERT INTO IFP_INVITATIONS_V2 (REQUEST_ID, USER_EMAIL, EMAIL_MASKED,PHONE,ENTITY_ID, STATUS, INVITE_TYPE, CREATED_AT, EXPIRES_AT, INVITEE_DESIGNATION, PLATFORM_ACCESS, REQUESTED_ACCESS)
          VALUES (:requestId, :userEmail, :emailMasked,:phone,:entityID,'PENDING',:inviteType, SYSDATE, SYSDATE + INTERVAL '2' DAY, :designation, :platformAccess, :requestedAccess)
        `;
    const binds = {
      requestId,
      phone,
      userEmail,
      emailMasked,
      entityID,
      inviteType,
      designation,
      platformAccess,
      requestedAccess
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createInvitationQuery with error ${err} `
    );
    throw err;
  }
}

async function listPendingInvitationsQuery(entityId, roles) {
  let binds = {};
  let entityQuery = "";
  let rolesQuery = "";

  if (entityId) {
    entityQuery = ` AND iiv.ENTITY_ID=:entityId`;
    binds.entityId = entityId;
  }

  if (roles && Array.isArray(roles) && roles.length > 0) {
    const rolesPlaceholders = roles.map((_, index) => `:role${index}`).join(", ");
    rolesQuery = ` AND iiv.INVITE_TYPE IN (${rolesPlaceholders})`;
    roles.forEach((role, index) => {
      binds[`role${index}`] = role;
    });
  }

  const query = `
  SELECT
    iiv.REQUEST_ID AS "invitationId",
    iiv.USER_EMAIL AS "inviteeEmail",
    iiv.INVITE_TYPE AS "inviteeRole",
    iiv.PHONE AS "inviteePhone",
    iiv.INVITEE_DESIGNATION AS "inviteeDesignation",
    iel.NAME AS "entityName",
    iel.DOMAIN AS "entityDomain",
    CASE 
      WHEN iiv.STATUS = 'PENDING' THEN 'registration_pending'
      WHEN ifuv.ACTIVATION_FLAG = 'ENTRA_ID_INVITE_SENT' THEN 'awaiting_entraid'
    END AS "status",
    iiv.PLATFORM_ACCESS AS "platformAccess",
    iiv.CREATED_AT AS "createdAt"
  FROM IFP_INVITATIONS_V2 iiv
  LEFT JOIN IFP_FLOW_USERS_V2 ifuv 
    ON iiv.USER_EMAIL = ifuv.EMAIL
  LEFT JOIN IFP_ENTITY_LOOKUP iel 
    ON iel.ID = iiv.ENTITY_ID
  WHERE
    (iiv.STATUS = 'PENDING' OR ifuv.ACTIVATION_FLAG = 'ENTRA_ID_INVITE_SENT')
    ${entityQuery}
    ${rolesQuery}
  ORDER BY "createdAt" DESC
  `;
  return { query, binds };
}

function deleteInvitationQuery(requestId) {
  const binds = { requestId };
  const query = `
    DELETE FROM IFP_INVITATIONS_V2 iiv WHERE iiv.REQUEST_ID = :requestId
  `;
  return { query, binds };
}

function updateInvitationQuery(requestId, data) {
  const binds = { requestId };

  let setClauses = [];
  for (let [column, value] of Object.entries(data)) {
    if (value !== undefined) {
      setClauses.push(`${column} = :${column}`);
      binds[column] = value;
    }
  }

  let setQuery = setClauses.join(", ");

  let query = `UPDATE IFP_INVITATIONS_V2 SET ${setQuery} WHERE REQUEST_ID = :requestId`;

  return { query, binds };
}

async function createEIDInvitationMappingQuery(
  requestId,
  hashedEID,
  encryptedEmail
) {
  try {
    const query = `
          INSERT INTO ifp_eid_request_mapping (REQUEST_ID, EID,EMAIL)
          VALUES (?, ?, ?)
        `;
    const binds = [
      requestId,
      hashedEID,
      encryptedEmail
    ];

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createEIDInvitationMappingQuery with error ${err} `
    );
    throw err;
  }
}

async function getEIDInvitationMappingQuery(requestId) {
    try {
      const query = `
            SELECT * FROM ifp_eid_request_mapping WHERE REQUEST_ID=?`;
      const binds = [requestId];
  
      return { query: query, binds: binds };
    } catch (err) {
      log.error(
        `<<<<<< Exited microservice-user.services.getQuery.service.getEIDInvitationMappingQuery with error ${err} `
      );
      throw err;
    }
}

async function getEIDInvitationMappingByEIDQuery(hashedEID) {
  try {
    const query = `
          SELECT * FROM ifp_eid_request_mapping WHERE EID=? ORDER BY CREATED_DT DESC`;
    const binds = [hashedEID];

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `Error with getEIDInvitationMappingByEIDQuery with error ${err} `
    );
    throw err;
  }
}

async function getInvitationQuery(requestId) {
  try {
    let binds = {
      requestId: requestId,
    };
    let query = `
    SELECT
      *
    FROM
      IFP_INVITATIONS_V2 iiv
    JOIN IFP_ENTITY_LOOKUP iel 
      ON iiv.ENTITY_ID = iel.ID 
    WHERE
      REQUEST_ID = :requestId
      AND STATUS NOT IN ('INVALID')
    `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getInvitationQuery with error ${err} `
    );
    throw err;
  }
}

function getInvitationV2Query(filters) {
  try {
    let query = "SELECT * FROM IFP_INVITATIONS_V2";
    const binds = {};
    const conditions = [];

    Object.keys(filters).forEach((field) => {
      const values = filters[field];
      if (Array.isArray(values)) {
        const placeholders = values
          .map((_, index) => `:${field}${index}`)
          .join(", ");
        conditions.push(`${field} IN (${placeholders})`);
        values.forEach((value, index) => {
          binds[`${field}${index}`] = value;
        });
      } else {
        conditions.push(`${field} = :${field}`);
        binds[field] = values;
      }
    });

    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }

    query += " ORDER BY CREATED_AT DESC";

    return { query, binds };
  } catch (err) {
    log.error(`Error with getInvitationV2Query: ${err}`);
    throw err;
  }
}

/**
 * Check if 'PENDING' invite already exists with EITHER
 * email or phone specified.
 * @param {string} email 
 * @param {string} phoneNumber 
 * @returns 
 */
function checkExistingInvitationsQuery(email, phoneNumber) {
  let binds = {};
  let whereConditions = [];
  let query = `
  SELECT
    *
  FROM
    IFP_INVITATIONS_V2 iiv
  WHERE
    STATUS = 'PENDING' AND (
  `;

  if (email) {
    binds.email = email;
    whereConditions.push("USER_EMAIL = :email");
  }

  if (phoneNumber) {
    binds.phoneNumber = phoneNumber;
    whereConditions.push("PHONE = :phoneNumber");
  }

  if (whereConditions.length > 1) {
    query += whereConditions.join(" OR ");
  } else {
    query += whereConditions.join(" ");
  }

  query += " )"

  return { query, binds };
}

function getEntityInvitesQuery(type, status,entity){
  const binds = { type, status,entity };
  const query = `
    SELECT
      *
    FROM
      IFP_INVITATIONS_V2
    WHERE
      INVITE_TYPE = :type
      AND STATUS = :status
      AND ENTITY_ID = :entity
  `;
  return { query, binds };
}

async function getDGOnboardingStatusQuery(entityId) {
  const binds = { entityId };
  const query = `
    SELECT
      iiv.STATUS AS "invitationStatus",
      ifuv.STATUS AS "registrationStatus",
      ifuv.EXISTING_USER AS "isExistingUser"
    FROM
      IFP_FLOW_USERS_V2 ifuv
    LEFT JOIN IFP_INVITATIONS_V2 iiv ON
      iiv.USER_EMAIL = ifuv.EMAIL
    WHERE
      (iiv.INVITE_TYPE = 'DG' OR ifuv."ROLE" = 'DG')
      AND (iiv.ENTITY_ID = :entityId OR ifuv.ENTITY_ID = :entityId)
      AND ifuv.ACTIVATION_FLAG = 'ACTIVE'
    ORDER BY
      iiv.CREATED_AT DESC
  `;
  return { query, binds };
}

async function updateInvitationStatusQuery(requestId, status) {
  try {
    let binds = {
      requestId: requestId,
      status: status,
    };
    let query = `UPDATE IFP_INVITATIONS_V2 SET STATUS=:status WHERE REQUEST_ID = :requestId`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.updateInvitationStatusQuery with error ${err} `
    );
    throw err;
  }
}

async function createUserQuery(
  userId,
  name,
  email,
  phoneNumber,
  role,
  entityId,
  designation,
  linkAndNdaStatus,
  requestJustification,
  mobileSyncStatus,
) {
  try {
    const binds = {
      userId: userId,
      name: name,
      email: email,
      phoneNumber: phoneNumber,
      role: role,
      entityId: entityId,
      designation: designation,
      linkAndNdaStatus,
      requestJustification,
      mobileSyncStatus
    };
    const query = `
      INSERT INTO IFP_FLOW_USERS_V2 (ID, NAME, EMAIL, PHONE_NUMBER,DESIGNATION, ROLE, ENTITY_ID, STATUS, ACTIVATION_FLAG, CREATED_AT, UPDATED_AT, EXISTING_USER, REQUEST_JUSTIFICATION, MOBILE_SYNC_STATUS)
      SELECT :userId, :name, :email, :phoneNumber,:designation, :role, :entityId, 'REGISTERED', 'PENDING', SYSDATE, SYSDATE, :linkAndNdaStatus, :requestJustification, :mobileSyncStatus
      FROM DUAL
      WHERE NOT EXISTS (SELECT 1 FROM IFP_FLOW_USERS_V2 WHERE ID = :userId)
    `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createUserQuery with error ${err} `
    );
    throw err;
  }
}

async function createEIDUserQuery(
  userId,
  maskedEID,
  email
) {
  try {
    const binds = {
      userId: userId,
      maskedEID: maskedEID,
      email: email
    }
    const query = `
      INSERT INTO IFP_EID_USERS (USER_ID, MASKED_EID, EMAIL, CREATED_AT)
      SELECT :userId, :maskedEID, :email, SYSDATE
      FROM DUAL
      WHERE NOT EXISTS (SELECT 1 FROM IFP_EID_USERS WHERE USER_ID = :userId)
    `;
    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createEIDUserQuery with error ${err} `
    );
    throw err;
  }
}

async function getUserDataQuery(email) {
  try {
    const binds = {
      email: email,
    };
    const query = `
      SELECT * FROM IFP_FLOW_USERS_V2 WHERE EMAIL=:email
    `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getUserDataQuery with error ${err} `
    );
    throw err;
  }
}

async function getUserDataV2Query(filters) {
  try {
    let query = "SELECT * FROM IFP_FLOW_USERS_V2";
    const binds = {};
    const conditions = [];

    Object.keys(filters).forEach((field) => {
      const values = filters[field];
      if (Array.isArray(values)) {
        const placeholders = values
          .map((_, index) => `:${field}${index}`)
          .join(", ");
        conditions.push(`${field} IN (${placeholders})`);
        values.forEach((value, index) => {
          binds[`${field}${index}`] = value;
        });
      } else {
        conditions.push(`${field} = :${field}`);
        binds[field] = values;
      }
    });

    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }

    return { query, binds };
  } catch (err) {
    console.error(`Error in getUserDataQuery: ${err}`);
    throw err;
  }
}

async function getUserDataV3Query(filters, logicalOperator) {
  try {
    let query = "SELECT * FROM IFP_FLOW_USERS_V2";
    const binds = {};
    const conditions = [];

    Object.keys(filters).forEach((field) => {
      const values = filters[field];
      if (Array.isArray(values)) {
        const placeholders = values
          .map((_, index) => `:${field}${index}`)
          .join(", ");
        conditions.push(`${field} IN (${placeholders})`);
        values.forEach((value, index) => {
          binds[`${field}${index}`] = value;
        });
      } else {
        conditions.push(`${field} = :${field}`);
        binds[field] = values;
      }
    });

    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(` ${logicalOperator} `);
    }

    return { query, binds };
  } catch (err) {
    console.error(`Error in getUserDataV3Query: ${err}`);
    throw err;
  }
}

async function updateUserStatusQuery(userId, status) {
  try {
    let binds = {
      userId: userId,
      status: status,
    };
    let query = `UPDATE IFP_FLOW_USERS_V2 SET STATUS=:status WHERE ID = :userId`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.updateUserStatusQuery with error ${err} `
    );
    throw err;
  }
}

/**
 * Update user
 * @param {string} updateByField Field which record to be updated.
 * Either ID or EMAIL.
 * @param {string} identifier Value for the updateByField
 * @param {object} updateFields Object of key value pairs where key
 * is the column name and value the value to be updated
 * @returns 
 */
function updateUserDataQuery(updateByField, identifier, updateFields) {
  try {
    let binds = { identifier };

    let setClauses = [];
    for (let [column, value] of Object.entries(updateFields)) {
      if (value !== undefined) {
        setClauses.push(`${column} = :${column}`);
        binds[column] = value;
      }
    }

    let setQuery = setClauses.join(", ");

    let whereClause = "WHERE ID = :identifier";
    if (updateByField == "EMAIL") {
      whereClause = "WHERE EMAIL = :identifier";
    } else if (updateByField == "MOBILE") {
      whereClause = "WHERE PHONE_NUMBER = :identifier"
    }
    let query = `UPDATE IFP_FLOW_USERS_V2 SET ${setQuery} ${whereClause}`;

    return { query, binds };
  } catch (err) {
    log.error(`Error with updateUserDataQuery with error ${err}`);
    throw err;
  }
}

async function setUserDeleteStatusQuery(userId, deletedById) {
  try {
    let binds = {
      userId: userId,
      deletedById: deletedById,
    };
    let query = `UPDATE IFP_FLOW_USERS_V2 SET STATUS='DELETED', deleted_by = :deletedById, deleted_at = SYSTIMESTAMP WHERE ID = :userId`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.setUserDeleteStatusQuery with error ${err} `
    );
    throw err;
  }
}

async function setUserActivationStatusQuery(userId, status) {
  try {
    const query = `
          UPDATE IFP_FLOW_USERS_V2 SET
          ACTIVATION_FLAG = :status, UPDATED_AT = SYSDATE
          WHERE EMAIL = :userId
        `;
    const binds = {
      userId,
      status,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.setUserActivationStatusQuery with error ${err} `
    );
    throw err;
  }
}

//Get the active superuser from the table
async function getActiveSuperuserQuery(userEmail) {
  try {
    const query = `
          SELECT ID, ROLE, ENTITY_ID, EMAIL, DESIGNATION FROM IFP_FLOW_USERS_V2 WHERE EMAIL = :userEmail AND ACTIVATION_FLAG='ACTIVE' AND ROLE IN ('PRIMARY_SUPERUSER','SECONDARY_SUPERUSER')
        `;
    const binds = {
      userEmail,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getActiveSuperuserQuery with error ${err} `
    );
    throw err;
  }
}

//Get the active DG from the table
async function getActiveDGUserQuery(userEmail) {
  try {
    const query = `
          SELECT ID, ROLE, ENTITY_ID, EMAIL, DESIGNATION FROM IFP_FLOW_USERS_V2 WHERE EMAIL = :userEmail AND ACTIVATION_FLAG='ACTIVE' AND ROLE = 'DG'
        `;
    const binds = {
      userEmail,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getActiveDGUserQuery with error ${err} `
    );
    throw err;
  }
}

//Get the active DG from the table
async function getActivePEUserQuery(userEmail) {
  try {
    const query = `
          SELECT ID, ROLE, ENTITY_ID, EMAIL, DESIGNATION FROM IFP_FLOW_USERS_V2 WHERE EMAIL = :userEmail AND ACTIVATION_FLAG='ACTIVE' AND ROLE = 'PRIMARY_PE_USER'
        `;
    const binds = {
      userEmail,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getActivePEUserQuery with error ${err} `
    );
    throw err;
  }
}

async function getActiveUserByEmailQuery(userEmail) {
  try {
    const query = `
          SELECT ID, ROLE,EMAIL, ENTITY_ID FROM IFP_FLOW_USERS_V2 WHERE EMAIL = :userEmail AND ACTIVATION_FLAG = 'ACTIVE'
        `;
    const binds = {
      userEmail,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getActiveUserByEmailQuery with error ${err} `
    );
    throw err;
  }
}

async function getActiveUserByIdQuery(userId) {
  try {
    const query = `
          SELECT ID, ROLE,EMAIL, ENTITY_ID FROM IFP_FLOW_USERS_V2 WHERE ID = :userId AND ACTIVATION_FLAG = 'ACTIVE'
        `;
    const binds = {
      userId,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getActiveUserByIdQuery with error ${err} `
    );
    throw err;
  }
}

async function createUserAccessRequestQuery(
  requestId,
  userId
) {
  try {
    const query = `
          INSERT INTO IFP_USER_ACCESS_REQUEST (REQUEST_ID,USER_ID, STATUS)
          VALUES (:requestId,:userId,'PENDING')
        `;
    const binds = {
      requestId,
      userId
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createUserAccessRequestQuery with error ${err} `
    );
    throw err;
  }
}

async function updateUserAccessRequestStatusQuery(
  requestId,
  status
) {
  try {
    const query = `
          UPDATE IFP_USER_ACCESS_REQUEST SET STATUS=:status WHERE REQUEST_ID=:requestId
        `;
    const binds = {
      requestId,
      status
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.updateUserAccessRequestStatusQuery with error ${err} `
    );
    throw err;
  }
}

async function updateUserAccessRequestApproverLevelQuery(
  requestId,
  status
) {
  try {
    const query = `
          UPDATE IFP_USER_ACCESS_REQUEST SET STATUS=:status WHERE REQUEST_ID=:requestId
        `;
    const binds = {
      requestId,
      status
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.updateUserAccessRequestStatusQuery with error ${err} `
    );
    throw err;
  }
}

async function updateUserAccessRequestIntraIdStatusQuery(
  requestId,
  status
) {
  try {
    const query = `
          UPDATE IFP_USER_ACCESS_REQUEST SET INTRA_ID_ASSIGN_STATUS=:status, INTRA_ID_ASSIGN_DT=SYSDATE WHERE REQUEST_ID=:requestId
        `;
    const binds = {
      requestId,
      status
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.updateUserAccessRequestStatusQuery with error ${err} `
    );
    throw err;
  }
}

async function updateUserAccessRequestUserIdQuery(currUserId, newUserId) {
  const binds = { currUserId, newUserId }
  const query = `
    UPDATE IFP_USER_ACCESS_REQUEST
    SET USER_ID = :newUserId
    WHERE USER_ID = :currUserId
  `;
  return { query, binds };
}

async function createUserAccessQuery(
  requestId,
  accessId,
  userId,
  domain,
  accessLevel,
  accessOperation
) {
  try {
    const query = `
          INSERT INTO IFP_USER_ACCESS (REQUEST_ID,ACCESS_ID, USER_ID, DOMAIN, ACCESS_LEVEL,ACCESS_OPERATION, CREATED_AT, UPDATED_AT)
          VALUES (:requestId,:accessId,:userId, :domain, :accessLevel,:accessOperation, SYSDATE, SYSDATE)
        `;
    const binds = {
      requestId,
      accessId,
      userId,
      domain,
      accessLevel,
      accessOperation
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createUserAccessQuery with error ${err} `
    );
    throw err;
  }
}

async function deleteUserAccessQuery(userId, filters) {
  try {
    let whereConditions = [`USER_ID=:userId`];
    const binds = { userId };

    // Construct dynamic WHERE conditions based on filters
    if (filters && typeof filters === "object") {
      Object.keys(filters).forEach((key) => {
        const value = filters[key];

        if (Array.isArray(value)) {
          // If the filter value is an array, use IN condition
          whereConditions.push(`UPPER(${key}) IN (:${key})`);
          binds[key] = value.map((v) => v.toUpperCase());;
        } else {
          // Simple equality condition
          whereConditions.push(`UPPER(${key})=UPPER(:${key})`);
          binds[key] = value;
        }
      });
    }

    const query = `
      DELETE FROM IFP_USER_ACCESS 
      WHERE ${whereConditions.join(" AND ")}
    `;

    return { query, binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.deleteUserAccessQuery with error ${err} `
    );
    throw err;
  }
}

async function updateUserAccessUserIdQuery(currUserId, newUserId) {
  const binds = { currUserId, newUserId }
  const query = `
    UPDATE IFP_USER_ACCESS
    SET USER_ID = :newUserId
    WHERE USER_ID = :currUserId
  `;
  return { query, binds };
}

async function deleteUserAccessRequestsQuery(userId) {
  try {
    
    const query = `
      DELETE FROM IFP_USER_ACCESS_REQUEST WHERE USER_ID=:userId
    `;

    const binds = { userId };

    return { query, binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.deleteUserAccessRequestsQuery with error ${err} `
    );
    throw err;
  }
}

async function deleteUserDataQuery(userId) {
  try {
    const query = `DELETE FROM IFP_FLOW_USERS_V2 WHERE ID = :userId`;
    const binds = {
      userId,
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.deleteUserDataQuery with error ${e} `);
    throw e;
  }
}

async function updatePrimarySUQuery(userId) {
  try {
    const query = `UPDATE IFP_FLOW_USERS_V2 SET ROLE='PRIMARY_SUPERUSER' WHERE ID = :userId`;
    const binds = {
      userId,
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.updatePrimarySUQuery with error ${e} `);
    throw e;
  }
}

async function updateSecondarySUQuery(userId) {
  try {
    const query = `UPDATE IFP_FLOW_USERS_V2 SET ROLE='SECONDARY_SUPERUSER' WHERE ID = :userId`;
    const binds = {
      userId,
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.updateSecondarySUQuery with error ${e} `);
    throw e;
  }
}


async function getSecondarySUQuery(entityId) {
  try {
    const query = `SELECT * FROM IFP_FLOW_USERS_V2 WHERE ROLE='SECONDARY_SUPERUSER' AND ENTITY_ID=:entityId`;
    const binds = {
      entityId
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.getSecondarySUQuery with error ${e} `);
    throw e;
  }
}

async function updatePrimaryPEQuery(userId) {
  try {
    const query = `UPDATE IFP_FLOW_USERS_V2 SET ROLE='PRIMARY_PE_USER' WHERE ID = :userId`;
    const binds = {
      userId,
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.updatePrimarySUQuery with error ${e} `);
    throw e;
  }
}

async function getSecondaryPEQuery(entityId) {
  try {
    const query = `SELECT * FROM IFP_FLOW_USERS_V2 WHERE ROLE='SECONDARY_PE_USER'`;
    const binds = {
      entityId
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.getSecondaryPEQuery with error ${e} `);
    throw e;
  }
}

async function updateSecondaryPEQuery(entityId) {
  try {
    const query = `UPDATE IFP_FLOW_USERS_V2 SET ROLE='SECONDARY_PE_USER' WHERE ID = :userId`;
    const binds = {
      entityId
    };
    return { query, binds };
  }
  catch(e){
    log.error(`<<<<<< Exited microservice-user.services.getQuery.service.getSecondaryPEQuery with error ${e} `);
    throw e;
  }
}



async function removeUserAccessQuery(userId) {
  try {
    const query = `
          DELETE FROM IFP_USER_ACCESS WHERE USER_ID=:userId
        `;
    const binds = {
      userId,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.removeUserAccessQuery with error ${err} `
    );
    throw err;
  }
}

async function createUserAccessApprovalQuery(
  id,
  userAccessId,
  status,
  nextApprovalLevel,
  approverId,
  reason
) {
  try {
    const query = `
          INSERT INTO IFP_USER_ACCESS_APPROVALS (ID, USER_ACCESS_ID, STATUS, APPROVAL_LEVEL, APPROVER_ID, REASON, CREATED_AT, UPDATED_AT)
            VALUES (:id, :userAccessId, :status, :nextApprovalLevel, :approverId, :reason, SYSDATE, SYSDATE)
        `;
    const binds = {
      id,
      userAccessId,
      status,
      nextApprovalLevel,
      approverId,
      reason,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.createUserAccessApprovalQuery with error ${err} `
    );
    throw err;
  }
}

async function updateUserAccessApprovalQuery(
  userAccessId,
  status,
  approvalLevel
) {
  try {
    const query = `
          UPDATE IFP_USER_ACCESS_APPROVALS
          SET STATUS = :status, UPDATED_AT = SYSDATE
          WHERE USER_ACCESS_ID = :userAccessId AND APPROVAL_LEVEL = :approvalLevel
        `;
    const binds = {
      userAccessId,
      status,
      approvalLevel,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.updateUserAccessApproval with error ${err} `
    );
    throw err;
  }
}

async function getUserAccessApprovalsQuery(
 filters
) {
  try {
    let query = `SELECT * FROM (
      SELECT A.ID,
        B.REQUEST_ID,
        C.STATUS AS REQUEST_STATUS,
        A.USER_ACCESS_ID,
        B.USER_ID ,
        A.STATUS ,
        A.APPROVAL_LEVEL ,
        A.REASON,
        A.ENTRA_ID_ASSIGN_STATUS
      FROM
        IFP_USER_ACCESS_APPROVALS A
      JOIN IFP_USER_ACCESS B ON
        A.USER_ACCESS_ID = B.ACCESS_ID
      JOIN IFP_USER_ACCESS_REQUEST C ON
        C.REQUEST_ID = B.REQUEST_ID)`;
    const binds = {};
    const conditions = [];

    Object.keys(filters).forEach((field) => {
      const values = filters[field];
      if (Array.isArray(values)) {
        const placeholders = values
          .map((_, index) => `:${field}${index}`)
          .join(", ");
        conditions.push(`${field} IN (${placeholders})`);
        values.forEach((value, index) => {
          binds[`${field}${index}`] = value;
        });
      } else {
        conditions.push(`${field} = :${field}`);
        binds[field] = values;
      }
    });

    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }

    return { query, binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getUserAccessApprovalsQuery with error ${err} `
    );
    throw err;
  }
}

async function getDisseminationAccessPolicyQuery(entityId) {
  try {
    const query = `
          SELECT DOMAIN,CLASSIFICATION FROM IFP_DISS_ACCESS_POLICY WHERE ENTITY_ID=:entityId
        `;
    const binds = {
      entityId,
    };

    return { query: query, binds: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getDisseminationAccessPolicyQuery with error ${err} `
    );
    throw err;
  }
}

async function getUserAccessRequestsQuery(filters) {
  try {
    let query = "SELECT * FROM IFP_USER_ACCESS_REQUEST";
    const binds = {};
    const conditions = [];

    Object.keys(filters).forEach((field) => {
      const values = filters[field];
      if (Array.isArray(values)) {
        const placeholders = values
          .map((_, index) => `:${field}${index}`)
          .join(", ");
        conditions.push(`${field} IN (${placeholders})`);
        values.forEach((value, index) => {
          binds[`${field}${index}`] = value;
        });
      } else {
        conditions.push(`${field} = :${field}`);
        binds[field] = values;
      }
    });

    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }

    return { query, binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getUserAccessRequestsQuery with error ${err} `
    );
    throw err;
  }
}

async function getUserAccessLevelsQuery(filters) {
  try {
    let query = "SELECT * FROM IFP_USER_ACCESS";
    const binds = {};
    const conditions = [];

    Object.keys(filters).forEach((field) => {
      const values = filters[field];
      if (Array.isArray(values)) {
        const placeholders = values
          .map((_, index) => `:${field}${index}`)
          .join(", ");
        conditions.push(`${field} IN (${placeholders})`);
        values.forEach((value, index) => {
          binds[`${field}${index}`] = value;
        });
      } else {
        conditions.push(`${field} = :${field}`);
        binds[field] = values;
      }
    });

    if (conditions.length > 0) {
      query += " WHERE " + conditions.join(" AND ");
    }

    return { query, binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-user.services.getQuery.service.getUserAccessLevelsQuery with error ${err} `
    );
    throw err;
  }
}

async function getIntraIDPendingAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'CREATED_AT', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(NAME) LIKE UPPER(:search) OR UPPER(EMAIL) LIKE UPPER(:search) OR UPPER(DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let query = `

      SELECT

      ID AS "id",
      NAME AS "name",
      EMAIL AS "email",
      ROLE AS "role",
      ACTIVATION_FLAG AS "activationStatus",
      ENTITY_ID AS "entityId",
      DESIGNATION AS "designation"
      FROM
        IFP_FLOW_USERS_V2 
      
      WHERE
        	ROLE = 'USER'
          AND ACTIVATION_FLAG = 'PENDING'
        AND ENTITY_ID = :entityId
        ${searchQuery}
        ORDER BY ${sortBy} ${order}
          OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getIntraIDPendingAccessQuery with error ${err}`);
    throw err;
  }
}

async function getSUNewAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE = 'USER'
        AND A.ACTIVATION_FLAG='ACTIVE'
        AND A.ENTITY_ID = :entityId
        ${searchQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getSUNewAccessQuery with error ${err}`);
    throw err;
  }
}

async function getSUPendingAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_DT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE = 'USER'
        AND A.ACTIVATION_FLAG='ACTIVE'
        AND A.ENTITY_ID = :entityId
        ${searchQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getSUPendingAccessQuery with error ${err}`);
    throw err;
  }
}


async function getSUExistingAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc', currentSURole = 'PRIMARY_SUPERUSER', userType) {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let userTypeQuery = "";
    if (userType == "existing") {
      userTypeQuery = ` AND EXISTING_USER IS NOT NULL`;
    } else if (userType == "new") {
      userTypeQuery = ` AND EXISTING_USER IS NULL`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE NOT IN ('PRIMARY_PE_USER', 'SECONDARY_PE_USER', '${currentSURole}')
        AND A.ACTIVATION_FLAG='ACTIVE'
        AND A.ENTITY_ID = :entityId
        ${searchQuery}
        ${userTypeQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY 
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getSUExistingAccessQuery with error ${err}`);
    throw err;
  }
}

async function getEntityUsersForExportQuery(entityId, userType) {
  let binds  = {};

  let query = `
  SELECT
    ifuv.NAME AS "Name",
    ifuv.DESIGNATION AS "Designation",
    ifuv.EMAIL AS "Email",
    ifuv.PHONE_NUMBER AS "Mobile",
    ifuv.ROLE as "Role",
    iel.NAME AS "Entity Name",
    CASE 
    	WHEN ifuv.EXISTING_USER IS NULL THEN 'New'
    	ELSE 'Existing'
    END AS "User Type"
  FROM
    IFP_FLOW_USERS_V2 ifuv
  JOIN IFP_ENTITY_LOOKUP iel ON
    ifuv.ENTITY_ID = iel.ID
  `;

  if (entityId) {
    binds.entityId = entityId;
    query += `WHERE ifuv.ENTITY_ID = :entityId `;
  } else {
    query += `WHERE 1=1 `;
  }
  

  if (userType === "new") {
    query += ` AND ifuv.EXISTING_USER IS NULL `;
  } else if (userType === "existing") {
    query += ` AND ifuv.EXISTING_USER IS NOT NULL `;
  }

  query += `
    AND ifuv."ROLE" IN ('PRIMARY_SUPERUSER', 'SECONDARY_SUPERUSER', 'DG', 'USER')
    AND ifuv.STATUS != 'DELETED'
  `
  return { query, binds };
}

async function getApprovedAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_DT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;

    let query = `
      SELECT
      DISTINCT A.ID as "id",
      A.NAME as "name",
      A.EMAIL as "email",
      A.PHONE_NUMBER as "phone",
      A.ENTITY_ID as "entityId",
      C.NAME as "entityName",
      A.DESIGNATION as "designation"
    FROM
      IFP_FLOW_USERS_V2 A
    JOIN IFP_USER_ACCESS_REQUEST B ON
      A.ID = B.USER_ID
    LEFT JOIN IFP_ENTITY_LOOKUP C ON
      A.ENTITY_ID = C.ID
    WHERE
      A.ROLE = 'USER'
      AND A.STATUS = 'REGISTERED'
      AND B.STATUS = 'APPROVED'
    `

    const binds = {};

    if (entity) {
      query += ` AND A.ENTITY_ID = :entityId`;
      binds.entityId = entity;
    }

    if (search) {
      query += ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    query += ` ORDER BY ${sortBy} ${order}`;
    query += ` OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY`;

    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getApprovedAccessQuery with error ${err}`);
    throw err;
  }
}

async function getDGPendingAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE = 'USER'
        AND A.ACTIVATION_FLAG='ACTIVE'
        AND A.ENTITY_ID = :entityId
        ${searchQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getDGPendingAccessQuery with error ${err}`);
    throw err;
  }
}

async function getDGNewAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE = 'USER'
        AND A.ACTIVATION_FLAG='ACTIVE'
        AND A.ENTITY_ID = :entityId
        ${searchQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getDGNewAccessQuery with error ${err}`);
    throw err;
  }
}

async function getDGExistingAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    binds.entityId = entity;
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE IN ('USER', 'PRIMARY_SUPERUSER', 'SECONDARY_SUPERUSER')
        AND A.ACTIVATION_FLAG='ACTIVE'
        AND A.ENTITY_ID = :entityId
        ${searchQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getDGExistingAccessQuery with error ${err}`);
    throw err;
  }
}

async function getPENewAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc') {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

   
    
    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let entityQuery=''
    if (entity) {
      entityQuery = ` AND ENTITY_ID=:entityId`;
      binds.entityId = entity;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE = 'USER'
        AND A.ACTIVATION_FLAG='ACTIVE'
        ${entityQuery}
        ${searchQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `
    
    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getPENewAccessQuery with error ${err}`);
    throw err;
  }
}

async function getPEExistingAccessQuery(page = 1, limit = 10, search = '', entity, sortBy = 'NAME', order = 'desc', userType) {
  try {
    const sortByKeys = ['ID','NAME','EMAIL','DESIGNATION','CREATED_AT']
    if(sortBy && !sortByKeys.includes(sortBy))
      throw new IFPError(400,"Invalid sortBy")

    const offset = (page - 1) * limit;
    const binds = {};

    let searchQuery = ''
    if (search) {
      searchQuery = ` AND (UPPER(A.NAME) LIKE UPPER(:search) OR UPPER(A.EMAIL) LIKE UPPER(:search) OR UPPER(A.DESIGNATION) LIKE UPPER(:search))`;
      binds.search = `%${search}%`;
    }

    let entityQuery=''
    if (entity) {
      entityQuery = ` AND ENTITY_ID=:entityId`;
      binds.entityId = entity;
    }

    let userTypeQuery = "";
    if (userType == "existing") {
      userTypeQuery = ` AND EXISTING_USER IS NOT NULL`;
    } else if (userType == "new") {
      userTypeQuery = ` AND EXISTING_USER IS NULL`;
    }

    let query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.EMAIL AS "email",
        A.ROLE AS "role",
        A.ENTITY_ID AS "entityId",
        B.NAME AS "entityName",
        A.DESIGNATION AS "designation",
        A.EXISTING_USER AS "isLinked",
        COUNT(*) OVER () AS "totalCount"
      FROM
        IFP_FLOW_USERS_V2 A
      LEFT JOIN IFP_ENTITY_LOOKUP B ON
        A.ENTITY_ID = B.ID
      WHERE
        A.ROLE NOT IN ('PRIMARY_PE_USER','SECONDARY_PE_USER')
        AND A.ACTIVATION_FLAG='ACTIVE'
        ${entityQuery}
        ${searchQuery}
        ${userTypeQuery}
      ORDER BY A.${sortBy} ${order}
      OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
    `

    binds.offset = offset;
    binds.limit = limit;

    return { query: query, binds: binds };
  } catch (err) {
    console.error(`<<<<<< Exited microservice-user.services.getPEExistingAccessQuery with error ${err}`);
    throw err;
  }
}

async function getSensitiveAccessDataQuery(userIds) {
  try{
    const binds = {}
    const bindPlaceholders = userIds.map((id, index) => {
      const bindKey = `userId${index}`;
      binds[bindKey] = id;
      return `:${bindKey}`; 
    }).join(", ");
    const query = `SELECT
              A.USER_ID,
              B.ACCESS_ID,
              B."DOMAIN",
              B.ACCESS_LEVEL,
              C.STATUS
            FROM
              IFP_USER_ACCESS_REQUEST A
            JOIN IFP_USER_ACCESS B ON
              A.REQUEST_ID = B.REQUEST_ID
            JOIN IFP_USER_ACCESS_APPROVALS C ON 
              B.ACCESS_ID = C.USER_ACCESS_ID
            WHERE
              A.USER_ID IN (${bindPlaceholders})
              AND A.STATUS = 'PENDING'
              AND C.STATUS = 'PENDING'`;
    return { query, binds };
  }
  catch(err){
    console.error(`<<<<<< Exited microservice-user.services.getSensitiveAccessDataQuery with error ${err}`);
    throw err;
  }
}

function getDeletedUsersListQuery(entityId) {
  let binds = {};
  let query = `
  SELECT
    delee.NAME AS "userName",
    delee.EMAIL as "userEmail",
    delee.ROLE AS "userRole",
    iel.NAME AS "entityName",
    deler.NAME AS "deletedBy",
    delee.DELETED_AT AS "deletedAt",
    deler.ROLE AS "deletedByRole"
  FROM
    IFP_FLOW_USERS_V2 delee
  JOIN IFP_FLOW_USERS_V2 deler ON delee.DELETED_BY = deler.ID
  JOIN IFP_ENTITY_LOOKUP iel ON delee.ENTITY_ID = iel.ID 
  WHERE delee.STATUS = 'DELETED'
  `;

  if (entityId) {
    binds.entityId = entityId;
    query += ` AND delee.ENTITY_ID = :entityId`;
  }

  return { query, binds };
}

/**
 * Query that extracts user's access records for the different request
 * types (new, pending & existing).
 * 
 * Example: Access records for -> Superuser Pending requests, DG New Requests,
 * PE New requests, Superuser New requests etc.
 * 
 * The query consists of join between requests, access records and approval
 * records. The result is a list of all the users access request, the access
 * records part of said requests, their grant status (GRANT or REVOKE), their
 * approval status (APPROVED or PENDING) and who it's pending with (DG, PE, SUPERUSER).
 * 
 * @param {string} userId User ID whose details are required.
 * @param {string} requestType Must be one of: 'new', 'pending' or 'existing'
 * @param {string} approvalLevel Only used for new request,
 * use this to get access records pending at various user levels. Eg:
 * For New requests at DG, specify approvalLevel as 'DG'. For new requests
 * at Superuser, specify as 'SUPERUSER'
 * @param {string} requestorRole Only used for pending requests,
 * Used to filter access records which the requestorRole is waiting for.
 * Example: If value is "SUPERUSER", returns access records pending at either
 * PE or DG. 
 */
function getUserAccessV2Query(userId, requestType, approvalLevel, requestorRole) {
  let directionQuery = "";
  let approvalCycleFilter = "";
  const binds = { userId };

  if (requestType == "new") {
    approvalCycleFilter = `
    AND iuaa.STATUS = 'PENDING'
    AND iuaa.APPROVAL_LEVEL = '${getRoleGroupCode(approvalLevel)}'
    `;
  } else if (requestType == "pending") {
    directionQuery = `
    CASE
      WHEN iuaa.APPROVAL_LEVEL IN ('DG', 'PRODUCT_ENGAGEMENT') THEN 'approval'
      ELSE 'revert'
    END AS "direction"
    `;
    let approvalLevelFilter = `AND iuaa.APPROVAL_LEVEL != 'SUPERUSER'`;
    if (requestorRole == "DG") {
      approvalLevelFilter = `
      AND (iuaa.APPROVAL_LEVEL = 'PRODUCT_ENGAGEMENT' OR iuaa.APPROVAL_LEVEL = 'SUPERUSER')
      `;
    }
    approvalCycleFilter = `
      AND iuaa.STATUS = 'PENDING'
      ${approvalLevelFilter}
    `;
  } else {
    approvalCycleFilter = `
    AND iuaa.STATUS IN ('APPROVED', 'REVOKED')
    `;
  }

  let query = `
  SELECT
    iua.ACCESS_ID AS "accessId",
    iua.REQUEST_ID AS "requestId",
    iua."DOMAIN" AS "domain",
    iua.ACCESS_LEVEL AS "classification",
    iua.ACCESS_OPERATION AS "accessOperation",
    iuaa.STATUS AS "approvalStatus",
    iuaa.REASON AS "reason",
    iuaa.APPROVAL_LEVEL AS "approvalLevel"${directionQuery ? "," : ""}
    ${directionQuery}
  FROM
    IFP_USER_ACCESS_APPROVALS iuaa
  JOIN IFP_USER_ACCESS iua ON
    iuaa.USER_ACCESS_ID = iua.ACCESS_ID
  JOIN IFP_USER_ACCESS_REQUEST iuar ON
    iua.REQUEST_ID = iuar.REQUEST_ID
  WHERE
    iuar.USER_ID = :userId
    ${approvalCycleFilter}
  ORDER BY
    iuar.CREATED_DT
  `;
  return { query, binds };
}

/**
 * 
 * @param {string} userId 
 * @param {Array<string>} accessIds 
 */
function getRequestIDsforPendingAccessQuery(userId, accessIds) {
  let binds = { userId };
  const query = `
  SELECT
    iua.ACCESS_ID AS "accessId",
    iua."DOMAIN" AS "domain",
    iua.ACCESS_LEVEL AS "classification",
    iua.ACCESS_OPERATION AS "accessOperation" ,
    iua.REQUEST_ID as "requestId"
  FROM
    IFP_USER_ACCESS_REQUEST iuar
  JOIN IFP_USER_ACCESS iua 
    ON iuar.REQUEST_ID = iua.REQUEST_ID 
  WHERE
    iuar.USER_ID = :userId
    AND iua.ACCESS_ID IN (${accessIds.map((i) => `'${i}'`).join(", ")})
    AND iuar.STATUS = 'PENDING'
  `;
  return { query, binds };
}

function deleteInvitationByEmailQuery(userEmail) {
  let binds = { userEmail };
  let query = `
    DELETE
    FROM
      IFP_INVITATIONS_V2 iiv
    WHERE
      iiv.USER_EMAIL = :userEmail
  `;

  return { query, binds };
}

function deleteUserAccessApprovalQuery(userId) {
  let binds = { userId };
  const query = `
    DELETE
    FROM
      IFP_USER_ACCESS_APPROVALS iuaa
    WHERE
      iuaa.USER_ACCESS_ID IN (
      SELECT
        ACCESS_ID
      FROM
        IFP_USER_ACCESS iua
      WHERE
        iua.USER_ID = :userId
    )
  `
  return { query, binds }
}

function deleteEidMappingByEmailQuery(userEmail) {
  let binds = { userEmail };
  const query = `
    delete
    from
      ifp_eid_request_mapping ierm
    where
      ierm.EMAIL = :userEmail
  `;
  return { query, binds };
}

module.exports = {
  getEntityListQuery,
  getEntityDetailsQuery,
  createInvitationQuery,
  listPendingInvitationsQuery,
  deleteInvitationQuery,
  updateInvitationQuery,
  getInvitationQuery,
  getInvitationV2Query,
  checkExistingInvitationsQuery,
  getDGOnboardingStatusQuery,
  createUserQuery,
  createEIDUserQuery,
  getUserDataQuery,
  getUserDataV2Query,
  getUserDataV3Query,
  setUserActivationStatusQuery,
  getActiveSuperuserQuery,
  getActiveDGUserQuery,
  getActivePEUserQuery,
  getActiveUserByEmailQuery,
  getActiveUserByIdQuery,
  updateInvitationStatusQuery,
  updateUserStatusQuery,
  setUserDeleteStatusQuery,
  createUserAccessQuery,
  removeUserAccessQuery,
  deleteUserAccessQuery,
  updateUserAccessUserIdQuery,
  createUserAccessApprovalQuery,
  updateUserAccessApprovalQuery,
  getDisseminationAccessPolicyQuery,
  createUserAccessRequestQuery,
  updateUserAccessRequestStatusQuery,
  updateUserAccessRequestIntraIdStatusQuery,
  updateUserAccessRequestUserIdQuery,
  getUserAccessApprovalsQuery,
  getUserAccessRequestsQuery,
  getUserAccessLevelsQuery,
  getIntraIDPendingAccessQuery,
  getSUNewAccessQuery,
  getSUPendingAccessQuery,
  getSUExistingAccessQuery,
  getApprovedAccessQuery,
  deleteUserDataQuery,
  updatePrimarySUQuery,
  getSecondarySUQuery,
  getDGPendingAccessQuery,
  getDGNewAccessQuery,
  getDGExistingAccessQuery,
  getSecondaryPEQuery,
  updatePrimaryPEQuery,
  getPENewAccessQuery,
  getPEExistingAccessQuery,
  deleteUserAccessRequestsQuery,
  createEIDInvitationMappingQuery,
  getEIDInvitationMappingQuery,
  getEIDInvitationMappingByEIDQuery,
  updateSecondarySUQuery,
  updateSecondaryPEQuery,
  updateUserDataQuery,
  getEntityUsersForExportQuery,
  getEntityInvitesQuery,
  getSensitiveAccessDataQuery,
  getDeletedUsersListQuery,
  getUserAccessV2Query,
  getRequestIDsforPendingAccessQuery,
  deleteInvitationByEmailQuery,
  deleteUserAccessApprovalQuery,
  deleteEidMappingByEmailQuery,
};
