const { IFPError } = require("../../utils/error");
const stream = require('stream');
const fileType = require('file-type');
const { getDomains, getPeriodicities, getDataClassifications } = require("../../microservice-cms/services/executeQuery.service");
const { getPublicationId, getInterval, getPriodicityId } = require("../../microservice-ai-insight-report/services/sanadkomAPI.service")
const moment = require('moment')
const pgModels = require("../../database/postgres/models");

function validateBody(body, requiredKeys) {
  const missingKeys = requiredKeys.filter((key) => !(key in body));

  if (missingKeys.length > 0) {
    throw new IFPError(400, `Missing keys: ${missingKeys.join(", ")}`);
  }
}

async function validatePublicationAttachments(attachments) {

    if (!Array.isArray(attachments))
      throw new IFPError(400, "ReleaseDocuments should be an array of objects");

    let fileReq = {
      excel_attachment: [],
      excel_attachment_ar: [],
      publication_attachment: [],
      publication_attachment_ar: [],
    };
    const supportedLanguages = ["en", "ar"];
    const supportedExtensions = ["xlsx", "pdf"];

    let errorFlag = 0;
    let errorMessage = "";

    await Promise.all(
      attachments.map(async (file) => {
        await Promise.all(
          Object.entries(file).filter(([lang, data]) => !!data).map(async ([lang, data]) => {
            if (!supportedLanguages.includes(lang))
              throw new IFPError(400, `Unsupported language type ${lang}`);

            if (data && "Title" in data && data.File) {
              const buffer = Buffer.from(data.File, "base64");
              const readableStream = new stream.Readable();
              readableStream.push(buffer);
              readableStream.push(null);
              const fileTypeResponse = await fileType.stream(readableStream);
              if (fileTypeResponse.fileType) {
                const extension = fileTypeResponse.fileType.ext;

                if (!supportedExtensions.includes(extension))
                  throw new IFPError(
                    400,
                    `Unsupported file extension ${extension}`
                  );

                const fileObj = {
                  stream: buffer,
                  name: `${data.Title}.${extension}`,
                };

                if (extension == "xlsx") {
                  if (lang == "en") {
                    fileReq["excel_attachment"].push(fileObj);
                  } else fileReq["excel_attachment_ar"].push(fileObj);
                } else if (extension == "pdf") {
                  if (lang == "en")
                    fileReq["publication_attachment"].push(fileObj);
                  else fileReq["publication_attachment_ar"].push(fileObj);
                }
              } else {
                errorFlag = 1;
                errorMessage = `Invalid File in ${lang}`;
              }
            } else {
              errorFlag = 1;
              errorMessage = `Document ${lang} should be of type object with keys 'Title' and 'File'`;
            }
          })
        );
      })
    );

    if (errorFlag) throw new IFPError(400, errorMessage);

    return fileReq
}

const validatePublications = async (req, res, next) => {
    try{

      // Identify publication type
      let publicationType;
      const { TopicID, SmartPublisher, ReleaseID } = req.body;
      if (TopicID == undefined || TopicID == null) {
        throw new IFPError(400, "TopicID is required");
      } else if (TopicID == 0) {
        publicationType = "consolidated";
      } else {
        publicationType = "domain";
      }
      
      // validate body based on publication type
      let requiredKeys = [
        "TopicID",
        "SmartPublisher",
        "ReleaseID",
        "IssueDate",
        "IntervalID",
        "ReleaseDocuments",
      ];
      if (publicationType == "consolidated") {
        requiredKeys.push(...[
          "PublicationName",
          "PublicationNameAr",
          "PriodicityID",
        ]);
      }
      validateBody(req.body, requiredKeys);
      
      // validate and transform attachments as per requirement
      const validatedAttachments = await validatePublicationAttachments(req.body.ReleaseDocuments)
      req.files = validatedAttachments
      req.ticketNumber = ReleaseID;
      
      const domains = await getDomains();
      let domain = domains.find(domain => domain.ID == req.body.TopicID)
      if (!domain)
        throw new IFPError(400,'Invalid Domain')
      
      let classification = {ID: 1};
      
      const reportData = await pgModels.AiInsightReport.findOne({
        where: {
          ticket_id: String(ReleaseID),
        },
      })
      if (!reportData)
        throw new IFPError(400,'Invalid Report')

      req.cleanedBody = {}
      req.cleanedBody.smart_publisher = SmartPublisher
      req.cleanedBody.publicationType = publicationType
      req.cleanedBody.domain = domain.NAME
      req.cleanedBody.filter_type = "quarterly"
      req.cleanedBody.security = classification.ID
      req.cleanedBody.title = `${domain.NAME}_Q${reportData.quarter}_Report`
      req.cleanedBody.subtitle = `${domain.NAME}_Q${reportData.quarter}_Report`
      req.cleanedBody.subtitle_ar = `${domain.NAME}_Q${reportData.quarter}_Report`
      // req.cleanedBody.title_ar = `${domain.NAME}_Q${req.body.IntervalID}_Report`
      req.cleanedBody.publication_date = moment(req.body.IssueDate, "DD/MM/YYYY HH:mm:ss").format("YYYY-MM-DD");

      next()

    }
    catch(exp){
        next(exp)
    }
}          


module.exports = {
    validatePublications
}