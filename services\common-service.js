const constants = require('../config/constants.json');
const db = require('./database.service');
const moment = require('moment');

const axiosInstance = require('axios');
const http = require('http');
const https = require('https');
const axios = axiosInstance.create({
    httpAgent: new http.Agent({ keepAlive: true }),
    httpsAgent: new https.Agent({ keepAlive: true })
});
const Logger = require('scad-library').logger;
const he = require('html-entities');
const { getUserGroupDetails, syncUserGroup } = require('./authorization.service');

const crypto = require('crypto')
const { setRedis, getRedis } = require('./redis.service');
const { IFPError } = require('../utils/error');

const log = new Logger().getInstance();
require('dotenv').config();

const formatReqToLogMsg = (req) => { //middleware function to log every incoming requests
    let current_datetime = new Date();
    let formatted_date =
        current_datetime.getFullYear() +
        "-" +
        (current_datetime.getMonth() + 1) +
        "-" +
        current_datetime.getDate() +
        " " +
        current_datetime.getHours() +
        ":" +
        current_datetime.getMinutes() +
        ":" +
        current_datetime.getSeconds();
    let method = req.method;
    let url = req.url;
    let message = `[${formatted_date}] ${method}:${url}`;
    return message;
};

async function getMetaFromCMS(request,cmsLoginUrl, cmsMetaPath, userGroups,url='',req='') {
    log.debug('>>>>>Entered services.common-service.getMetaFromCMS');
    log.info(`API call to ${cmsMetaPath} from getMetaFromCMS`);
    try{
        let userCredentials = {};
        let keyIdentifier = '';
        const result = await syncUserGroup(request);
        if (!(result && result.groups)){
            userCredentials = await getUserGroupDetails(userGroups);
            if (userCredentials[0] == 401) {
                log.error(`User does not belongs to any valid content group!!!`);
                throw new IFPError(404,`User does not belongs to any valid content group!!!`);
            }
            log.info(`User belonged to user group: ${userCredentials.username}`);
            keyIdentifier = userCredentials.username
        }
        else{
            userCredentials={
                username:result.email,
                password:process.env.USER_SYNC_DEFAULT_PASSWORD,
                groups: result.groups
            }
            log.info(`User belonged to groups: ${result.groups}`);
            keyIdentifier = result.groups
        }


        let cmsCacheKey = 'responseService_'+crypto.createHash('md5').update(keyIdentifier).digest("hex")+'_'+ crypto.createHash('md5').update(JSON.stringify(cmsMetaPath)).digest("hex")
        const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
        if (cmsCacheResults){
            log.info(`[getMetaFromCMS] Found cache in the key ${cmsCacheKey}`)
            return JSON.parse(cmsCacheResults)
        }

        let cookieIdentifier = `cookie_${crypto.createHash('md5').update(keyIdentifier).digest("hex")}`
        let cookie = await getCookie(cookieIdentifier)

        if (!cookie){
            let postResponse = await axios.post(`${cmsLoginUrl}`, {
                name: userCredentials.username, pass: userCredentials.password
            })
            cookie = postResponse ? (postResponse.headers['set-cookie'][0]).split(';')[0] : '';
            await setCookie(cookieIdentifier,cookie)
        }

        let getResponse = await axios.get(`${cmsMetaPath}`, {headers: { Cookie: cookie }})

        if (getResponse.data && getResponse.data.length > 0 && getResponse.data[0].type && getResponse.data[0].type.toLowerCase() === 'geospatial') {
            let response = await removeDuplicate(getResponse.data)
            Object.assign(getResponse.data, response);
        }

        // handle success
        if (getResponse && getResponse.data && (getResponse.data.length > 0 || Object.keys(getResponse.data).length > 0)) {
        
            let cleanedData = []
            if (["/api/content-type/analytical-apps","/api/mobile/content-type/analytical-apps"].includes(url)){
                let cleanedAnalyticalData;
                let analyticalPromises = getResponse.data.map(async analytical_app => {
                    cleanedAnalyticalData = await cleanupCMSData([analytical_app])
                    cleanedData.push(cleanedAnalyticalData)
                })
                await Promise.all(analyticalPromises)
                
                return cleanedData;
            }
            else{
                let data = await cleanupCMSData(getResponse.data)
                setRedis(cmsCacheKey, JSON.stringify(data), constants.redis.cmsResponseTTL, req.headers)
                return data     
            }
        } else {
            if (cmsMetaPath.includes(constants.cmsGroupUrl.CMS_SEARCH_URL_V2) || cmsMetaPath.includes(constants.cmsUrl.CMS_INDICATOR_LIST) || cmsMetaPath.includes(constants.cmsUrl.CMS_PRODUCT_LIST)) {
                // Handle empty search
                log.error(`Data not found for url ${cmsMetaPath}`);
                return []
            }
            // Handle error or unauthorized node access
            log.error(`Invalid content id or Data not found for url ${cmsMetaPath}`);
            let errorObj = new IFPError(404,`Invalid content id or Data not found`)
            errorObj.access=false
            throw errorObj
        }
    }
    catch(err) {
        log.error(`Exited  services.common-service.getMetaFromCMS with Error while getting CMS data from url - ${cmsMetaPath} ERROR: ${err}`);
        throw err
    }    
}

async function getMetaFromCMSAdmin(request,cmsLoginUrl, cmsMetaPath, userGroups,url='',req='') {
    log.debug('>>>>>Entered services.common-service.getMetaFromCMSAdmin');
    log.info(`API call to ${cmsMetaPath} from getMetaFromCMSAdmin`);
    try{
        let userCredentials = {
            username: "<EMAIL>",
			password: "5Iu88Q5JP07hLP8",
        };


        let cmsCacheKey = 'responseService_'+userCredentials.username+'_'+ crypto.createHash('md5').update(JSON.stringify(cmsMetaPath)+JSON.stringify(userCredentials)).digest("hex")
        const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
        if (cmsCacheResults)
            return JSON.parse(cmsCacheResults)

        let cookieIdentifier = `${process.env.NODE_ENV}_${userCredentials.username}`
        let cookie = await getCookie(cookieIdentifier)

        if (!cookie){
            let postResponse = await axios.post(`${cmsLoginUrl}`, {
                name: userCredentials.username, pass: userCredentials.password
            })
            cookie = postResponse ? (postResponse.headers['set-cookie'][0]).split(';')[0] : '';
            await setCookie(cookieIdentifier,cookie)
        }

        let getResponse = await axios.get(`${cmsMetaPath}`, {headers: { Cookie: cookie }})

        if (getResponse.data && getResponse.data.length > 0 && getResponse.data[0].type && getResponse.data[0].type.toLowerCase() === 'geospatial') {
            let response = await removeDuplicate(getResponse.data)
            Object.assign(getResponse.data, response);
        }

        // handle success
        if (getResponse && getResponse.data && (getResponse.data.length > 0 || Object.keys(getResponse.data).length > 0)) {
        
            let cleanedData = []
            if (["/api/content-type/analytical-apps","/api/mobile/content-type/analytical-apps"].includes(url)){
                let cleanedAnalyticalData;
                let analyticalPromises = getResponse.data.map(async analytical_app => {
                    cleanedAnalyticalData = await cleanupCMSData([analytical_app])
                    cleanedData.push(cleanedAnalyticalData)
                })
                await Promise.all(analyticalPromises)
                
                return cleanedData;
            }
            else{
                let data = await cleanupCMSData(getResponse.data)
                setRedis(cmsCacheKey, JSON.stringify(data), constants.redis.cmsResponseTTL, req.headers)
                return data     
            }
        } else {
            log.error(`Invalid content id or Data not found for url ${cmsMetaPath}`);
            let errorObj = new IFPError(404,`Invalid content id or Data not found`)
            errorObj.access=false
            throw errorObj
        }
    }
    catch(err) {
        log.error(`Exited  services.common-service.getMetaFromCMSAdmin with Error while getting CMS data from url - ${cmsMetaPath} ERROR: ${err}`);
        throw err
    }    
}

async function removeDuplicate(data) {
    try {
        const filteredData = data.filter((value, index, array) => array.findIndex(t => t.id == value.id) == index);
        Object.keys(filteredData).forEach((key) => {
            filteredData[key].endPointUrls = [];
            let endPointUrls = [];
            data.forEach(element => {
                if (filteredData[key].id == element.id) {
                    endPointUrls.push({
                        label: element.endpoint_label,
                        url: element.endpoint_url,
                        defaultLayer: element.default_layer && element.default_layer.toLowerCase() === "true" ? true : false,
                        defaultId: element.defaultDistrictId,
                        showLegend: element.show_on_legend && element.show_on_legend.toLowerCase() === "true" ? true : false,
                        type: element.endpointType,
                        indicatorId: element.nodeId,
                        summaryCardId: element.summaryCardId,
                        cardDate: element.cardDate,
                        title: element.endpoint_title,
                        iconId: element.endpoint_icon_id,
                    })
                }
            });

            delete filteredData[key].endpoint_url; delete filteredData[key].endpoint_label; delete filteredData[key].show_on_legend;
            delete filteredData[key].default_layer; delete filteredData[key].defaultDistrictId; delete filteredData[key].endpointType;
            delete filteredData[key].endpoint_title; delete filteredData[key].endpoint_icon_id; delete filteredData[key].nodeId; delete filteredData[key].summaryCardMeta;
            filteredData[key].endPointUrls = endPointUrls;
        })
        return filteredData;
    } catch (error) {
        throw error;
    }
}

//function to clean html tags, special characters and pick chart config from jso nvariable and put into meta obj
async function cleanupCMSData(data) {
    try {
        log.debug('>>>>>Entered services.common-service.cleanupCMSData');
        if (data[0])
            if (data[0].json)
                if (!(data[0].json.includes('{') &&data[0].json.includes('}')))
                    delete data[0].json
        
        if (data[0] && data[0].json) {
            const chartConfig = data[0].json.replace(/`/g, "\"");
            delete data[0].json;
            Object.keys(data[0]).forEach(e => {
                if (e.includes('enable') || e.includes('ENABLE')) {
                    if (data[0][e] === 'True' || data[0][e] === 'TRUE' || data[0][e] === 'true') {
                        data[0][e] = true;
                    } else if (data[0][e] === 'False' || data[0][e] === 'FALSE' || data[0][e] === 'false') {
                        data[0][e] = false;
                    }
                }
            });
            data[0].compare_data = he.decode(data[0].compare_data);
            let decodedMeta = JSON.parse((JSON.stringify(data).replace(/(<([^>]+)>)|\\\\n|\\n|\\\\r|\\r|(amp;)|(nbsp;)|(&lt;p&gt;)|(&lt;\/p&gt;)/g, '')));
            decodedMeta = he.decode(decodedMeta);
            const decodedChartConfig = JSON.parse(he.decode(chartConfig).replace(/(<([^>]+)>)|\\\\n|\\n|(amp;)|(nbsp;)|(&lt;p&gt;)|(&lt;\/p&gt;)/g, ''));
            if (data[0].enableDynamicPanel && data[0].listofDyanmicPanelContent.length > 0) {
                decodedMeta[0].dynamicPanel = [];
                data[0].listofDyanmicPanelContent.forEach(panelContent => {
                    let id = panelContent.toLowerCase().replace(/ /g, "-");
                    switch (id) {
                        case "infographic-insights": {
                            decodedMeta[0].dynamicPanel.push({
                                "id": id,
                                "title": panelContent,
                                "meta": {
                                    "infogramUrl": data[0].infogramUrl
                                }
                            });
                            delete decodedMeta[0].infogramUrl;
                            break;
                        }
                        case "highlights": {
                            let highlightsMetaConfig = '';
                            if (data[0].highlightsMeta.includes('`')) {
                                highlightsMetaConfig = data[0].highlightsMeta.replace(/\"/g, "");
                                highlightsMetaConfig = highlightsMetaConfig.replace(/`/g, "\"");
                            } else {
                                highlightsMetaConfig = data[0].highlightsMeta.replace(/`/g, "\"");
                            }
                            const decodedHighlightsConfig = JSON.parse(he.decode(highlightsMetaConfig).replace(/(<([^>]+)>)|\\\\n|\\n|(amp;)|(nbsp;)|(&lt;p&gt;)|(&lt;\/p&gt;)/ig, ''));
                            decodedMeta[0].dynamicPanel.push({
                                "id": id,
                                "title": panelContent,
                                "meta": decodedHighlightsConfig
                            });
                            delete decodedMeta[0].highlightsMeta;
                            break;
                        }
                    }

                });
                delete decodedMeta[0].listofDyanmicPanelContent;
            }
            let finalObj = Object.assign({}, decodedMeta[0], decodedChartConfig);
            let urlWithBasepathData = await getUrlWithBasepath(finalObj)
            return urlWithBasepathData;
        } else {
            const decodedMeta = JSON.parse(he.decode(JSON.stringify(data).replace(/&quot;/g, '')).replace(/(<([^>]+)>)|\\\\n|\\n|<p>|<\/p>|(amp;)|(nbsp;)|(&quot;)|(&lt;p&gt;)|(&lt;\/p&gt;)/ig, ''));
            if (decodedMeta.length > 0) {
                Object.keys(decodedMeta[0]).forEach(e => {
                    if (e.includes('enable') || e.includes('ENABLE')) {
                        if (decodedMeta[0][e] === 'True' || decodedMeta[0][e] === 'TRUE' || decodedMeta[0][e] === 'true') {
                            decodedMeta[0][e] = true;
                        } else if (decodedMeta[0][e] === 'False' || decodedMeta[0][e] === 'FALSE' || decodedMeta[0][e] === 'false') {
                            decodedMeta[0][e] = false;
                        }
                    }
                });
                let urlWithBasepathData = await getUrlWithBasepath(decodedMeta)
                return urlWithBasepathData;
            } else {
                return decodedMeta;
            }
        }
    } catch (err) {
        log.error(`<<<<<Exited services.common-service.cleanupCMSData with err ${err} for data ${typeof (data) === 'object' ? JSON.stringify(data) : data}`);
        throw err;
    }
}

//function to add cms basepath to all the asset related fields (like attachment, imgSrc, pdf etc)
const getUrlWithBasepath = async (data) => {
    try {
        log.debug('>>>>>Entered services.common-service.getUrlWithBasepath');
        if (typeof (data) === 'array' || data.length > 0) {
            data.forEach(element => {
                if (element.imgSrc || element.page_icon || element.page_menu_icon || element.attachment || element.publication_attachment || element.excel_attachment || element.country_flag) {
                    element.imgSrc = element.imgSrc && element.imgSrc.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.imgSrc}` : "";
                    element.attachment = element.attachment && element.attachment.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.attachment}` : "";
                    element.publication_attachment = element.publication_attachment && element.publication_attachment.length > 0 ? element.publication_attachment.map(p=>`${process.env.CMS_BASEPATH_URL}${p}`) : [];
                    element.excel_attachment = element.excel_attachment && element.excel_attachment.length > 0 ? element.excel_attachment.map(e=>`${process.env.CMS_BASEPATH_URL}${e}`) : [];
                    element.country_flag = element.country_flag && element.country_flag.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.country_flag}` : "";
                    element.page_icon = element.page_icon && element.page_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.page_icon}` : "";
                    element.page_light_icon = element.page_light_icon && element.page_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.page_light_icon}` : "";
                    element.field_page_light_icon = element.field_page_light_icon && element.field_page_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.field_page_light_icon}` : "";
                    element.page_menu_icon = element.page_menu_icon && element.page_menu_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.page_menu_icon}` : "";
                    element.page_menu_light_icon = element.page_menu_light_icon && element.page_menu_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.page_menu_light_icon}` : "";
                    element.field_page_menu_light_icon = element.field_page_menu_light_icon && element.field_page_menu_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${element.field_page_menu_light_icon}` : "";
                }
            })

        } else if (data.imgSrc || data.attachment || data.country_flag || data.page_icon || data.page_menu_icon) {
            data.imgSrc = data.imgSrc && data.imgSrc.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.imgSrc}` : "";
            data.attachment = data.attachment && data.attachment.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.attachment}` : "";
            data.country_flag = data.country_flag && data.country_flag.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.country_flag}` : "";
            data.page_icon = data.page_icon && data.page_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.page_icon}` : "";
            data.page_light_icon = data.page_light_icon && data.page_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.page_light_icon}` : "";
            data.field_page_light_icon = data.field_page_light_icon && data.field_page_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.field_page_light_icon}` : "";
            data.page_menu_icon = data.page_menu_icon && data.page_menu_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.page_menu_icon}` : "";
            data.page_menu_light_icon = data.page_menu_light_icon && data.page_menu_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.page_menu_light_icon}` : "";
            data.field_page_menu_light_icon = data.field_page_menu_light_icon && data.field_page_menu_light_icon.length > 0 ? `${process.env.CMS_BASEPATH_URL}${data.field_page_menu_light_icon}` : "";
        }
        return data;
    } catch (err) {
        log.error(`<<<<<Exited services.common-service.getUrlWithBasepath with err ${err} for data ${typeof (data) === 'object' ? JSON.stringify(data) : data}`);
        throw err
    }
}

const getIndicatorValuesIcon = async (req,valuesMeta, userGroups) => {
    try {
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        let iconIds = [];
        let iconLightIds = [];
        let counter = [];
        valuesMeta.forEach(value => {
            if (value.iconId && value.iconId.length > 0) {
                iconIds.push(value.iconId);
            }
            if (value.iconLightId && value.iconLightId.length > 0) {
                iconLightIds.push(value.iconLightId);
            }
            counter.push(1);
        })
        if (iconIds.length > 0 && counter.length === valuesMeta.length) {
            let iconCodes = iconIds.map(i => i).join(',');
            let iconLightCodes = iconLightIds.map(i => i).join(',');
            const cmsCompareUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_COUNTRY_LIST}${iconCodes}`
            const cmsCompareLightUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_COUNTRY_LIST}${iconLightCodes}`

            const iconData = await getMetaFromCMS(req,cmsLoginUrl, cmsCompareUrl, userGroups);
            let iconLightData = []
            if (iconLightCodes)
                iconLightData = await getMetaFromCMS(req,cmsLoginUrl, cmsCompareLightUrl, userGroups);
            valuesMeta.forEach(value => {
                if (value.iconId && value.iconId.length > 0) {
                    iconData.forEach(data => {
                        if (data.country_id === value.iconId) {
                            value.iconId = data.country_flag;
                        }
                    })
                }
                if (value.iconLightId && value.iconLightId.length > 0) {
                    iconLightData.forEach(data => {
                        if (data.country_id === value.iconLightId) {
                            value.iconLightId = data.country_flag;
                        }
                    })
                }
            })
            return valuesMeta;
        } else {
            return valuesMeta;
        }
    } catch (err) {
        throw err;
    }
}

async function getColors(data) {
    try {
        data.seriesMeta.filter(series => {
            if (series.data && series.data.length > 0) {
                let changeAccessorvalue = (series.changeAccessor) ? series.data[0][series.changeAccessor.path] : undefined;
                if (changeAccessorvalue || changeAccessorvalue != undefined) {
                    let value = Math.abs(series.data[0][data.selectRangeBy]);
                    let selectedRange = data.colorsRange.filter(r => value > r.valueLL && value <= r.valueUL);
                    let valueType = Math.sign(series.data[0][data.selectRangeBy]);
                    if (selectedRange.length > 0) {
                        series.color = selectedRange[0].range === 'neutral'
                            ? data.colorCodes[selectedRange[0].range]
                            : valueType >= 1
                                ? data.colorCodes.positive[selectedRange[0].range]
                                : valueType <= -1
                                    ? data.colorCodes.negetive[selectedRange[0].range]
                                    : data.colorCodes['neutral'];
                    } else {
                        series.color = data.colorCodes['neutral'];
                    }

                }
            }
        })
        return;
    }
    catch (err) {
        log.error(`<<<<< Exited common-services.getColors.service with error ${err}`);
        throw new IFPError(404,'Unable to get color code by range');
    }
}

async function setCookie(username,cookie){
    try{
        if(constants.redis.enabled){
            setRedis(username, cookie, constants.redis.cmsResponseTTL, {})
        }
        else{
            const currentTime = moment().format('DD-MMM-YYYY hh:mm:ss A');
            let query = 
            `MERGE INTO IFP_CMS_SESSION t
                USING (SELECT '${username}' AS USER_NAME FROM dual) s
        
            ON (t.USER_NAME=s.USER_NAME)
            WHEN MATCHED THEN
            UPDATE SET t.COOKIE = '${cookie}', t.EXPIRY_DT = TO_TIMESTAMP('${currentTime}')  + interval '10' minute 
            
            WHEN NOT MATCHED THEN
            INSERT (USER_NAME,COOKIE,EXPIRY_DT)
            Values('${username}','${cookie}',TO_TIMESTAMP('${currentTime}') + interval '10' minute)`
            await db.simpleExecute(query, {});
        }
    }
    catch(err){
        log.error(`Error while setting cookie ${err}`)
        throw new IFPError(500,'Error while setting cookie')
    }
}

async function getCookie(username){
    try{
        if (constants.redis.enabled){
            let cookie = await getRedis(username)
            return cookie
        }
        else{
            const currentTime = moment().format('DD-MMM-YYYY hh:mm:ss A');
            let query = `SELECT COOKIE FROM IFP_CMS_SESSION WHERE USER_NAME='${username}' AND extract( day from (EXPIRY_DT-TO_TIMESTAMP('${currentTime}'))*24*60*60)>180 `
            let data = await db.simpleExecute(query, {})
            if (data.length){
                return data[0].COOKIE;
            }
            else{
                return null
            }
        }
    }
    catch(err){
        log.error(`Error while setting cookie ${err}`)
        throw new IFPError(500,'Error while setting cookie')
    }
}

module.exports = {
    formatReqToLogMsg, getMetaFromCMS,getMetaFromCMSAdmin, cleanupCMSData, getUrlWithBasepath, getIndicatorValuesIcon, getColors, setCookie, getCookie
}