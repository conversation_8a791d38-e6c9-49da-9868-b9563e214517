const express = require('express');
const router = new express.Router();
const chartInsightsController = require('../microservice-insights/insightsv3.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/list/:id', async (req, res, next) => {
    try {
      const data = await chartInsightsController.listChartInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
});

router.post('/add', async (req, res, next) => {
  try {
    const data = await chartInsightsController.addChartInsights(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.put('/update/:id', async (req, res, next) => {
  try {
    const data = await chartInsightsController.updateChartInsights(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.delete('/delete/:id', async (req, res, next) => {
  try {
    const data = await chartInsightsController.deleteChartInsights(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.post('/approve', async (req, res, next) => {
  try {
    const data = await chartInsightsController.approveChartInsights(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.post('/reject', async (req, res, next) => {
  try {
    const data = await chartInsightsController.rejectChartInsights(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.post('/request-approval', async (req, res, next) => {
  try {
    const data = await chartInsightsController.sendChartInsightsForApproval(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.post('/request-edit', async (req, res, next) => {
  try {
    const data = await chartInsightsController.requestEditChartInsights(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});
  


module.exports = router;
