const { IFPError } = require("../../utils/error")
const constants = require("../../config/constants.json");
const { getCensusAccessData } = require("../../microservice-geospatial-revamp/services/executeQuery.service");
const validateGeospatialPermissions = async (req,res,next)=>{
    try{
        let populationAccess = false;
        let realEstateAccess = false;
        let labourForceAccess = false;

        let userGroups = req.user.groups
        const populationGroups = constants.geospatialAccessGroups.population
        const realEstateGroups = constants.geospatialAccessGroups.realEstate
        const labourForceGroups = constants.geospatialAccessGroups.labourForce

        req.geospatialAccess = {
            "communityAccess": false,
            "nationalityAccess": false,
            "religionAccess": false
        }

        if (req.user.groups.includes(constants.geospatialAccessGroups.community)){ //For checking community access. Only a temp solution. To be migrated to CMS for unified access control.
            req.geospatialAccess.communityAccess = true
        }
        if (req.user.groups.includes(constants.geospatialAccessGroups.nationality)){ //For checking community access. Only a temp solution. To be migrated to CMS for unified access control.
            req.geospatialAccess.nationalityAccess = true
        }
        if (req.user.groups.includes(constants.geospatialAccessGroups.religion)){ //For checking community access. Only a temp solution. To be migrated to CMS for unified access control.
            req.geospatialAccess.religionAccess = true
        }

        let censusModuleAccessData = await getCensusAccessData(req.user.preferred_username, 1);
        if (censusModuleAccessData && censusModuleAccessData.length > 0) {
            censusModuleAccessData.forEach(access => {
                if (access.MODULE === 'NATIONALITY') {
                    req.geospatialAccess.nationalityAccess = true;
                }
                if (access.MODULE === 'RELIGION') {
                    req.geospatialAccess.religionAccess = true;
                }
            })
        }
        
        populationAccess = userGroups.some(group => populationGroups.includes(group));
        realEstateAccess = userGroups.some(group => realEstateGroups.includes(group));
        labourForceAccess = userGroups.some(group => labourForceGroups.includes(group));
        
        if (populationAccess || realEstateAccess || labourForceAccess) {
            req.geospatialAccess = {...req.geospatialAccess,
                populationAccess,
                realEstateAccess,
                labourForceAccess
            };
        } else {
            throw new Error("User does not have access to any geospatial domains.");
        }

        next();
    }
    catch(err){
        next(new IFPError(403, `Error validating geospatial permissions: ${err.message}`));
    }
}

module.exports = {
    validateGeospatialPermissions
}