{"paths": {"/content-type/whatsnew/": {"get": {"tags": [" What's New"], "summary": "Retrieves latest Official and Experimental indicators for What's New Section", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "Title of the page."}, "warnings": {"type": "array", "items": {"type": "string"}, "description": "List of warnings related to the page, if any."}, "pageDescription": {"type": ["string", "null"], "description": "Description of the page."}, "pageType": {"type": "string", "description": "Type of the page, indicating it's a 'what's new' page."}, "subsections": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "Label for the subsection."}, "title": {"type": "string", "description": "Title of the subsection."}, "key": {"type": "string", "description": "Unique key identifier for the subsection."}, "sectionAlignment": {"type": "string", "description": "Alignment of the section on the page."}, "sectionOrder": {"type": "integer", "description": "Order in which the section appears on the page."}, "indicatorList": {"type": "array", "items": {"type": "object", "properties": {"indicatorId": {"type": "integer", "description": "Unique identifier for the indicator."}, "title": {"type": "string", "description": "Title of the indicator."}, "contentType": {"type": "string", "description": "Type of content the indicator represents."}, "appType": {"type": ["string", "null"], "description": "Type of application the indicator is associated with, if applicable."}, "type": {"type": "string", "description": "Type classification of the indicator."}, "domain": {"type": "string", "description": "Domain to which the indicator belongs."}}, "required": ["indicatorId", "title", "contentType", "type", "domain"]}, "description": "List of indicators under the subsection."}}, "required": ["label", "title", "key", "sectionAlignment", "sectionOrder", "indicatorList"]}}}, "required": ["title", "warnings", "pageType", "subsections"], "description": "Schema for a response detailing a 'What's new' page with various subsections and indicators."}}}}}}}}}