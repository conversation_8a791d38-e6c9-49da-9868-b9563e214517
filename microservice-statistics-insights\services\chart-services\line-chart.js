const scadLibrary = require('scad-library');

const { calculateQuarterYearChanges } = require('../quarterYearChange.js');
const Logger = scadLibrary.logger;
const log = new Logger().getInstance();
const { util } = scadLibrary;
const moment = require('moment');

/**
 * Function to process data according to how the UI needs it
 * @param {*} data object
 * @param {*} seriesData object
 * @param {*} seriesValues object
 */
const processLineChartData = (data, visualization, indicatorType, maxPointLimit) => {
  try {
    visualization.seriesMeta.forEach(series => {
      series["data"] = [];
    });

    return new Promise((resolve, reject) => {
      if (indicatorType === 'coi') {
        if (visualization.dimension) {
          try {
            if (data.length > 0) {
              data.forEach(element => {
                if (element) {
                  element.OBS_DT = util.convertDate(element["OBS_DT"].toString());
                  visualization.seriesMeta.forEach(series => {
                    if (series.dimensionValue.toLowerCase() === (element[visualization.dimensionColumn]).toLowerCase().trim()) {
                      Object.entries(element).forEach(([key, value]) => {
                        if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                          delete element[key];
                        }
                      });
                      element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                      series.data.push(element);
                    }
                  })
                } else {
                  log.debug(`Received null value inside db result`);
                }
              })
            } else {
              log.error(`Data not available in DB for visualization ${visualization}`);
              reject([404, `Data not available in DB`]);
            }
            let scadLastElement = [];
            visualization.seriesMeta.forEach(series => {
              if (series.data && series.data.length > 0) {
                if (maxPointLimit && !series.id.includes('-forecast')) {
                  series.data = series.data.slice(`-${maxPointLimit}`);
                }  
                let xFields = []; let yFields = [];
                if (!series.id.includes('-forecast')) {
                  scadLastElement.push({ id: `${series.id}-forecast`, data: series.data[series.data.length - 1] });
                  if (visualization.markersMeta.length > 0) {
                    visualization.markersMeta[0].data = { "DATE": series.data[series.data.length - 1][series.xAccessor.path] }
                  }
                }
                series.data.forEach(element => {
                  xFields.push(new Date(element[series.xAccessor.path]));
                  yFields.push(element[series.yAccessor.path]);
                  if ("VALUE_UL" in element)
                    yFields.push(element.VALUE_UL);
                  if ("VALUE_LL" in element)
                    yFields.push(element.VALUE_LL);
                });

                if (yFields.length > 0) {
                  series.yMax = Math.max(...yFields);
                  series.yMin = Math.min(...yFields);
                }

                if (xFields.length > 0) {
                  series.xMin = util.convertDate(Math.min.apply(null, xFields));
                  series.xMax = util.convertDate(Math.max.apply(null, xFields));
                }
              }
            })
            
            if (visualization.showQuarterOnQuarter || visualization.showYearOnYear)
              visualization = calculateQuarterYearChanges(visualization)
            

            visualization.seriesMeta.forEach(series => {
              if (series.id.includes('-forecast')) {
                scadLastElement.forEach(element => {
                  if (series.id === element.id) {
                    series.data.unshift(element.data);
                  }
                })
              }
            });
            if (!visualization.showPointLabels) {
              visualization["showPointLabels"] = false;
            }
            resolve(visualization);
          } catch (err) {
            
            log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
            reject([422, err]);
          }
        } else {
          try {
            visualization.seriesMeta.forEach(series => {
              if (data && data.length > 0) {
                data.forEach(element => {
                  if (element) {
                    element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
                    let count = [];
                    let dimensionLength = Object.keys(series.dimension).length;
                    Object.entries(series.dimension).forEach(([column, value]) => {
                      let elementValue = element[column] !== null && element[column] !== undefined ? element[column].toUpperCase().trim() : "";
                      let dimensionValue = value !== null ? value.toUpperCase().trim() : "";
                      if (value === element[column] || dimensionValue === elementValue) {
                        count.push(1);
                      }
                    });
                    if (count.length === dimensionLength) {
                      Object.entries(element).forEach(([key, value]) => {
                        if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                          delete element[key];
                        }
                      });
                      element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                      series.data.push(element);
                    }
                  } else {
                    log.debug(`Received null value inside db result`);
                  }
                })
              } else {
                log.error(`Data not available in DB for visualization ${visualization}`);
                reject([404, `Data not available in DB`]);
              }
            })

            if (visualization.showQuarterOnQuarter || visualization.showYearOnYear)
              visualization = calculateQuarterYearChanges(visualization)

            let scadLastElement = [];
            visualization.seriesMeta.forEach(series => {
              if (maxPointLimit && !series.id.includes('-forecast')) {
                series.data = series.data.slice(`-${maxPointLimit}`);
              }
              if (series.data && series.data.length > 0) {
                let xFields = []; let yFields = [];
                if (!series.id.includes('-forecast') && series.data.length > 0) {
                  scadLastElement.push({ id: `${series.id}-forecast`, data: series.data[series.data.length - 1] });
                  if (visualization.markersMeta.length > 0) {
                    visualization.markersMeta[0].data = { "DATE": util.convertDate(`${series.data[series.data.length - 1][series.xAccessor.path]}`) }
                  }
                }
                series.data.forEach(element => {
                  xFields.push(new Date(element[series.xAccessor.path]));
                  yFields.push(element[series.yAccessor.path]);
                  if ("VALUE_UL" in element)
                    yFields.push(element.VALUE_UL);
                  if ("VALUE_LL" in element)
                    yFields.push(element.VALUE_LL);
                });

                if (yFields.length > 0) {
                  series.yMax = Math.max(...yFields);
                  series.yMin = Math.min(...yFields);
                }

                if (xFields.length > 0) {
                  series.xMin = util.convertDate(Math.min.apply(null, xFields));
                  series.xMax = util.convertDate(Math.max.apply(null, xFields));
                }
              }
            })
            
            visualization.seriesMeta.forEach(series => {
              if (series.id.includes('-forecast')) {
                scadLastElement.forEach(element => {
                  if (series.id === element.id) {
                    series.data.unshift(element.data);
                  }
                })
              }
            });
            if (!visualization.showPointLabels) {
              visualization["showPointLabels"] = false;
            }
            resolve(visualization);
          } catch (err) {
            
            log.error(`Error in microservice-statistics-insights.services.chart-services.line-chart.processLineChartData ${err}`);
            reject([422, err]);
          }
        }
      } else if (indicatorType === 'scad') {
        if (visualization.dimension) {
          try {
            if (data.length > 0) {
              data.forEach(element => {
                if (element) {
                  element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
                  visualization.seriesMeta.forEach(series => {
                    if (series.dimensionValue && (element[visualization.dimensionColumn]) && series.dimensionValue.toLowerCase().trim() === (element[visualization.dimensionColumn]).toLowerCase().trim()) {
                      Object.entries(element).forEach(([key, value]) => {
                        if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                          delete element[key];
                        }
                      });
                      element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                      series.data.push(element);
                    } else if (series.dimension) {
                      let count = [];
                      let dimensionLength = Object.keys(series.dimension).length;
                      Object.entries(series.dimension).forEach(([column, value]) => {
                        let elementValue = element[column] !== null ? element[column].toUpperCase().trim() : "";
                        let dimensionValue = value !== null ? value.toUpperCase().trim() : "";
                        if (value === element[column] || dimensionValue === elementValue) {
                          count.push(1);
                        }
                      });
                      if (count.length === dimensionLength) {
                        Object.entries(element).forEach(([key, value]) => {
                          if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                            delete element[key];
                          }
                        });
                        element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                        series.data.push(element);
                      }
                    }
                  })
                } else {
                  log.debug(`Received null value inside db result`);
                }
              })
            } else {
              log.error(`Data not available in DB for visualization ${visualization}`);
              reject([404, `Data not available in DB`]);
            }
            visualization.seriesMeta.forEach(series => {
              if (series.data && series.data.length > 0) {
                if (maxPointLimit) {
                  series.data = series.data.slice(`-${maxPointLimit}`);
                }
                let xFields = []; let yFields = [];
                series.data.forEach(element => {
                 
                  xFields.push(new Date(element[series.xAccessor.path]));
                  yFields.push(element[series.yAccessor.path]);
                  if ("VALUE_UL" in element)
                    yFields.push(element.VALUE_UL);
                  if ("VALUE_LL" in element)
                    yFields.push(element.VALUE_LL);
                });

                if (yFields.length > 0) {
                  series.yMax = Math.max(...yFields);
                  series.yMin = Math.min(...yFields);
                }

                if (xFields.length > 0) {
                  series.xMin = util.convertDate(Math.min.apply(null, xFields));
                  series.xMax = util.convertDate(Math.max.apply(null, xFields));
                }
              }
            })
            if (!visualization.showPointLabels) {
              visualization["showPointLabels"] = false;
            }
            resolve(visualization);
          } catch (err) {
            
            log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
            reject([422, err]);
          }
        } else {
          try {
            if (data.length > 0) {
              data.forEach(element => {
                if (element) {
                  element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
                  visualization.seriesMeta.forEach(series => {
                    if (series.dbIndicatorId) {
                      if (series.dbIndicatorId.toLowerCase() === (element[visualization.dbColumn]).toLowerCase()) {
                        Object.entries(element).forEach(([key, value]) => {
                          if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                            delete element[key];
                          }
                        });
                        element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                        series.data.push(element);
                      }
                    }
                  });
                } else {
                  log.debug(`Received null value inside db result`);
                }
              })
              visualization.seriesMeta.forEach(series => {
                if (visualization.yearlyData) {
                  let yearlyData = data.filter(e => e[visualization.yearlyData.dbColumn] === visualization.yearlyData.dbIndicatorId);
                  series.data = series.data.concat(yearlyData);
                }
                if (maxPointLimit) {
                  series.data = series.data.slice(`-${maxPointLimit}`);
                }
                let xFields = []; let yFields = [];
                series.data.forEach(element => {
                  xFields.push(new Date(element[series.xAccessor.path]));
                  yFields.push(element[series.yAccessor.path]);
                  if ("VALUE_UL" in element)
                    yFields.push(element.VALUE_UL);
                  if ("VALUE_LL" in element)
                    yFields.push(element.VALUE_LL);
                });

                if (yFields.length > 0) {
                  series.yMax = Math.max(...yFields);
                  series.yMin = Math.min(...yFields);
                }

                if (xFields.length > 0) {
                  series.xMin = util.convertDate(Math.min.apply(null, xFields));
                  series.xMax = util.convertDate(Math.max.apply(null, xFields));
                }
              })
            }
            else {
              log.error(`Data not available in DB for visualization ${visualization}`);
              reject([404, `Data not available in DB`]);
            }
            if (!visualization.showPointLabels) {
              visualization["showPointLabels"] = false;
            }
            resolve(visualization);

          } catch (err) {
            
            log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
            reject([422, err]);
          }
        }
      }
      else if (indicatorType === 'official_statistics' || indicatorType === 'innovative_statistics') {
        if (visualization.dimension) {
          try {
            if (data.length > 0) {
              data.forEach(element => {
                if (element) {
                  element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
                  visualization.seriesMeta.forEach(series => {
                    if (series.dimensionValue && (element[visualization.dimensionColumn]) && series.dimensionValue.toLowerCase().trim() === (element[visualization.dimensionColumn]).toLowerCase().trim()) {
                      Object.entries(element).forEach(([key, value]) => {
                        if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                          delete element[key];
                        }
                      });
                      element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                      series.data.push(element);
                    } else if (series.dimension) {
                      let count = [];
                      let dimensionLength = Object.keys(series.dimension).length;
                      Object.entries(series.dimension).forEach(([column, value]) => {
                        let elementValue = element[column] !== null ? element[column].toUpperCase().trim() : "";
                        let dimensionValue = value !== null ? value.toUpperCase().trim() : "";
                        if (value === element[column] || dimensionValue === elementValue) {
                          count.push(1);
                        }
                      });
                      if (count.length === dimensionLength) {
                        Object.entries(element).forEach(([key, value]) => {
                          if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                            delete element[key];
                          }
                        });
                        element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                        series.data.push(element);
                      }
                    }
                  })
                } else {
                  log.debug(`Received null value inside db result`);
                }
              })
            } else {
              log.error(`Data not available in DB for visualization ${visualization}`);
              reject([404, `Data not available in DB`]);
            }
            visualization.seriesMeta.forEach(series => {
              if (series.data && series.data.length > 0) {
                if (maxPointLimit) {
                  series.data = series.data.slice(`-${maxPointLimit}`);
                }
                let xFields = []; let yFields = [];
                series.data.forEach(element => {
                 
                  xFields.push(new Date(element[series.xAccessor.path]));
                  yFields.push(element[series.yAccessor.path]);
                  if ("VALUE_UL" in element)
                    yFields.push(element.VALUE_UL);
                  if ("VALUE_LL" in element)
                    yFields.push(element.VALUE_LL);
                });
                if (yFields.length > 0) {
                  series.yMax = Math.max(...yFields);
                  series.yMin = Math.min(...yFields);
                }
                if (xFields.length > 0) {
                  series.xMin = util.convertDate(Math.min.apply(null, xFields));
                  series.xMax = util.convertDate(Math.max.apply(null, xFields));
                }
              }
            })
            if (!visualization.showPointLabels) {
              visualization["showPointLabels"] = false;
            }
            resolve(visualization);
          } catch (err) {
            
            log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
            reject([422, err]);
          }
        } else {
          try {
            if (data.length > 0) {
              visualization.seriesMeta[0].data = data
              
              visualization.seriesMeta.forEach(series => {
                
                if (maxPointLimit) {
                  series.data = series.data.slice(`-${maxPointLimit}`);
                }
                
                series.yMax = series.data[0][`MAX_${series.yAccessor.path}`];
                series.yMin = series.data[0][`MIN_${series.yAccessor.path}`];
                series.xMin = series.data[0][`MIN_${series.xAccessor.path}`];
                series.xMax = series.data[0][`MAX_${series.xAccessor.path}`];
              })
            }
            else {
              log.error(`Data not available in DB for visualization ${visualization}`);
              reject([404, `Data not available in DB`]);
            }
            if (!visualization.showPointLabels) {
              visualization["showPointLabels"] = false;
            }
            resolve(visualization);
          } catch (err) {
            
            log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
            reject([422, err]);
          }
        }
      } 
    });
  } catch (err) {
    
    log.error(`Error in microservice-statistics-insights.services.chart-services.line-chart.processLineChartData ${err}`);
    reject([422, err]);
  }
}

module.exports = { processLineChartData };
