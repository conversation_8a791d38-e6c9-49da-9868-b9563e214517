const Logger = require('scad-library').logger;
const moment = require('moment');
const log = new Logger().getInstance();
require('dotenv').config();
const { getMetaFromCMS, getIndicatorValuesIcon, getColors } = require('../services/common-service');
const statisticsInsightsController = require('../microservice-statistics-insights/statistics-insights.controller');
const constants = require('../config/constants.json');

async function indicatorsCrawler(){
    const lang = '';
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST}`;
    const domains = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, 'developer');
    for (let domain of Object.values(domains.domain)){
        log.info(`Fetching nodes for domain: ${domain.parent_domain_name}`)
        const cmsDomainDetailUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAIN_DETAIL}${domain.id}`;
        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainDetailUrl, 'developer');
        classifications =  Object.values(data.data).filter(classification => [constants.classifications.innovativeStatistics,constants.classifications.officialStatistics].includes(classification.classification))
        for (let classification of classifications){
            for (let domain of classification.domains){
                for( let subdomain of domain.subdomains){
                    for( let subtheme of subdomain.subthemes){
                        for( let product of subtheme.products){
                            let nodes = product.nodes
                            for (let node of nodes){
                                const req = {
                                    params : {
                                        id:node.id
                                    },
                                    user:{
                                        groups:'developer'
                                    },
                                    headers:{
                                        'accept-language':'en'
                                    },
                                    query:{

                                    }
                                }
                                try{
                                let response = await new statisticsInsightsController().getStatisticsInsightsById(req)
                                }
                                catch(exp){}
                            }
                            
                        }
                    }
                }
            }
        }

    }
    
}

module.exports = {indicatorsCrawler};
