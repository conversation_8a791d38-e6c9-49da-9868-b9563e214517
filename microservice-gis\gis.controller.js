const Logger = require('scad-library').logger;
require('dotenv').config();
const log = new Logger().getInstance();

const { getMapDistrictsColumnsData, getMapDistrictsData, getMapDistrictsV2Data, getMapDistrictsV3Data } = require('./services/executeQuery');
var qs = require('qs');
const http = require('http');
const https = require('https');
const axiosInstance = require('axios');
const axios = axiosInstance.create({
    httpAgent: new http.Agent({ keepAlive: true }),
    httpsAgent: new https.Agent({ keepAlive: true })
});

async function getMapDistricts(req) {
  log.debug(`>>>>>Entered microservice.whatsnew.controller.getWhatsNew`);
  return new Promise(async (resolve, reject) => {

      try {

        let data = await getMapDistrictsData()
        if (data.length)
          resolve(data[0])
        else
          resolve({'message':'No data available','status':false})

      } catch (err) {
          
          log.error(`<<<<< Exited microservice.gis.controller.getMapDistricts with error ${err} `)
          reject(err);
      }
  });

}

async function getMapDistrictsV2(req) {
    log.debug(`>>>>>Entered microservice.whatsnew.controller.getWhatsNew`);
    return new Promise(async (resolve, reject) => {

        try {
          let query = req.query
          let cleanedQuery = {}
          let filters = undefined
          Object.entries(query).map(([key,value])=>{
            if(key=='filters')
              filters = value
            else
              cleanedQuery[key] = value
          })
          let data = await getMapDistrictsV2Data(cleanedQuery,filters)
          if (data.length){
            let result = {
              "crs": {
                "properties": {
                    "name": "urn:ogc:def:crs:OGC:1.3:CRS84"
                },
                "type": "name"
              },
              "name": "Districts_geJSON",
              "type": "FeatureCollection",
              "features":data
            }
            resolve(result)
         }
          else
            resolve({'message':'No data available','status':false})

        } catch (err) {
            
            log.error(`<<<<< Exited microservice.gis.controller.getMapDistricts with error ${err} `)
            reject(err);
        }
    });

}


async function getMapDistrictsV3(req) {
  log.debug(`>>>>>Entered microservice.whatsnew.controller.getWhatsNew`);
  return new Promise(async (resolve, reject) => {

      try {
        let query = req.query
        let cleanedQuery = {}
        let filters = undefined

        Object.entries(query).map(([key,value])=>{
          if(key=='filters')
            filters = value
          else
            cleanedQuery[key] = value
        })

        let columns = await getMapDistrictsColumnsData()
        let data = await getMapDistrictsV3Data(cleanedQuery,filters,columns)
        if (data.length){
          featureData = data.map(d=>{
            const featureObj = {
              geometry:{
                coordinates:d.GEOMETRY,
                type: "MultiPolygon"
              },
              properties: {},
              type: "Feature"
            }
            Object.entries(d).map(([key,value])=>{
              if (! ['COORDS','GEOMETRY','LONGX','LATY'].includes(key))
                featureObj.properties[key] = value
            })
            return featureObj
          })
          let result = {
            "crs": {
              "properties": {
                  "name": "urn:ogc:def:crs:OGC:1.3:CRS84"
              },
              "type": "name"
            },
            "name": "Districts_geJSON",
            "type": "FeatureCollection",
            "features":featureData
          }
          resolve(result)
       }
        else
          resolve({'message':'No data available','status':false})

      } catch (err) {
          
          log.error(`<<<<< Exited microservice.gis.controller.getMapDistricts with error ${err} `)
          reject(err);
      }
  });

}

async function getArcGISToken() {
  log.debug('>>>>>Entered microservice-gis.getArcGISToken');
  log.info(`API call to ${process.env.GIS_AUTH_URL} from getArcGISToken`);
  try{
      let body = qs.stringify({
          client_id: process.env.GIS_CLIENT_ID,
          client_secret: process.env.GIS_CLIENT_SECRET,
          grant_type: "client_credentials",
          expiration: 1440,
          redirect_uri: process.env.GIS_REDIRECT_URL,
      })
      let response = await axios.post(`${process.env.GIS_AUTH_URL}`,body,{
              headers:
                  { 
                      'Content-Type' : 'application/x-www-form-urlencoded; charset=UTF-8'
                  }
          }
      )
      return response.data
  }
  catch(err){
      throw err
  }
}


module.exports = {
  getMapDistrictsColumnsData,
  getMapDistricts,
  getMapDistrictsV2,
  getMapDistrictsV3,
  getArcGISToken
};
