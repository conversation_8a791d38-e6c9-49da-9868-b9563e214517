const axios = require("axios").default;
const { getRedis } = require("../../services/redis.service");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

/**
 * get dxp token on behalf of via bayaan token
 */
async function getDXPTokenOBO(bayaanToken) {
  // TODO: Implement Entra OBO
  // See: https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-node/docs/request.md#on-behalf-of-flow
  const dxpToken = await getRedis("node_be:dxp:dxpToken");
  return dxpToken;
}

// Dxp platform api client
const dxpApiClient = axios.create({
  baseURL: "https://marketplace-api.stg.dxp.data.abudhabi/prod/api",
});

dxpApiClient.interceptors.request.use(
  async (config) => {
    // const { authorization: bayaanToken } = config.headers;
    const bayaanToken = config.headers.Authorization;
    const dxpOboToken = await getDXPTokenOBO(bayaanToken);
    config.headers.authorization = `Bearer ${dxpOboToken}`;
    log.info(`Set DXP token`);
    return config;
  },
  (error) => {
    log.error(`DXP Interceptor failed with: ${error}`);
    throw error;
  }
);

const deltaShareConsumer = axios.create({
  baseURL: "http://localhost:8000/api",
  headers: {
    "x-api-key": "88e68d67-390a-448f-a0ec-2f571740dc75",
  },
});

/**
 * Wrapper to fetch data from DXP.
 * @param {} req
 * @param {string} endpoint
 * @returns
 */
async function getDXPData(req, method, endpoint, body = undefined) {
  try {
    const response = await dxpApiClient.request({
      method: method || req.method,
      headers: {
        Authorization: req.headers["authorization"],
      },
      url: endpoint,
      data: body,
    });
    const data = response.data;
    return data;
  } catch (error) {
    log.error(`getDXPData failed with error: ${error}`);
    throw error;
  }
}

module.exports = {
  getDXPData,
  deltaShareConsumer,
};
