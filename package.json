{"name": "scad-insights-be", "version": "2.2.3", "description": "IFP 2.0 Backend Project", "main": "server.js", "engines": {"node": "^18.16.1"}, "scripts": {"start": "node server.js", "start-watch": "nodemon server.js", "pm2": "pm2", "build": "webpack", "coverage": "./node_modules/.bin/jest ../scad-insights-be --coverage", "test": "jest", "test:hc": "jest --config=jest.config.hc.js", "sequelize:postgres:migration:generate": "sequelize --options-path ./.postgres.sequelizerc migration:generate", "sequelize:postgres:migration:migrate": "sequelize --options-path ./.postgres.sequelizerc db:migrate", "sequelize:oracle:migration:generate": "sequelize --options-path ./.oracle.sequelizerc migration:generate", "sequelize:oracle:migration:migrate": "sequelize --options-path ./.oracle.sequelizerc db:migrate"}, "author": "ACN", "license": "ISC", "bundledDependencies": ["express", "scad-library"], "pre-commit": [], "dependencies": {"@aws-sdk/client-s3": "^3.600.0", "@azure/msal-node": "^2.6.2", "@azure/openai": "^2.0.0", "@clickhouse/client": "^0.0.15", "axios": "^0.21.0", "cors": "^2.8.5", "dotenv": "^8.2.0", "encodeurl": "^1.0.2", "express": "^4.17.1", "express-status-monitor": "^1.3.4", "express-validator": "^7.0.1", "file-type": "^11.0.0", "html-entities": "^2.0.2", "ioredis": "^5.3.2", "js-base64": "^3.7.7", "jsonwebtoken": "^9.0.2", "moment": "^2.29.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "node-cron": "^3.0.2", "nodemailer": "^6.6.0", "nodemon": "^3.0.3", "nunjucks": "^3.2.4", "openai": "^4.93.0", "oracledb": "^6.2.0", "passport": "^0.4.1", "passport-azure-ad": "^4.3.0", "passport-http-bearer": "^1.0.1", "passport-jwt": "^4.0.1", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "pm2": "^5.3.0", "pre-commit": "^1.2.2", "puppeteer": "^24.4.0", "redis": "^4.0.0", "redlock": "^4.2.0", "sanitize-html": "^2.13.1", "scad-library": "^1.18.24", "sequelize": "^6.37.6", "spdy": "^4.0.2", "superagent": "^6.1.0", "swagger-ui-express": "^4.1.6", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "babel-loader": "^8.1.0", "jest": "^26.6.3", "jest-allure": "^0.1.3", "jest-junit": "^12.2.0", "sequelize-cli": "^6.6.2"}, "bundleDependencies": ["express", "scad-library"]}