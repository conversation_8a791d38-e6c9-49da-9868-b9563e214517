const express = require('express');
const router = new express.Router();
const slaController = require('../microservice-sla/sla.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/check', async (req, res, next) => {
    try {
      const data = await slaController.getSLAStatus(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
