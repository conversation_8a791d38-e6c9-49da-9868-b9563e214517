const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getComputedDataQuery(indicatorId, viewName, dimensions,operation){
    try {
        let binds = {}
        let parentDimensions=[];
        let childDimensions = [
            {
                KEY: 'INDICATOR_ID',
                value: indicatorId
            }
        ]
        Object.entries(dimensions).map(([key,value])=>{
            if (Array.isArray(value))
                value.forEach(v=>{
                    parentDimensions.push({
                        KEY:key,
                        value:v
                    })
                })
            
            else
                childDimensions.push({
                    KEY:key,
                    value:value
                })
        })

        let conditionalDimensions = childDimensions.map(cDimension => `UPPER(${cDimension.KEY}) = UPPER('${cDimension.value}')`)

        let pQueries = []
        parentDimensions.forEach((pdimension,index)=>{
            let pConditionalDimensions = [`UPPER(${pdimension.KEY}) = UPPER('${pdimension.value}')`]
            pConditionalDimensions = pConditionalDimensions.concat(conditionalDimensions) .join(' AND ')

            if (index == 0){
                let opFields;
                if (operation == '/')
                    opFields = parentDimensions.map((p,i)=>i?`NULLIF(Q${i}.VALUE,0)`:`Q${i}.VALUE`).join(operation)
                else
                    opFields = parentDimensions.map((p,i)=>`Q${i}.VALUE`).join(operation)
                let valueFields = [`${opFields} AS RESULT`].concat([`Q0.OBS_DT AS OBS_DT`]).join(',')
                let query = `SELECT ${valueFields} FROM (
                    SELECT * FROM ${viewName} 
                        WHERE ${pConditionalDimensions}
                ) Q${index}`

                pQueries.push(query)
            }
            else{
                let query = `(SELECT VALUE,OBS_DT FROM (
                                SELECT * FROM ${viewName} 
                                    WHERE ${pConditionalDimensions}
                            )) Q${index} ON Q0.OBS_DT = Q${index}.OBS_DT`

                pQueries.push(query)
            }
        })

        let joinQuery = pQueries.join(' JOIN ')
        let resultQuery = `
        SELECT RESULT
                AS VALUE,
            MAX(VALUE) OVER() AS MAX_VALUE,
            MIN(VALUE) OVER() AS MIN_VALUE,
            formatDateTime(toDate(toString(MAX(OBS_DT) OVER())), '%Y-%m-%d') AS MAX_OBS_DT,
            formatDateTime(toDate(toString(MIN(OBS_DT) OVER())), '%Y-%m-%d') AS MIN_OBS_DT,
            formatDateTime(toDate(toString(OBS_DT)), '%Y-%m-%d') AS OBS_DT,
            formatDateTime(toDate(toString(OBS_DT)), '%Y') AS YEAR
        FROM (${joinQuery}) WHERE RESULT IS NOT NULL ORDER BY OBS_DT`
        return {query:resultQuery,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-compute.services.getQuery.service.getIndicatorDataQuery with error ${err} `);
        throw err;
    }

}

async function getIndicatorConfigurationQuery(indicatorId){
    try{
        let binds = {
            indicatorId: indicatorId
        }
        let query = 'SELECT * FROM VW_DYNAMIC_JSON_CONFIG WHERE INDICATOR_ID={indicatorId:String}'
        return {query:query,binds:binds}
    }
    catch(err){
        log.error(`<<<<<< Exited microservice-compute.services.getQuery.service.getIndicatorConfiguration with error ${err} `);
        throw err;
    }
}

async function getComputeNodeInfoQuery(indicatorId,lang){
    try {

        const binds = {
            indicatorId:indicatorId
        };

        const query = `SELECT 
        INDICATOR_ID,
        INDICATOR_NAME_${lang} AS INDICATOR_NAME,
        SOURCE_TABLE,
        UNIT
        FROM VW_INDICATOR_MAP WHERE INDICATOR_ID = {indicatorId:String}`;

        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-compute.services.getQuery.service.getComputeNodeInfoQuery with error ${err} `);
        throw err
    }
    
}
module.exports = { 
    getComputedDataQuery,
    getIndicatorConfigurationQuery,
    getComputeNodeInfoQuery
 };
