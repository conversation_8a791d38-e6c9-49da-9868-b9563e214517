const mailer = require('nodemailer');
const nunjucks = require('nunjucks');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const messages = require('./messages');
const { IFPError } = require('../../../utils/error');
const { getRoleName } = require('../../helper');

async function sendEmail(data) {
    try{
     
        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: false,
            auth:{
                user:'<EMAIL>',
                pass:'lBYvXp0Ql5eHSwWfcJnr3VwgFTUIy3bi'
            },
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });
        
        const oldEmailAttachments = [
            {
                filename: 'ifp-logo.png',
                path: __dirname + '/images/ifp-logo.png',
                cid: 'ifp-logo'
            },
            {
                filename: 'scad-logo.png',
                path: __dirname + '/images/scad-logo.png',
                cid: 'scad-logo'
            },
            {
                filename: 'link-icon.png',
                path: __dirname + '/images/link-icon.png',
                cid: 'link-icon'
            },
            {
                filename: 'mail-icon.png',
                path: __dirname + '/images/mail-icon.png',
                cid: 'mail-icon'
            },
            {
                filename: 'phone-icon.png',
                path: __dirname + '/images/phone-icon.png',
                cid: 'phone-icon'
            },
            {
                filename: 'spacer-image.png',
                path: __dirname + '/images/spacer-image.png',
                cid: 'spacer-image'
            },
        ];
        const newMessageBanner = {
            filename: 'email-new-message-banner.png',
            path: __dirname + '/images/email-new-message-banner.png',
            cid: 'email-new-message-banner'
        };

        let emailMessage;
        var mailOptions = {
            from: process.env.SYSTEM_MAILID,
            to: data.recepientEmail,
            subject: data.subject || `Bayaan Access`,
            attachments: oldEmailAttachments,
        };

        switch(data.userType){
            case 'USER':
                switch (data.emailType){
                    case 'PLATFORM_INVITE':
                        emailMessage = nunjucks.render(
                          "microservice-usersv3/platform-invite.njk",
                          { ...data, emailBanner: "invite-banner" }
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'register-proceed-icon.png',
                                path: __dirname + '/images/register-proceed-icon.png',
                                cid: 'register-proceed-icon'
                            }
                        ];
                        break;
                    case 'REGISTER_OTP':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/new-otp.njk",
                            { ...data, emailBanner: "generate-otp-banner" }
                        );
                        mailOptions.attachments = [{
                            filename: 'generate-otp-banner.png',
                            path: __dirname + '/images/generate-otp-banner.png',
                            cid: 'generate-otp-banner'
                        }];
                        break;
                    case 'EXISTING_USER_PLATFORM_INVITE':
                        data.userType = getRoleName(data.userType);
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/existing-user-platform-invite.njk",
                            { ...data, emailBanner: "invite-banner" }
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'register-proceed-icon.png',
                                path: __dirname + '/images/register-proceed-icon.png',
                                cid: 'register-proceed-icon'
                            }
                        ];
                        break;
                    case 'ACCESS_REVOKE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/access-revoked-notify-user.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'USER_DELETE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/user-delete-notify-user.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'REQUEST_STATUS_UPDATE':
                        if (data.action == "approved") {
                            emailMessage = nunjucks.render(
                                "microservice-usersv3/request-approved.njk",
                                { ...data, emailBanner: "email-new-message-banner"}
                            )
                        } else if (data.action == "rejected") {
                            emailMessage = nunjucks.render(
                                "microservice-usersv3/request-rejected-notify-user.njk",
                                { ...data, emailBanner: "email-new-message-banner"}
                            )
                        }
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'REGISTER_COMPLETE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/registration-complete.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_DOWNGRADE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/user-access-downgrade-notify-user.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    default:
                        throw new IFPError(500,'Invalid emailType')
                }
                
                break;

            case 'SUPERUSER':
                switch (data.emailType){
                    case 'ENTITY_INVITE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/platform-invite.njk",
                            { ...data, emailBanner: "invite-banner" }
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'register-proceed-icon.png',
                                path: __dirname + '/images/register-proceed-icon.png',
                                cid: 'register-proceed-icon'
                            }
                        ];
                        break;
                    case 'REGISTER_OTP':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/new-otp.njk",
                            { ...data, emailBanner: "generate-otp-banner" }
                        );
                        mailOptions.attachments = [{
                            filename: 'generate-otp-banner.png',
                            path: __dirname + '/images/generate-otp-banner.png',
                            cid: 'generate-otp-banner'
                        }];
                        break;
                    case 'REGISTER_COMPLETE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/registration-complete.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'EXISTING_USER_PLATFORM_INVITE':
                        data.userType = getRoleName(data.userType);
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/existing-user-platform-invite.njk",
                            { ...data, emailBanner: "invite-banner" }
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'register-proceed-icon.png',
                                path: __dirname + '/images/register-proceed-icon.png',
                                cid: 'register-proceed-icon'
                            }
                        ];
                        break;
                    case 'REQUEST_STATUS_UPDATE':
                        if (data.action == "approved") {
                            emailMessage = nunjucks.render(
                                "microservice-usersv3/request-approved.njk",
                                { ...data, emailBanner: "email-new-message-banner"}
                            )
                        } else if (data.action == "rejected") {
                            emailMessage = nunjucks.render(
                                "microservice-usersv3/request-rejected-notify-stakeholders.njk",
                                { ...data, emailBanner: "email-new-message-banner"}
                            )
                        }
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_REVOKE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/access-revoked-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_REQUEST_REVERT_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/request-revert.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'SECONDARY_SUPERUSER_ROLE_CHANGE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/secondary-superuser-delegation-notify-user.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'EXISTING_LINK_INIT_REQUEST':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/existing-user-link-init-request.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    default:
                        throw new IFPError(500,'Invalid emailType')
                }

                break;

            case 'DG':
                switch (data.emailType){
                    case 'PLATFORM_INVITE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/platform-invite.njk",
                            { ...data, emailBanner: "invite-banner" }
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'register-proceed-icon.png',
                                path: __dirname + '/images/register-proceed-icon.png',
                                cid: 'register-proceed-icon'
                            }
                        ];
                        break;
                    case 'REGISTER_COMPLETE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/registration-complete.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'REGISTER_OTP':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/new-otp.njk",
                            { ...data, emailBanner: "generate-otp-banner" }
                        );
                        mailOptions.attachments = [{
                            filename: 'generate-otp-banner.png',
                            path: __dirname + '/images/generate-otp-banner.png',
                            cid: 'generate-otp-banner'
                        }];
                        break;
                    case 'EXISTING_USER_PLATFORM_INVITE':
                        data.userType = getRoleName(data.userType);
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/existing-user-platform-invite.njk",
                            { ...data, emailBanner: "invite-banner" }
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'register-proceed-icon.png',
                                path: __dirname + '/images/register-proceed-icon.png',
                                cid: 'register-proceed-icon'
                            }
                        ];
                        break;
                    case 'NEW_ACCESS_REQUEST':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/new-access-request.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'REQUEST_STATUS_UPDATE':
                        if (data.action == "approved") {
                            emailMessage = nunjucks.render(
                                "microservice-usersv3/request-approved.njk",
                                { ...data, emailBanner: "email-new-message-banner"}
                            )
                        } else if (data.action == "rejected") {
                            emailMessage = nunjucks.render(
                                "microservice-usersv3/request-rejected-notify-stakeholders.njk",
                                { ...data, emailBanner: "email-new-message-banner"}
                            )
                        }
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_REVOKE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/access-revoked-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'USER_DELETE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/user-delete-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_REQUEST_REVERT_BACK_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/request-revert-back.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'SECONDARY_SUPERUSER_ROLE_CHANGE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/secondary-superuser-delegation-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_DOWNGRADE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/user-delete-downgrade-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    default:
                        throw new IFPError(500,'Invalid emailType')
                }

                break;

            case 'PRODUCT_ENGAGEMENT':
                switch (data.emailType){
                    case 'NEW_ACCESS_REQUEST':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/new-access-request.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'USER_DELETE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/user-delete-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_REVOKE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/access-revoked-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case "USER_WELCOME_EMAIL":
                        emailMessage = nunjucks.render(
                            "welcome-email.njk",
                            data
                        );
                        mailOptions.attachments = [
                            {
                                filename: 'invite-banner.png',
                                path: __dirname + '/images/invite-banner.png',
                                cid: 'invite-banner'
                            },
                            {
                                filename: 'arrow.png',
                                path: __dirname + '/images/arrow.png',
                                cid: 'arrow'
                            },
                            {
                                filename: 'lap.png',
                                path: __dirname + '/images/lap.png',
                                cid: 'lap'
                            },
                            {
                                filename: 'mobile.png',
                                path: __dirname + '/images/mobile.png',
                                cid: 'mobile'
                            },
                            {
                                filename: 'apple-icon.png',
                                path: __dirname + '/images/apple-icon.png',
                                cid: 'apple-icon'
                            },
                            {
                                filename: 'qr-im.png',
                                path: __dirname + '/images/qr-im.png',
                                cid: 'qr-im'
                            },
                            {
                                filename: 'or-arabic.png',
                                path: __dirname + '/images/or-arabic.png',
                                cid: 'or-arabic'
                            },
                            {
                                filename: 'lock.png',
                                path: __dirname + '/images/lock.png',
                                cid: 'lock'
                            },
                            {
                                filename: 'or.png',
                                path: __dirname + '/images/or.png',
                                cid: 'or'
                            },
                            {
                                filename: 'android-icon.png',
                                path: __dirname + '/images/android-icon.png',
                                cid: 'android-icon'
                            }
                        ];
                        break;
                    case 'ACCESS_REQUEST_REVERT_BACK_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/request-revert-back.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'SECONDARY_SUPERUSER_ROLE_CHANGE':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/secondary-superuser-delegation-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'EXISTING_LINK_INIT_REQUEST':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/existing-user-link-init-request.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    case 'ACCESS_DOWNGRADE_NOTIFICATION':
                        emailMessage = nunjucks.render(
                            "microservice-usersv3/user-delete-downgrade-notify-stakeholders.njk",
                            { ...data, emailBanner: "email-new-message-banner"}
                        );
                        mailOptions.attachments = [newMessageBanner];
                        break;
                    default:
                        throw new IFPError(500,'Invalid emailType')
                }

                break;
                        
           default:
            throw new IFPError(500,'Invalid user type');
                
        }

        mailOptions.html= emailMessage
        transporter.sendMail(mailOptions, function (error, info) {
            if (error) {
                throw error;
            }
            if (info) {
                log.info(`Email sent successfully to ${data.recepientEmail} \n${info.response}`);
            }
        })
    }
    catch(exp){
        throw exp;
    }
}


module.exports = {sendEmail}