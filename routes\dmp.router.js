const express = require('express');
const router = new express.Router();

const { getProductData, navigationListController, getOrCreateDeltaShareToken, getAssetData } = require("../microservice-dmp/dmp.controller");

router.put("/product/:productId/delta-share-token", getOrCreateDeltaShareToken)
router.get("/product/:productId/asset/:assetId/table-data", getAssetData)
router.get("/navigation", navigationListController)
router.get("/:productId", getProductData)

module.exports = router