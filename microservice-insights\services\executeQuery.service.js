const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { 
  listInsightDataQuery, addInsightDataQuery, getInsightDataQuery, updateInsightDataQuery, updateInsightStatusQuery, 
  approveInsightDataQuery, rejectInsightDataQuery, deleteInsightDataQuery,getSubmitRequestInsightsDataQuery,
  updateSubmitRequestInsightsDataQuery,getInsightsDataQuery, requestEditInsightDataQuery } = require('./getQuery.service'); 

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function listInsightData(nodeId) {
  return new Promise((resolve, reject) => {
    listInsightDataQuery(nodeId).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.listInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.listInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function addInsightData(insightData) {
  return new Promise((resolve, reject) => {
    addInsightDataQuery(insightData).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.addInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.addInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function getInsightData(id) {
  return new Promise((resolve, reject) => {
    getInsightDataQuery(id).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightData with error ${err}`);
      reject(err);
    })
  })
}
/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function getInsightsData(ids) {
  return new Promise((resolve, reject) => {
    getInsightsDataQuery(ids).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightData with error ${err}`);
      reject(err);
    })
  })
}
/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function updateInsightData(insightData) {
  return new Promise((resolve, reject) => {
    updateInsightDataQuery(insightData).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function updateInsightStatus(userEmail) {
  return new Promise((resolve, reject) => {
    updateInsightStatusQuery(userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateInsightStatus with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateInsightStatus with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function approveInsightData(ids,date) {
  return new Promise((resolve, reject) => {
    approveInsightDataQuery(ids,date).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.rejectInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.rejectInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function rejectInsightData(ids,date) {
  return new Promise((resolve, reject) => {
    rejectInsightDataQuery(ids,date).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.rejectInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.rejectInsightData with error ${err}`);
      reject(err);
    })
  })
}


/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function requestEditInsightData(ids,date) {
  return new Promise((resolve, reject) => {
    requestEditInsightDataQuery(ids,date).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.requestEditInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.requestEditInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function deleteInsightData(id) {
  return new Promise((resolve, reject) => {
    deleteInsightDataQuery(id).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.deleteInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.deleteInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function getSubmitRequestInsightsData(email, insightId) {
  return new Promise((resolve, reject) => {
    getSubmitRequestInsightsDataQuery(email, insightId).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getSubmitRequestInsightsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getSubmitRequestInsightsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function updateSubmitRequestInsightsData(email,insightId) {
  return new Promise((resolve, reject) => {
    updateSubmitRequestInsightsDataQuery(email, insightId).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateSubmitRequestInsightsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateSubmitRequestInsightsData with error ${err}`);
      reject(err);
    })
  })
}




async function getData(query) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-user-settings.services.executeQuery.service.getData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-user-settings.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-user-settings.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = { 
  listInsightData, addInsightData, getInsightData, updateInsightData, updateInsightStatus, approveInsightData,
   rejectInsightData, deleteInsightData, getSubmitRequestInsightsData, updateSubmitRequestInsightsData,getInsightsData, requestEditInsightData, getData };
