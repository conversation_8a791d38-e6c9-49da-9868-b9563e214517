const Logger = require("scad-library").logger;
require("dotenv").config();
const axios = require("axios").default;
const log = new Logger().getInstance();
const constants = require("../../config/constants.json");
const puppeteer = require("puppeteer");
const fs = require("fs");
const base64 = require("js-base64");
const insightsConstants = require("../helper/constants.json");
const model = require("../../database/models");
const config = require("../ai-insight-report.constants.json")[process.env.NODE_ENV || "dev"];

const SANADKOM_CLIENT_ID = config.SANADKOM_CLIENT_ID;
const SANADKOM_CLIENT_SECRET = config.SANADKOM_CLIENT_SECRET;
const SMART_PUBLISHER_BASE_URL = config.SMART_PUBLISHER_BASE_URL;

const sanadkomClient = axios.create({
  baseURL: SMART_PUBLISHER_BASE_URL
});

sanadkomClient.interceptors.request.use(async (config) => {
  let token = await getSanadkomAccessToken();
  config.headers = { Authorization: `Bearer ${token}` }
  return config;
});

// Function to find an object by name
function findObjectByName(data, targetName) {
  // Use find() to get the first matching object
  return data.result.find((item) => item.name === targetName);
}
function getQuarterMonths(quarter) {
  switch (quarter) {
    case 1:
      return "January-March";
    case 2:
      return "April-June";
    case 3:
      return "July-September";
    case 4:
      return "October-December";
    default:
      throw new Error("Invalid quarter value");
  }
}

function getQuarter(quarter) {
  switch (quarter) {
    case 1:
      return "First Quarter";
    case 2:
      return "Second Quarter";
    case 3:
      return "Third Quarter";
    case 4:
      return "Fourth Quarter";
    default:
      throw new Error("Invalid quarter value");
  }
}

async function getServiceID(domain_name) {
  // this function is used to get the service id from the domain name
  const service = insightsConstants.domainServiceIDs.find(
    (item) => item.domain === domain_name
  );
  return service ? service.serviceId : null;
}

async function restructureData(jsonData, lang) {
  try {
    let comparisonData = null;
    let report_data = null;
    let comparisonKey = "comparison"; // Default key name

    //   if (!jsonData.dataValues) {
    //     if (jsonData["comparison"]) {
    //       comparisonData = jsonData["comparison"];
    //     } else {
    //       comparisonData = jsonData;
    //     }
    //   }
    //   else {
    //   report_data = jsonData.dataValues.report_data;
    //   //Find the comparison key dynamically
    //   comparisonKey = Object.keys(report_data).find((key) =>
    //     Array.isArray(report_data[key]?.rows)
    //   );
    //   if (!comparisonKey) {
    //     throw new Error(
    //       "Invalid data structure: 'comparison' key with 'rows' array is missing."
    //     );
    //   }

    //  comparisonData = jsonData
    // }

    // Extract headers dynamically, excluding keys with '_progress', replace '_' with ' ', and capitalize the first letter of each word
    const headers =
      jsonData.rows.length > 0
        ? Object.keys(jsonData.rows[0])
            .filter((key) => !key.endsWith("_progress"))
            .map((key) =>
              key
                .replace(/_/g, " ")
                .replace(/\b\w/g, (char) => char.toUpperCase())
            )
        : [];

    // Restructure the data
    let reStructuredData = {
      [comparisonKey]: {
        headers,
        rows: jsonData.rows.map((row) => {
          const newRow = {};

          // Dynamically handle keys in the row
          Object.keys(row).forEach((key) => {
            if (key.endsWith("_progress")) {
              // Handle keys with '_progress'
              const baseKey = key.replace("_progress", "");
              if (baseKey in row) {
                newRow[baseKey] = {
                  value: row[baseKey] || 0,
                  progress: row[key],
                };
              }
            } else if (!Object.keys(row).includes(`${key}_progress`)) {
              // Handle keys without '_progress'
              newRow[key] = row[key] || 0;
            }
          });

          return newRow;
        }),
      },
    };
    //   if (jsonData.domain)  {// check if domain is present in the response
    //   reStructuredData.domain = {
    //     name: jsonData.domain.dataValues[lang === "en" ? "name_en" : "name_ar"],
    //     id: jsonData.domain.dataValues.cms_id,
    //   };
    //   reStructuredData.quarter = `Q${jsonData.quarter} Report (${getQuarterMonths(jsonData.quarter)})`;
    // }

    return reStructuredData;
  } catch (error) {
    console.error(error.message);
  }
}

// function for getting auth token from SANADKOM

async function getSanadkomAccessToken(req) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.getSanadkomAccessToken`
  );
  try {
    const client_id = `${SANADKOM_CLIENT_ID}`;
    const cliend_secret = `${SANADKOM_CLIENT_SECRET}`;
    const sanadkomAccessToken = await axios.post(
      `${SMART_PUBLISHER_BASE_URL}/Token/GetToken`,
      null,
      {
        params: {
          client_id: client_id,
          cliend_secret: cliend_secret,
        },
      }
    );

    return sanadkomAccessToken.data.result.accessToken;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getPublicationId() {
  try {
    let result = await sanadkomClient.get(
      `${SMART_PUBLISHER_BASE_URL}/Publication/GetPublicationList`
    );
    const publicationId = findObjectByName(
      result.data,
      "Bayaan Insights-Quarterly"
    );

    return publicationId.id;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getPriodicityId(publicationId) {
  try {
    let result = await sanadkomClient.get(
      `${SMART_PUBLISHER_BASE_URL}/Publication/GetPriodicity`,
      {
        params: {
          publicationId: publicationId,
        },
      }
    );
    const priodicityId = findObjectByName(result.data, "Quarterly");
    return priodicityId.id;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getIntervalId(publicationId, priodicityId, report_id) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.getIntervalId`
  );
  try {
    const reportData =  await model.AiInsightReport.findOne({
          where: {
            id: report_id,
          },
        })
    let result = await axios.get(
      `${SMART_PUBLISHER_BASE_URL}/Publication/GetInterval`,
      {
        params: {
          publicationId: publicationId,
          year: 2025,
          periodicityId: priodicityId,
        },
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    const targetQuarter = getQuarter(
      reportData.quarter ? reportData.quarter : 1
    );
    const getIntervalId = findObjectByName(result.data, targetQuarter);
    return getIntervalId.id;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function generatePdf(req, report_type) {
  try {
    let report_id = req.body?.report_id;
    let webpageUrl = null;
    let getToken = req.headers.authorization.replace("Bearer ", ""); // Get the token from the request header
    // const browser = await puppeteer.launch({    // Use this line for while you are in (local development)
    //   executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', //Step 1: Launch the browser
    //   headless: false, // Set headless: true for production
    //   args: ["--disable-web-security"],
    // });

    const browser = await puppeteer.launch({
      //Step 1: Launch the browser
      headless: true, // Set headless: true for production
      args: ["--disable-web-security", "--no-sandbox", "--disable-setuid-sandbox"],
    });
    const page = await browser.newPage();
    if (report_type === "domain") {
      await page.setViewport({ width: 2000, height: 10000000 });
      webpageUrl = `${process.env.PLATFORM_BASEPATH}/insight-report-pdf-preview/${report_id}?token=${getToken}`; // Step 2: Navigate to the webpage
    } else {
      await page.setViewport({ width: 2000, height: 10000000 });
      webpageUrl = `${process.env.PLATFORM_BASEPATH}/consolidated-pdf-preview/${report_id}?token=${getToken}`; // Step 2: Navigate to the webpage
    }
    await page.goto(webpageUrl, {
      waitUntil: "networkidle2",
      timeout: 0,
    }); // Wait until the page is fully loaded
    // Get the full page height using offsetHeight
    // const fullPageHeight = await page.evaluate(() => {
    //   return Math.max(
    //     document.body.offsetHeight,
    //     document.documentElement.offsetHeight
    //   );
    // });
    //await page.setViewport({width:1532, height: fullPageHeight})  // Set the height of the page
    const elementHeight = await page.evaluate(() => {
      const el = document.querySelector('.ifp-ai-report__main');
      if (!el) return 2000; // fallback in case element is not found
      const rect = el.getBoundingClientRect();
      return rect.height + 50;
    });
    const pdfBuffer = await page.pdf({
      // Step 3: Generate PDF
      // format: "A1", // PDF format
      printBackground: true, // Include background graphics
      height: `${elementHeight}px`,
      width: "2000px",
      timeout: 60000000,
    });
    // const pdfBase64 = base64.fromUint8Array(pdfBuffer);
    //fs.writeFileSync("Domain-Insight-Report.pdf", pdfBuffer); // Save the PDF as 'report.pdf' for testing purpose
    await browser.close(); // Step 6: Close the browser
    return pdfBuffer;
  } catch (error) {
    console.error("Error:", error);
  }
}

async function restructureConsolidatedData(jsonData, lang) {
  try {
    const report_data = jsonData.dataValues.report_data;
    let reStructuredDomains = report_data.domains.map((item) => {
      const comparisonKey = Object.keys(item).find(
        (
          key // Find the comparison key dynamically
        ) => Array.isArray(item[key]?.rows)
      );
      if (!comparisonKey) {
        throw new Error(
          "Invalid data structure: 'comparison' key with 'rows' array is missing."
        );
      }

      const comparisonData = item[comparisonKey];

      // Extract headers dynamically, excluding keys with '_progress', replace '_' with ' ', and capitalize the first letter of each word
      const headers =
        comparisonData.rows.length > 0
          ? Object.keys(comparisonData.rows[0])
              .filter((key) => !key.endsWith("_progress"))
              .map((key) =>
                key
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (char) => char.toUpperCase())
              )
          : [];

      // Restructure the data
      let reStructuredData = {
        [comparisonKey]: {
          headers,
          rows: comparisonData.rows.map((row) => {
            const newRow = {};

            // Dynamically handle keys in the row
            Object.keys(row).forEach((key) => {
              if (key.endsWith("_progress")) {
                // Handle keys with '_progress'
                const baseKey = key.replace("_progress", "");
                if (baseKey in row) {
                  newRow[baseKey] = {
                    value: row[baseKey] || 0,
                    progress: row[key],
                  };
                }
              } else if (!Object.keys(row).includes(`${key}_progress`)) {
                // Handle keys without '_progress'
                newRow[key] = row[key] || 0;
              }
            });

            return newRow;
          }),
        },
      };
      reStructuredData.domain = {
        name: jsonData.domain.dataValues[lang === "en" ? "name_en" : "name_ar"],
        id: jsonData.domain.dataValues.cms_id,
      };

      return reStructuredData;
    });
    reStructuredDomains.quarter = `Q${
      jsonData.quarter
    } Report (${getQuarterMonths(jsonData.quarter)})`;
    return reStructuredDomains;
  } catch (error) {
    console.error(error.message);
  }
}

async function getInterval(publicationId, priodicityId, intervalId) {
  try {
    let result = await sanadkomClient.get(
      `${SMART_PUBLISHER_BASE_URL}/Publication/GetInterval`,
      {
        params: {
          publicationId: publicationId,
          year: 2025,
          periodicityId: priodicityId,
        },
      }
    );
    const interval = result.data.result.find((item) => item.id === intervalId);
    return interval;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

module.exports = {
  getSanadkomAccessToken,
  getPublicationId,
  getPriodicityId,
  getIntervalId,
  generatePdf,
  restructureData,
  getServiceID,
  restructureConsolidatedData,
  getQuarterMonths,
  getInterval,
};
