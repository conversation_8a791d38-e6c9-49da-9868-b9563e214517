const { Sequelize, DataTypes } = require("sequelize");

/**
 * Existing BayaanAI model created for Bayaan UI used to store list
 * of "DropdownCategory" in Bayaan. DropdownCategory is a list of
 * categories that are used to group DropdownOptions.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const DropdownCategory = sequelize.define(
    "DropdownCategory",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      object_id: {
        type: DataTypes.UUID,
        allowNull: false,
        unique: true,
        defaultValue: DataTypes.UUIDV4,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
    },
    {
      tableName: "common_dropdowncategory",
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      deletedAt: 'deleted_at'
    }
  );
  return DropdownCategory;
}
module.exports = model;
