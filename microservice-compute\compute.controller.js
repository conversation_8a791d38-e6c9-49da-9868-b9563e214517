const Logger = require('scad-library').logger;
const moment = require('moment');
require('dotenv').config();

const { getComputedData, getComputeNodeInfo } = require('./services/executeQuery');

const { processLineChartData } = require('./services/chart-services/line-chart');
const uuid = require('uuid');

const log = new Logger().getInstance();

const constants = require('../config/constants.json');
const indicatorTemplate = require('./services/chart-services/template.json')
const indicatorTemplateAr = require('./services/chart-services/template_ar.json')

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { IFPError } = require('../utils/error');

function formatComputedValuesMessage(items, lang) {
  if (items.length === 0) return "";
  if (items.length === 1) {
    return lang === "EN"
      ? `Computed values for ${items[0]}.`
      : `تم حساب القيم لـ ${items[0]}.`;
  }
  const allButLast = items.slice(0, -1).join(", ");
  const lastItem = items[items.length - 1];

  if (lang === "EN") {
    return items.length === 2
      ? `Computed values for ${allButLast} and ${lastItem}.`
      : `Computed values for ${allButLast} and ${lastItem}.`;
  } else {
    return items.length === 2
      ? `تم حساب القيم لـ ${allButLast} و ${lastItem}.`
      : `تم حساب القيم لـ ${allButLast} و ${lastItem}.`;
  }
}

async function computeData(req) {
    log.debug(`>>>>>Entered microservice.compute.controller.computeData`);
    try {

        const indicator = req.body.id
        const dimensions = req.body.dimensions
        const operation = req.body.operation
        const lang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;

        let response = lang === "AR"
            ? JSON.parse(JSON.stringify(indicatorTemplateAr))
            : JSON.parse(JSON.stringify(indicatorTemplate));

        response.id = indicator

        let computeCacheKey = `compareIndicators_${indicator}_${crypto.createHash('md5').update(JSON.stringify(response)+JSON.stringify(req.body)).digest("hex")}`
        const computeCacheResults = await getRedis(computeCacheKey, req.headers);
        if (computeCacheResults) {
            log.info(`<<<<<Cache found for microservice.compute.controller.computeData`);
            return JSON.parse(computeCacheResults);
        }

        //Get nodes information from Indicator Map Table
        let indicatorInfo = await getComputeNodeInfo(indicator,lang)
        
        if (indicatorInfo.length<1)
            throw new IFPError(400,'Invalid Indicator ID')
        
        let infoData = indicatorInfo[0]
        const data = await getComputedData(infoData.INDICATOR_ID, infoData.SOURCE_TABLE,dimensions,operation);

        if (! data.length){
            return {
                'message': 'Compute results could not be generated with given parameters',
                'status': 'failed'
            }
        }

        let indicatorData = {
            id: infoData.INDICATOR_ID,
            title: infoData.INDICATOR_NAME,
            viewName : infoData.SOURCE_TABLE,
            unit : lang == 'EN'?infoData['UNIT']:infoData[`UNIT_${lang}`],
            data: data,
        }

        let visualization = {
            "id": `compute-data`,
            "type": "line-chart",
            "seriesTitles": {},
            "seriesMeta": [

            ],
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "d3-number",
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "xAxisLabel": "",
            "yAxisLabel": ""
        }


        let genericSeries = {
            "type": "solid",
            "xAccessor": {
                "type": "date",
                "path": "OBS_DT",
                "specifier": "%Y-%m-%d"
            },
            "yAccessor": {
                "type": "value",
                "path": "VALUE"
            },
            "data": [],
            "id": '',
            "dbIndicatorId": '',
            "label": null,
            "color": "#000000"
        }
        // extracting params for xaxis legend from dimentions
        let result = Object.values(dimensions).reduce((acc, value) => {
            if (Array.isArray(value) && value.length > 1) {
                acc.push(...value);
            }
            return acc;
            }, []);

        let xAxisLabel = formatComputedValuesMessage(result, lang);
        let series = JSON.parse(JSON.stringify(genericSeries))
        series.id = `series-${indicatorData.id}`
        series.label = xAxisLabel
        visualization.seriesTitles[series.id] = indicatorData.title
        series = processLineChartData(indicatorData.data, series);
        visualization.seriesMeta.push(series)

        response.indicatorVisualizations.visualizationsMeta = [visualization];
        response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id

        setRedis(computeCacheResults, JSON.stringify(response), constants.redis.dataResponseTTL, req.headers);
        return response;

    } catch (err) {
        log.error(`<<<<< Exited microservice.compute.controller.computeData with error ${err} `)
        throw err;
    }
}



module.exports = {
    computeData
};
