"use strict";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable("instruction_regeneration", {
      id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      object_id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
      },
      quarter: {
        type: Sequelize.INTEGER,
      },
      domain_id: {
        type: Sequelize.INTEGER,
      },
      full_content: {
        type: Sequelize.TEXT,
      },
      main_content: {
        type: Sequelize.TEXT,
      },
      exp_content: {
        type: Sequelize.TEXT,
      },
      report_name: {
        type: Sequelize.TEXT,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal("CURRENT_TIMESTAMP"),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable("instruction_regeneration");
  },
};
