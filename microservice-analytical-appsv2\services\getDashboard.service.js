const axios = require('axios');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getDashboardData(cmsResponse, req) {
    try{
        let lang = `${req.headers["accept-language"]}`;
        const host = req.get('host');

        let indicatorList = cmsResponse.indicator_list.split(',').map(function (item) {
            return item.trim();
        });

        if (indicatorList.length < 0)
            throw new Error('No indicators found for Insights Discovery');

        cmsResponse["sortVisualizations"] = true;
        cmsResponse.visualizations = [];
        let invalidData = [];
        const indicatorPromises = indicatorList.map(async (id, index) => {
            let url;
            if (req.baseUrl.includes('/api/mobile/'))
                if (host.includes('scad-insights-be'))
                    url = `http://${host}/api/mobile/content-type/statistics-insights/${id}`;
                else
                    url = `https://${host}/api/mobile/content-type/statistics-insights/${id}`;
            else
                url = `http://${host}/api/content-type/statistics-insights/${id}`;
            log.info(`API call to ${url} from getDashboard.service.js`);
            const headers = {
                Authorization: req.headers.authorization,
                appType: 'insights-discovery',
                "Accept-Language": lang
            }
            let response = await axios.get(`${url}`, {headers: headers})            
            let sortOrderCount = index + 1;

            response.data["sortOrder"] = (invalidData.length > 0) ? sortOrderCount - invalidData.length : sortOrderCount;
            cmsResponse.visualizations.push(response.data);
        })
        await Promise.all(indicatorPromises)
        return cmsResponse
    }catch(err){
        throw err
    }
}

module.exports = { getDashboardData }