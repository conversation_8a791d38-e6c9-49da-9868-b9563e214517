const Logger = require('scad-library').logger;

const log = new Logger().getInstance();
const {
    setIndicatorTCData,
    getIndicatorTCStatusData
} = require('./services/executeQuery.service');


async function getIndicatorTCStatus(req) {
    log.debug(`>>>>>Entered microservice-indicator-tc.controller.getIndicatorTCStatus`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username
            const nodeId = req.query.nodeId
            let data = await getIndicatorTCStatusData(nodeId, userEmail)
            if (data.length)
                resolve({ 'message': 'User has accepted TC for this node', 'status':true })
            else
                resolve({ 'message': 'User has not accepted TC for this node', 'status':false })
        } catch (err) {
            
            log.error(`<<<<<Exited microservice-indicator-tc.controller.getIndicatorTCStatus with error ${err}`);
            reject(err);
        }
    })
}


/**
 * function to get notifications
 * @param {*} req 
 */
async function setIndicatorTC(req) {
    log.debug(`>>>>>Entered microservice-indicator-tc.controller.setIndicatorTC`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username
            const nodeId = req.query.nodeId
            await setIndicatorTCData(nodeId, userEmail)
            resolve({ 'message': 'User has accepted TC for this node'})
        } catch (err) {
            
            log.error(`<<<<<Exited microservice-indicator-tc.controller.setIndicatorTC with error ${err}`);
            reject(err);
        }
    })
}

module.exports = {
    setIndicatorTC,
    getIndicatorTCStatus
};
