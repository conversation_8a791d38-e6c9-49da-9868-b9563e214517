const domainMatrixMap = require("./helpers/domainMatrixMap.json");
const groupMatrix = require("./helpers/groupMatrix.json");
const clkdb = require("./clk-database.service");
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getDomainMatrixMap() {
    return domainMatrixMap
  }

async function getBayaanGroupMatrix() {
    return groupMatrix
}

async function getBayaanExternalGroupMatrix() {
    let externalGroupMatrix = {}
    Object.entries(groupMatrix).forEach(([key, value]) => {
        if (value.startsWith("MATRIX__")) {
            externalGroupMatrix[key] = value
        }
    })
    return externalGroupMatrix
}


/**
 * Fetch Bayaan group details from Group Matrix table.
 * @param {string} findBy one of: 'id', 'groupName', 'domainClassification'
 * @param {*} filters fields to filter based on findBy value
 * @param {string} filters.groupId for findBy = 'id', group ID to filter by
 * @param {string} filters.groupName for findBy = 'groupName', group name to filter by
 * @param {string} filters.domain for findBy = 'domainClassification', domain to filter by
 * @param {string} filters.classification for findBy = 'domainClassification', classification to filter by
 * 
 * NOTE: findBy = 'domainClassification' will return only MATRIX_ prefixed groups for given domain & classification 
 */
async function getBayaanGroups(findBy = 'id', filters = {}) {
    let whereCondition = ''
    if (findBy == 'id') {
        whereCondition = `WHERE ID='${filters.groupId}'`
    } else if (findBy == 'groupName') {
        whereCondition = `WHERE NAME='${filters.groupName}'`
    } else if (findBy == 'classification') {
        whereCondition = `WHERE startsWith(NAME, 'MATRIX__') AND endsWith(NAME, '${filters.classification}')`
    } else if (findBy == 'domainClassification') {
        whereCondition = `WHERE match(NAME, 'MATRIX__${filters.domain.toUpperCase()}__${filters.classification.toUpperCase()}')`
    } else {
        log.warn(`Invalid findBy value: ${findBy}`)
        return []
    }
    try {
      const query = `SELECT * FROM IFP_GROUP_MATRIX ${whereCondition}`;
      const group = await clkdb.simpleExecute(query);
      return group
    } catch (error) {
        log.error(`Something went wrong at getBayaanGroups: ${error}`)
        return []
    }
}

module.exports = {   
    getDomainMatrixMap,
    getBayaanGroupMatrix,
    getBayaanExternalGroupMatrix,
    getBayaanGroups
};
