
const express = require('express');
const router = new express.Router();
const externalController = require('../microservice-external/external.controller');
const { validateCommonAuth } = require('./validators/external.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

router.get('/entity/user-count/:id', validateCommonAuth, async (req, res, next) => {
    try {
      const data = await externalController.getEntityUserCount(req,res)
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for indicator-tc content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

module.exports = router;