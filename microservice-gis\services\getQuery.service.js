const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json');


async function getMapDistrictsColumnsDataQuery() {
  return new Promise((resolve, reject) => {
    try {
        let query =  `SELECT name FROM system.columns WHERE database = 'GeoSpatial' AND table = 'VW_MAP_DISTRICTS_DATA' AND name NOT IN ('LONGX', 'LATY', 'COORDS')`
        return resolve({ query: query, binds: {} });
        
    } catch (err) {
        
        log.error(`<<<<<< Exited microservice-gis.services.getQuery.service.getMapDistrictsQuery with error ${err} `);
        reject([424, err]);
    }
});
}

async function getMapDistrictsQuery(type) {
  return new Promise((resolve, reject) => {
      try {
          let query = `SELECT * FROM GeoSpatial.MAP_DISTRICTS`
          return resolve({ query: query, binds: {} });
          
      } catch (err) {
        
          log.error(`<<<<<< Exited microservice-gis.services.getQuery.service.getMapDistrictsQuery with error ${err} `);
          reject([424, err]);
      }
  });
}


async function getMapDistrictsV2Query(params,filters) {
  return new Promise((resolve, reject) => {
      try {
          const PROPERTIES = {
            NUM:['LAT_CENTER','LENGTH_KM','LNG_CENTER','AREA_KM2'],
            STR:['D_NAME_AR','D_NAME_EN','E_NAME_AR','E_NAME_EN','R_NAME_ABR','R_NAME_AR','R_NAME_EN','SCAD_D_ID','SCAD_E_ID','SCAD_R_ID']
          }
          try{

            whereClauses = Object.entries(params).map(([key,value])=>{
              return `JSONExtractString(toJSONString(PROPERTIES),'${key}') = '${value}'`
            })
            
            if (filters){
              filters=filters.split('|')
              filters.forEach(filter => {
                if(filter.includes('==')){
                  splitFilter = filter.split('==')
                  splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') = '${splitFilter[1]}'`):[]
                }
                else if(filter.includes('<=')){
                  splitFilter = filter.split('<=')
                  if (PROPERTIES.NUM.includes(splitFilter[0]))
                    splitFilter.length == 2?whereClauses.push(`toFloat64(JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}')) <= '${splitFilter[1]}'`):[]
                  else                    
                    splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') <= '${splitFilter[1]}'`):[]
                }
                else if(filter.includes('>=')){
                  splitFilter = filter.split('>=')
                  if (PROPERTIES.NUM.includes(splitFilter[0]))
                    splitFilter.length == 2?whereClauses.push(`toFloat64(JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}')) <= '${splitFilter[1]}'`):[]
                  else
                  splitFilter.length == 2?whereClauses.push(`toFloat64(JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}')) <= '${splitFilter[1]}'`):[]
                splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') >= '${splitFilter[1]}'`):[]  
                  
                  splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') >= '${splitFilter[1]}'`):[]
                }
                else if(filter.includes('<>')){
                  splitFilter = filter.split('<>')
                  splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') != '${splitFilter[1]}'`):[]
                }
                else if(filter.includes('<')){
                  splitFilter = filter.split('<')
                  if (PROPERTIES.NUM.includes(splitFilter[0]))
                    splitFilter.length == 2?whereClauses.push(`toFloat64(JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}')) < '${splitFilter[1]}'`):[]
                  else
                    splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') < ${splitFilter[1]}`):[]
                }
                else if(filter.includes('>')){
                  splitFilter = filter.split('>')
                  if (PROPERTIES.NUM.includes(splitFilter[0]))
                    splitFilter.length == 2?whereClauses.push(`toString(JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}')) > '${splitFilter[1]}'`):[]
                  else
                    splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') > '${splitFilter[1]}'`):[]
                }
                else if(filter.includes('!=')){
                  splitFilter = filter.split('!=')
                  splitFilter.length == 2?whereClauses.push(`JSONExtractString(toJSONString(PROPERTIES),'${splitFilter[0]}') != '${splitFilter[1]}'`):[]
                }
              });
            }
          }
          catch(exp){
          }
            
          
          whereClauses = whereClauses.join(' AND ')
          let query = `SELECT * FROM GeoSpatial.MAP_DISTRICTSV2 ${whereClauses.length?'WHERE '+whereClauses:''}`
          return resolve({ query: query, binds: {} });
          
      } catch (err) {
          
          log.error(`<<<<<< Exited microservice-gis.services.getQuery.service.getMapDistrictsQuery with error ${err} `);
          reject([424, err]);
      }
  });
}

async function getMapDistrictsV3Query(params,filters,columns) {
  return new Promise((resolve, reject) => {
      try {
          let geometry = {}
          let whereClauses = Object.entries(params).map(([key,value])=>{
            if (key == 'geometry')
              geometry=JSON.parse(value)
            else
              return `UPPER(${key}) = UPPER('${value}')`
          }).filter(w=>w)

          if (Object.values(geometry).length){
            whereClauses.push(`LONGX>=${geometry.xmin}`)
            whereClauses.push(`LATY>=${geometry.ymin}`)
            whereClauses.push(`LONGX<=${geometry.xmax}`)
            whereClauses.push(`LATY<=${geometry.ymax}`)
          }

          columns = columns.map(c=>c.name).join(',')
          let query = `SELECT
                          *
                       FROM
                         (
                          SELECT
                            DISTINCT SCAD_D_ID ,
                            [[arrayCompact(groupArray([LONGX,LATY]))]] AS GEOMETRY
                          FROM
                            GeoSpatial.VW_MAP_DISTRICTS_DATA
                          GROUP BY
                            SCAD_D_ID ${whereClauses.length?`HAVING ${whereClauses.join(' AND ')}`:''}
                          ) T
                        INNER JOIN 
                        (
                          SELECT
                            DISTINCT ${columns}
                          FROM
                            GeoSpatial.VW_MAP_DISTRICTS_DATA
                        ) S ON
                          T.SCAD_D_ID = S.SCAD_D_ID`

          return resolve({ query: query, binds: {} });
          
      } catch (err) {
          
          log.error(`<<<<<< Exited microservice-gis.services.getQuery.service.getMapDistrictsQuery with error ${err} `);
          reject([424, err]);
      }
  });
}


module.exports = { 
  getMapDistrictsColumnsDataQuery,
  getMapDistrictsQuery,
  getMapDistrictsV2Query,
  getMapDistrictsV3Query }