const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require("uuid");
const crypto = require('crypto');
const oracledb = require('oracledb');
const { toTitleCase } = require("../services/helpers/helper");

function generateInvitationToken(
  requestId,
  maskedEmail,
  maskedPhone,
  role,
  entityName,
  entityId,
  accessPolicy,
  platformAccess,
  designation="",
  requestJustification="",
  superUserId=""
) {
  const payload = {
    requestId: requestId,
    userId: maskedEmail,
    phone: maskedPhone,
    role: role.replace(" ", "_").toUpperCase(),
    entity: entityName,
    entityId: entityId,
    accessPolicy: accessPolicy.map(policy => {
      return { "domain": policy.domain?policy.domain:policy.DOMAIN, "classification": policy.classification?policy.classification:policy.CLASSIFICATION }
    }),
    designation:designation,
    requestJustification: requestJustification,
    suid: superUserId,
    platformAccess: platformAccess,
  };
  const options = {
    expiresIn: "2d",
    issuer: "scad.gov.ae",
  };
  return jwt.sign(payload, process.env.APP_SECRET_KEY, options);
}

function maskEmail(email) {
  const [localPart, domain] = email.split("@");
  const maskedLocalPart =
    localPart[0] + "*".repeat(localPart.length - 2) + localPart.slice(-1);
  return `${maskedLocalPart}@${domain}`;
}

function maskPhone(phone) {
  return phone.replace(/.(?=.{4})/g, "*");
}

function hashEmiratesId(emiratesId) {
  emiratesId = emiratesId.toString().trim();
  //Use a more secure hashing algorithm in production
  const hash = crypto.createHash('sha256').update(emiratesId, 'utf8').digest();
  const hashedId = hash.toString('base64');

  return hashedId;
}

function formatAccess(accessRecords) {
  
  // Filter out revoked access
  const approvedAccess = filterApprovedUserAccess(accessRecords);

  // Fix casing
  approvedAccess.forEach((a) => {
    a.domain = toTitleCase(a.domain);
    a.classification = toTitleCase(a.classification);
  });

  // Ensure Consistent Sorting
  approvedAccess.sort((a, b) => a.domain.localeCompare(b.domain));

  // Extract approvalLevel & direction for 'Pending' requests
  const approvalLevel = approvedAccess.map((a) => a.approvalLevel).sort()[0];
  const direction = approvedAccess[0].direction;

  const accessLevels = ["Open", "Confidential", "Sensitive"];
  let formattedAccess =  {};
  let maxClassificationIndex = 0; // Index to track the highest classification across all access

  approvedAccess.forEach(item => {
      if (!formattedAccess[item.domain]) {
          // If domain doesn't exist, create a new entry
          formattedAccess[item.domain] = {
              name: item.domain,
              levels: accessLevels.map(level => ({
                  classification: level,
                  accessId: null,
                  selected: false
              }))
          };
      }

      // Find the matching classification in the levels array and update it
      formattedAccess[item.domain].levels.forEach(level => {
          if (level.classification.toUpperCase() === item.classification.toUpperCase()) {
              level.accessId = item.accessId;
              
              if(item.reason){
                if(item.reason.endsWith('_:DG')){
                  level.reason = item.reason;
                  level.reason = level.reason.replace(/_:DG$/, '');
                  level.reverted_by = 'DG';
                }
                else if(item.reason.endsWith('_:PRIMARY_PE_USER')){
                  level.reason = item.reason;
                  level.reason = level.reason.replace(/_:PRIMARY_PE_USER$/, '');
                  level.reverted_by = 'PRODUCT_ENGAGEMENT';
                }
                else if(item.reason.endsWith('_:PRIMARY_SUPERUSER')){
                  level.reason = item.reason;
                  level.reason = level.reason.replace(/_:PRIMARY_SUPERUSER$/, '');
                  level.approved_by = 'SUPERUSER';
                }
                else if(item.reason.endsWith('_:SECONDARY_SUPERUSER')){
                  level.reason = item.reason;
                  level.reason = level.reason.replace(/_:SECONDARY_SUPERUSER$/, '');
                  level.approved_by = 'SUPERUSER';
                }
              }
              if (item.approvalLevel) {
                level.approvalLevel = item.approvalLevel
              }


              // Update the maxClassificationIndex if the current classification is higher
              const currentClassificationIndex = accessLevels.indexOf(item.classification);
              if (currentClassificationIndex > maxClassificationIndex) {
                  maxClassificationIndex = currentClassificationIndex;
              }
          }
      });
  });

  // Now iterate again to set `selected: true` only for the maximum classification for each domain
  Object.values(formattedAccess).forEach(domain => {
      let highestClassificationIndex = 0;

      // Find the highest classification level in the domain
      domain.levels.forEach((level, index) => {
          if (level.accessId !== null && index > highestClassificationIndex) {
              highestClassificationIndex = index;
          }
      });

      // Set `selected: true` only for the highest classification
      domain.levels.forEach((level, index) => {
          level.selected = index === highestClassificationIndex;
      });
  });

  // Set the maxClassification in the result object
  const maxClassification = accessLevels[maxClassificationIndex];

  // Extract revert cycle information (if present)
  let revertInfo;
  Object.values(formattedAccess).map(v=>{
    v.levels.forEach(element => {
      if(element.approved_by){
            revertInfo = {
              approved_by: element.approved_by,
              reason: element.reason,
              direction: direction
            }
          }
          else if(element.reverted_by){
            revertInfo = {
              reverted_by: element.reverted_by,
              reason: element.reason,
              direction: direction
            }
          }
      });
  })

  // Convert the result back to an array of objects
  formattedAccess = Object.values(formattedAccess);
  
  return { formattedAccess, maxClassification, approvalLevel, revertInfo };
}

/**
 * @param {string | oracledb.Lob} accessData 
 * @returns 
 */
async function parseAccessData(accessData) {
  let result = [];
  if (typeof accessData == "string") {
    try {
      result = JSON.parse(accessData);
    } catch (err) {
      console.error(`Something went wrong when parsing string access data: ${err}`);
    }
  } else {
    try {
      result = await accessData.getData()
      result = JSON.parse(result.replace(/\n/g, '\\n'))
    } catch (err) {
      console.error(`Something went wrong when parsing lob acces data: ${err}`)
    }
  }
  return result
}

/**
 * Get user friendly role names for respective role
 * codes stored in BE.
 * @param {string} role Eg: DG, SUPERUSER, USER
 */
function getRoleName(role) {
  const roleMapping = {
    "USER": "User",
    "SUPERUSER": "Superuser",
    "PRIMARY_SUPERUSER": "Primary Superuser",
    "SECONDARY_SUPERUSER": "Secondary Superuser",
    "DG": "Director General / Under Secretary",
  };
  return roleMapping[role.toUpperCase()] || role;
}

/**
 * Fetch the general role group for given role.
 * Example: PRIMARY_PE_USER -> PRODUCT_ENGAGEMENT
 * @param {string} userRole 
 */
function getRoleGroupCode(userRole) {
  if (userRole.includes("PE_USER")) {
    return "PRODUCT_ENGAGEMENT";
  } else if (userRole.includes("SUPERUSER")) {
    return "SUPERUSER";
  } else {
    return userRole;
  }
}

/**
 * Cleans the ',' filled user name received from UAE Pass
 * into a user friendly title cased name.
 * @param {string} userName 
 * @returns 
 */
function cleanEIDName(userName) {
  if (typeof userName == "string") {
    return userName
      .replace(/,/g, " ")
      .trim()
      .replace(/\s+/g, " ")
      .toLowerCase()
      .replace(/\b\w/g, (char) => char.toUpperCase());
  }
  return userName;
}

/**
 * Filter our 'REVOKE' access records given list of entire user access
 * records list (belonging to all requests)
 * @param {Array<object>} allAccessRecords 
 */
function filterApprovedUserAccess(allAccessRecords) {
  const grantedAccess = new Map();
  for (const accessRecord of allAccessRecords) {
    const recordKey = `${accessRecord.domain}-${accessRecord.classification}`;
    if (
      accessRecord.accessOperation == "REVOKE" &&
      grantedAccess.has(recordKey)
    ) {
      grantedAccess.delete(recordKey);
    } else if (accessRecord.accessOperation == "GRANT") {
      grantedAccess.set(recordKey, accessRecord);
    }
  }
  const approvedAccess = Array.from(grantedAccess.values());
  return approvedAccess;
}


module.exports = {
  generateInvitationToken,
  maskEmail,
  maskPhone,
  hashEmiratesId,
  formatAccess,
  parseAccessData,
  getRoleName,
  cleanEIDName,
  filterApprovedUserAccess,
  getRoleGroupCode,
};
