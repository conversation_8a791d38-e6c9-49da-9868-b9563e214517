const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function checkSurveyAttendQuery(userId,surveyId,launch_date) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP2_SURVEY_RESPONSE WHERE EMAIL = '${userId}' AND SURVEY_ID = '${surveyId}' AND LAUNCH_DATE = TO_DATE('${launch_date}','DD/MM/YYYY')`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function attendIndividualSurveyQuery(email,survey,query_type,survey_query_en,survey_query_ar,survey_response,survey_rating_reason='',survey_uid,name) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP2_SURVEY_RESPONSE ( EMA<PERSON>, SURVEY_ID, SURVEY_TITLE_EN, SURVEY_TITLE_AR, SURVEY_DESC_EN, SURVEY_DESC_AR, LAUNCH_DATE, QUERY_TYPE, QUERY_EN, QUERY_AR, RESPONSE,RATING_FEEDBACK,SURVEY_UID,NAME ) VALUES ( '${email}', '${survey.id}','${survey.title_en}','${survey.title_ar}','${survey.desc_en}','${survey.desc_ar}', TO_DATE('${survey.launch_date}','DD/MM/YYYY'),'${query_type}', '${survey_query_en}', '${survey_query_ar}', '${survey_response}','${survey_rating_reason}','${survey_uid}','${name}')`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function checkUserTCAcceptQuery(userId) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_USERS_TC WHERE LOWER(USER_ID) = LOWER('${userId}') ORDER BY TC_ACCEPT_DT`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


module.exports = { checkSurveyAttendQuery, attendIndividualSurveyQuery, checkUserTCAcceptQuery };
