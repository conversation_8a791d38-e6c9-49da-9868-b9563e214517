const scadLib = require('scad-library');
const util = scadLib.util;
const Logger = scadLib.logger;
const log = new Logger().getInstance();

const getSunBurstSeries = async (visualization, seriesData) => {

    return new Promise((resolve, reject) => {
        try {
            if (seriesData && seriesData.length > 0) {
                visualization.seriesMeta.forEach( series => {
                    series.data = [];
                    let matchingValueObject = series.dimension;
                    let result = seriesData.filter(function(item) {
                        item.OBS_DT = util.convertDate(item.OBS_DT.toString());
                        for (var key in matchingValueObject) {
                            let itemColumn = item[key] === null ? '': item[key].toUpperCase();
                            let dimensionValue = matchingValueObject[key] === null ? '' : matchingValueObject[key].toUpperCase();
                            if ( item[key] === undefined || itemColumn != dimensionValue){
                                return false;
                            }
                        }
                        Object.keys(item).forEach((k) => item[k] == null && delete item[k]);
                        return true;
                      });

                    if( result.length >= 1 ) series.data = result ;

                })
            } else {
                log.error(`Data not available in DB for visualization ${visualization}`);
                reject([404, `Data not available in DB`]);
            }

            resolve(visualization);
        } catch (err) {
            reject(err);
        }
    })
}


module.exports = { getSunBurstSeries }
