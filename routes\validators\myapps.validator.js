const { getShareApp } = require('../../microservice-myapps/services/executeQuery.service');
const { checkUserAppAssignment } = require('../../services/graph');
const { IFPError } = require('../../utils/error');

const validateSubmitApps =  (req, res, next) => {
  const add = req.body.add
  const remove = req.body.remove

  if (!Array.isArray(add))
    throw new IFPError(400, 'The "add" field must be an array');

  if (!Array.isArray(remove))
    throw new IFPError(400, 'The "remove" field must be an array');

  if (remove && remove.length === 0 && add.length === 0) {
    throw new IFPError(400, 'At least one app should be added to "add" when the "remove" is empty');
  }

  if (add && add.length === 0 && remove.length === 0) {
    throw new IFPError(400, 'At least one app should be added to "remove" when the "add" is empty');
  }

  if (add && Array.isArray(add)) {
    add.forEach(item => {
      if (!(item && typeof item === 'object' && item.id && item.contentType)) {
        throw new IFPError(400, 'Each item in the "add" array must be an object with "id" and "contentType" fields');
      }
    });
  }

  if (remove && Array.isArray(remove)) {
    remove.forEach(item => {
      if (!(item && typeof item === 'object' && item.id)) {
        throw new IFPError(400, 'Each item in the "remove" array must be an object with "id" field');
      }
    });
  }

  next();
};

const validateDraftApps = (req, res, next) => {
  const add = req.body.add
  const remove = req.body.remove

  if (!Array.isArray(add))
    throw new IFPError(400, 'The "add" field must be an array');

  if (!Array.isArray(remove))
    throw new IFPError(400, 'The "remove" field must be an array');

  if (remove && remove.length === 0 && add.length === 0) {
    throw new IFPError(400, 'At least one app should be added to "add" when the "remove" is empty');
  }

  if (add && add.length === 0 && remove.length === 0) {
    throw new IFPError(400, 'At least one app should be added to "remove" when the "add" is empty');
  }

  if (add && Array.isArray(add)) {
    add.forEach(item => {
      if (!(item && typeof item === 'object' && item.id && item.contentType)) {
        throw new IFPError(400, 'Each item in the "add" array must be an object with "id" and "contentType" fields');
      }
    });
  }

  if (remove && Array.isArray(remove)) {
    remove.forEach(item => {
      if (!(item && typeof item === 'object' && item.id)) {
        throw new IFPError(400, 'Each item in the "remove" array must be an object with "id" field');
      }
    });
  }

  next();
};

const validateState = (req, res, next) => {
  const data = req.body;

  if (!Array.isArray(data)) {
    throw new IFPError(400, "Invalid data format: Expected an array of domains.");
  }

  for (const domain of data) {
    if (typeof domain !== 'object' || !domain.name || !Array.isArray(domain.nodeData)) {
      throw new IFPError(400, "Invalid domain format: Each domain must be an object with a name and nodeData.");
    }

    for (const classification of domain.nodeData) {
      if (!classification.name)
        throw new IFPError(400, "Invalid classification format: Each classification must have a name.");
      if (!Array.isArray(classification.nodes)) {
        throw new IFPError(400, "Invalid classification format: Each classification must have nodes.");
      }

      for (const node of classification.nodes) {
        if (typeof node !== 'object' && !node.NODE_ID && !node.TITLE) {
          throw new IFPError(400, "Invalid node format: Each node must be an object with NODE_ID and TITLE.");
        }
        if ( !node.NODE_ID){
          throw new IFPError(400, "Invalid node format: Each node must be an object with NODE_ID and TITLE.");
        }
        if (!node.TITLE){
          throw new IFPError(400, "Invalid node format: Each node must be an object with NODE_ID and TITLE.");
        }
      }
    }
  }
  next();
};

const validateCreateShare = async (req, res, next) => {
  try{

    if (!req.body.shareNodes)
      throw new IFPError(400, `Please provide shareNodes`)

    if (!Array.isArray(req.body.shareNodes))
      throw new IFPError(400, `ShareNodes should be an array`)

    if (!req.body.shareList)
      throw new IFPError(400, `Please provide shareList`)

    if (!Array.isArray(req.body.shareList))
      throw new IFPError(400, `ShareList should be an array`)
    
    const shareEmailChecks = req.body.shareList.map(async (email) => {
      const isEmailRegistered = await checkUserAppAssignment(email);
      if (!isEmailRegistered)
        throw new IFPError(400, `The email ${email} is not registered with the system`)
    });

    await Promise.all(shareEmailChecks);

    next()
  }
  catch(error){
    next(error)
  }

};

const validateShareList = (req, res, next) => {
  try {
    const type = req.params.type
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;

    const validTypes = ['received', 'sent']
    if (!validTypes.includes(type))
      throw new IFPError(400, `Please provide a valid option for type: [${validTypes.join(',')}]`)

    if (page < 1)
      throw new IFPError(400, `{${page} is not a valid page number`)

    if (limit < 1)
      throw new IFPError(400, `{${limit} is not a valid page number`)

    next()
  }
  catch (error) {
    next(error)
  }
}

const validateDeleteShareApp = async (req, res, next) => {
  try {

    const type = req.params.type
    if (!['sent', 'received'].includes(type))
      throw new IFPError(400, `Please provide a valid type: [sent, received]`)

    const id = req.params.id
    if (!id)
      throw new IFPError(400, `Please provide a valid type id`)

    const userEmail = req.user.preferred_username
    const requestor = type == 'sent' ? 'sender' : 'recepient'
    shareData = await getShareApp(id, userEmail, requestor)

    if (shareData.length < 1)
      throw new IFPError(403, `Either this share app is not available or you don't have necessary privilege to access it`)

    next()
  }
  catch (error) {
    next(error)
  }

}

const validateReadShareApp = async (req, res, next) => {
  try {
    const id = req.params.id
    if (!id)
      throw new IFPError(400, `Please provide a valid type id`)

    const userEmail = req.user.preferred_username
    const requestor = 'recepient'
    shareData = await getShareApp(id, userEmail, requestor)

    if (shareData.length < 1)
      throw new IFPError(404, `Either this share app is not available or you don't have necessary privilege to access it`)
    next()
  }
  catch (error) {
    next(error)
  }
}

module.exports = {
  validateSubmitApps,
  validateDraftApps,
  validateState,
  validateCreateShare,
  validateReadShareApp,
  validateShareList,
  validateDeleteShareApp
}
