const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  listChartInsightDataQuery, addChartInsightDataQuery, getChartInsightDataQuery, updateChartInsightDataQuery,
  deleteChartInsightDataQuery, getChartInsightsDataQuery, getSubmitRequestChartInsightsDataQuery, updateSubmitRequestChartInsightsDataQuery, 
  approveChartInsightDataQuery, rejectChartInsightDataQuery, requestEditChartInsightDataQuery,getInsightUsersQuery, 
  getInsightApproverQuery, getInsightApproverNameQuery} = require('./getQuery.service');

/**
 * Function to get chart insights data from the database
 * @param {number|string} nodeId - The ID of the node for which insights are fetched
 * @returns {Promise<Object[]>} - A promise that resolves with the list of chart insights
 */
async function listChartInsightData(nodeId, isSubNode) {
  return new Promise((resolve, reject) => {
    listChartInsightDataQuery(nodeId, isSubNode).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.listChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.listChartInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to add chart insight data to the database
 * @param {Object} insightData - The chart insight data to be added
 * @returns {Promise<Object>} - A promise that resolves with the result of adding the chart insight
 */
async function addChartInsightData(insightData) {
  return new Promise((resolve, reject) => {
    addChartInsightDataQuery(insightData).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.addChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.addChartInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to retrieve chart insight data from the database
 * @param {number|string} id - The ID of the chart insight to retrieve
 * @returns {Promise<Object[]>} - A promise that resolves with the retrieved chart insight data
 */
async function getChartInsightData(id) {
  return new Promise((resolve, reject) => {
    getChartInsightDataQuery(id).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getChartInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to retrieve chart insights data from the database
 * @param {number|string} ids - The IDs of the chart insight to retrieve
 * @returns {Promise<Object[]>} - A promise that resolves with the retrieved chart insight data
 */
async function getChartInsightsData(ids) {
  return new Promise((resolve, reject) => {
    getChartInsightsDataQuery(ids).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getChartInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to update chart insight data in the database
 * @param {Object} insightData - The data to update the chart insight with
 * @returns {Promise<Object>} - A promise that resolves with the updated chart insight data
 */
async function updateChartInsightData(insightData) {
  return new Promise((resolve, reject) => {
    updateChartInsightDataQuery(insightData).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateChartInsightData with error ${err}`);
      reject(err);
    })
  })
}


/**
 * Function to delete chart insight data from the database
 * @param {number|string} id - The unique ID of the chart insight to be deleted
 * @returns {Promise<Object>} - A promise that resolves with the result of the deletion process
 */
async function deleteChartInsightData(id) {
  return new Promise((resolve, reject) => {
    deleteChartInsightDataQuery(id).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.deleteInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.deleteInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to submit chart insights data for Approval in the database.
 * @param {string} email - User's email associated with the request.
 * @param {Array<string>} insightIds - IDs of the chart insights to get approved.
 * @returns {Promise<Object>} - A promise that resolves with the updated chart insights data.
 */
async function updateSubmitRequestChartInsightsData(email, insightIds) {
  return new Promise((resolve, reject) => {
    updateSubmitRequestChartInsightsDataQuery(email, insightIds).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateSubmitRequestInsightsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.updateSubmitRequestInsightsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to execute a query and retrieve data from the database.
 * Handles database queries with basic error handling.
 * @param {string} query - SQL query to execute.
 * @returns {Promise<Object>} - A promise that resolves with the query result data.
 */
async function getData(query) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-user-settings.services.executeQuery.service.getData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-user-settings.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-user-settings.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

/**
 * Function to retrieve submit request chart insights data from the database.
 * @param {string} email - Email of the user requesting data.
 * @param {Array<string>} insightIds - IDs of the insights to fetch.
 * @returns {Promise<Object>} - A promise that resolves with the fetched insights data.
 */
async function getSubmitRequestChartInsightsData(email, insightIds) {
  return new Promise((resolve, reject) => {
    getSubmitRequestChartInsightsDataQuery(email, insightIds).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getSubmitRequestChartInsightsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getSubmitRequestChartInsightsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to approve chart insights in the database.
 * Marks the provided insight IDs as approved for the given date.
 * @param {Array<string>} ids - IDs of the chart insights to approve.
 * @param {Date} date - Approval date.
 * @returns {Promise<Object>} - A promise that resolves with the approved chart insights data.
 */
async function approveChartInsightData(ids,date) {
  return new Promise((resolve, reject) => {
    approveChartInsightDataQuery(ids,date).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.approveChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.approveChartInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to reject chart insights in the database.
 * @param {Array<string>} ids - IDs of the chart insights to reject.
 * @param {Date} date - Rejection date.
 * @returns {Promise<Object>} - A promise that resolves with the rejected chart insights data.
 */
async function rejectChartInsightData(ids,date) {
  return new Promise((resolve, reject) => {
    rejectChartInsightDataQuery(ids,date).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.rejectChartInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.rejectChartInsightData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to request an edit for chart insights in the database.
 * @param {Array<string>} ids - IDs of the chart insights to request edits for.
 * @param {Date} date - Date the edit request is being made.
 * @returns {Promise<Object>} - A promise that resolves with the updated insights data.
 */
async function requestEditChartInsightData(ids,date) {
  return new Promise((resolve, reject) => {
    requestEditChartInsightDataQuery(ids,date).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.requestEditInsightData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.requestEditInsightData with error ${err}`);
      reject(err);
    })
  })
}


/**
 * Function to retrieve all insight users from the database
 * @returns {Promise<Object[]>} - A promise that resolves with the retrieved insight users data
 */
async function getInsightUsers() {
  return new Promise((resolve, reject) => {
    getInsightUsersQuery().then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightUsers with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightUsers with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to retrieve the insight approver from the database
 * @returns {Promise<Object[]>} - A promise that resolves with the retrieved insight approver data
 */
async function getInsightApprover() {
  return new Promise((resolve, reject) => {
    getInsightApproverQuery().then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightApprover with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightApprover with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to retrieve the insight approver name from the database
 * @returns {Promise<Object[]>} - A promise that resolves with the retrieved insight approver name
 */
async function getInsightApproverName() {
  return new Promise((resolve, reject) => {
    getInsightApproverNameQuery().then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightApproverName with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-insights.executeQuery.service.getInsightApproverName with error ${err}`);
      reject(err);
    })
  })
}
module.exports = {
  listChartInsightData, addChartInsightData, getChartInsightData, updateChartInsightData,
  deleteChartInsightData, updateSubmitRequestChartInsightsData, getChartInsightsData, getData, 
  getSubmitRequestChartInsightsData, approveChartInsightData, rejectChartInsightData, requestEditChartInsightData,
  getInsightUsers, getInsightApprover, getInsightApproverName
};
