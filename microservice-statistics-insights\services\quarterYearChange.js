const moment = require('moment');

function calculateQuarterYearChanges(visualization) {
    let valueFormat = visualization.yearOnYearValueFormat ? visualization.yearOnYearValueFormat : "percentage";
    let seriesSet = {};
    let dimension = visualization.filterColumn?visualization.filterColumn :"NOCOLUMN"
    visualization.seriesMeta.forEach(series => {
        if (series.id.includes('-forecast')) {
            let series_id = series.id.replace('-forecast', '');
            if (!(series_id in seriesSet)) {
                seriesSet[series_id] = {};
            }
            series.data.forEach(dataPoint => {
                if (!seriesSet[series_id][dataPoint[dimension]]) {
                    seriesSet[series_id][dataPoint[dimension]] = { nowcast: [], forecast: [] };
                }
                seriesSet[series_id][dataPoint[dimension]].forecast.push(dataPoint);
            });
        } else {
            if (!(series.id in seriesSet)) {
                seriesSet[series.id] = {};
            }
            series.data.forEach(dataPoint => {
                if (!seriesSet[series.id][dataPoint[dimension]]) {
                    seriesSet[series.id][dataPoint[dimension]] = { nowcast: [], forecast: [] };
                }
                seriesSet[series.id][dataPoint[dimension]].nowcast.push(dataPoint);
            });
        }
    });

    Object.entries(seriesSet).forEach(([series_id, typeData]) => {
        Object.entries(typeData).forEach(([type, data]) => {
            let combined_data = data.nowcast.concat(data.forecast);

            combined_data.forEach((element, dindex) => {
                if (combined_data[dindex - 1]) {
                    if (visualization.showQuarterOnQuarter) {
                        if(visualization.useTableQOQ){
                            element['CHANGE_QQ']=element.QOQ
                        }
                        else{
                            if (valueFormat == "absolute") {
                                element['CHANGE_QQ'] = element.VALUE - combined_data[dindex - 1].VALUE;
                            } else {
                                element['CHANGE_QQ'] = (element.VALUE - combined_data[dindex - 1].VALUE) / combined_data[dindex - 1].VALUE * 100;
                            }
                        }
                    }
                    if (visualization.showYearOnYear) {
                        if(visualization.useTableYOY){
                            element['CHANGE_YY']=element.YOY
                        }
                        else{
                            let prevDate = moment(element.OBS_DT).subtract(1, 'years').format(moment.HTML5_FMT.MONTH);
                            let tempData = combined_data.find(childData => moment(childData.OBS_DT).format(moment.HTML5_FMT.MONTH) === prevDate && childData.type === element.type);
                            if (tempData) {
                                if (valueFormat == "absolute") {
                                    element['CHANGE_YY'] = element.VALUE - tempData.VALUE;
                                } else {
                                    element['CHANGE_YY'] = (element.VALUE - tempData.VALUE) / tempData.VALUE * 100;
                                }
                            }
                        }
                    }
                }
            });
            data.nowcast = combined_data.slice(0, data.nowcast.length);
            data.forecast = combined_data.slice(data.nowcast.length);
        });
    });

    visualization.seriesMeta.forEach(series => {
        let series_id = series.id.includes('-forecast') ? series.id.replace('-forecast', '') : series.id;
        let typeData = seriesSet[series_id];

        series.data = [];
        Object.values(typeData).forEach(data => {
            if (series.id.includes('-forecast')) {
                series.data = series.data.concat(data.forecast);
            } else {
                series.data = series.data.concat(data.nowcast);
            }
        });
    });

    return visualization;
}
module.exports = { calculateQuarterYearChanges }