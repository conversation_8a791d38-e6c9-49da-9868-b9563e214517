const db = require('../../services/database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getUserSettingsDataQuery, updateUserSettingsDataQuery, deleteUserSettingsDataQuery } = require('./getQuery.service');

/**
 * Function to get user settings from DB
 * @param {*} userEmail - user email 
 */
async function getUserSettingsData(userEmail) {
  return new Promise((resolve, reject) => {
    getUserSettingsDataQuery(userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        log.error(`<<<<< Exited microservice-user-settings.executeQuery.service.getUserSettingsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-user-settings.executeQuery.service.getUserSettingsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to insert user setting values into DB
 * @param {*} userEmail - user email 
 * @param {*} setting - setting key 
 * @param {*} value - setting value
 */
async function updateUserSettingsData(userEmail,setting,value) {
  return new Promise((resolve, reject) => {
    updateUserSettingsDataQuery(userEmail,setting,value).then((query) => {
      getData(query).then((data) => {
        return resolve(data);
      }).catch(err => {
        log.error(`<<<<< Exited microservice-user-settings.executeQuery.service.updateUserSettingsData with error ${err}`);
        reject(err);
      })
    }).catch(err => {
      log.error(`<<<<< Exited microservice-user-settings.executeQuery.service.updateUserSettingsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to delete previous setting values from DB
 * @param {*} userEmail - user email 
 */
 async function deleteUserSettingsData(userEmail) {
  return new Promise((resolve, reject) => {
    deleteUserSettingsDataQuery(userEmail).then((query) => {
      getData(query).then((data) => {
        return resolve(data);
      }).catch(err => {
        log.error(`<<<<< Exited microservice-user-settings.executeQuery.service.deleteUserSettingsData with error ${err}`);
        reject(err);
      })
    }).catch(err => {
      log.error(`<<<<< Exited microservice-user-settings.executeQuery.service.deleteUserSettingsData with error ${err}`);
      reject(err);
    })
  })
}

async function getData(query) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-user-settings.services.executeQuery.service.getData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-user-settings.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        log.error(`<<<<< Exit microservice-user-settings.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = { getUserSettingsData, updateUserSettingsData, deleteUserSettingsData, getData };
