const express = require('express');

const router = new express.Router();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require("../config/constants.json");
const { getCMSData } = require("../microservice-kpi/services/executeQuery.service");


const cmsController = require('../microservice-cms/cms.controller');
const { validatePublications } = require('./validators/cms.validator');
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage() })
const passport = require("passport");

router.post('/publications/create', upload.any(),validatePublications, async (req, res, next) => {
  try {
      const data = await cmsController.createPublications(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
  } catch (err) {
      log.error(`Error creating publications content-type, ERROR: ${err}`);
      next(err);
    }
  }
);

router.get(
  "/nodes-list",
  async (req, res, next) => {
    try {
      const data = await getCMSData();
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error creating publications content-type, ERROR: ${err}`);
      next(err);
  }
});

module.exports = router;
