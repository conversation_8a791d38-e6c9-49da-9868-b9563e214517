const oracleModels = require('../../database/models/oracle'); // Oracle models
const { encryptEmail } = require("../../services/encryption.service");

// Oracle models
const { IfpFlowUser } = oracleModels;

// User role mapping
const userRoleMapping = {
  'REQUESTOR': [
    'USER'
  ],
  'APPROVER': [
    'SECONDARY_SUPERUSER'
  ],
}

/**
 * Helper function to get object details based on objectType and objectId
 * This would need to be implemented based on your application's architecture
 */
async function getObjectDetails(objectType, objectId) {
  // This is a placeholder implementation
  // In a real application, you would fetch details from the appropriate service/model
  // based on the objectType

  switch (objectType) {
    case 'KPI':
      // Fetch KPI details
      // return await KpiService.getKpiDetails(objectId);
      return { type: 'KPI', name: 'Sam<PERSON> KPI', id: objectId };

    case 'DASHBOARD':
      // Fetch Dashboard details
      // return await DashboardService.getDashboardDetails(objectId);
      return { type: 'DASHBOARD', name: 'Sample Dashboard', id: objectId };

    case 'ENTITY':
      // Fetch Entity details
      // return await EntityService.getEntityDetails(objectId);
      return { type: 'ENTITY', name: 'Sample Entity', id: objectId };

    default:
      return { id: objectId };
  }
}

/**
 * Formats an approval request object into a standardized response shape with optimized performance.
 *
 * @param {Object} approvalRequest - The raw approval request object from the database.
 * @param {Object} [options] - Optional parameters to include additional information.
 * @param {string} [options.action] - The action performed (e.g., "approved", "rejected").
 * @param {string} [options.currentUserEmail] - Email of the current user performing the action.
 * @param {Object} [options.commentRecord] - Comment object associated with the action.
 * @param {Object} [options.requesterDetails] - Optional requester info { name, email }.
 * @param {Object} [options.assigneeDetails] - Optional assignee info { name, email }.
 * @param {Object} [options.objectDetails] - Optional domain-specific object metadata.
 *
 * @returns {Object} The formatted approval request object.
 */
async function formatApprovalRequest(approvalRequest, options = {}) {
  const {
    action = null,
    currentUserEmail = null,
    commentRecord = null,
    requesterDetails = null,
    assigneeDetails = null,
    objectDetails = null
  } = options;

  // Start with basic response shape
  const response = {
    id: approvalRequest.id,
    status: approvalRequest.status,
    createdAt: approvalRequest.created_at,
    updatedAt: approvalRequest.updated_at,
    lastActionAt: approvalRequest.last_action_at,
  };

  // Collect all user detail promises to be resolved in parallel
  const promises = [];
  
  // Setup user detail promises only if the IDs exist and details aren't already provided
  if (approvalRequest.requestor_id && !requesterDetails) {
    promises.push(
      getUserDetails(approvalRequest.requestor_id)
        .then(details => { response.requester = details; })
    );
  } else if (requesterDetails) {
    response.requester = requesterDetails;
  }

  if (approvalRequest.assignee_id && !assigneeDetails) {
    promises.push(
      getUserDetails(approvalRequest.assignee_id)
        .then(details => { response.assignee = details; })
    );
  } else if (assigneeDetails) {
    response.assignee = assigneeDetails;
  }

  // Add action details if any action-related options are provided
  if (action || currentUserEmail || commentRecord) {
    response.actionDetails = {
      action,
      comment: commentRecord?.content ?? null
    };
    
    // Only fetch current user details if email is provided and not already in another field
    if (currentUserEmail && 
        currentUserEmail !== approvalRequest.requestor_id && 
        currentUserEmail !== approvalRequest.assignee_id) {
      promises.push(
        getUserDetails(currentUserEmail)
          .then(details => { response.actionDetails.user = details; })
      );
    }
  }

  // Add object details if provided
  if (approvalRequest.object_id && !objectDetails) {
    // Get object details based on object type
    promises.push(
      getObjectDetails(
        approvalRequest.object_type, 
        approvalRequest.object_id
      ).then(
        details => { response.objectDetails = details; }
      )
    );
  } else if (objectDetails) {
    response.objectDetails = objectDetails;
  }

  // Wait for all user detail promises to resolve
  if (promises.length > 0) {
    await Promise.all(promises);
  }

  return response;
}

/**
 * Gets user details from the database with error handling
 * 
 * @param {string} userEmail - The email of the user to fetch details for
 * @returns {Object} User details object with name, email, and role
 */

async function getUserDetails(userEmail) {
  try {
    const encryptedEmail = encryptEmail(userEmail);
    const user = await IfpFlowUser.findOne({
      where: { EMAIL: encryptedEmail },
      attributes: ['name', 'role']
    });
    
    const userDetails = {
      name: user?.name ?? null,
      email: userEmail,
      role: user?.role ?? null,
    };
    
    return userDetails;
  } catch (err) {
    console.error(`Failed to fetch user details: ${err.message}`);
    const fallbackDetails = {
      name: null,
      email: userEmail,
      role: null,
    };
    return fallbackDetails;
  }
}

/**
 * Formats an approval request comment object into a standardized response shape with optimized performance.
 *
 * @param {Object} comment - The raw comment object from the database.
 * @param {Object} [options] - Optional parameters to include additional information.
 * @param {Array} [options.attachments] - Array of attachment objects related to this comment.
 * @param {Object} [options.userDetails] - Optional user details { id, name, email, avatar }.
 * @param {boolean} [options.fetchUserDetails=false] - Whether to fetch user details if not already provided.
 * @param {boolean} [options.fetchAttachments=false] - Whether to fetch attachments if not already provided.
 * @param {string} [options.commentType] - Override the comment type if needed.
 * @param {Object} [options.metadata] - Additional metadata to include with the comment.
 *
 * @returns {Promise<Object>} The formatted approval request comment object.
 */
async function formatApprovalRequestComment(comment, options = {}) {
  const {
    attachments = null,
    userDetails = null,
    fetchUserDetails = false,
    fetchAttachments = false,
    metadata = null
  } = options;

  // Start with basic response shape
  const response = {
    id: comment.id,
    // requestId: comment.request_id,
    // userId: comment.user_id,
    content: comment.content,
    commentType: comment.comment_type,
    createdAt: comment.created_at,
    updatedAt: comment.updated_at
  };

  // Collect all promises to be resolved in parallel
  const promises = [];

  // Get attachments if needed
  if (fetchAttachments && !attachments && comment.id) {
    promises.push(
      getCommentAttachments(comment.id)
        .then(fetchedAttachments => { response.attachments = fetchedAttachments; })
    );
  } else {
    if (attachments && attachments.length > 0) {
      response.attachments = attachments;
    }
  }

  // Get user details if needed
  if (fetchUserDetails && !userDetails && comment.user_id) {
    promises.push(
      getUserDetails(comment.user_id)
        .then(details => { response.userDetails = details; })
    );
  } else {
    response.userDetails = userDetails || {
      name: null,
      email: comment.user_id,
      role: null,
    };
  }

  // Add additional metadata if provided
  if (metadata) {
    response.metadata = metadata;
  }

  // Wait for all promises to resolve
  if (promises.length > 0) {
    await Promise.all(promises);
  }

  return response;
}

/**
 * Helper function to get comment attachments
 * @param {string|number} commentId - ID of the comment
 * @returns {Promise<Array>} - Array of attachment objects
 */
async function getCommentAttachments(commentId) {
  // Implementation would depend on your data access layer
  // This is a placeholder for the actual implementation
  return []; // Return empty array for now
}

module.exports = {
  getObjectDetails,
  formatApprovalRequest,
  getUserDetails,
  formatApprovalRequestComment,
  userRoleMapping,
};