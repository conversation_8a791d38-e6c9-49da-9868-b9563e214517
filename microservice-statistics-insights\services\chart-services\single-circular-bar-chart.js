const scadLib = require('scad-library');
const { util } = scadLib;
const Logger = scadLib.logger;
const log = new Logger().getInstance();


async function getSingleCircularBarSeries(visualization, results) {
    log.debug('>>>>> Enter services.chart-services.single-circular-bar-chart.getStackedVerticalBarData');
    return new Promise((resolve, reject) => {
        try {
            const sortField = visualization.sortField;

            visualization.seriesMeta.forEach(series => {
                series.data = [];
                let matchingValueObject = series.dimension;
                let result = results.filter(function (item) {
                    item.OBS_DT = util.convertDate(`${item.OBS_DT}`);
                    for (var key in matchingValueObject) {
                        let itemColumn = item[key] === null ? '' : item[key].toUpperCase();
                        let dimensionValue = matchingValueObject[key] === null ? '' : matchingValueObject[key].toUpperCase();
                        if (item[key] === undefined || itemColumn != dimensionValue) {
                            return false;
                        }
                    }
                    series.sortField = item[sortField] ? item[sortField].value : '';
                    Object.keys(item).forEach((k) => item[k] == null && delete item[k] && delete item['INSERT_DT'] && delete item['INSERT_USER_ID']); 
                    return true;
                });
                if (result.length >= 1) series.data = result;

            });


            let sortObj = {
                sortField: sortField,
                sortType: visualization.sortOrder
            };

            util.sortArray(visualization.seriesMeta, sortObj);
            log.debug('<<<<< Exit services.chart-services.single-circular-bar-chart.getStackedVerticalBarData successfully');
            resolve(visualization);
        } catch (err) {
            log.error(`<<<<< Exit services.chart-services.single-circular-bar-chart.getStackedVerticalBarData with error ${err}`);
            reject(err);
        }
    });
};

module.exports = {
    getSingleCircularBarSeries,
};
