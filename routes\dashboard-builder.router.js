const express = require('express');
const router = new express.Router();
const dashboardBuilderController = require('../microservice-dashboard-builder/dashboard-builder.controller');
const {
    validateDashboardList,
    validateDashboard,
    validateCreateDashboard,
    validateEditDashboard,
    validateDeleteDashboard,
    validateShareDashboard,
    validateShareListDashboard,
    validateDeleteShareDashboard,
    validateDashboardCardData,
} = require('./validators/dashboard-builder.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const multer = require('multer');
const upload = multer({ storage: multer.memoryStorage(),limits: { fileSize: 4 * 1024 * 1024 } }); 

router.get('/dashboards', validateDashboardList, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).getDashboardsList(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching dashboard list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/dashboard/:id',validateDashboard, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).getDashboardDetail(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching dashboard detail, ERROR: ${err}`);
        next(err);
    }
});

router.post('/create',upload.fields([{ name: 'logo',maxCount: 1}, { name: 'thumbnailLight',maxCount: 1 },{ name: 'thumbnailDark',maxCount: 1 }]), validateCreateDashboard, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).createDashboard(req);
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error creating dashboard, ERROR: ${err}`);
        next(err);
    }
});

router.put('/dashboard/:id',upload.fields([{ name: 'logo',maxCount: 1}, { name: 'thumbnailLight',maxCount: 1 }, { name: 'thumbnailDark',maxCount: 1 }]), validateEditDashboard, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).editDashboard(req);
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error editing dashboard, ERROR: ${err}`);
        next(err);
    }
});

// Delete Dashboard
router.delete('/dashboard/:id',validateDeleteDashboard, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).deleteDashboard(req);
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error deleting dashboard, ERROR: ${err}`);
        next(err);
    }
});

router.post('/dashboard/share',validateShareDashboard, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).shareDashboard(req);
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error deleting dashboard, ERROR: ${err}`);
        next(err);
    }
});

router.get('/dashboard/share/:id', async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).getSharedDashboard(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching dashboard detail, ERROR: ${err}`);
        next(err);
    }
});

router.get("/dashboard/share/list/:type", validateShareListDashboard, async (req, res, next) => {
    try {
      const data = await new dashboardBuilderController(req).getShareDashboardList(req);
      res.set("Content-Type", "application/json");
      res.send(data);
      next();
    } catch (err) {
      log.error(
        `Error fetching data for myapps ERROR: ${err}`
      );
      next(err);
    }
  });

router.delete("/dashboard/share/delete/:type/:id",validateDeleteShareDashboard,async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).deleteShareDashboard(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(
        `Error fetching data for myapps ERROR: ${err}`
        );
        next(err);
    }
});

router.post("/card-data", validateDashboardCardData, async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).saveDashboardCardData(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(
        `Error fetching data for myapps ERROR: ${err}`
        );
        next(err);
    }
});

router.put("/card-data/:dataId", validateDashboardCardData,  async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).updateDashboardCardData(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(
        `Error fetching data for myapps ERROR: ${err}`
        );
        next(err);
    }
});


router.get("/card-data/:dataId", async (req, res, next) => {
    try {
        const data = await new dashboardBuilderController(req).getDashboardCardData(req);
        res.set("Content-Type", "application/json");
        res.send(data);
        next();
    } catch (err) {
        log.error(
        `Error fetching data for myapps ERROR: ${err}`
        );
        next(err);
    }
});

module.exports = router;
