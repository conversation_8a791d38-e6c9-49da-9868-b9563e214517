{"paths": {"/content-type/survey/check": {"get": {"tags": ["Survey"], "summary": "Retrieve survey attend status for a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A general message regarding the survey status."}, "surveyStatus": {"type": "boolean", "description": "Indicates the availability of a survey for the user to attend."}}, "required": ["message", "surveyStatus"], "oneOf": [{"properties": {"surveyStatus": {"enum": [true], "description": "True indicates that there is at least one survey available for the user."}, "surveyDetails": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the survey."}, "title_en": {"type": "string", "description": "The English title of the survey."}, "title_ar": {"type": "string", "description": "The Arabic title of the survey."}, "desc_en": {"type": "string", "description": "The English description of the survey."}, "desc_ar": {"type": "string", "description": "The Arabic description of the survey."}, "launch_date": {"type": "string", "format": "date", "description": "The launch date of the survey in DD-MM-YYYY format."}, "queries": {"type": "array", "items": {"type": "object", "properties": {"query_en": {"type": "string", "description": "Survey question in English."}, "query_ar": {"type": "string", "description": "Survey question in Arabic."}}, "required": ["query_en", "query_ar"], "description": "A list of queries or questions included in the survey."}}}, "required": ["id", "title_en", "title_ar", "desc_en", "desc_ar", "launch_date", "queries"], "description": "Detailed information about the survey available for the user."}}, "required": ["surveyDetails"]}, {"properties": {"surveyStatus": {"enum": [false], "description": "False indicates that there are no surveys available for the user to attend."}, "reason": {"type": "string", "description": "Explains why there are no surveys available for the user."}}, "required": ["reason"], "description": "Information provided when there are no surveys for the user to attend."}], "description": "Schema for validating responses related to survey availability, including scenarios where a survey is available and where no surveys are available."}}}}}}}, "/content-type/survey/attend": {"post": {"tags": ["Survey"], "summary": "Attend survey for a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Survey Attend Data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SurveyAttend"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message indicating the result of the survey submission."}, "status": {"type": "boolean", "description": "A boolean status indicating the success or failure of the survey submission."}}, "required": ["message", "status"], "description": "Schema for validating the response of a survey submission."}}}}}}}}, "components": {"schemas": {"SurveyAttend": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the survey."}, "title_en": {"type": "string", "description": "The English title of the survey."}, "title_ar": {"type": "string", "description": "The Arabic title of the survey."}, "desc_en": {"type": "string", "description": "The English description of the survey."}, "desc_ar": {"type": "string", "description": "The Arabic description of the survey."}, "launch_date": {"type": "string", "description": "The launch date of the survey in DD-MM-YYYY format.", "pattern": "^\\d{2}-\\d{2}-\\d{4}$"}, "feedback": {"type": "string", "description": "General feedback comments from the user about the survey."}, "overall_rating": {"type": "integer", "description": "Overall satisfaction rating provided by the user, on a scale (e.g., 1 to 5).", "minimum": 1, "maximum": 5}, "queries": {"type": "array", "items": {"type": "object", "properties": {"query_en": {"type": "string", "description": "Survey question in English."}, "query_ar": {"type": "string", "description": "Survey question in Arabic."}, "response": {"type": "string", "description": "The user's response rating to the query, typically a numerical value."}, "question_feedback": {"type": "string", "description": "Optional feedback provided by the user for the specific question."}}, "required": ["query_en", "query_ar", "response"], "description": "A list of queries or questions included in the survey along with the user's responses."}}}, "required": ["id", "title_en", "title_ar", "desc_en", "desc_ar", "launch_date", "feedback", "overall_rating", "queries"], "description": "Schema for validating the structure of a survey feedback submission request."}}}}