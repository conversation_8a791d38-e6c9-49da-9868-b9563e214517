const { util } = require('scad-library');

const moment = require('moment');
const { getFilterOptionsFromDB, getAllFilterDataFromDB } = require('./getGraphData');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getFilterOptions(filterPanel, lang) {
    return new Promise((resolve, reject) => {
        try {
            if (filterPanel.properties.length > 0) {
                filterPanel.properties.forEach(filter => {
                    filter["options"] = [];
                    filter["optionsMap"] = [];
                });
                getFilterOptionsFromDB(filterPanel).then(results => {
                    results.forEach(element => {
                        let key='DIMENSION';
                        if(lang == '/ar' && element.DIMENSION !='OBS_DT'){
                            key='DIMENSION_AR'
                        }
                        try{
                            filterPanel.properties.forEach(filter => {
                                if (filter.path.toUpperCase() === element[key].toUpperCase()) {
                                    filter["optionsMap"].push(element);
                                    if (lang != '/ar') {
                                        filter.options.push(element.VALUE);
                                    }
                                    else {
                                        filter.options.push(element.VALUE_AR);
                                    }
                                }
                            });
                        }
                        catch(err){
                            
                            log.error(err);
                            reject(err);
                        }

                    });
                    filterPanel.properties.forEach(filter => {
                        if (filter.selectDefault) {
                            let arr = [];
                            filter.options.forEach(e => {
                                let i = !isNaN(parseInt(e)) ? e : '';
                                if (i) {
                                    arr.push(i);
                                }
                            });
                            arr.sort((a, b) => b - a);
                            filter["default"] = arr.slice(0, filter.selectDefault);
                        }
                    })
                    resolve(filterPanel);
                })
            }
        } catch (err) {
            
            log.error(err);
            reject(err);
        }
    })
}

async function getFilterDimensionTypes(filterDimensionTypes) {
    try {
        if (filterDimensionTypes.properties.length > 0) {
            filterDimensionTypes.properties.forEach(filter => {
                filter["options"] = {};
            });
            let results = await getFilterOptionsFromDB(filterDimensionTypes)
            
            results.forEach(element => {
                filterDimensionTypes.properties.forEach(filter => {
                    if (filter.path.toUpperCase() === element.DIMENSION.toUpperCase()) {
                        filter.options[element.VALUE] = element.VALUE_TYPE;
                    }
                });

            });
            return filterDimensionTypes;
        }
    } catch (err) {
        log.error(err);
        
        throw err
    }
}


async function getAllFilterData(meta) {
    try {
        await Promise.all(meta.map(async (element) => {
            const data = await getAllFilterDataFromDB(element)
            element["data"] = data;
            if (element.yearlyData) {
                const yearlyData = await getAllFilterDataFromDB(element.yearlyData, true)
                element.data.push(...yearlyData);
            }
        }))
        return meta;
    } catch (err) {
        throw err;
    }
}

module.exports = { getFilterOptions, getAllFilterData, getFilterDimensionTypes }