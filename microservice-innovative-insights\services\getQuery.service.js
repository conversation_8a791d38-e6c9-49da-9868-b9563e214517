const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const clkTables = require('../../config/constants.json').clickhouseTables;

async function getInnovativeIndicatorDataQuery(indicatorId, viewName, lang) {
    return new Promise((resolve, reject) => {
        try {

            let query;
            let binds = {
                indicatorId: indicatorId
            }

            let translatedKeys = {
                titleColumn : lang == 'EN'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR',
                unitColumn : lang == 'EN'?'UNIT':'UNIT_AR',
                dataSourceColumn : lang == 'EN'?'DATA_SOURCE':'DATA_SOURCE_ARABIC',
                topicColumn : lang == 'EN'?'TOPIC_NAME_ENGLISH':'TOPIC_NAME_ARABIC',
                themeColumn : lang == 'EN'?'THEME_NAME_ENGLISH':'THEME_NAME_ARABIC'
            }

            if (clkTables.includes(viewName))
                query = `SELECT 
                t.VALUE,
                t.${translatedKeys.unitColumn} AS UNIT,
                t.${translatedKeys.titleColumn} AS INDICATOR_NAME,
                MAX(t.VALUE) OVER() AS MAX_VALUE,
                MIN(t.VALUE) OVER() AS MIN_VALUE,
                formatDateTime(toDate(toString(MAX(t.OBS_DT) OVER())), '%Y-%m-%d') AS MAX_OBS_DT,
                formatDateTime(toDate(toString(MIN(t.OBS_DT) OVER())), '%Y-%m-%d') AS MIN_OBS_DT,
                formatDateTime(toDate(toString(t.OBS_DT)), '%Y-%m-%d') AS OBS_DT,
                formatDateTime(toDate(toString(t.OBS_DT)), '%Y') AS YEAR,
                t.OBS_DT_LATEST,
                t.VALUE_LATEST,
                t.MONTHLY_CHANGE_VALUE,
                t.MONTHLY_COMPARE_VALUE,
                t.QUARTERLY_CHANGE_VALUE,
                t.QUARTERLY_COMPARE_VALUE,
                t.YEARLY_CHANGE_VALUE,
                t.YEARLY_COMPARE_VALUE,
                t.MONTHLY,
                t.QUARTERLY,
                t.YEARLY,
                t.INSERT_DT,
                ${viewName=='VW_IFP_IND_CENSUS_POP_C_VALUES'?'t.MARITAL_STATUS_EN,t.MARITAL_STATUS_AR,':''}
                t.${translatedKeys.dataSourceColumn} AS DATA_SOURCE,
                t.${translatedKeys.topicColumn} AS TOPIC,
                t.${translatedKeys.themeColumn} AS THEME
                FROM ${viewName} AS t 
                WHERE INDICATOR_ID = {indicatorId:String}
                ORDER BY OBS_DT;`
            

            else {
                binds.indicatorId = indicatorId
                query = `SELECT \
                t.VALUE,\
                t.${translatedKeys.unitColumn} AS UNIT,\
                t.${translatedKeys.titleColumn} AS INDICATOR_NAME,\
                MAX(t.VALUE) OVER () AS MAX_VALUE,\
                MIN(t.VALUE) OVER () AS MIN_VALUE,\
                TO_CHAR(TO_DATE(MAX(t.OBS_DT) OVER (), 'YYYYMMDD'), 'YYYY-MM-DD') AS MAX_OBS_DT,\
                TO_CHAR(TO_DATE(MIN(t.OBS_DT) OVER (), 'YYYYMMDD'), 'YYYY-MM-DD') AS MIN_OBS_DT,\
                TO_CHAR(TO_DATE(t.OBS_DT, 'YYYYMMDD'), 'YYYY-MM-DD') AS OBS_DT,\
                TO_CHAR(TO_DATE(t.OBS_DT, 'YYYYMMDD'), 'YYYY') AS YEAR, \
                t.OBS_DT_LATEST,\
                t.VALUE_LATEST,\
                t.MONTHLY_CHANGE_VALUE,\
                t.MONTHLY_COMPARE_VALUE,\
                t.QUARTERLY_CHANGE_VALUE,\
                t.QUARTERLY_COMPARE_VALUE,\
                t.YEARLY_CHANGE_VALUE,\
                t.YEARLY_COMPARE_VALUE,\
                t.MONTHLY,\
                t.QUARTERLY,\
                t.YEARLY,\
                t.INSERT_DT,\
                t.${translatedKeys.dataSourceColumn} AS DATA_SOURCE,\
                t.${translatedKeys.topicColumn} AS TOPIC,\
                t.${translatedKeys.themeColumn} AS THEME\
                FROM ${viewName} t WHERE INDICATOR_ID=:indicatorId ORDER BY OBS_DT`
            }

            return resolve({ query: query, binds: binds });
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeIndicatorDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInnovativeFilterDataQuery(filterIndicator,lang) {
    return new Promise((resolve, reject) => {
        try {

            let binds = {}
            let query;

            let translatedKeys = {
                dimensionColumn: lang == 'EN'?'DIMENSION':'DIMENSION_AR',
                dimensionLabelColumn: lang == 'EN'?'DIMENSION_LABEL':'DIMENSION_LABEL_AR',
                valueColumn: lang == 'EN'?'VALUE':'VALUE_AR',
                valueLabelColumn: lang == 'EN'?'VALUE_LABEL':'VALUE_LABEL_AR'
            }

            binds = {
                filterIndicator: filterIndicator
            }

            query = `SELECT 
            ${translatedKeys.dimensionColumn} AS DIMENSION,
            ${translatedKeys.dimensionLabelColumn} AS DIMENSION_LABEL,
            ${translatedKeys.valueColumn} AS VALUE,
            ${translatedKeys.valueLabelColumn} AS VALUE_LABEL,
            DEFAULT_SELECTION
            FROM VW_IFP_INDICATOR_ATTRIBUTES WHERE INDICATOR={filterIndicator: String} ORDER BY FILTER_SORT,RANK`
            return resolve({ query: query, binds: binds });
        
            
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeFilterDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInnovativeIndicatorsDataQuery(viewName, filters, sortBy = {}, offset, limit,lang) {
    return new Promise((resolve, reject) => {
        try {
            let binds = {}
            
            let titleColumn = lang == 'EN'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR';
            let unitColumn = lang == 'EN'?'UNIT':'UNIT_AR';

            const whereClauses = [];
            let bindCounter = 1;

            for (const filterKey in filters) {
                if (filters[filterKey].length > 0) {
                    if (Array.isArray(filters[filterKey])) {
                        const bindKeys = filters[filterKey].map(value => {
                            binds[`value${bindCounter}`] = value.toUpperCase();
                            bindCounter++;
                            return `{value${bindCounter - 1}:String}`;
                        });
                        whereClauses.push(`UPPER(${filterKey}) IN (${bindKeys.join(', ')})`);
                    } else {
                        binds[`value${bindCounter}`] = filters[filterKey].toUpperCase();
                        whereClauses.push(`UPPER(${filterKey}) = {value${bindCounter}:String}`);
                        bindCounter++;
                    }
                }
            }

            const sortByClauses = [];
            for (const sortKey in sortBy) {
                if (sortKey === 'alphabetical') {
                    sortByClauses.push(`t.INDICATOR_NAME ${sortBy[sortKey]}`);
                } else if (sortKey === 'byValue') {
                    sortByClauses.push(`t.VALUE_LATEST ${sortBy[sortKey]}`);
                } else if (sortKey === 'byChange') {
                    sortByClauses.push(`t.YEARLY_CHANGE_VALUE ${sortBy[sortKey]}`);
                }
            }

            if (Object.keys(sortBy).length == 0)
                sortByClauses.push(`t.INDICATOR_ID ASC`)

            query =
                `SELECT 
                    COUNT(*) OVER() AS TOTAL, 
                    t.INDICATOR_ID,
                    t.INDICATOR_NAME,
                    t.OBS_DT_LATEST,
                    t.VALUE_LATEST,
                    t.MONTHLY_CHANGE_VALUE,
                    t.MONTHLY_COMPARE_VALUE,
                    t.QUARTERLY_CHANGE_VALUE,
                    t.QUARTERLY_COMPARE_VALUE,
                    t.YEARLY_CHANGE_VALUE,
                    t.YEARLY_COMPARE_VALUE,
                    t.MONTHLY,
                    t.QUARTERLY,
                    t.YEARLY,
                    t.${unitColumn}  AS UNIT
                FROM 
                (
                    SELECT DISTINCT 
                        INDICATOR_ID,
                        ${titleColumn} AS INDICATOR_NAME,
                        OBS_DT_LATEST,
                        VALUE_LATEST,
                        MONTHLY_CHANGE_VALUE,
                        MONTHLY_COMPARE_VALUE,
                        QUARTERLY_CHANGE_VALUE,
                        QUARTERLY_COMPARE_VALUE,
                        YEARLY_CHANGE_VALUE,
                        YEARLY_COMPARE_VALUE,
                        MONTHLY,
                        QUARTERLY,
                        YEARLY,
                        ${unitColumn}
                    FROM ${viewName} 
                    ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''}
                ) AS t
                ORDER BY ${sortByClauses.join(',')}
                LIMIT ${limit} OFFSET ${offset}`

            return resolve({ query: query, binds: binds });
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeFilterDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getIndicatorMetaDataQuery(indicatorId,lang) {
    return new Promise((resolve, reject) => {
        try {
            let translatedKeys = {
                titleColumn : lang == 'EN'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR',
                topicColumn : lang == 'EN'?'TOPIC_NAME_ENGLISH':'TOPIC_NAME_ARABIC',
                themeColumn : lang == 'EN'?'THEME_NAME_ENGLISH':'THEME_NAME_ARABIC'
            }

            let binds ={
                indicatorId:indicatorId
            }
            let query = `SELECT 
                INDICATOR_ID,
                ${translatedKeys.titleColumn} AS INDICATOR_NAME,
                ${translatedKeys.topicColumn} AS TOPIC_NAME,
                ${translatedKeys.themeColumn} AS THEME_NAME,
                SOURCE_TABLE FROM VW_INDICATOR_MAP WHERE INDICATOR_ID={indicatorId: String}`

            return resolve({ query: query, binds: binds });
            
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeFilterDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = {
    getInnovativeIndicatorDataQuery,
    getInnovativeFilterDataQuery,
    getInnovativeIndicatorsDataQuery,
    getIndicatorMetaDataQuery
};
