const Logger = require('scad-library').logger;
require('dotenv').config();

const { getMetaFromCMS } = require('../services/common-service');
const { getUserJourneyStatusData,updateUserJourneyStatus} = require('./services/executeQuery');
const log = new Logger().getInstance();
const constants = require('../config/constants.json');

async function getUserJourney(req) {
    log.debug(`>>>>>Entered microservice.user-journey.controller.getUserJourney`);
    return new Promise(async (resolve, reject) => {

        try {

            const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
            const cmsUserJourney = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_USER_JOURNEY}`;
            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
            
            [userJourney,userJourneyStatus] = await Promise.all([
                getMetaFromCMS(req,cmsLoginUrl, cmsUserJourney, req.user.groups),
                getUserJourneyStatusData(req.user.preferred_username),
            ])

            let response = {}

            if (userJourneyStatus.length) {
              let attendStatus = userJourneyStatus[0].STATUS;
              response.status = attendStatus ? true : false;
              userJourney.forEach(journey =>{
                if (journey.image)
                  journey.image = `${process.env.CMS_BASEPATH_URL}${journey.image}`
                journey.sections.forEach(section=>{
                  if (section.image)
                    section.image = `${process.env.CMS_BASEPATH_URL}${section.image}`
                })
              })
              response.data = userJourney;
            } else {
              response.status = false;
              userJourney.forEach(journey =>{
                if (journey.image)
                  journey.image = `${process.env.CMS_BASEPATH_URL}${journey.image}`
                journey.sections.forEach(section=>{
                  if (section.image)
                    section.image = `${process.env.CMS_BASEPATH_URL}${section.image}`
                })
              })
              response.data = userJourney;
            }
              
            return resolve(response)

        } catch (err) {
          
            log.error(`<<<<< Exited microservice.user-journey.controller.getUserJourney with error ${err} `)
            reject(err);
        }
    });
}

async function attendUserJourney(req) {
  log.debug(`>>>>>Entered microservice.user-journey.controller.attendUserJourney`);
  return new Promise(async (resolve, reject) => {

      try {

          await updateUserJourneyStatus(req.user.preferred_username,1)
          
          let response = {
            "message":"User journey attended successfully",
            "status":true
          }
            
          return resolve(response)

      } catch (err) {
          
          log.error(`<<<<< Exited microservice.user-journey.controller.attendUserJourney with error ${err} `)
          reject(err);
      }
  });
}


module.exports = {
  getUserJourney,
  attendUserJourney
};
