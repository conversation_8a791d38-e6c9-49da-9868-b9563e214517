
require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { 
    setInteractionData
 } = require('./services/executeQuery.service');

/**
 * function to get notifications
 * @param {*} req 
 */
async function setInteraction(req) {
    log.debug(`>>>>>Entered microservice-interaction.controller.listMyApps`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username
            const nodeId = req.body.nodeId
            await setInteractionData(nodeId,userEmail)
            resolve({'message':'Interaction recorded successfully'})
        } catch (err) {
            
            log.error(`<<<<<Exited microservice-interaction.controller.listMyApps with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { setInteraction};
