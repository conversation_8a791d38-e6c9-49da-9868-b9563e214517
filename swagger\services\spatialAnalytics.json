{"paths": {"/content-type/spatial-analytics/": {"get": {"tags": ["Spatial Analytics"], "summary": "Retrieves Spatial Analytical information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for each map configuration."}, "baseMapLightPortal": {"type": "string", "description": "Identifier for the light-themed base map."}, "baseMapDarkPortal": {"type": "string", "description": "Identifier for the dark-themed base map."}, "token": {"type": ["string", "null"], "description": "Authentication token if required for accessing map services."}, "customLayers": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the custom layer."}, "endpoint": {"type": "string", "description": "Service endpoint URL for the custom layer."}, "dark_icon": {"type": "string", "description": "URL to the icon for the dark theme."}, "light_icon": {"type": ["string", "null"], "description": "URL to the icon for the light theme. Can be null."}}}, "description": "Custom layers specific to this map configuration."}, "commonLayers": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the common layer."}, "endpoint": {"type": "string", "description": "Service endpoint URL for the common layer."}, "dark_icon": {"type": "string", "description": "URL to the icon for the dark theme."}, "light_icon": {"type": "string", "description": "URL to the icon for the light theme."}}}, "description": "Common layers available across multiple map configurations."}, "modules": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the module."}, "key": {"type": "string", "description": "Key identifier for the module."}, "name": {"type": "string", "description": "Name of the module."}, "attachment_excel": {"type": ["string", "null"], "description": "URL to an Excel file associated with the module, if any."}, "attachment_pdf": {"type": ["string", "null"], "description": "URL to a PDF file associated with the module, if any."}, "layers": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the layer."}, "endpoint": {"type": "string", "description": "Service endpoint URL for the layer."}, "dark_icon": {"type": ["string", "null"], "description": "URL to the icon for the dark theme. Can be null."}, "light_icon": {"type": ["string", "null"], "description": "URL to the icon for the light theme. Can be null."}}}, "description": "Layers included in the module."}, "configuration": {"type": "object", "properties": {"filters": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the filter."}, "path": {"type": "string", "description": "Data path for the filter."}, "type": {"type": "string", "description": "Type of filter (e.g., 'checkbox')."}}}, "description": "Filters applicable to the module."}, "tabs": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the tab."}, "figures": {"type": "object", "additionalProperties": true, "description": "Configuration for figures within the tab."}, "visualization": {"type": "object", "additionalProperties": true, "description": "Configuration for visualizations within the tab."}}}, "description": "Tabs configuration within the module."}, "order": {"type": "integer", "description": "Display order of the module."}, "display": {"type": "boolean", "description": "Whether the module is displayed."}, "indicator": {"type": "integer", "description": "Indicator ID associated with the module."}, "displayCheckboxes": {"type": "boolean", "description": "Whether to display checkboxes for the module."}}, "description": "Configuration details for the module."}}}, "description": "Mo<PERSON><PERSON> included in this map configuration."}}, "required": ["id", "baseMapLightPortal", "baseMapDarkPortal", "customLayers", "commonLayers", "modules"]}}}}}}}}}}