const { getRedis, setRedis } = require('./redis.service');
const auth = require('./auth');
const crypto = require('crypto')
const constants = require('../config/constants.json');
const axios = require('axios');
const { setRequestStatus, setUserActivationStatus } = require('../microservice-users-v3/services/executeQuery');
const { IFPError } = require('../utils/error');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getAuthToken(){
    let authCacheKey = crypto.createHash('md5').update('authCache').digest("hex")
    let authTokenResult = await getRedis(authCacheKey)
    let accessToken;
    if (authTokenResult)
        accessToken = JSON.parse(authTokenResult).accessToken
    else{
        let authData = await auth.getToken(auth.tokenRequest);
        accessToken = authData.accessToken
        setRedis(authCacheKey,JSON.stringify(authData),1000)
    }   

    return accessToken
}

async function checkUserAppAssignment(userEmail) {
    try {
        const groupAssignments = {
            "ProductionInternal":"ad85f6c4-04af-48ad-aed6-c63e7f9f768d",
            "ProductionExternal":"a0d83e83-22c0-4406-b0b8-6eb7246aa436",
            "StagingInternal":"93732bd5-d769-4b9a-bb0e-2e414a50b463"
        }
        const authToken = await getAuthToken();
        if (!authToken) throw new Error('Invalid Intra ID Auth Token');

        const options = {
            headers: {
                Authorization: `Bearer ${authToken}`
            }
        };

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${userEmail}`,
            options
        );

        const user = response.data
        if (!user) return false
        const userGroups = await getUserGroups(user.id);
        if (!userGroups) return false
        let groupIds= Object.values(groupAssignments)
        let assignedGroups = userGroups.filter(group => groupIds.includes(group.id))
        if (!assignedGroups.length) return false;
        return true
    } catch (error) {
        log.error(error)
        return false
    }
}

/**
 * Invites a user to Microsoft Intra ID 
 */
async function inviteIntraIdUser(user){
    try {
        const authToken = await getAuthToken()
        if(!authToken)
            throw new Error('Invalid Intra ID Auth Token ')

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }}

        const data = {
          invitedUserEmailAddress:user,
          inviteRedirectUrl:'https://ifp-dev.scad.gov.ae',
          sendInvitationMessage:true
        }

        const response = await axios.post(`${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.invitations}`, data,options);
        log.info(`[INTRAIDINVITER CRON] User ${user} has been invited`)
        // await setRequestStatus(user,'INTRA_ID_INVITE','INVITE_SENT')
        await setUserActivationStatus(user,'ENTRA_ID_INVITE_SENT')
        return response.data;
      } catch (error) {
          throw error;
      }
}


async function inviteStatusIntraIdUser(user){
    try {
        const authToken = await getAuthToken()
        if(!authToken)
            throw new Error('Invalid Intra ID Auth Token ')

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }}

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$select=Id,userPrincipalName,userType,displayName,mail,accountEnabled,creationType,externalUserState&$filter=mail eq '${user}'`,
             options
            );
        let userData = response.data.value.length?response.data.value[0]:null
        if (userData){
            if (userData.externalUserState == 'Accepted')
                return  {'id':userData.id,'mail':userData.mail,'displayName':userData.displayName,'inviteStatus':true,'accountEnabled':userData.accountEnabled};
            else
                return  {'id':userData.id,'mail':userData.mail,'displayName':userData.displayName,'inviteStatus':false,'accountEnabled':userData.accountEnabled};
        }
        else
            throw new Error(`No user data found for ${user}`)
        
      } catch (error) {
          throw error;
      }
}

async function getUserGroups(user_id){
    try{
        const authToken = await getAuthToken()
        if(!authToken)
            throw new Error('Invalid Intra ID Auth Token ')

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }}
        
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}/${user_id}/memberOf`,
             options
            );
        return response.data.value
    }
    catch(exp){
        throw exp;
    }
}

async function assignUserToGroup(user_id,groups){
    try {
        let userGroups = await getUserGroups(user_id)
        userGroups.forEach(group => {
            groups = groups.filter(g=>g!= group.id)
        });
        if (!groups.length)
            return {
                'status':true,
                'message':'The user is already assigned to the given groups'
            }

        const authToken = await getAuthToken()
        if(!authToken)
            throw new Error('Invalid Intra ID Auth Token ')

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }}

        const groupPromises = [];
        for (const group of groups) {
            const data = {
              "@odata.id": `https://graph.microsoft.com/v1.0/users/${user_id}`
            };
            groupPromises.push(
              axios.post(
                `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${group}/members/$ref`,
                data,
                options
              )
            );
          }
        
        response = await Promise.all(groupPromises);
        log.info(`[INTRAIDINVITEACCEPTPOLL CRON] User ${request.EMAIL} has been assigned to [${groups.join(',')}]`)
        return {"status":true,"message":`User has been assigned to [${groups.join(',')}]`}
        
        
      } catch (error) {
          return {"status":false,"error":error} ;
      }
}

async function getUserInfo(email){
    try {
        const authToken = await getAuthToken()
        if(!authToken)
            throw new Error('Invalid Intra ID Auth Token ')

        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }}

        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.users}?$select=Id,userPrincipalName,userType,displayName,mail,accountEnabled,creationType,externalUserState&$filter=mail eq '${email}' and accountEnabled eq true`,
            options
        );

        let userData;
        if (response.data && response.data.value && response.data.value.length > 0) {
            userData = response.data.value[0];
            return {'id':userData.id,'mail':userData.mail,'displayName':userData.displayName,'accountEnabled':userData.accountEnabled};
        }
      } catch (error) {
          throw error;
      }
}

async function unassignUserFromGroup(userId, groupId) {
    const authToken = await getAuthToken();
    if (!authToken) throw new Error("Invalid Intra ID Auth Token ");

    const options = {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    };

    try {
      await axios.delete(
        `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${groupId}/members/${userId}/$ref`,
        options
      );
      console.log(
        `Successfully unassigned user ${userId} from group ${groupId}`
      );
    } catch (err) {
      console.error(
        `Error unassigning user ${userId} from group ${groupId}:`,
        err.message
      );
    }
}

async function getGroupMembers(group_id){
    try{
        const authToken = await getAuthToken()
        if(!authToken)
            throw new Error('Invalid Intra ID Auth Token ')
 
        const options = {headers :  {
            Authorization: `Bearer ${authToken}`
        }}
       
        const response = await axios.get(
            `${process.env.USER_GRAPH_ENDPOINT}${constants.AD.url.groups}/${group_id}/members`,
             options
            );
        return response.data.value
    }
    catch(exp){
        throw exp;
    }
}

module.exports = {
    inviteIntraIdUser,
    inviteStatusIntraIdUser,
    assignUserToGroup,
    getUserInfo,
    getUserGroups,
    unassignUserFromGroup,
    checkUserAppAssignment,
    getGroupMembers
}
