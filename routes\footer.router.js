const express = require('express');
const router = new express.Router();
const footerController = require('../microservice-footer/footer.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
  try {
    const data = await footerController.getFooter(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/about-us', async (req, res, next) => {
  try {
    const data = await footerController.aboutUs(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/products', async (req, res, next) => {
  try {
    const data = await footerController.products(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/privacy-policy', async (req, res, next) => {
  try {
    const data = await footerController.privacyPolicy(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
