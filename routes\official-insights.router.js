const express = require('express');
const router = new express.Router();
const officialInsightsController = require('../microservice-official-insights/official-insights.controller');
const { validateOfficialInsights, validateOfficialInsightsList } = require('./validators/official-insights.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.post('/', validateOfficialInsightsList, async (req, res, next) => {
    try {
        const data = await officialInsightsController.getOfficialIndicators(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for official-insights by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/:id',validateOfficialInsights, async (req, res, next) => {
    try {
        const data = await officialInsightsController.getOfficialInsightsById(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for official-insights by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/filters/:id', async (req, res, next) => {
    try {
        const data = await officialInsightsController.getOfficialFilters(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for official-insights content-type, ERROR: ${err}`);
        next(err);
    }
});


module.exports = router;
