{"paths": {"/content-type/indicator-compare/create": {"post": {"tags": ["Indicator <PERSON><PERSON><PERSON>"], "summary": "Compare two or more indicators", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Indicator Compare data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompareCreate"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Composite identifier combining multiple indicator IDs."}, "type": {"type": "string", "description": "Type of the content, indicating its general category."}, "content_classification": {"type": "string", "description": "Classification of the content, providing a more specific categorization."}, "tagName": {"type": "string", "description": "Tag name associated with the content for further identification."}, "language": {"type": "string", "description": "Language of the content."}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the tool."}, "disabled": {"type": "boolean", "description": "Flag indicating whether the tool is disabled."}, "label": {"type": "string", "description": "Label of the tool."}}, "required": ["id", "disabled", "label"]}, "description": "Collection of tools related to the indicator."}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the filter."}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the option."}, "label": {"type": "string", "description": "Label of the option."}, "unit": {"type": ["string", "null"], "description": "Unit of measurement for the option."}, "value": {"type": ["integer", "null"], "description": "Value associated with the option."}, "isSelected": {"type": "boolean", "description": "Flag indicating whether the option is selected."}}, "required": ["id", "label", "isSelected"]}}}, "required": ["id", "options"]}, "description": "Collection of filters available for the indicator."}, "indicatorDrivers": {"type": "array", "description": "Drivers influencing the indicator, potentially empty."}, "indicatorValues": {"type": "object", "description": "Object holding indicator values, structure may vary."}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object", "properties": {"indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the visualization."}, "type": {"type": "string", "description": "Type of visualization (e.g., 'line-chart')."}, "seriesTitles": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Mapping of series identifiers to titles."}, "seriesMeta": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of series (e.g., 'solid')."}, "xAccessor": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of x-axis accessor (e.g., 'date')."}, "path": {"type": "string", "description": "Data path for the x-axis values."}, "specifier": {"type": "string", "description": "Format specifier for x-axis values."}}, "required": ["type", "path", "specifier"]}, "yAccessor": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of y-axis accessor (e.g., 'value')."}, "path": {"type": "string", "description": "Data path for the y-axis values."}}, "required": ["type", "path"]}, "data": {"type": "array", "items": {"type": "object", "properties": {}}, "description": "Data points for the series."}, "id": {"type": "string", "description": "Identifier for the series."}, "dbIndicatorId": {"type": "string", "description": "Database identifier for the indicator associated with the series."}, "label": {"type": "string", "description": "Label for the series."}, "color": {"type": "string", "description": "Color for the series."}, "yMax": {"type": "number", "description": "Maximum value for the y-axis."}, "yMin": {"type": "number", "description": "Minimum value for the y-axis."}, "xMin": {"type": "string", "format": "date-time", "description": "Minimum date for the x-axis."}, "xMax": {"type": "string", "format": "date-time", "description": "Maximum date for the x-axis."}}, "required": ["type", "xAccessor", "yAccessor", "data", "id", "label", "color", "yMax", "yMin", "xMin", "xMax"]}, "description": "Metadata for each series in the visualization."}, "tooltipTitleFormat": {"type": "string", "description": "Format for titles in tooltips."}, "tooltipValueFormat": {"type": "string", "description": "Format for values in tooltips."}, "xAxisFormat": {"type": "string", "description": "Format for the x-axis."}, "yAxisFormat": {"type": "string", "description": "Format for the y-axis."}, "xAxisLabel": {"type": "string", "description": "Label for the x-axis."}, "yAxisLabel": {"type": "string", "description": "Label for the y-axis."}}, "required": ["id", "type", "seriesMeta", "tooltipTitleFormat", "tooltipValueFormat", "xAxisFormat", "yAxisFormat", "xAxisLabel", "yAxisLabel"]}, "description": "Metadata for visualizations associated with the indicator."}, "visualizationDefault": {"type": "string", "description": "Identifier for the default visualization to be displayed."}}, "required": ["visualizationsMeta", "visualizationDefault"], "description": "Details about the visualizations for the indicator, including the default visualization and metadata for each visualization."}}}}, "visualizationDefault": {"type": "string", "description": "Identifier for the default visualization."}}, "description": "Details about visualizations for the indicator."}}, "required": ["id", "type", "content_classification", "tagName", "language", "indicatorTools", "indicatorFilters", "indicatorDrivers", "indicatorValues", "indicatorVisualizations"], "description": "Schema for a response including configuration details for indicators, tools, filters, and visualizations."}}}}}}}, "/content-type/indicator-compare/add-to-myapps": {"post": {"tags": ["Indicator <PERSON><PERSON><PERSON>"], "summary": "Add compared indicators to My Apps as a new indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Add Compared Indicators to My Apps data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompareAddToMyApps"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A textual message conveying the result of the operation."}, "status": {"type": "boolean", "description": "A boolean flag indicating the success status of the operation."}, "compareId": {"type": "string", "format": "uuid", "description": "A unique identifier for the compare operation."}}, "required": ["message", "status", "compareId"], "description": "Schema for a response indicating the outcome of adding a compare app to 'my apps'."}}}}}}}, "/content-type/indicator-compare/list": {"get": {"tags": ["Indicator <PERSON><PERSON><PERSON>"], "summary": "", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/indicator-compare/{id}": {"get": {"tags": ["Indicator <PERSON><PERSON><PERSON>"], "summary": "Retrieve compared indicators using id", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Compared Indicators"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Composite identifier combining multiple indicator IDs."}, "type": {"type": "string", "description": "Type of the content, indicating its general category."}, "content_classification": {"type": "string", "description": "Classification of the content, providing a more specific categorization."}, "tagName": {"type": "string", "description": "Tag name associated with the content for further identification."}, "language": {"type": "string", "description": "Language of the content."}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the tool."}, "disabled": {"type": "boolean", "description": "Flag indicating whether the tool is disabled."}, "label": {"type": "string", "description": "Label of the tool."}}, "required": ["id", "disabled", "label"]}, "description": "Collection of tools related to the indicator."}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the filter."}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the option."}, "label": {"type": "string", "description": "Label of the option."}, "unit": {"type": ["string", "null"], "description": "Unit of measurement for the option."}, "value": {"type": ["integer", "null"], "description": "Value associated with the option."}, "isSelected": {"type": "boolean", "description": "Flag indicating whether the option is selected."}}, "required": ["id", "label", "isSelected"]}}}, "required": ["id", "options"]}, "description": "Collection of filters available for the indicator."}, "indicatorDrivers": {"type": "array", "description": "Drivers influencing the indicator, potentially empty."}, "indicatorValues": {"type": "object", "description": "Object holding indicator values, structure may vary."}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object", "properties": {"indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the visualization."}, "type": {"type": "string", "description": "Type of visualization (e.g., 'line-chart')."}, "seriesTitles": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Mapping of series identifiers to titles."}, "seriesMeta": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of series (e.g., 'solid')."}, "xAccessor": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of x-axis accessor (e.g., 'date')."}, "path": {"type": "string", "description": "Data path for the x-axis values."}, "specifier": {"type": "string", "description": "Format specifier for x-axis values."}}, "required": ["type", "path", "specifier"]}, "yAccessor": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of y-axis accessor (e.g., 'value')."}, "path": {"type": "string", "description": "Data path for the y-axis values."}}, "required": ["type", "path"]}, "data": {"type": "array", "items": {"type": "object", "properties": {}}, "description": "Data points for the series."}, "id": {"type": "string", "description": "Identifier for the series."}, "dbIndicatorId": {"type": "string", "description": "Database identifier for the indicator associated with the series."}, "label": {"type": "string", "description": "Label for the series."}, "color": {"type": "string", "description": "Color for the series."}, "yMax": {"type": "number", "description": "Maximum value for the y-axis."}, "yMin": {"type": "number", "description": "Minimum value for the y-axis."}, "xMin": {"type": "string", "format": "date-time", "description": "Minimum date for the x-axis."}, "xMax": {"type": "string", "format": "date-time", "description": "Maximum date for the x-axis."}}, "required": ["type", "xAccessor", "yAccessor", "data", "id", "label", "color", "yMax", "yMin", "xMin", "xMax"]}, "description": "Metadata for each series in the visualization."}, "tooltipTitleFormat": {"type": "string", "description": "Format for titles in tooltips."}, "tooltipValueFormat": {"type": "string", "description": "Format for values in tooltips."}, "xAxisFormat": {"type": "string", "description": "Format for the x-axis."}, "yAxisFormat": {"type": "string", "description": "Format for the y-axis."}, "xAxisLabel": {"type": "string", "description": "Label for the x-axis."}, "yAxisLabel": {"type": "string", "description": "Label for the y-axis."}}, "required": ["id", "type", "seriesMeta", "tooltipTitleFormat", "tooltipValueFormat", "xAxisFormat", "yAxisFormat", "xAxisLabel", "yAxisLabel"]}, "description": "Metadata for visualizations associated with the indicator."}, "visualizationDefault": {"type": "string", "description": "Identifier for the default visualization to be displayed."}}, "required": ["visualizationsMeta", "visualizationDefault"], "description": "Details about the visualizations for the indicator, including the default visualization and metadata for each visualization."}}}}, "visualizationDefault": {"type": "string", "description": "Identifier for the default visualization."}}, "description": "Details about visualizations for the indicator."}}, "required": ["id", "type", "content_classification", "tagName", "language", "indicatorTools", "indicatorFilters", "indicatorDrivers", "indicatorValues", "indicatorVisualizations"], "description": "Schema for a response including configuration details for indicators, tools, filters, and visualizations."}}}}}}}}, "components": {"schemas": {"CompareCreate": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"nodes": {"type": "array", "items": {"type": "object", "properties": {"indicatorId": {"type": "integer", "description": "The unique identifier for the indicator."}}, "required": ["indicatorId"]}, "description": "An array of node objects, each representing an indicator and its associated view."}}, "required": ["nodes"], "description": "Schema for a request body that includes an array of indicators and their associated views."}, "CompareAddToMyApps": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"nodes": {"type": "array", "items": {"type": "object", "properties": {"indicatorId": {"type": "string", "description": "The unique identifier for the indicator."}}, "required": ["indicatorId"]}, "description": "An array of nodes, each containing an indicator ID."}, "title": {"type": "string", "description": "The title for the set of compared indicators."}}, "required": ["nodes", "title"], "description": "Schema for a request body that includes an array of indicator IDs and a title for the comparison result."}}}}