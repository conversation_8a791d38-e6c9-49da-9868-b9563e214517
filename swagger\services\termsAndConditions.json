{"paths": {"/content-type/terms-and-conditions/": {"get": {"tags": ["Terms And Conditions"], "summary": "Retrieve latest terms and conditions", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "Title of the terms and conditions document."}, "tcVersion": {"type": "string", "description": "Version of the terms and conditions."}, "pageType": {"type": "string", "description": "Type of the page, indicating it's a terms and conditions page."}, "sections": {"type": "array", "items": {"type": "object", "properties": {"sectionOrder": {"type": "string", "description": "The order in which the section appears in the document."}, "sectionTitle": {"type": "string", "description": "Title of the section."}, "subsections": {"type": "array", "items": {"type": "object", "properties": {"subsectionContent": {"type": "string", "description": "Content of the subsection."}, "subsectionOrder": {"type": "string", "description": "The order in which the subsection appears within its section."}, "subsectionTitle": {"type": ["string", "null"], "description": "Title of the subsection, which can be null."}}, "required": ["subsectionContent", "subsectionOrder"], "description": "A detailed description of a subsection within a section."}}}, "required": ["sectionOrder", "sectionTitle", "subsections"], "description": "A detailed description of a section within the terms and conditions document."}}}, "required": ["title", "tcVersion", "pageType", "sections"], "description": "A terms and conditions document with detailed sections and subsections."}, "description": "An array of terms and conditions documents, each with its own structure of sections and subsections."}}}}}}}, "/content-type/terms-and-conditions/accept": {"post": {"tags": ["Terms And Conditions"], "summary": "Accept the terms and conditions for a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Terms and Conditions Accept", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TermsAndConditionsAccept"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"userId": {"type": "string", "description": "The unique identifier of the user."}, "organization": {"type": "string", "description": "The domain of the organization to which the user belongs."}, "tcVersion": {"type": "integer", "description": "The version of the Terms and Conditions that the user has accepted.", "minimum": 1}, "tcAcceptDate": {"type": "string", "description": "The date on which the user accepted the Terms and Conditions.", "format": "date"}}, "required": ["userId", "organization", "tcVersion", "tcAcceptDate"], "description": "A schema representing the request body for a user accepting the Terms and Conditions."}}}}}}}, "/content-type/terms-and-conditions/check/{userId}/{organization}/{tcVersion}": {"get": {"tags": ["Terms And Conditions"], "summary": "Retrieve accept status of terms and conditions for a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "email"}, "description": "The Email of the user"}, {"name": "organization", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The Organization of the user"}, {"name": "tcVersion", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "Version of the terms and conditions to check for"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message indicating the user's status regarding the acceptance of Terms and Conditions."}, "tcAcceptStatus": {"type": "boolean", "description": "A boolean indicating whether the user has accepted the Terms and Conditions."}, "tcVersion": {"type": "string", "description": "The version of the Terms and Conditions that is being referenced."}}, "required": ["message", "tcAcceptStatus", "tcVersion"], "oneOf": [{"properties": {"tcAcceptStatus": {"enum": [true]}, "result": {"type": "object", "properties": {"USER_ID": {"type": "string", "description": "The user's ID."}, "TC_ID": {"type": "integer", "description": "The ID of the accepted Terms and Conditions."}, "TC_ACCEPT_DT": {"type": "string", "format": "date-time", "description": "The date and time when the Terms and Conditions were accepted."}, "INSERT_USER": {"type": "string", "description": "The user who inserted the record."}, "INSERT_DT": {"type": "string", "format": "date-time", "description": "The date and time when the record was inserted."}, "USER_ORGANIZATION": {"type": "string", "description": "The organization of the user."}, "LANGUAGE": {"type": "string", "description": "The language preference of the user."}, "LAST_UPDATE_DT": {"type": "string", "format": "date-time", "description": "The last date and time when the record was updated, if applicable."}}, "required": ["USER_ID", "TC_ID", "TC_ACCEPT_DT", "INSERT_USER", "INSERT_DT", "USER_ORGANIZATION", "LANGUAGE"], "description": "Details of the user's acceptance of the Terms and Conditions."}}, "required": ["result"]}, {"properties": {"tcAcceptStatus": {"enum": [false]}}}], "description": "Schema for a response indicating a user's acceptance status of the Terms and Conditions, including detailed acceptance information if applicable."}}}}}}}, "/content-type/terms-and-conditions/update-lang-preferrence": {"post": {"tags": ["Terms And Conditions"], "summary": "", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"TermsAndConditionsAccept": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"userId": {"type": "string", "description": "The unique identifier of the user."}, "organization": {"type": "string", "description": "The domain of the organization to which the user belongs."}, "tcVersion": {"type": "integer", "description": "The version of the Terms and Conditions that the user has accepted.", "minimum": 1}, "tcAcceptDate": {"type": "string", "description": "The date on which the user accepted the Terms and Conditions.", "format": "date"}}, "required": ["userId", "organization", "tcVersion", "tcAcceptDate"], "description": "A schema representing the request body for a user accepting the Terms and Conditions."}}}}