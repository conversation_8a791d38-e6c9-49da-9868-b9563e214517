{"paths": {"/content-type/user-journey/status": {"get": {"tags": ["User Journey"], "summary": "Retrieve User Journey status and information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "boolean", "description": "Indicates the success status of the response."}, "data": {"type": "array", "description": "Contains the navigation guide items.", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "A unique identifier for the guide item."}, "title": {"type": "string", "description": "The title of the guide item."}, "description": {"type": "array", "description": "An array of strings containing the description of the guide item.", "items": {"type": "string"}}, "image": {"type": ["string", "null"], "description": "The URL to an image related to the guide item. Can be null if no image is provided."}, "sections": {"type": "array", "description": "Contains subsections related to the guide item, if any.", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "The title of the subsection."}, "description": {"type": "string", "description": "The description of the subsection."}, "image": {"type": ["string", "null"], "description": "The URL to an image related to the subsection. Can be null if no image is provided."}}, "required": ["title", "description"]}}}, "required": ["key", "title", "description", "sections"]}}}, "required": ["status", "data"]}}}}}}}, "/content-type/user-journey/attend": {"post": {"tags": ["User Journey"], "summary": "Set User Journey status as attend", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "A message indicating the result of the operation."}, "status": {"type": "boolean", "description": "Indicates the success status of the operation."}}, "required": ["message", "status"]}}}}}}}}}