const { PutObjectCommand, DeleteObjectCommand } = require("@aws-sdk/client-s3");
require("dotenv").config();
const { awsClient } = require("../../config/awsClient");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const config = require("../ai-insight-report.constants.json")[process.env.NODE_ENV || "dev"];

const AI_INSIGHT_REPORT_BUCKET = config.AI_INSIGHT_REPORT_BUCKET;
const AI_INSIGHT_REPORT_FOLDER = "ai_insight-reports";

async function uploadReport(reportId, reportBuffer) {
  const command = new PutObjectCommand({
    Bucket: AI_INSIGHT_REPORT_BUCKET,
    Key: `${AI_INSIGHT_REPORT_FOLDER}/${reportId}.pdf`,
    Body: reportBuffer,
    ContentType: "application/pdf",
  });
  try {
    await awsClient.send(command);
  } catch (error) {
    log.error(`Something went wrong when uploading report: ${error}`);
    return null;
  }
  return reportId;
}

async function deleteReport(reportId) {
  const command = new DeleteObjectCommand({
    Bucket: AI_INSIGHT_REPORT_BUCKET,
    Key: `${AI_INSIGHT_REPORT_FOLDER}/${reportId}.pdf`,
  });
  try {
    await awsClient.send(command);
  } catch (error) {
    log.error(`Something went wrong when deleting report: ${error}`);
    return null;
  }
  return reportId;
}

module.exports = {
  uploadReport,
  deleteReport,
};
