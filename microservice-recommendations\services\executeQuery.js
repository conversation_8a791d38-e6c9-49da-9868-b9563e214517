const clkdb = require('../../services/clk-database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {  getLatestRecommendationsDataQuery } = require('./getQuery.service');


async function getLatestRecommendationsData(type) {
  return new Promise((resolve, reject) => {
    getLatestRecommendationsDataQuery(type).then((results) => {
      log.debug(`>>>>> Enter microservice-recommendations.services.executeQuery.getLatestRecommendationsData`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-innovative-insights.services.executeQuery.getLatestRecommendationsData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-innovative-insights.services.executeQuery.getLatestRecommendationsData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getLatestRecommendationsData with error ${err}`);
      reject(err);
    })
  })
}

module.exports = {
  getLatestRecommendationsData
}