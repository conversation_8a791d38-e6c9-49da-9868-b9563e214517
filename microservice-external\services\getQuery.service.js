const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getEntityUserCountQuery(entityId) {
    try {
        let binds = {
            entityId: entityId
        }

        let query = `SELECT COUNT(*) AS USER_COUNT FROM IFP_FLOW_USERS_V2 A JOIN IFP_ENTITY_LOOKUP B ON A.ENTITY_ID = B.ID WHERE B.SID = :entityId`
        return { query: query, binds: binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-external.services.getQuery.getEntityUserCountQuery with error ${err} `);
        throw err;
    }

}

module.exports = { 
    getEntityUserCountQuery
 };
