const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const clkTables = require('../../config/constants.json').clickhouseTables;
const {
	getDomainOfficialStatisticsCountQuery,
	getDataClassificationCountQuery,
	getDataClassificationQuery,
	getDomainExperimentalStatisticsCountQuery,
	getDomainOfficialScreenerCountQuery,
	getViewNameByIdQuery
} = require("./getQuery.service");
const constants = require('../helper/constants.json');
let cms_mappings = require('../../config/cms_mappings.json');
const cmsClassifications = require('../../config/constants.json').cmsClassifications;

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-bayaan-product-library.services.executeQuery.service.getData`);
    const containsClView = clkTables.some(view => query.includes(view));
    const executeFunction = containsClView ? clkdb.simpleExecute : db.simpleExecute;
    const data = await executeFunction(query, binds);
    log.debug(`<<<<< Exit microservice-bayaan-product-library.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-bayaan-product-library.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getDomainOfficialStatisticsCount(
	domainId = null,
	userGroups = [],
) {
	try {
		const { query, binds } = await getDomainOfficialStatisticsCountQuery(
			domainId,
			userGroups,
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDomainOfficialStatisticsCount with error ${err}`
		);
		throw err;
	}
}

async function getDataClassificationCount(
	domainId = null,
  label = null,
	userGroups = [],
) {
	try {
		const { query, binds } = await getDataClassificationCountQuery(
			domainId,
      label,
			userGroups,
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDataClassificationCount with error ${err}`
		);
		throw err;
	}
}

async function getDataClassification(
) {
	try {
		// const { query, binds } = await getDataClassificationQuery(
		// );
		const data_security = [];
		// Extract classification names from cmsClassifications values like "0 - open"
		for (const value of Object.values(cmsClassifications)) {
			let classification = {}
			const parts = value.split('-');
			if (parts.length > 1) {
				const name = parts[1].trim();
				classification = {
					label: name.charAt(0).toUpperCase() + name.slice(1),
					name: value
				}
				data_security.push(classification);
			}
		}
		//const { query, binds } = await getDataClassificationQuery();
		return data_security;
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDataClassification with error ${err}`
		);
		throw err;
	}
}

async function getDomainExperimentalStatisticsCount(
	screenerViews = []
) {
	try {
		const { query } = await getDomainExperimentalStatisticsCountQuery(
			screenerViews
		);
		return await getData(query);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDomainOfficialStatisticsCount with error ${err}`
		);
		throw err;
	}
}

async function getDomainWiseScreenerViews(
	domainId = null,
	classificationKey = null,
	screenerConfig = {}
) {
	try {
		const screenerViews = screenerConfig[classificationKey][domainId] || [];
		return screenerViews
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDomainWiseScreenerViews with error ${err}`
		);
		throw err;
	}
}
async function getDomainWiseofficialScreenerViews(
	domainId = null,
	classificationKey = null
) {
	try {
		const screenerViews = constants.screenerViews[classificationKey][domainId] || [];
		return screenerViews
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDomainWiseofficialScreenerViews with error ${err}`
		);
		throw err;
	}
}

async function getDomainOfficialScreenerCount(
	screenerViews = [],
	domainName = null
) {
	try {
		const { query , binds } = await getDomainOfficialScreenerCountQuery(
			screenerViews,
			domainName
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getDomainOfficialStatisticsCount with error ${err}`
		);
		throw err;
	}
}

async function getViewNameById(id) {
  let { query, binds } = await getViewNameByIdQuery(id);
  let data = await clkdb.simpleExecute(query, binds);
  if (data.length) return data[0].VIEWNAME;
  else
    throw new IFPError(
      400,
      `Invalid source. Not valid source available with this id: ${id}`
    );
}

async function getScreenerViewStructure(screenerConfig = []) {
  try {
    let screenerViews = {};
    let views = [];
	for (const screener of screenerConfig) {
	  if (screener.key !== "experimental_statistics") continue;
      if (Array.isArray(screener.indicatorList)) {
        await Promise.all(
          screener.indicatorList.map(async (indicator) => {
            let configKey = null;
            if (
              indicator.subDomainDetails &&
              indicator.subDomainDetails.show_screener
            ) {
              const domainIdCMSMap =
                cms_mappings.domainIDCMSMap[process.env.NODE_ENV] || {};
              const domainId = indicator.domainDetails.id;
              let domain = null;

              for (const [key, value] of Object.entries(domainIdCMSMap)) {
                if (value === Number(domainId)) {
                  domain = key;
                  break;
                }
              }
              if (domain) {
                if (indicator.subDomainDetails.screener_configuration) {
                  configKey =
                    indicator.subDomainDetails.screener_configuration
                      .screenerView;
                  const viewName = await getViewNameById(configKey);
                  if (!screenerViews[screener.key]) {
                    screenerViews[screener.key] = {};
                  }
                  if (!screenerViews[screener.key][domain]) {
                    screenerViews[screener.key][domain] = [];
                  }
                  screenerViews[screener.key][domain].push(`${viewName}`);
                  views.push(`${configKey}:${viewName}`);
                }
              }
            }
          })
        );
      }
    }
    return screenerViews;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-bayaan-product-library.executeQuery.service.getScreenerViewStructure with error ${err}`
    );
    throw err;
  }
}

module.exports = {
	getData,
	getDomainOfficialStatisticsCount,
  	getDataClassificationCount,
	getDataClassification,
	getDomainExperimentalStatisticsCount,
	getDomainWiseScreenerViews,
	getDomainWiseofficialScreenerViews,
	getDomainOfficialScreenerCount,
	getScreenerViewStructure,
	getViewNameById
};
