const Logger = require('scad-library').logger;
require('dotenv').config();
const fs = require('fs');
const moment = require('moment');
const constants = require('../config/constants.json');
const { getGraphData, getComboId, getAccuracyMetricsData } = require('./services/getGraphData.service');
const { getValuesData, } = require('./services/getValuesData.service');
const { getNewValuesData, isNewValuesMeta } = require('./services/getNewValuesData.service');
const { processLineChartData } = require('./services/chart-services/line-chart');
const { getMetaFromCMS, getIndicatorValuesIcon, getColors } = require('../services/common-service');
const { getTreeSeries } = require('./services/chart-services/tree-map-with-change-chart');
const { getSunBurstSeries } = require('./services/chart-services/sunburst-with-change-chart');
const { getDashboardData } = require('./services/getDashboard.service');


const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service')

class AnalyticalAppsData {
  constructor() {
    this.cmsResponse = {};
    this.reqBody = [];
    this.log = {};
    this.graphData = [];
    this.lang = '';
  }


  getAnalyticalAppsById = (req) => {
    this.log = this.log = new Logger().getInstance();
    this.contentId = req.params.id;
    this.reqBody = req.body;
    const counter = [];
    return new Promise(async (resolve, reject) => {
      try {
        if (this.contentId === 'GDP01') {
          fs.readFile(`./microservice-analytical-apps/GDP01-${req.headers["accept-language"]}.json`, (err, staticData) => {
            if (err) reject(err);
            resolve(staticData);
          })
        } else {
          this.lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
          const cmsAnalyticalAppsByIdUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsGroupUrl.CMS_ANALYTICAL_APPS_BYID}${this.contentId}`;
          const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
          this.cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsAnalyticalAppsByIdUrl, req.user.groups);
          let isWhatIf = false
        
          if (this.cmsResponse.type == 'What If'){
            this.cmsResponse.type = 'Internal'
            isWhatIf = true
          }

          let indDriver = Object.keys(this.reqBody.indicatorDrivers).length?this.reqBody.indicatorDrivers:{}
          let cmsCacheKey = 'cmsMetaAnalytical_'+ crypto.createHash('md5').update(JSON.stringify(this.cmsResponse)+JSON.stringify(indDriver)).digest("hex")
          const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
          if (cmsCacheResults){
              this.log.info(`<<<<<Cache found for microservice-analytical-apps.getAnalyticalAppsById`);
              return resolve(JSON.parse(cmsCacheResults));
          }

          if (['eci-insights','basket-insights'].includes(this.cmsResponse.type.toLowerCase())){
            return resolve(this.cmsResponse)
          }

          if ( this.cmsResponse.indicatorValues &&  this.cmsResponse.indicatorValues.overviewValuesMeta &&  this.cmsResponse.indicatorValues.overviewValuesMeta.length) {
            if (this.cmsResponse.type.toLowerCase() == 'internal'){
                let newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.overviewValuesMeta);
                if (!newValuesMeta) {
                  // this.cmsRespons.indicatorValues.overviewValuesMeta = await getIndicatorValuesIcon(req,this.cmsRespons.indicatorValues.overviewValuesMeta, req.user.groups);
                  try {
                    await getValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, {}, {});
                  } catch (err) {
                    reject(err);
                  }
                } else {
                  await getNewValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, req.user.groups,{});
                }
            }
            else if (this.cmsResponse.type.toLowerCase() == 'tableau-internal'){
              let newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.overviewValuesMeta);
              if (!newValuesMeta) {
                // this.cmsRespons.indicatorValues.overviewValuesMeta = await getIndicatorValuesIcon(req,this.cmsRespons.indicatorValues.overviewValuesMeta, req.user.groups);
                try {
                  await getValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, {}, {});
                } catch (err) {
                  reject(err);
                }

              } else {
                await getNewValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, req.user.groups,{});
                try{
                  this.cmsResponse.indicatorValues.overviewValuesMeta.forEach(ov=>{
                    if (ov.id == 'latest-date-value'){
                      this.cmsResponse.publication_date=moment(ov.dateStart, 'YYYY-MM-DD').format('DD/MM/YYYY');
                      this.cmsResponse.updatedDateFromDB = true
                    }
                  })
                }
                catch(err){
                  reject(err)
                }
              }
          }
            else{
              try {
                await getValuesData( this.cmsResponse.indicatorValues.overviewValuesMeta, {}, {});
              } catch (err) {
                reject(err);
              }

            }

        }

          if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === 'internal') || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === 'internal')) {
            
            if (Object.keys(this.reqBody.indicatorDrivers).length){
              let driverKeys = Object.keys(this.reqBody.indicatorDrivers)
              this.cmsResponse.indicatorDrivers.forEach(indicatorDriver =>{
                driverKeys.forEach(driverKey => {
                  if (indicatorDriver.id == driverKey){
                    let options = indicatorDriver.options
                    options.forEach(option => {
                      if (option.value == this.reqBody.indicatorDrivers[driverKey]){
                        option.isSelected = true;
                      }
                      else{
                        option.isSelected = false;
                      }
                    })
                  }
                })
              })
            }
            
            let newValuesMeta;
            if (req.query.view == "homepage" && this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.overviewValuesMeta){
              newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.overviewValuesMeta);
              if (newValuesMeta) {
                await getNewValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, req.user.groups, this.reqBody.indicatorDrivers);
              }
              this.cmsResponse.indicatorValues.valuesMeta = []
            }

            if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0) {
              newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.valuesMeta);
              if (newValuesMeta) {
                await getNewValuesData(this.cmsResponse.indicatorValues.valuesMeta, req.user.groups, this.reqBody.indicatorDrivers);
              }
            }           

            if (this.cmsResponse.indicatorVisualizations && this.cmsResponse.indicatorVisualizations.visualizationsMeta) {
              const indicatorVisualizationsMeta = this.cmsResponse.indicatorVisualizations.visualizationsMeta;
              let visualizationLen = this.cmsResponse.indicatorVisualizations.visualizationsMeta.length;
              this.graphData = this.cmsResponse;
              this.graphData.indicatorVisualizations.visualizationsMeta = [];
              let chartType = '';

              if (visualizationLen > 0) {
                indicatorVisualizationsMeta.forEach(async (visualization,vizIndex) => {
                  
                  if (this.cmsResponse.minLimitYAxis) {
                    visualization["minLimitYAxis"] = this.cmsResponse.minLimitYAxis;
                    delete this.cmsResponse.minLimitYAxis;
                  }
                  let comboId;
                  if (this.reqBody.indicatorDrivers) {
                    if (!Object.keys(this.reqBody.indicatorDrivers).length > 0) {
                      if (!visualization.isScadProjection && !visualization.hasDefault) {
                        this.reqBody.indicatorDrivers = constants.indicatorDrivers;
                      } else if (visualization.hasDefault) {
                        this.reqBody.indicatorDrivers = constants.quarterlyIndicatorDrivers;
                      } else {
                        this.reqBody.indicatorDrivers = constants.indicatorDriversPopulation;
                      }
                    }
                    comboId = await getComboId(visualization, this.reqBody.indicatorDrivers);
                    let results;
                    try {
                      results = await getGraphData(comboId, visualization);
                    } catch (err) {
                      
                      reject(err);
                    }

                    if (visualization.type === "dual-line-bar-chart" || visualization.type === "sunburst-with-line-chart") {
                      chartType = visualization.subtype;
                    } else {
                      chartType = visualization.type;
                    }
                    switch (chartType) {
                      case "line-chart": {
                        let finalResults, scadEstimate;
                        if (visualization.isScadProjection && comboId !== 'P0000') {
                          scadEstimate = results.filter(e => e.PARAMETER_COMBO_ID === 'P0000');
                        }
                        if (scadEstimate) {
                          finalResults = results.filter(e => !scadEstimate.includes(e));
                        }
                        results = finalResults ? finalResults : results;
                        try {
                          const data = await processLineChartData(results, visualization, this.cmsResponse.maxPointLimit);
                          
                          if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                            await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, scadEstimate);
                          }

                          if (this.cmsResponse.multiDrivers){
                            try{
                              if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.multiValuesMeta && this.cmsResponse.indicatorValues.multiValuesMeta.length > 0) {
                                await getValuesData(this.cmsResponse.indicatorValues.multiValuesMeta[vizIndex], data, scadEstimate)
                              }
                            }
                            catch(exp){
                              this.log.error(`Error while processing multivalue meta ${exp}`);
                            }
                          }

                          this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                          counter.push(1);
                        } catch (err) {
                          
                          reject(err);
                        }
                        break;
                      }
                      case "tree-map-with-change-chart": {
                        try {
                          const data = await getTreeSeries(visualization, results);
                          if (this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                            await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data);
                          }
                          if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
                            await getColors(data);
                          }
                          this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                          counter.push(1);
                        } catch (err) {
                          reject(err);
                        }
                        break;
                      }
                      case "sunburst-with-change-chart": {
                        try {
                          const data = await getSunBurstSeries(visualization, results);
                          if (this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                            await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data);
                          }
                          if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
                            await getColors(data);
                          }
                          this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                          counter.push(1);
                        } catch (err) {
                          reject(err);
                        }
                        break;
                      }
                      default: {
                        this.log.debug(`Chart type not available`);
                        break;
                      }
                    }
                    if (counter.length === visualizationLen) {
                      this.graphData.indicatorVisualizations.visualizationsMeta = this.graphData.indicatorVisualizations.visualizationsMeta.sort((a, b) => a.sortOrder - b.sortOrder);
                      if (isWhatIf){
                        this.cmsResponse.type == 'What If'
                      }
                      try{
                        if (this.graphData.indicatorVisualizations && this.graphData.indicatorVisualizations.visualizationsMeta && this.graphData.indicatorVisualizations.visualizationsMeta.length){
                          this.graphData.indicatorVisualizations.visualizationsMeta.forEach(meta=>{
                            if (meta.type == 'tree-map-with-change-chart'){
                              this.graphData.publication_date = moment(meta.seriesMeta[0].data[0].OBS_DT, 'YYYY-MM-DD').format('DD/MM/YYYY');
                              this.graphData.updatedDateFromDB = true
                            }
                            else if (meta.type=='line-chart'){
                              this.graphData.publication_date = moment(meta.seriesMeta[0].xMax, 'YYYY-MM-DD').format('DD/MM/YYYY');
                              this.graphData.updatedDateFromDB = true
                            }
                            else
                              throw Error('No Check')
                          })
                        }
                      }
                      catch(exp){
                        this.log.error(`Error while setting updated date ${exp}`)
                      }
                      this.graphData.indicatorVisualizations.visualizationsMeta.forEach(meta=>{
                        // delete meta.viewName
                        delete meta.comboIdTable
                      })
                      setRedis(cmsCacheKey, JSON.stringify(this.graphData),constants.redis.cmsResponseTTL,req.headers);
                      return resolve(this.graphData);
                    }
                  } else {
                    reject([422, { message: 'Invalid Request Body' }]);
                  }
                });
              }
            } else {
              this.log.error(`Invalid input json indicatorVisualization or visualizationsMeta unavailable`);
              reject([422, { message: `Invalid input json. indicatorVisualization or visualizationsMeta unavailable` }])
            }
          }
          else if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === 'external') || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === 'external')) {
            setRedis(cmsCacheKey, JSON.stringify(this.cmsResponse),constants.redis.cmsResponseTTL,req.headers);
            return resolve(this.cmsResponse);
          }
          else if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === 'tableau-internal') || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === 'external')) {
            setRedis(cmsCacheKey, JSON.stringify(this.cmsResponse),constants.redis.cmsResponseTTL,req.headers);
            return resolve(this.cmsResponse);
          }
          else if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === 'geospatial-dashboard') || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === 'geospatial-dashboard')) {
            setRedis(cmsCacheKey, JSON.stringify(this.cmsResponse),constants.redis.cmsResponseTTL,req.headers);
            return resolve(this.cmsResponse);
          }
          else if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === 'dashboard') || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === 'dashboard')) {
            try {
              let result = await getDashboardData(this.cmsResponse, req);
              setRedis(cmsCacheKey, JSON.stringify(result),constants.redis.cmsResponseTTL,req.headers);
              return resolve(result)
            } catch (err) {
              
              reject(err);
            }
          }
          else if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === "insights-discovery") || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === "insights-discovery")) {
            this.cmsResponse.related_items = this.cmsResponse.related_items.replace(" ","")
            this.cmsResponse.ifp_indicators = this.cmsResponse.related_items.split(',')
            delete this.cmsResponse.related_items;
            let newValuesMeta;
            if (this.cmsResponse.enableDynamicPanel && this.cmsResponse.dynamicPanel.length > 0) {
              const highlightsIndex = this.cmsResponse.dynamicPanel.findIndex(e => e.id === 'highlights')
              if (highlightsIndex >= 0) {
                newValuesMeta = await isNewValuesMeta(this.cmsResponse.dynamicPanel[highlightsIndex].meta.indicatorValues.valuesMeta);
                if (!newValuesMeta) {
                  this.cmsResponse.dynamicPanel[highlightsIndex].meta.indicatorValues.valuesMeta = await getIndicatorValuesIcon(req,this.cmsResponse
                    .dynamicPanel[highlightsIndex].meta.indicatorValues.valuesMeta, req.user.groups);
                  try {
                    await getValuesData(this.cmsResponse.dynamicPanel[highlightsIndex].meta.indicatorValues.valuesMeta, {}, {});
                  } catch (err) {
                    reject(err);
                  }
                } else {
                  await getNewValuesData(this.cmsResponse.dynamicPanel[highlightsIndex].meta.indicatorValues.valuesMeta, req.user.groups);
                }
              }
            }
            if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
              this.cmsResponse.indicatorValues.valuesMeta = await getIndicatorValuesIcon(req,this.cmsResponse
                .indicatorValues.valuesMeta, req.user.groups);
              await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, {}, {});
            }
            try {
              let result = await getDashboardData(this.cmsResponse, req);
              try{
                let defaultViz = result.visualizations.find(viz=>viz.id == result.default_visualisation)
                if (defaultViz.indicatorVisualizations && defaultViz.indicatorVisualizations.visualizationsMeta && defaultViz.indicatorVisualizations.visualizationsMeta.length){
                  defaultViz.indicatorVisualizations.visualizationsMeta.forEach(meta=>{
                    if (meta.type=='line-chart'){
                      result.publication_date = moment(meta.seriesMeta[0].xMax, 'YYYY-MM-DD').format('DD/MM/YYYY');
                      result.updatedDateFromDB = true
                    }
                  })
                }
                
              }catch(exp){
                this.log.error(`Error while setting updated date ${exp}`);
              }
              setRedis(cmsCacheKey, JSON.stringify(result),constants.redis.cmsResponseTTL,req.headers);
              return resolve(result)
            } catch (err) {
              
              reject(err);
            }
          }
          else if ((this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === "geospatial") || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === "geospatial")) {
            let summaryCardEndpoints = [];
            let chartEndpoints = [];
            if (this.cmsResponse.endPointUrls && this.cmsResponse.endPointUrls.length > 0) {
              this.cmsResponse.endPointUrls = await getIndicatorValuesIcon(req,this.cmsResponse.endPointUrls, req.user.groups);
              this.cmsResponse.endPointUrls.filter(endpoint => {
                if (endpoint.type === 'SUMMARYCARD') summaryCardEndpoints.push(endpoint);
                if (endpoint.indicatorId !== '') chartEndpoints.push(endpoint);
              })
            }
            if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.length > 0) {
              await this.cmsResponse.indicatorValues.forEach(card => {
                Object.entries(card.valuesMeta).forEach(async ([column, value]) => {
                  value = await getIndicatorValuesIcon(value, req.user.groups);
                });

                let summaryCard = summaryCardEndpoints.find( meta => meta.summaryCardId == card.summary_card_id)
                card.valuesMeta.level1[0].cardDate = summaryCard ? summaryCard.cardDate : '';
              })
            }
            try {
              let result = await getDashboardData(this.cmsResponse, req);
              chartEndpoints.filter(chart => {
                result.visualizations.filter(vis => {
                  if(vis.id == chart.indicatorId) {
                    if (vis.indicatorValues.valuesMeta ) {
                      vis.indicatorValues.valuesMeta[0].value = chart.cardDate;   
                    }
                  }
                })
              })

              setRedis(cmsCacheKey, JSON.stringify(result),constants.redis.cmsResponseTTL,req.headers);
              return resolve(result)
            } catch (err) {
              reject(err);
            }

          }
        }
      } catch (err) {
        
        reject(err);
      }
    });

  }

  getAnalyticalApps = (req) => {
    return new Promise(async (resolve, reject) => {
      try {
        this.log = this.log = new Logger().getInstance();
        this.lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsAnalyticalAppsUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsGroupUrl.CMS_ANALYTICAL_APPSV2}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        this.cleanedCmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsAnalyticalAppsUrl, req.user.groups,req.baseUrl);
        let includeTypes = ['Internal','insights-discovery','What If','Tableau-Internal']
        this.cleanedCmsResponse = this.cleanedCmsResponse.filter(app => includeTypes.includes(app.type))
        this.cmsResponse = this.cleanedCmsResponse.slice(0, 10)

        let cmsCacheKey = 'cmsMetaAnalytical_'+ crypto.createHash('md5').update(JSON.stringify(this.cmsResponse)).digest("hex")
        const cmsCacheResults = await getRedis(cmsCacheKey,req.headers);
        if (cmsCacheResults){
            this.log.info(`<<<<<Cache found for microservice-analytical-apps.getAnalyticalApps`);
            return resolve(JSON.parse(cmsCacheResults));
        }

        await Promise.all(this.cmsResponse.map(async app => {

          let type = app.type
          app.showSecondLevel = true
          
          if (type.toLowerCase() == 'dashboard') {
            app.showSecondLevel = false
            if (app.indicatorValues && app.indicatorValues.overviewValuesMeta && app.indicatorValues.overviewValuesMeta.length) {
                try {
                  await getValuesData(app.indicatorValues.overviewValuesMeta, {}, {})
                    .catch(error => {
                      console.error(`Something went wrong during fetching data for app: ${app.type}: ${error}`)
                    });
                } catch (err) {
                  
                  reject(err);
                }
            }

          }
          else if (type.toLowerCase() == 'internal' || type.toLowerCase() == 'what if') {
            if (app.indicatorVisualizations.visualizationsMeta.length > 0){
              app.showSecondLevel = app.indicatorVisualizations.visualizationsMeta[0].id.includes("tree-chart")?false:true
            }
            if (app.indicatorValues && app.indicatorValues.overviewValuesMeta && app.indicatorValues.overviewValuesMeta.length) {

              let newValuesMeta = await isNewValuesMeta(app.indicatorValues.overviewValuesMeta);
              if (!newValuesMeta) {
                app.indicatorValues.overviewValuesMeta = await getIndicatorValuesIcon(req,app.indicatorValues.overviewValuesMeta, req.user.groups);
                try {
                  await getValuesData(app.indicatorValues.overviewValuesMeta, {}, {})
                    .catch(error => {
                      console.error(`Something went wrong during fetching data for app: ${app.type}: ${error}`)
                    });
                } catch (err) {
                  
                  reject(err);
                }

              } else {
                await getNewValuesData(app.indicatorValues.overviewValuesMeta, req.user.groups,{});
              }

            }
          }
          else if (type.toLowerCase() == 'external') {
            app.showSecondLevel = false
            if (app.indicatorValues && app.indicatorValues.overviewValuesMeta && app.indicatorValues.overviewValuesMeta.length) {

                try {
                  await getValuesData(app.indicatorValues.overviewValuesMeta, {}, {})
                    .catch(error => {
                      console.error(`Something went wrong during fetching data for app: ${app.type}: ${error}`)
                    });
                } catch (err) {
                  
                  reject(err);
                }
            }

          }
          else if (type.toLowerCase() == 'insights-discovery') {
            app.showSecondLevel = true
            if (app.indicatorValues && app.indicatorValues.overviewValuesMeta && app.indicatorValues.overviewValuesMeta.length) {
                try {
                  await getValuesData(app.indicatorValues.overviewValuesMeta, {}, {})
                    .catch(error => {
                      console.error(`Something went wrong during fetching data for app: ${app.type}: ${error}`)
                    });
                } catch (err) {
                  
                  reject(err);
                }
            }
          }
        }))
        let response = [{ id: "Analytical Apps", items: this.cmsResponse }]
        setRedis(cmsCacheKey, JSON.stringify(response),constants.redis.cmsResponseTTL, req.headers);
        resolve(response);
      } catch (err) {
        
        reject(err);
      }
    });

  }

  getAnalyticalAppsV2 = (req) => {
    return new Promise(async (resolve, reject) => {
      try {
        const log = new Logger().getInstance();
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsAnalyticalAppsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_ANALYTICAL_APPSV2}`;
        const cmsNodeClassificationUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST}`;
        const cmsCategoryUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CATEGORY_LIST}`;
        const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
       
        let cleanedCmsResponse;
        let nodeMap;
        let categoryData;
        let domains;
        [cleanedCmsResponse,nodeMap,categoryData,domains] = await Promise.all([
          getMetaFromCMS(req,cmsLoginUrl, cmsAnalyticalAppsUrl, req.user.groups,req.baseUrl),
          getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
          getMetaFromCMS(req,cmsLoginUrl, cmsCategoryUrl, req.user.groups),
          getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups)
        ])
       
        let includeTypes = ['Geospatial','Dashboard']
        let cmsResponse = cleanedCmsResponse.filter(app => ! includeTypes.includes(app.type))

        let cmsCacheKey = 'cmsMetaAnalytical_'+ crypto.createHash('md5').update(JSON.stringify(this.cmsResponse)).digest("hex")
        const cmsCacheResults = await getRedis(cmsCacheKey,req.headers);
        if (cmsCacheResults){
            log.info(`<<<<<Cache found for microservice-analytical-apps.getAnalyticalApps`);
            return resolve(JSON.parse(cmsCacheResults));
        }

        let domainMap = {}
        domains.forEach(domain =>{
          domainMap[domain.name] = {
            ...domain,
            light_icon: `${process.env.CMS_BASEPATH_URL}${domain.light_icon}`,
            dark_icon: `${process.env.CMS_BASEPATH_URL}${domain.dark_icon}`,            
            items:[]
          }
        })

        let categoryMap = {}
        Object.values(categoryData.categories).map((category) => {
          categoryMap[category.name]={
              id: category.id,
              name: category.name,
              light_icon: `${process.env.CMS_BASEPATH_URL}${category.light_icon_path}`,
              dark_icon: `${process.env.CMS_BASEPATH_URL}${category.icon_path}`,              
              node_count: category.nodes_count,
              isSelected: false
          }
        })

        let categoryInfo = {}

        let nodeIds = Object.keys(nodeMap)
        cmsResponse.forEach(node =>{
          if (nodeIds.includes(node.id)){
            let nodeInfo = nodeMap[node.id]
            if (nodeInfo.content_classification && nodeInfo.content_classification.length && nodeInfo.domain && nodeInfo.category){
              let domain =  nodeInfo.domain
              let category = nodeInfo.category
              if (!Object.keys(categoryInfo).includes(category))
                categoryInfo[category] = {}
              if (!Object.keys(categoryInfo[category]).includes(domain))
                categoryInfo[category][domain] = JSON.parse(JSON.stringify(domainMap[domain]))

              let cleanedNode = {
                id:  node.id,
                type: node.type,
                content_type: 'analytical-apps',
                title: node.component_title,
                subTitle: node.component_subtitle,
                domain:domain,
                category: category
              }
              categoryInfo[category][domain].items.push(cleanedNode)
            }
          }
        })

        let response = Object.entries(categoryInfo).map(([category,domains]) =>{
          const categoryObj = {
            ...categoryMap[category],
            domains: Object.entries(domains).map(([domain,data]) =>{
              const domainObj = {
                ...data
              }
              return domainObj
            })
          }
          return categoryObj
        })
        
        
        setRedis(cmsCacheKey, JSON.stringify(response),constants.redis.cmsResponseTTL, req.headers);
        resolve(response);
      } catch (err) {
        
        reject(err);
      }
    });

  }

  getAnalyticalAppsV3 = (req) => {
    return new Promise(async (resolve, reject) => {
      try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;

        const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
        const cmsClassificationsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN}0`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        let domains;
        let classificationsData;
        [classificationsData,domains] = await Promise.all([
          getMetaFromCMS(req,cmsLoginUrl, cmsClassificationsUrl, req.user.groups),
          getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups)
        ])

        let analyticalAppClassification = classificationsData.classification.find(classification=> classification.key == 'analytical_apps')

        let excludeTypes = [
          'livability'
        ]

        let categoryMap = {}

        await Promise.all(
          domains.map(async domain =>{
            const queryParams = [`classification_id=${analyticalAppClassification.id}`]   
            const page = 1, limit = 20
            queryParams.push(`page=${page}`);
            queryParams.push(`items_per_page=${limit}`);
            const queryString = queryParams.length ? `?${queryParams.join('&')}` : '';

            const cmsClassificationsByDomainsFilterUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS}${domain.id}?classification_id=${analyticalAppClassification.id}`;
            const cmsDomainNodesUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAIN_NODES_LIST}${domain.id}${queryString}`;
            let [domainNodes,categoryData] = await Promise.all([
              getMetaFromCMS(req,cmsLoginUrl, cmsDomainNodesUrl, req.user.groups),
              getMetaFromCMS(req,cmsLoginUrl, cmsClassificationsByDomainsFilterUrl, req.user.groups)
            ])

            Object.values(categoryData.category).forEach((category) => {
              if (!(category.title in categoryMap)){
                categoryMap[category.title]={
                    id: category.id,
                    name: category.title,
                    node_count: 0,
                    isSelected: false,
                    domains:{}
                }
              }
            })

            if ('results' in domainNodes){
              domainNodes.results.forEach(node=>{
                if (!excludeTypes.includes(node.app_type)){
                  if (node.category.name in categoryMap){
                    let categoryNodeData = categoryMap[node.category.name]
                    if (!(domain.name in categoryNodeData.domains))
                      categoryNodeData.domains[domain.name] = {
                        ...domain,
                        items:[]
                      }
                      categoryNodeData.node_count += 1
                    // temporary filter out eci node from mobile as per their request
                    if (req.baseUrl.includes("mobile") || req.originalUrl.includes("mobile")) {
                      const ECI_NODE_ID =
                        process.env.NODE_ENV == "prod" ? "6779" : "6821";
                      if (node.id != ECI_NODE_ID) {
                        categoryNodeData.domains[domain.name].items.push(node);
                      }
                    } else {
                      categoryNodeData.domains[domain.name].items.push(node);
                    }
                  }
                }
              })
            }
          })
        )

        let results = Object.values(categoryMap).map(category =>{
          const categoryObj = category
          categoryObj.domains = Object.values(categoryObj.domains)
          categoryObj.domains = categoryObj.domains.filter(domain=>domain.items.length)
          return categoryObj
        })     
       
        resolve(results);
      } catch (err) {
        reject(err);
      }
    });
  }
}



module.exports = AnalyticalAppsData;
