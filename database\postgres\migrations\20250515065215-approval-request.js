'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create approval request table
    await queryInterface.createTable('bayaan_approval_requests', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      object_id: {
        type: Sequelize.UUID,
        allowNull: false
      },
      object_type: {
        type: Sequelize.STRING(50),
        allowNull: false
      },
      requestor_id: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      assignee_id: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'pending'
      },
      last_action_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      approver_id: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });
    
    // Add indexes for better performance
    await queryInterface.addIndex('bayaan_approval_requests', ['object_id', 'object_type']);
    await queryInterface.addIndex('bayaan_approval_requests', ['requestor_id']);
    await queryInterface.addIndex('bayaan_approval_requests', ['assignee_id']);
    await queryInterface.addIndex('bayaan_approval_requests', ['status']);
  },
  
  async down(queryInterface, Sequelize) {
    // Drop the approval request table
    await queryInterface.dropTable('bayaan_approval_requests');
  }
};