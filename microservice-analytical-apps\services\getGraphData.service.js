const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getQuery, getComboIdQuery, getValuesQuery, getValuesDataFromDBQuery, getAccuracyMetricsDataQuery } = require('./getQuery.service');
const constants = require('../../config/constants.json');


const clkTables = constants.clickhouseTables

/**
 * Function to get data from db
 * @param {*} id - content id 
 * @param {*} visualization - metadata 
 */
async function getGraphData(comboId, visualization) {
  return new Promise((resolve, reject) => {
    try {
      getQuery(visualization, comboId).then((query) => {
        getData(query).then(async (data) => {
          resolve(data);
        }).catch((err) => {
          
          reject(err);
        })
      }).catch((err) => {
        
        reject(err);
      })
    } catch (err) {
      
      reject(err);
    }
  })
}

/**
 * Function to get parameter_combo_id from db based on values provided in request body indicatorDrivers
 * @param {*} indicatorDrivers - request body object 
 * @param {*} visualization - metadata 
 */
async function getComboId(visualization, indicatorDrivers) {
  return new Promise((resolve, reject) => {
    getComboIdQuery(indicatorDrivers, visualization.comboIdTable).then((comboIdQuery) => {
      getData(comboIdQuery).then((comboId) => {
        let id = comboId[0].PARAMETER_COMBO_ID;
        resolve(id);
      }).catch(err => {
        
        log.error(`<<<<< Exited microservice-analytical-apps.getGraphData.service.getComboId with error ${err}`)
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  })
}

/**
 * Function to get tree chart data
 * @param {*} indicatorDrivers - request body object 
 * @param {*} visualization - metadata 
 */

async function getData(query) {

  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-analytical-apps.services.getGraphData.service.getData`);
    const containsClView = clkTables.some(view => query.includes(view));
    if (containsClView)
      clkdb.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-analytical-apps.services.getGraphData.service.getData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-analytical-apps.services.getGraphData.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    else
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-analytical-apps.services.getGraphData.service.getData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-analytical-apps.services.getGraphData.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
  });
}

async function getValuesDataFromDB(value) {
  return new Promise((resolve, reject) => {
    getValuesDataFromDBQuery(value).then(query => {
      getData(query).then(data => {
        resolve(data);
      }).catch(err => {
        
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  })
}

async function getAccuracyMetricsData(accuracyMetricsMeta, contentId){
  return new Promise((resolve, reject) => {
    getAccuracyMetricsDataQuery(accuracyMetricsMeta, contentId).then(query => {
      getData(query).then(data => {
        resolve(data);
      }).catch(err => {
        
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  });
}

async function getValuesDataFromQuery(valuesMeta, comboId) {
  return new Promise((resolve, reject) => {
    getValuesQuery(valuesMeta, comboId).then(query => {
      getData(query).then(data => {
        resolve(data);
      }).catch(err => {
        
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  });
}
module.exports = { getGraphData, getComboId, getData, getValuesDataFromDB, getAccuracyMetricsData, getValuesDataFromQuery }