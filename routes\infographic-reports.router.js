const express = require('express');
const router = new express.Router();
const infographicReportsController = require('../microservice-infographic-reports/infographic-reports.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/:id', async (req, res, next) => {
    try {
      const data = await infographicReportsController.getInfographicReportsById(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for infographic reports content-type, ERROR: ${err}`);
      next(err);
    }
  });

router.get('/', async (req, res, next) => {
  try {
    const data = await infographicReportsController.getInfographicReports(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for infographic reports content-type, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
