const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const {
  listMyAppsDataQuery,
  addAppsToMyAppsDataQuery,
  removeAppsFromMyAppsDataQuery,
  removeCompareAppsFromMyAppsDataQuery,
  removeCompareAppsFromSourceDataQuery,
  draftMyAppsDataQuery,
  updateDraftStatusMyAppsDataQuery,
  idListMyAppsDataQuery,
  getDraftMyAppsDataQuery,
  createDraftMyAppsDataQuery,
  getDraftNodesMyAppsDataQuery,
  deleteDraftMyAppsDataQuery,
  deleteDraftMyAppsNodesDataQuery,
  removeAppsFromDraftMyAppsDataQuery,
  deleteDraftMasterMyAppsDataQuery,
  dragMyAppsDataQuery,
  deleteDragMyAppsDataQuery,
  deleteDraftNodesMyAppsDataQuery,
  deleteDragByNodesMyAppsDataQuery,
  storeDragMyAppsDataQuery,
  getHierarchyDataQuery,
  createDashboardDataQuery,
  deleteDashboardDataQuery,
  detailDashboardDataQuery,
  myAppsFilterDataQuery,
  getDashboardsDataQuery,
  removeDashboardNodesDataQuery,
  createShareAppQuery,
  dragShareMyAppsDataQuery,
  storeDragShareMyAppsDataQuery,
  createShareNodesQuery,
  getShareAppQuery,
  getShareAppsListQuery,
  createShareNotificationsQuery,
  createShareMyAppsRequestQuery,
  getShareMyAppsRequestQuery,
  deleteShareQuery,
  deleteShareDragQuery,
  deleteShareNodesQuery,
  readShareAppsQuery
 } = require('./getQuery.service');


/**
 * Function to get my apps
 * @param {*} userEmail - user email 
 */
async function listMyAppsData(userEmail) {
  try {
    const query = await listMyAppsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.listMyAppsData with error ${err}`);
    throw err;
  }
}


/**
 * Function to retrieve list of apps
 * @param {*} id - node id
 * @param {*} userEmail - user email 
 */
async function idListMyAppsData(userEmail) {
  try {
    const query = await idListMyAppsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.idListMyAppsData with error ${err}`);
    throw err;
  }
}

async function addAppsToMyAppsData(nodes, userEmail) {
  try {
    const {query,binds} = await addAppsToMyAppsDataQuery(nodes, userEmail);
    const data = await getData(query,binds  );
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.addAppsToMyAppsData with error ${err}`);
    throw err;
  }
}

async function removeAppsFromMyAppsData(nodes, userEmail) {
  try {
    const {query,binds} = await removeAppsFromMyAppsDataQuery(nodes, userEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeAppsFromMyAppsData with error ${err}`);
    throw err;
  }
}

async function removeCompareAppsFromMyAppsData(nodes, userEmail) {
  try {
    const query = await removeCompareAppsFromMyAppsDataQuery(nodes, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeAppsFromMyAppsData with error ${err}`);
    throw err;
  }
}

async function removeCompareAppsFromSourceData(nodes, userEmail) {
  try {
    const query = await removeCompareAppsFromSourceDataQuery(nodes, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeCompareAppsFromSourceData with error ${err}`);
    throw err;
  }
}

async function draftMyAppsData(nodes, userEmail, draftId, type) {
  try {
    const {query,binds} = await draftMyAppsDataQuery(nodes, userEmail, draftId, type);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.draftAppsFromMyAppsData with error ${err}`);
    throw err;
  }
}

async function updateDraftStatusMyAppsData(userEmail) {
  try {
    const query = await updateDraftStatusMyAppsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.updateDraftStatusMyAppsData with error ${err}`);
    throw err;
  }
}

async function getDraftMyAppsData(userEmail) {
  try {
    const query = await getDraftMyAppsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function getDraftNodesMyAppsData(id, userEmail) {
  try {
    const query = await getDraftNodesMyAppsDataQuery(id, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function createDraftMyAppsData(draftId, userEmail) {
  try {
    const query = await createDraftMyAppsDataQuery(draftId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.createDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteDraftMyAppsData(draftId, userEmail) {
  try {
    const query = await deleteDraftMyAppsDataQuery(draftId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.createDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteDraftMyAppsNodesData(draftId, userEmail) {
  try {
    const query = await deleteDraftMyAppsNodesDataQuery(draftId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteDraftMyAppsNodesData with error ${err}`);
    throw err;
  }
}

async function removeAppsFromDraftMyAppsData(nodes, draftId, userEmail) {
  try {
    const query = await removeAppsFromDraftMyAppsDataQuery(nodes, draftId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeAppsFromDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteDraftMasterMyAppsData(draftId, userEmail) {
  try {
    const query = await deleteDraftMasterMyAppsDataQuery(draftId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeAppsFromDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteDraftNodesMyAppsData(draftId, userEmail) {
  try {
    const query = await deleteDraftNodesMyAppsDataQuery(draftId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeAppsFromDraftMyAppsData with error ${err}`);
    throw err;
  }
}

async function listCompareAppsData(email) {
  try {
    log.debug(`>>>>> Enter microservice-indicator-compare.services.executeQuery.listCompareAppsData`);
    const query = `SELECT DISTINCT ID AS ID, TITLE AS TITLE, TO_CHAR(TO_TIMESTAMP(INSERT_DT, 'DD/MM/RR HH:MI:SS.FF AM'), 'DD-MM-YYYY') AS PUBLISH_DATE, INSERT_DT FROM IFP_COMPARE_NODES WHERE USER_EMAIL='${email}' ORDER BY INSERT_DT DESC`;
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-indicator-compare.services.executeQuery.listCompareAppsData with error ${err}`);
    throw err;
  }
}

async function dragMyAppsData(userEmail) {
  try {
    const query = await dragMyAppsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.dragMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteDragMyAppsData(userEmail) {
  try {
    const query = await deleteDragMyAppsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteDragMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteDragByNodesMyAppsData(nodes, userEmail) {
  try {
    const query = await deleteDragByNodesMyAppsDataQuery(nodes, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteDragMyAppsData with error ${err}`);
    throw err;
  }
}

async function storeDragMyAppsData(stateData) {
  try {
    const query = await storeDragMyAppsDataQuery(stateData);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.storeDragMyAppsData with error ${err}`);
    throw err;
  }
}

async function getHierarchyData(nodes, language) {
  try {
    const query = await getHierarchyDataQuery(nodes, language);
    const data = await getClickhouseData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getHierarchyData with error ${err}`);
    throw err;
  }
}

async function createDashboardData(dashboardData) {
  try {
    const query = await createDashboardDataQuery(dashboardData);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.createDashboardData with error ${err}`);
    throw err;
  }
}


async function deleteDashboardData(dashboardId) {
  try {
    const query = await deleteDashboardDataQuery(dashboardId);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteDashboardData with error ${err}`);
    throw err;
  }
}

async function detailDashboardData(dashboardId, userEmail) {
  try {
    const query = await detailDashboardDataQuery(dashboardId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.detailDashboardData with error ${err}`);
    throw err;
  }
}

async function myAppsFilterData(dashboardId, userEmail) {
  try {
    const query = await myAppsFilterDataQuery(dashboardId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.myAppsFilterData with error ${err}`);
    throw err;
  }
}

async function getDashboardsData(userEmail) {
  try {
    const query = await getDashboardsDataQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getDashboardsData with error ${err}`);
    throw err;
  }
}

async function removeDashboardNodesData(nodes, dashboardId, userEmail) {
  try {
    const query = await removeDashboardNodesDataQuery(nodes, dashboardId, userEmail);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeDashboardNodesData with error ${err}`);
    throw err;
  }
}

async function createShareApp(shareApp) {
  try {
    const results = await createShareAppQuery(shareApp);
    const data = await getData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeDashboardNodesData with error ${err}`);
    throw err;
  }
}

async function getShareAppsListData(type,userEmail,page,limit){
  try{
    let results = await getShareAppsListQuery(type,userEmail,page,limit);
    let data = await getData(results.query,results.binds);
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getShareAppsListData with error ${err}`);
    throw err;
  }
}

async function readShareAppsData(id,userEmail){
  try{
    let results = await readShareAppsQuery(id, userEmail);
    let data = await getData(results.query,results.binds);
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.readShareAppsData with error ${err}`);
    throw err;
  }
}


async function getShareApp(shareId, userEmail,requestor) {
  try {
    const results = await getShareAppQuery(shareId, userEmail, requestor);
    const data = await getData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeDashboardNodesData with error ${err}`);
    throw err;
  }
}

async function createShareAppNodes(shareNodes) {
  try {
    const query = await createShareNodesQuery(shareNodes);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.removeDashboardNodesData with error ${err}`);
    throw err;
  }
}

async function createShareDragNodes(shareDragData) {
  try {
    const query = await storeDragShareMyAppsDataQuery(shareDragData);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.storeDragShareMyAppsData with error ${err}`);
    throw err;
  }
}

async function dragShareMyAppsData(shareId) {
  try {
    const query = await dragShareMyAppsDataQuery(shareId);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.dragMyAppsData with error ${err}`);
    throw err;
  }
}

async function deleteShareData(shareId,requestor,userEmail) {
  try {
    const {query,binds} = await deleteShareQuery(shareId,requestor,userEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteShareData with error ${err}`);
    throw err;
  }
}

async function deleteShareDragData(shareId,requestor,userEmail) {
  try {
    const {query,binds} = await deleteShareDragQuery(shareId,requestor,userEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteShareDragData with error ${err}`);
    throw err;
  }
}

async function deleteShareNodesData(shareId,requestor,userEmail) {
  try {
    const {query,binds} = await deleteShareNodesQuery(shareId,requestor,userEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteShareNodesData with error ${err}`);
    throw err;
  }
}

async function createShareNotificationsData(notificationData){
  try {
    const {query,binds} = await createShareNotificationsQuery(notificationData);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.createShareNotificationsData with error ${err}`);
    throw err;
  }
}


async function createShareMyAppsRequestData(requestData){
  try {
    const {query,binds} = await createShareMyAppsRequestQuery(requestData);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.createShareMyAppsRequestData with error ${err}`);
    throw err;
  }
}

async function getShareMyAppsRequestData(requestorEmail){
  try {
    const {query,binds} = await getShareMyAppsRequestQuery(requestorEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getShareMyAppsRequestData with error ${err}`);
    throw err;
  }
}



async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-myapps.services.executeQuery.service.getData`);
    let data = await db.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-myapps.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-myapps.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getClickhouseData(query,binds={}){
  try {
    log.debug(`>>>>> Enter microservice-myapps.services.executeQuery.service.getData`);
    let data = await clkdb.simpleExecute(query);
    log.debug(`<<<<< Exit microservice-myapps.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-myapps.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw error;
  }
}


module.exports = {
  listMyAppsData,
  addAppsToMyAppsData,
  removeAppsFromMyAppsData,
  removeCompareAppsFromMyAppsData,
  removeCompareAppsFromSourceData,
  draftMyAppsData,
  updateDraftStatusMyAppsData,
  idListMyAppsData,
  getDraftMyAppsData,
  createDraftMyAppsData,
  getDraftNodesMyAppsData,
  deleteDraftMyAppsData,
  deleteDraftMyAppsNodesData,
  removeAppsFromDraftMyAppsData,
  deleteDraftMasterMyAppsData,
  deleteDraftNodesMyAppsData,
  listCompareAppsData,
  dragMyAppsData,
  deleteDragMyAppsData,
  storeDragMyAppsData,
  deleteDragByNodesMyAppsData,
  getHierarchyData,
  createDashboardData,
  deleteDashboardData,
  detailDashboardData,
  myAppsFilterData,
  getDashboardsData,
  removeDashboardNodesData,
  createShareApp,
  readShareAppsData,
  getShareAppsListData,
  createShareAppNodes,
  createShareDragNodes,
  dragShareMyAppsData,
  getShareApp,
  createShareNotificationsData,
  createShareMyAppsRequestData,
  getShareMyAppsRequestData,
  deleteShareData,
  deleteShareDragData,
  deleteShareNodesData
};
