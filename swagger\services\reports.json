{"paths": {"/content-type/reports/": {"get": {"tags": [" Reports"], "summary": "Retrieve list of reports available to a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"domain": {"type": "string", "description": "The domain category of the content."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the node."}, "title": {"type": "string", "description": "The title of the report or content."}, "subtitle": {"type": "string", "description": "A subtitle for the content, if available."}, "newsletter_URL": {"type": "string", "description": "The URL to access the newsletter or report."}, "policy_guide": {"type": "string", "description": "A guide or policy document associated with the content, if available."}, "search_tags": {"type": "array", "items": {"type": "string"}, "description": "Array of tags associated with the content for search optimization."}, "updated": {"type": "string", "format": "date", "description": "The date when the content was last updated."}, "imgSrc": {"type": "string", "description": "The source URL of the image associated with the content, if available."}, "domain": {"type": "string", "description": "The specific domain category of the node."}, "publication_date": {"type": "string", "format": "date", "description": "The publication date of the content."}, "content_classification": {"type": "string", "description": "The classification of the content (e.g., Reports)."}, "content_classification_key": {"type": "string", "description": "A key that represents the content classification in a standardized form."}}, "required": ["id", "title", "publication_date", "content_classification", "content_classification_key"]}, "description": "An array of node objects each representing a piece of content within the domain."}}, "required": ["domain", "nodes"]}, "description": "A response structure containing an array of domains, each with their respective content nodes."}}}}}}}, "/content-type/reports/{id}": {"get": {"tags": [" Reports"], "summary": "Retrieve detail information of a report", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Report"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the report."}, "title": {"type": "string", "description": "The title of the weekly report."}, "subtitle": {"type": "string", "description": "A subtitle for the report, if any."}, "newsletter_URL": {"type": "string", "format": "uri", "description": "The URL linking to the newsletter or the detailed report."}, "policy_guide": {"type": "string", "description": "A link or reference to a related policy guide, if applicable."}, "search_tags": {"type": "array", "items": {"type": "string"}, "description": "Search tags associated with the report for easier retrieval."}, "updated": {"type": "string", "format": "date", "description": "The date when the report was last updated."}, "imgSrc": {"type": "string", "description": "Source URL for an image associated with the report, if any."}, "domain": {"type": "string", "description": "The domain or category to which the report belongs."}, "publication_date": {"type": "string", "format": "date", "description": "The date when the report was published."}, "field_content_classification_export": {"type": "string", "description": "The classification of the content, e.g., 'Reports'."}, "field_key": {"type": "string", "description": "A key representing the content classification in a systematic way."}}, "required": ["id", "title", "newsletter_URL", "publication_date", "field_content_classification_export", "field_key"]}}}}}}}}}