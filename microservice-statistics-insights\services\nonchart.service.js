const util = require('scad-library').util;
const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log =  new Logger().getInstance();
async function nonChartData(value) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT VALUE, OBS_DT, INDICATOR_TITLE, INDICATOR_ID FROM VW_STAT_INDICATORS WHERE ${value.dbColumn} = '${value.dbIndicatorId}' AND OBS_DT = (SELECT MAX(OBS_DT) FROM VW_STAT_INDICATORS WHERE ${value.dbColumn} = '${value.dbIndicatorId}')`
            db.simpleExecute(query).then((data) => {
                value["value"] = data[0].VALUE;
                value["dateStart"] = util.convertDate(`${data[0].OBS_DT}`);
                resolve(value);
            }).catch(err => {
                
                log.error(`Exited microservices-statistics-insights.services.nonchart.service with error ${err}`);
                reject([423, err]);
            })
        } catch (err) {
            
            log.error(`Exited microservices-statistics-insights.services.nonchart.service with error ${err}`);
            reject([422, err]);
        }
    });
}

module.exports = { nonChartData }