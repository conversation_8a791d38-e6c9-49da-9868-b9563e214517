'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create comments table
    await queryInterface.createTable('bayaan_approval_request_comments', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      request_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'bayaan_approval_requests',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      user_id: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      comment_type: {
        type: Sequelize.STRING(20),
        allowNull: false,
        defaultValue: 'comment'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });
    
    // Add indexes
    await queryInterface.addIndex('bayaan_approval_request_comments', ['request_id']);
    await queryInterface.addIndex('bayaan_approval_request_comments', ['user_id']);
    await queryInterface.addIndex('bayaan_approval_request_comments', ['comment_type']);
  },
  
  async down(queryInterface, Sequelize) {
    // Drop the comments table
    await queryInterface.dropTable('bayaan_approval_request_comments');
  }
};