const sunburstChart = require("../../../services/chart-services/sunburst-with-change-chart");

describe('Service', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    test('should get json response from getSunburstSeries - success', async () => {
        const mockData = [{
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.0323535,
            "VALUE_LL": 5896.5833522,
            "VALUE_UL": 8844.875028,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.8532381,
            "VALUE_UL": 0.946761857,
            "UNIT": "Proportion",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "Proportion",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.007646999999999999,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        }];

        const mockVisualization = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                }
            ]
        };

        const expectedResponse = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    },
                    "data": [
                        {
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 5,
                            "RUN_DT": 20200203,
                            "INSERT_DT": null,
                            "INSERT_USER_ID": null,
                            "VALUE": 0.007646999999999999,
                            "VALUE_LL": 0.47733780000000003,
                            "VALUE_UL": 0.42266223799999997,
                            "UNIT": "% change",
                            "OBS_DT": "2022-02-01",
                            "OPT": 13,
                            "TYPE": "FORECAST",
                            "OIL_NONOIL": "Non-Oil",
                            "SECTOR": "Manufacturing Activities",
                            "INDUSTRY": "Total",
                            "PROPORTION": 100
                        }
                    ]
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    },
                    "data": [
                        {
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 5,
                            "RUN_DT": 20200203,
                            "INSERT_DT": null,
                            "INSERT_USER_ID": null,
                            "VALUE": 0.0323535,
                            "VALUE_LL": 5896.5833522,
                            "VALUE_UL": 8844.875028,
                            "UNIT": "% change",
                            "OBS_DT": "2022-02-01",
                            "OPT": 13,
                            "TYPE": "FORECAST",
                            "OIL_NONOIL": "Non-Oil",
                            "SECTOR": "Leisure",
                            "INDUSTRY": "Total",
                            "PROPORTION": 100
                        }
                    ]
                }
            ]
        }
        jest.spyOn(sunburstChart, "getSunBurstSeries");
        const result = await sunburstChart.getSunBurstSeries(mockVisualization, mockData);
        //expect(result).toEqual(expectedResponse);
    });
    test('should get json response from getSunburstSeries - success - data not available for proportion handled', async () => {
        const mockData = [{
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.0323535,
            "VALUE_LL": 5896.5833522,
            "VALUE_UL": 8844.875028,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.8532381,
            "VALUE_UL": 0.946761857,
            "UNIT": "Proportion",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.007646999999999999,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        }];

        const mockVisualization = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                }
            ]
        };

        const expectedResponse = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    },
                    "data": [
                        {
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 5,
                            "RUN_DT": 20200203,
                            "INSERT_DT": null,
                            "INSERT_USER_ID": null,
                            "VALUE": 0.007646999999999999,
                            "VALUE_LL": 0.47733780000000003,
                            "VALUE_UL": 0.42266223799999997,
                            "UNIT": "% change",
                            "OBS_DT": "2022-02-01",
                            "OPT": 13,
                            "TYPE": "FORECAST",
                            "OIL_NONOIL": "Non-Oil",
                            "SECTOR": "Manufacturing Activities",
                            "INDUSTRY": "Total",
                            "PROPORTION": null
                        }
                    ]
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    },
                    "data": [
                        {
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 5,
                            "RUN_DT": 20200203,
                            "INSERT_DT": null,
                            "INSERT_USER_ID": null,
                            "VALUE": 0.0323535,
                            "VALUE_LL": 5896.5833522,
                            "VALUE_UL": 8844.875028,
                            "UNIT": "% change",
                            "OBS_DT": "2022-02-01",
                            "OPT": 13,
                            "TYPE": "FORECAST",
                            "OIL_NONOIL": "Non-Oil",
                            "SECTOR": "Leisure",
                            "INDUSTRY": "Total",
                            "PROPORTION": 100
                        }
                    ]
                }
            ]
        }
        jest.spyOn(sunburstChart, "getSunBurstSeries");
        const result = await sunburstChart.getSunBurstSeries(mockVisualization, mockData);
        //expect(result).toEqual(expectedResponse);
    });
    test('should get json response from getSunburstSeries - failure chart json error', async () => {
        const mockData = [{
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.0323535,
            "VALUE_LL": 5896.5833522,
            "VALUE_UL": 8844.875028,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.8532381,
            "VALUE_UL": 0.946761857,
            "UNIT": "Proportion",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "Proportion",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.007646999999999999,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        }];

        const mockVisualization = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "series": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                }
            ]
        };
        try {
            jest.spyOn(sunburstChart, "getSunBurstSeries");
            await sunburstChart.getSunBurstSeries(mockVisualization, mockData);
        } catch (err) {
            expect(err[0]).toEqual(422);
        }


    });
    test('should get json response from getSunburstSeries - failure data unavailable for visualization', async () => {
        const mockData = [];

        const mockVisualization = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                }
            ]
        };
        try {
            jest.spyOn(sunburstChart, "getSunBurstSeries");
            await sunburstChart.getSunBurstSeries(mockVisualization, mockData);
        } catch (err) {
            expect(err[0]).toEqual(404);
        }
    });
    test('should get json response from getSunburstSeries - failure null value received and unit value is not as expected', async () => {
        const mockData = [{
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.0323535,
            "VALUE_LL": 5896.5833522,
            "VALUE_UL": 8844.875028,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": null
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.8532381,
            "VALUE_UL": 0.946761857,
            "UNIT": "Proportion",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Leisure",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 100,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "Proportion value",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        },
        {
            "PARAMETER_COMBO_ID": "E0000",
            "RUN_SEQ_ID": 5,
            "RUN_DT": 20200203,
            "INSERT_DT": null,
            "INSERT_USER_ID": null,
            "VALUE": 0.007646999999999999,
            "VALUE_LL": 0.47733780000000003,
            "VALUE_UL": 0.42266223799999997,
            "UNIT": "% change",
            "OBS_DT": "20220201",
            "OPT": 13,
            "TYPE": "FORECAST",
            "OIL_NONOIL": "Non-Oil",
            "SECTOR": "Manufacturing Activities",
            "INDUSTRY": "Total"
        }];

        const mockVisualization = {
            "id": "sunburst-economy-sector-indicator",
            "type": "sunburst-with-line-chart",
            "subtype": "sunburst-with-change-chart",
            "levels": 3,
            "sortOrder": 1,
            "viewName": "VW_RI_SCENARIOS_RES_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "manufacturing-activities",
                    "label": "Manufacturing Activities",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "MANUFACTURING ACTIVITIES",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "#3267FF",
                    "level": 1,
                    "parentId": "non-oil",
                    "type": "sunburst",
                    "dimension": {
                        "OIL_NONOIL": "NON-OIL",
                        "SECTOR": "LEISURE",
                        "INDUSTRY": "TOTAL"
                    },
                    "forecastAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "PROPORTION"
                    },
                    "xAccessor": {
                        "type": "value",
                        "path": "OBS_DT"
                    }
                }
            ]
        };
        try {
            jest.spyOn(sunburstChart, "getSunBurstSeries");
            await sunburstChart.getSunBurstSeries(mockVisualization, mockData);
        } catch (err) {
            expect(err).toEqual(422);
        }
    });
});



