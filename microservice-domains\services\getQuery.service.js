const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { IFPError } = require('../../utils/error');

async function getPopStatsDataQuery(nodeIds) {
    try {
        if (nodeIds.length<1)
            throw new IFPError(400,'No node ids found') 
        let query = `SELECT * FROM IFP_POPULAR_STATS WHERE NODE_ID IN (${nodeIds.join(',')}) ORDER BY HIT_COUNT DESC`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.listMyAppsDataQuery with error ${err} `);
        throw err;
    }
}
  
async function getScreenerCountQuery(screenerConfiguration) {
    try {
        let screenerFilters = screenerConfiguration.screenerFilterBy?screenerConfiguration.screenerFilterBy:{}
        const whereClauses = [];
        for (const filterKey in screenerFilters) {
            if (screenerFilters[filterKey].length > 0) {
                if (Array.isArray(screenerFilters[filterKey])) {
                    const values = screenerFilters[filterKey].map(value => `UPPER('${value.replace(/'/g, "''")}')`).join(', ');
                    whereClauses.push(`UPPER(${filterKey}) IN (${values})`);
                } else {
                    whereClauses.push(`UPPER(${filterKey}) = UPPER('${screenerFilters[filterKey].replace(/'/g, "''")}')`);
                }
            }
        }

        const query = `
            SELECT COUNT(DISTINCT INDICATOR_ID) AS COUNT
            FROM ${screenerConfiguration.screenerView} ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''}
        `;
        const binds = {};
        
        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getScreenerCountQuery with error ${err} `);
        throw err
    }
}

async function getSingleDimensionNodesQuery(nodes) {

    try {
        let binds = {
            nodeIds:nodes
        }
        let query = `SELECT NODE_ID FROM VW_DYNAMIC_JSON_SF WHERE JSON_KEY='isMultiDimension' and JSON_VALUE='FALSE' AND NODE_ID in {nodeIds:Array(String)}`
        return { query, binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getScreenerCountQuery with error ${err} `);
        throw err;
    }
}

async function getSingleDimensionNodesQueryV3(indicators) {

    try {
        let binds = {}
        indicators = indicators.map(n=>`'${n}'`).join(',')
        let query = `SELECT INDICATOR_ID FROM VW_DYNAMIC_JSON_SF WHERE JSON_KEY='isMultiDimension' and JSON_VALUE='FALSE' AND INDICATOR_ID in (${indicators})`
        return { query, binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getSingleDimensionNodesQueryV3 with error ${err} `);
        throw err;
    }
}

async function getNodeBasicDeatailsQuery(req){
    const user_groups = req.user.groups;
    const rawSearch = req?.query?.search;
    const search = rawSearch ? rawSearch : null;
    let binds = {
        group_ids: user_groups,
    };
    const lang = req.headers["accept-language"] === 'en' ? '' : '_ar';
    const commonColumns = `
        vdoi.indicator_id, vdoi.note, vdoi.enableCompare,
        vdoi.component_subtitle${lang} AS subtitle,
        vdoi.theme_id, vdoi.showLegend, vdoi.subtheme_id,
        vdoi.language, vdoi.showPointLabels, vdoi.yAxisFormat,
        vdoi.component_title${lang} AS title,
        vdoi.domain_id, vdoi.tooltipValueFormat, vdoi.product_id,
        vdoi.xAxisLabel, vdoi.publication_date,
        vdoi.theme${lang} AS theme,
        vdoi.product${lang} AS product,
        vdoi.subtheme${lang} AS subtheme,
        vdoi.narrative,
        vdoi.domain${lang} AS domain,
        vdoi.xAxisFormat, vdoi.domain_details,
        vdoi.label, vdoi.node_id,
        vdoi.data_source, vdoi.yAxisLabel,
        vdoi.name, vdoi.updated, vdoi.tooltipTitleFormat
    `;

    let searchClause = '';
    if (search) {    
        if (lang === '_ar') {
            searchClause = ` AND vdoi.component_title_ar LIKE {search:String}`;
        } else {
            searchClause= ` AND LOWER(vdoi.component_title) LIKE LOWER({search:String})`;
        }
        binds.search = `%${search}%`;
    }
    const applicationQuery = `
        SELECT DISTINCT ${commonColumns}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS AS vdoi
        WHERE vdoi.group_id IN {group_ids:Array(String)}
        ${searchClause}
    `;
    
    return {query:applicationQuery,binds:binds};
}


async function getScreenerDeatailsQuery(subthemeIds){
    try {
        let binds={
            subthemeIds:subthemeIds
        };
        let query = `SELECT * FROM VW_STAT_IND_SCREENER_CONFIG vsisc`;
        if (subthemeIds) {
            query += ` WHERE vsisc.subtheme_id IN {subthemeIds:Array(String)}`;
        }
        return {query,binds}
        
    } catch (err) {
        log.error(
            `<<<<<< Exited services.getQuery.service.revokeRefreshTokenQuery with error ${err} `
        );
        throw err;
    }
}

async function getRankDataQuery(id,key){
    const binds = {
        id: id,
        key:key
      };
    let query = `SELECT * FROM VW_BAYAAN_DOMAIN_RANK bdr`;
    query += ` WHERE ID={id:String}`;
    query += ` AND bdr."Key"={key:String}`;
    return {query,binds}
}

/**
 * Builds a query to fetch a paginated list of domain content for given filters.
 * 
 * @param {string|null} domainId - The domain ID to filter by (optional).
 * @param {string|null} subdomainId - The subdomain/theme ID to filter by (optional).
 * @param {Array<string>} userGroups - Array of user group IDs for filtering.
 * @param {number} limit - Number of records to return (default: 10).
 * @param {number} offset - Number of records to skip (default: 0).
 * @param {string} lang - Language code for the content (default: 'en').
 * @returns {{query: string, binds: object}} The SQL query and bind parameters.
 */
async function getDomainContentListQuery(domainId = null, subdomainId = null, subthemeId = null, productID = null, search = null, userGroups = [], limit = 10, offset = 0, lang = "en", data_security = null) {
    // Define columns to select with aliases
    // Set lang suffix: empty string for 'en', otherwise use '_' + lang
    const langSuffix = lang === "en" ? "" : `_${lang}`;
    const columns = {
        id: "node_id",
        title: `component_title${langSuffix}`,
        content_type: "'scad_official_indicator'",
        note: "indicator_id",
    };
    let is_screener = '0';
    // Build WHERE conditions
    let conditions = [
        "group_id IN ({userGroups:Array(String)})",
    ];
    if (Number(domainId)) conditions.push("domain_id = {domainId:String}");
    if (subdomainId) conditions.push("theme_id = {subdomainId:String}");
    if (subthemeId) conditions.push("subtheme_id = {subthemeId:String}");
    if (productID) conditions.push("product_id = {productID:String}");
    if (is_screener) conditions.push("is_screener = {is_screener:String}");
    if (data_security) conditions.push("name = {name:String}");

    if (search) {
        if (langSuffix === '_ar') {
            conditions.push(`component_title_ar LIKE {search:String}`);
        } else {
            conditions.push(`LOWER(component_title) LIKE LOWER({search:String})`);
        }
        
    }
    let whereClause = conditions.length ? `WHERE ${conditions.join(" AND ")}` : "";

    // Construct the query string
    let query = `
        SELECT DISTINCT ${Object.entries(columns).map(([alias, col]) => `${col} AS ${alias}`).join(", ")}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS
        ${whereClause}
        ORDER BY ${columns.title}
        LIMIT {limit:UInt32} OFFSET {offset:UInt32}
    `;

    // Bind parameters for the query
    let binds = {
        domainId: domainId,
        subdomainId: subdomainId || null,
        subthemeId: subthemeId || null,
        productID: productID || null,
        search: search ? `%${search}%` : null,
        is_screener: is_screener,
        name: data_security || null,
        limit: limit || 10,
        offset: offset || 0,
        userGroups: userGroups,
    };

    return { query, binds };
}

/**
 * Generates a SQL query and its bind parameters to count content in a domain, 
 * optionally filtered by domain, subdomain, and user groups.
 *
 * @param {string|null} [domainId=null] - The ID of the domain to filter by. If null, no domain filter is applied.
 * @param {string|null} [subdomainId=null] - The ID of the subdomain (theme) to filter by. If null, no subdomain filter is applied.
 * @param {string[]} [userGroups=[]] - An array of user group IDs to include in the query.
 * @param {string} [lang='en'] - The language code for the query. Default is 'en'.
 * @returns {{ query: string, binds: Object }} An object containing the SQL query string and the bind parameters.
 *
 * @example
 * const { query, binds } = getDomainContentCountQuery('domain123', 'subdomain456', ['group1', 'group2']);
 * // query: SQL string with WHERE clause for domain, subdomain, and user groups
 * // binds: { domainId: '3', subdomainId: '16', userGroups: ['zzasd-aad...', 'asdasd-asdasd-412....'] }
 */
async function getDomainContentCountQuery(
	domainId = null,
	subdomainId = null,
    subthemeId = null,
    productID = null,
    search = null,
	userGroups = [],
    lang = "en",
    data_security = null

) {
    const langSuffix = lang === "en" ? "" : `_${lang}`;
	// Build WHERE conditions
	let conditions = ["group_id IN ({userGroups:Array(String)})"];
	if (Number(domainId)) conditions.push("domain_id = {domainId:String}");
	if (subdomainId) conditions.push("theme_id = {subdomainId:String}");
    if (subthemeId) conditions.push("subtheme_id = {subthemeId:String}");
    if (productID) conditions.push("product_id = {productID:String}");
    if (data_security) conditions.push("name = {name:String}");
    if (search) {
        if (langSuffix === '_ar') {
            conditions.push(`component_title_ar LIKE {search:String}`);
        } else {
            conditions.push(`LOWER(component_title) LIKE LOWER({search:String})`);
        }
    }

	let whereClause = conditions.length
		? `WHERE ${conditions.join(" AND ")}`
		: "";

	// Construct the query string
	let query = `
        SELECT CAST(count(DISTINCT node_id) AS UInt32) AS total
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS
        ${whereClause}
    `;

	// Bind parameters for the query
	let binds = {
		domainId: domainId,
		subdomainId: subdomainId || null,
        subthemeId: subthemeId || null,
        productID: productID || null,
        search: search ? `%${search}%` : null,
		userGroups: userGroups,
        name: data_security || null
	};

	return { query, binds };
}

/**
 * Generates a SQL query and its bind parameters to retrieve a list of products
 * for a given domain, subdomain, and user groups, including product details and ranking.
 *
 * @async
 * @function getDomainContentProductListQuery
 * @param {string|null} [domainId=null] - The ID of the domain to filter products by. If null, no domain filter is applied.
 * @param {string|null} [subdomainId=null] - The ID of the subdomain (theme) to filter products by. If null, no subdomain filter is applied.
 * @param {string[]} [userGroups=[]] - An array of user group IDs to filter the products by group access.
 * @param {string} [lang='en'] - The language code for the product names. Default is 'en'.
 * @returns {Promise<{query: string, binds: Object}>} An object containing the SQL query string and the bind parameters.
 *
 * @example
 * const { query, binds } = await getDomainContentProductListQuery('domain123', 'subdomain456', ['group1', 'group2']);
 * // Use `query` and `binds` to execute against the database.
 */
async function getDomainContentProductListQuery(
	domainId = null,
	subdomainId = null,
    subthemeId = null,
	userGroups = [],
	lang = "en", 
) {
	// Build WHERE conditions
	let conditions = [
		"(r.Key = 'Product' OR r.Key IS NULL)",
		"group_id IN ({userGroups:Array(String)})",
	];
	if (domainId) conditions.push("p.domain_id = {domainId:String}");
	if (subdomainId) conditions.push("p.theme_id = {subdomainId:String}");
    if (subthemeId) conditions.push("p.subtheme_id = {subthemeId:String}");
	let whereClause = conditions.length
		? `WHERE ${conditions.join(" AND ")}`
		: "";

	// Construct the query string
	// Set lang suffix: empty string for 'en', otherwise use '_' + lang
	const langSuffix = lang === "en" ? "" : `_${lang}`;
	let query = `
        SELECT 
          p.product_id AS pid,
          p.product${langSuffix} AS title,
          CAST(count(DISTINCT p.node_id) AS UInt32) AS count,
          r.Rank
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS p
        LEFT JOIN VW_BAYAAN_DOMAIN_RANK r
          ON CAST(p.product_id AS String) = CAST(r.ID AS String)
        ${whereClause}
        GROUP BY p.product_id, p.product${langSuffix}, r.Rank
        ORDER BY r.Rank
    `;

	// Bind parameters for the query
	let binds = {
		domainId: domainId,
		subdomainId: subdomainId || null,
        subthemeId: subthemeId || null,
		userGroups: userGroups,
	};

	return { query, binds };
}

async function getDomainContentFiltersQuery(
	domainId = null,
	userGroups = [],
	lang = "en", 
) {
	// Build WHERE conditions
	let conditions = [
		"group_id IN ({userGroups:Array(String)})",
	];
	if (domainId) conditions.push("p.domain_id = {domainId:String}");
	let whereClause = conditions.length
		? `WHERE ${conditions.join(" AND ")}`
		: "";

	// Construct the query string
	// Set lang suffix: empty string for 'en', otherwise use '_' + lang
    const langSuffix = lang === "en" ? "" : `_${lang}`;
    const columns = {
        id: "theme_id",
        name: `theme${langSuffix}`,
        subthemeID: "subtheme_id",
        subthemeName: `subtheme${langSuffix}`,
        screenerConfiguration: "MAX(p.screener_config)",
        // Logic columns below for showScreener
        subthemeShowScreener: `MAX(
            CASE
                WHEN p.is_screener = '1'
                    AND p.screener_config IS NOT NULL
                    AND p.screener_config != ''
                    AND p.subtheme_id IS NOT NULL
                    AND p.subtheme_id != '' THEN TRUE
                ELSE FALSE
            END
        )`,
        showScreener: `MAX(
            CASE
                WHEN ((p.subtheme_id IS NULL OR p.subtheme_id = '')
                    AND (p.is_screener = '1'
                        AND p.screener_config IS NOT NULL
                        AND p.screener_config != ''
                        AND p.theme_id IS NOT NULL
                        AND p.theme_id != ''))
                    AND NOT (
                        p.is_screener = '1'
                        AND p.screener_config IS NOT NULL
                        AND p.screener_config != ''
                        AND p.subtheme_id IS NOT NULL
                        AND p.subtheme_id != ''
                    ) THEN TRUE
                ELSE FALSE
            END
        )`,
    };
    let query = `
        SELECT ${Object.entries(columns)
					.map(([alias, col]) => `${col} AS ${alias}`)
					.join(", ")}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS p
        LEFT JOIN VW_BAYAAN_DOMAIN_RANK themerank
            ON CAST(p.theme_id AS String) = CAST(themerank.ID AS String) AND themerank.Key = 'theme'
        LEFT JOIN VW_BAYAAN_DOMAIN_RANK subthemerank
            ON CAST(p.subtheme_id AS String) = CAST(subthemerank.ID AS String) AND subthemerank.Key = 'subtheme'
        ${whereClause}
        GROUP BY
            p.theme_id,
            p.theme${langSuffix},
            p.subtheme_id,
            p.subtheme${langSuffix},
            themerank.Rank,
            subthemerank.Rank
            
        ORDER BY
            themerank.Rank,
            subthemerank.Rank
    `;

    // Bind parameters for the query
    let binds = {
        domainId: domainId,
        userGroups: userGroups,
    };

	return { query, binds };
}

async function getDomainFilteredQuery(domainId,req){
    const user_groups = req.user.groups;
    const binds = {
        group_ids: user_groups,
        domain_id: domainId
    };
    const lang = req.headers["accept-language"] === 'en' ? '' : '_ar';
    const commonColumns = `
        vdoi.indicator_id, vdoi.note, vdoi.enableCompare,
        vdoi.component_subtitle${lang} AS subtitle,
        vdoi.theme_id, vdoi.showLegend, vdoi.subtheme_id,
        vdoi.language, vdoi.showPointLabels, vdoi.yAxisFormat,
        vdoi.component_title${lang} AS title,
        vdoi.domain_id, vdoi.tooltipValueFormat, vdoi.product_id,
        vdoi.xAxisLabel, vdoi.publication_date,
        vdoi.theme${lang} AS theme,
        vdoi.product${lang} AS product,
        vdoi.subtheme${lang} AS subtheme,
        vdoi.narrative,
        vdoi.domain${lang} AS domain,
        vdoi.xAxisFormat, vdoi.domain_details${lang} AS domain_details,
        vdoi.label, vdoi.node_id,
        vdoi.data_source, vdoi.yAxisLabel,
        vdoi.name, vdoi.updated, vdoi.tooltipTitleFormat
    `;
    const applicationQuery = `
        SELECT ${commonColumns}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS AS vdoi
        WHERE vdoi.domain_id = {domain_id:String}
        AND vdoi.group_id IN {group_ids:Array(String)}
    `;
    
    return {query:applicationQuery,binds:binds};
}

async function getDomainOfficialStatisticsCountQuery(
	domainId = null,
	userGroups = [],
	lang = "en"
) {
    
    // Build WHERE conditions
    let conditions = ["group_id IN ({userGroups:Array(String)})"];
    
	if (Number(domainId)) conditions.push("p.domain_id = {domainId:String}");
	let whereClause = conditions.length
		? `WHERE ${conditions.join(" AND ")}`
		: "";

	// Construct the query string
	const columns = {
		count: "COUNT(indicator_id)",
	};
	let query = `
        SELECT ${Object.entries(columns)
					.map(([alias, col]) => `${col} AS ${alias}`)
					.join(", ")}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS p
        ${whereClause}
    `;

	// Bind parameters for the query
	let binds = {
		domainId: domainId,
		userGroups: userGroups,
	};

	return { query, binds };
}
module.exports = {
	getPopStatsDataQuery,
	getScreenerCountQuery,
	getSingleDimensionNodesQuery,
	getSingleDimensionNodesQueryV3,
	getNodeBasicDeatailsQuery,
	getScreenerDeatailsQuery,
	getRankDataQuery,
	getDomainContentListQuery,
	getDomainContentCountQuery,
	getDomainContentProductListQuery,
	getDomainFilteredQuery,
	getDomainContentFiltersQuery,
	getDomainOfficialStatisticsCountQuery,
};