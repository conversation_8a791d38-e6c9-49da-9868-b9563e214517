const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { IFPError } = require('../../utils/error');

async function getPopStatsDataQuery(nodeIds) {
    try {
        if (nodeIds.length<1)
            throw new IFPError(400,'No node ids found') 
        let query = `SELECT * FROM IFP_POPULAR_STATS WHERE NODE_ID IN (${nodeIds.join(',')}) ORDER BY HIT_COUNT DESC`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.listMyAppsDataQuery with error ${err} `);
        throw err;
    }
}
  
async function getScreenerCountQuery(screenerConfiguration) {
    try {
        let screenerFilters = screenerConfiguration.screenerFilterBy?screenerConfiguration.screenerFilterBy:{}
        const whereClauses = [];
        for (const filterKey in screenerFilters) {
            if (screenerFilters[filterKey].length > 0) {
                if (Array.isArray(screenerFilters[filterKey])) {
                    const values = screenerFilters[filterKey].map(value => `UPPER('${value.replace(/'/g, "''")}')`).join(', ');
                    whereClauses.push(`UPPER(${filterKey}) IN (${values})`);
                } else {
                    whereClauses.push(`UPPER(${filterKey}) = UPPER('${screenerFilters[filterKey].replace(/'/g, "''")}')`);
                }
            }
        }

        const query = `
            SELECT COUNT(DISTINCT INDICATOR_ID) AS COUNT
            FROM ${screenerConfiguration.screenerView} ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''}
        `;
        const binds = {};
        
        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getScreenerCountQuery with error ${err} `);
        throw err
    }
}

async function getSingleDimensionNodesQuery(nodes) {

    try {
        let binds = {}
        nodes = nodes.map(n=>`'${n}'`).join(',')
        let query = `SELECT NODE_ID FROM VW_DYNAMIC_JSON_SF WHERE JSON_KEY='isMultiDimension' and JSON_VALUE='FALSE' AND NODE_ID in (${nodes})`
        return { query, binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getScreenerCountQuery with error ${err} `);
        throw err;
    }
}


module.exports = { getPopStatsDataQuery, getScreenerCountQuery, getSingleDimensionNodesQuery};
