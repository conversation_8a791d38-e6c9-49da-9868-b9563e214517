const { Sequelize, DataTypes, Model } = require("sequelize");
/**
 * Sequelize model for the existing IFP_FLOW_USERS_V2 table in Oracle database.
 * This model maps to an existing table and is used for ORM operations only.
 * @param {Sequelize} sequelize - Oracle Sequelize instance
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const IfpFlowUser = sequelize.define(
    "IfpFlowUser",
    {
      id: {
        type: DataTypes.STRING(255),
        allowNull: false,
        primaryKey: true,
        field: "ID"
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "NAME"
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "EMAIL"
      },
      phoneNumber: {
        type: DataTypes.STRING(255),
        allowNull: false,
        defaultValue: 'NA',
        field: "PHONE_NUMBER"
      },
      role: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "ROLE"
      },
      entityId: {
        type: DataTypes.STRING(255),
        allowNull: true,
        field: "ENTITY_ID"
      },
      status: {
        type: DataTypes.STRING(20),
        allowNull: true,
        field: "STATUS"
      },
      activationFlag: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: 'PENDING',
        field: "ACTIVATION_FLAG"
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        field: "CREATED_AT"
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        field: "UPDATED_AT"
      },
      designation: {
        type: DataTypes.STRING(100),
        allowNull: true,
        field: "DESIGNATION"
      },
      existingUser: {
        type: DataTypes.STRING(100),
        allowNull: true,
        defaultValue: '0',
        field: "EXISTING_USER"
      },
      deletedBy: {
        type: DataTypes.STRING(100),
        allowNull: true,
        field: "DELETED_BY"
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: "DELETED_AT"
      },
      isNdaAccepted: {
        type: DataTypes.NUMBER,
        allowNull: true,
        defaultValue: 1,
        field: "IS_NDA_ACCEPTED"
      },
      mobileSyncStatus: {
        type: DataTypes.STRING(50),
        allowNull: true,
        field: "MOBILE_SYNC_STATUS"
      },
      mobileSyncFailedAttempts: {
        type: DataTypes.NUMBER,
        allowNull: true,
        defaultValue: 0,
        field: "MOBILE_SYNC_FAILED_ATTEMPTS"
      },
      requestJustification: {
        type: DataTypes.STRING(500),
        allowNull: true,
        field: "REQUEST_JUSTIFICATION"
      },
      mobileSync: {
        type: DataTypes.NUMBER,
        allowNull: true,
        field: "MOBILE_SYNC"
      }
    },
    {
      tableName: "IFP_FLOW_USERS_V2",
      timestamps: false,     // Since we're manually managing the timestamps
      freezeTableName: true, // Prevent Sequelize from pluralizing the table name
    }
  );
 
  return IfpFlowUser;
}
module.exports = model;