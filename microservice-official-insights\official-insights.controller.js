const Logger = require('scad-library').logger;
const moment = require('moment');
require('dotenv').config();

const { getMetaFromCMS } = require('../services/common-service');
const { getOfficialIndicatorData, getOfficialFilterData, getOfficialIndicatorsData, getGeneratedVizConfigurationData, getGraphData ,getIndicatorMetaData} = require('./services/executeQuery');

const { processLineChartData } = require('./services/chart-services/line-chart');


const log = new Logger().getInstance();

const constants = require('../config/constants.json');
const indicatorTemplate = require('./services/chart-services/template.json')
const indicatorTemplateAr = require("./services/chart-services/template_ar.json");

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { assignHierarchyValues } = require('./services/helper');
const { getViewNameById } = require('../services/executeQuery.service');
const { IFPError } = require('../utils/error');
const { getInnovativeIndicatorsData, getInnovativeFilterData, getInnovativeIndicatorData } = require('../microservice-innovative-insights/services/executeQuery');

async function getOfficialInsightsById(req) {
    log.debug(`>>>>>Entered microservice.official-insights.controller.getOfficialInsightsById`);

    try {

        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const language = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
        const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        let domainList;
        let indicatorMetaData;
        let viewName;
        let indicatorId = req.params.id;

        let response = language === "AR"
            ? JSON.parse(JSON.stringify(indicatorTemplateAr))
            : JSON.parse(JSON.stringify(indicatorTemplate));

        response.id = indicatorId

        //This cache is used for storing the CMS domain list for assigning domain to the indicator node
        let domainCacheKey = `cmsMetaDomainList_${crypto.createHash('md5').update(cmsDomainsUrl).digest("hex")}`
        const domainCacheResults = await getRedis(domainCacheKey,req.headers);

        if (domainCacheResults){
            domainList = JSON.parse(domainCacheResults)
            indicatorMetaData = await getIndicatorMetaData(indicatorId,language)
        }
        else{
            [domainList,indicatorMetaData] = await Promise.all([
                getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups),
                getIndicatorMetaData(indicatorId,language)
            ])
            setRedis(domainCacheKey, JSON.stringify(domainList), constants.redis.cmsResponseTTL, req.headers);
        }

        if (! indicatorMetaData.length)
            throw new IFPError(404,`No such indicator found: ${indicatorId}`)

        let meta = indicatorMetaData[0]
        response = assignHierarchyValues(response,meta,domainList.domain)

        const counter = [];

        response.note = indicatorId
        response.viewName = meta.SOURCE_TABLE
        response.content_classification_key = 'official_statistics'
        viewName = meta.SOURCE_TABLE

        let nativeCacheKey = `nativeMetaOfficialInsights_${indicatorId}_${crypto.createHash('md5').update(JSON.stringify(response)).digest("hex")}`
        const nativeCacheResults = await getRedis(nativeCacheKey, req.headers);
        if (nativeCacheResults) {
            log.info(`<<<<<Cache found for microservice.official-insights.controller.getOfficialInsightsById`);
            return JSON.parse(nativeCacheResults);
        }

        if (response.filterPanel && response.filterPanel.isEnabled) {
            response.filterPanel = await getFilterOptions(response.filterPanel);
        }

        if (viewName == "VW_STATISTICAL_INDICATORS"){

            const [
                generatedDynamicVisualization, 
                indicatorData
                ] = await Promise.all([
                getGeneratedVizConfigurationData(indicatorId),
                getOfficialIndicatorData(indicatorId, viewName,language)
                ]);

            if (!generatedDynamicVisualization.length)
                throw new IFPError(404,`No configuration found for indicator: ${indicatorId}`)

            if (!indicatorData.length)
                throw new IFPError(404,`No data found for indicator: ${indicatorId}`)

            let dynamicVisualization = JSON.parse(generatedDynamicVisualization[0].CONFIGURATION)

            if (generatedDynamicVisualization[0].META_DATA){
                try{
                    let metaData = generatedDynamicVisualization[0].META_DATA
                    let parsedMetaData= JSON.parse(metaData.replace(/\\/g, '').replace(/\n/g, "\\n").replace(/\t/g, "\\t").replace(/_/g, ' '));
                    response.metaData =  parsedMetaData[language]
                }
                catch(exp){
                    response.metaData = {}
                }
            }

            dynamicVisualization[0].viewName = viewName
            
            if (dynamicVisualization[0].viewName){
                response.viewName = dynamicVisualization[0].viewName
            }
            
            if (dynamicVisualization[0].indicatorId){
                response.indicatorId = dynamicVisualization[0].indicatorId
            }

            if (dynamicVisualization[0].unit){
                response.unit = dynamicVisualization[0].unit
            }

            if (dynamicVisualization[0].data_source){
                response.data_source = dynamicVisualization[0].data_source
            }

            if (dynamicVisualization[0].isMultiDimension){
                response.isMultiDimension = true
            }
            else{
                response.isMultiDimension = false
            }

            if (dynamicVisualization[0].componentTitle){
                response.component_title = dynamicVisualization[0].componentTitle
            }

            if (dynamicVisualization[0].componentSubtitle){
                response.component_subtitle = dynamicVisualization[0].componentSubtitle
            }

            if (dynamicVisualization[0].indicatorFilters){
                response.indicatorFilters = dynamicVisualization[0].indicatorFilters
                delete dynamicVisualization[0].indicatorFilters
            }

            if (dynamicVisualization[0].filterPanelData){
                response.filterPanel = dynamicVisualization[0].filterPanelData
                delete dynamicVisualization[0].filterPanelData
            }
            else{
                response.filterPanel = false
            }

            response.updated= moment(data[0].MAX_OBS_DT).format('DD/MM/YYYY');
            response.publication_date = indicatorData[0].MAX_OBS_DT
            response.isMultiDimension = false

            let langCode = req.headers["accept-language"]
            let tableFields = [
                {label:( langCode == 'en' ? "INDICATOR ID" : 'معرف المؤشر'),path:"INDICATOR_ID"}
            ]

            if (response.filterPanel){
                response.filterPanel.properties = response.filterPanel.properties.filter(property => property.lang === language.toUpperCase());
                response.filterPanel.properties.forEach(property => {
                    tableFields.push({label:property.label,path:property.path})
                })
            }

            tableFields.push({label:( langCode == 'en' ? "VALUE": 'قيمة'),path:"VALUE"})
            let dateFields = tableFields.filter(field=>{return field.path == 'OBS_DT'})

            if (! dateFields.length){
                tableFields.push({label:( langCode == 'en' ? "DATE" : 'تاريخ'),path:"OBS_DT"})
            }

            response.tableFields = tableFields
            
            response.domain = indicatorData[0].TOPIC
            response.subdomain = indicatorData[0].THEME
            response.subtheme = indicatorData[0].SUBTHEME
            response.product = indicatorData[0].PRODUCT

            response.indicatorVisualizations.visualizationsMeta = dynamicVisualization;
            response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id
        }
        else{
            let data;
            let censusViewMap = constants.censusViewMap
            if (Object.keys(censusViewMap).includes(viewName)){
                data = await getInnovativeIndicatorData(indicatorId, meta.SOURCE_TABLE,language);
                response = {
                    ...response,
                    ...censusViewMap[viewName]
                }
                if (('MARITAL_STATUS_EN' in data[0] && data[0]['MARITAL_STATUS_EN'] != 'All') || ('MARITAL_STATUS_AR' in data[0] && data[0]['MARITAL_STATUS_AR'] != 'All'))
                    response.showMaritalDisclaimer = true
        
            }
            else{
                data = await getOfficialIndicatorData(indicatorId,  viewName,language);
            }
            
            let timeUnit = []

            response.overView = {
                "compareFilters": [
                ],
                "valueFormat": "number_1.1-1",
                "templateFormat": "date_y",
                "baseDate": data[0].OBS_DT_LATEST,
                "value": data[0].VALUE_LATEST,
                "yearlyCompareValue": data[0].YEARLY_COMPARE_VALUE,
                "yearlyChangeValue": data[0].YEARLY_CHANGE_VALUE,
                "quarterlyCompareValue": data[0].QUARTERLY_COMPARE_VALUE,
                "quarterlyChangeValue": data[0].QUARTERLY_CHANGE_VALUE,
                "monthlyCompareValue": data[0].MONTHLY_COMPARE_VALUE,
                "monthlyChangeValue": data[0].MONTHLY_COMPARE_VALUE,
            }
            if (data[0].YEARLY){
                response.overView.compareFilters.push("Y/Y")
                timeUnit.push('Yearly')
            }
            if (data[0].QUARTERLY){
                response.overView.compareFilters.push("Q/Q")
                timeUnit.push('Quarterly')
            }
            if (data[0].MONTHLY){
                response.overView.compareFilters.push("M/M")
                timeUnit.push('Monthly')
            }

            if (data) {

                let visualizationMeta = [
                    {
                        "id": `line-chart-official${indicatorId}`,
                        "type": "line-chart",
                        "seriesMeta": [
                            {
                                "type": "solid",
                                "xAccessor": {
                                    "type": "date",
                                    "path": "OBS_DT",
                                    "specifier": "%Y-%m-%d"
                                },
                                "yAccessor": {
                                    "type": "value",
                                    "path": "VALUE"
                                },
                                "data":data,
                                "id": `official${indicatorId}`,
                                "dbIndicatorId": indicatorId,
                                "label": null,
                                "color": "#000000"
                            }
                        ],
                        "tooltipTitleFormat": "date_MMM y",
                        "tooltipValueFormat": "d3-number",
                        "timeUnit":timeUnit,
                        "xAxisFormat": "date_y",
                        "yAxisFormat": "d3-number",
                        "xAxisLabel": "",
                        "yAxisLabel": data[0].UNIT,
                        "unit": data[0].UNIT
                    }
                ]

                response.unit = data[0].UNIT
                response.data_source = data[0].DATA_SOURCE
                response.updated= moment(data[0].MAX_OBS_DT).format('DD/MM/YYYY');
                response.publication_date = data[0].MAX_OBS_DT
                
                response.isMultiDimension = false

                let langCode = req.headers["accept-language"]
                let tableFields = [
                    {label:( langCode == 'en' ? "INDICATOR ID" : 'معرف المؤشر'),path:"INDICATOR_ID"},
                    {label:( langCode == 'en' ? "VALUE": 'قيمة'),path:"VALUE"},
                    {label:( langCode == 'en' ? "DATE" : 'تاريخ'),path:"OBS_DT"}
                ]

                response.tableFields = tableFields

                response.component_title = data[0].INDICATOR_NAME
                // response.domain = data[0].TOPIC
                // response.subdomain = data[0].THEME
                // response.subtheme = data[0].SUBTHEME
                // response.product = data[0].PRODUCT
                    
                response.indicatorVisualizations.visualizationsMeta = visualizationMeta;
                response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id

            }
        }
        const indicatorVisualizationsMeta = response.indicatorVisualizations.visualizationsMeta;

        let visualizationLen = response.indicatorVisualizations.visualizationsMeta.length;
        let graphData = response;
        graphData.indicatorVisualizations.visualizationsMeta = [];

        let vizFlag = false
        let vizPromises = indicatorVisualizationsMeta.map(async visualization => {
            let results;
            if (meta.SOURCE_TABLE == "VW_STATISTICAL_INDICATORS"){
                let isFilterPanelEnabled = response.filterPanel ? response.filterPanel.isEnabled : "";
                results = await getGraphData(visualization, isFilterPanelEnabled,response.filterPanel);
            }
            visualization.seriesMeta.forEach(series => {
                let chartType = visualization.type
                switch (chartType) {

                    case "line-chart": {
                        try {
                            if (meta.SOURCE_TABLE == "VW_STATISTICAL_INDICATORS")
                                series = processLineChartData(results, series);
                            else
                                series = processLineChartData(series.data, series);
                        } catch (err) {
                            
                            log.error(`Error executing processLineChartData from microservice.official-insights.controller.getOfficialInsightsById ${err}`);
                            reject(err);
                        }
                        break;
                    }

                    default: {
                        log.debug(`Chart type not available`);
                        counter.push(1);
                        break;
                    }
                }
            })

            graphData.indicatorVisualizations.visualizationsMeta.push(visualization);
            counter.push(1);

            if (response.type == "official_statistics")
                response.type = "scad"
            if (counter.length === visualizationLen) {
                delete graphData.viewName
                vizFlag = true
            }
        });
        await Promise.all(vizPromises);
        if (vizFlag){
            setRedis(nativeCacheKey, JSON.stringify(graphData), constants.redis.cmsResponseTTL, req.headers);
            return (graphData);
        }
        else
            throw new IFPError('Error generating visualization')

    } catch (err) {
        
        log.error(`<<<<< Exited microservice.official-insights.controller.getOfficialInsightsById with error ${err} `)
        throw err;
    }
}


async function getOfficialFilters(req) {
    log.debug(`>>>>>Entered microservice.official-insights.controller.getOfficialInsightsById`);
    return new Promise(async (resolve, reject) => {
        try {
            const language = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
            let filterIndicator = req.params.id
            let filterMap = {}
            
            let data;
            if (constants.censusIndicators.includes(filterIndicator))
                data = await getInnovativeFilterData(filterIndicator,language)
            else
                data = await getOfficialFilterData(filterIndicator,language)

            data.forEach(filter => {
                if (!Object.keys(filterMap).includes(filter.DIMENSION))
                    filterMap[filter.DIMENSION] = {items:[]}
                filterMap[filter.DIMENSION]['items'].push({
                    name: filter.VALUE_LABEL,
                    value: filter.VALUE
                })
                filterMap[filter.DIMENSION]['name'] = filter.DIMENSION_LABEL
                if (filter.DEFAULT_SELECTION)
                    filterMap[filter.DIMENSION]['default'] = {
                        name: filter.VALUE_LABEL,
                        value: filter.VALUE
                    }
            })

            let response = Object.entries(filterMap).map(([filter, data]) => {
                const filterObj = {
                    name: data.name,
                    key: filter,
                    default: data.default,
                    items: data.items
                }
                return filterObj;
            })
            resolve(response)
        } catch (err) {
            
            log.error(`<<<<< Exited microservice.official-insights.controller.getOfficialFilters with error ${err} `)
            reject(err);
        }
    });
}


async function getOfficialIndicators(req) {
    log.debug(`>>>>>Entered microservice.official-insights.controller.getOfficialInsightsById`);
    return new Promise(async (resolve, reject) => {
        try {
            let viewName = req.body.viewName
            let filters = req.body.filters
            let filterBy = req.body.filterBy?req.body.filterBy:{}
            let sortBy = req.body.sortBy

            const language = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;

            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;

            if (Number(viewName)){
                viewName = await getViewNameById(viewName)
            }
            
            let censusViewMap = constants.censusViewMap

            const isCensus = Object.keys(censusViewMap).includes(viewName)
            let data;
            if (isCensus)
                data = await getInnovativeIndicatorsData(viewName, filters,sortBy, offset, limit,language)
            else
                data = await getOfficialIndicatorsData(viewName, filters,filterBy,sortBy, offset, limit,language)
    
            let results = []
            
            if (isCensus){
                data.forEach(element => {
                    let tempElement = {
                        "indicatorId": element.INDICATOR_ID,
                        "title": element.INDICATOR_NAME,
                        "compareFilters": [
                        ],
                        "valueFormat": "number_1.1-1",
                        "templateFormat": "date_y",
                        "baseDate": element.OBS_DT_LATEST,
                        "value": element.VALUE_LATEST,
                        "yearlyCompareValue": element.YEARLY_COMPARE_VALUE,
                        "yearlyChangeValue": element.YEARLY_CHANGE_VALUE,
                        "quarterlyCompareValue": element.QUARTERLY_COMPARE_VALUE,
                        "quarterlyChangeValue": element.QUARTERLY_CHANGE_VALUE,
                        "monthlyCompareValue": element.MONTHLY_COMPARE_VALUE,
                        "monthlyChangeValue": element.MONTHLY_COMPARE_VALUE,
                        "unit": element.UNIT
                    }
                    if (element.YEARLY)
                        tempElement.compareFilters.push("Y/Y")
                    if (element.QUARTERLY)
                        tempElement.compareFilters.push("Q/Q")
                    if (element.MONTHLY)
                        tempElement.compareFilters.push("M/M")
                    tempElement = {
                        ...tempElement,
                        ...censusViewMap[viewName]
                    }

                    results.push(tempElement)
                })
            }
            else{
                data.forEach(element => {
                    let tempElement = {
                        "indicatorId": element.INDICATOR_ID,
                        "title": element.INDICATOR_NAME,
                        "compareFilters": [
                        ],
                        "valueFormat": "number_1.1-1",
                        "templateFormat": "date_y",
                        "baseDate": element.OBS_DT_LATEST,
                        "value": element.VALUE_LATEST,
                        "yearlyCompareValue": element.YEARLY_COMPARE_VALUE,
                        "yearlyChangeValue": element.YEARLY_CHANGE_VALUE,
                        "quarterlyCompareValue": element.QUARTERLY_COMPARE_VALUE,
                        "quarterlyChangeValue": element.QUARTERLY_CHANGE_VALUE,
                        "monthlyCompareValue": element.MONTHLY_COMPARE_VALUE,
                        "monthlyChangeValue": element.MONTHLY_COMPARE_VALUE,
                        "domain" : data[0].TOPIC,
                        "subdomain" : data[0].THEME,
                        "subtheme" : data[0].SUBTHEME,
                        "product" : data[0].PRODUCT,
                        "unit": element.UNIT
                    }
                    if (element.YEARLY)
                        tempElement.compareFilters.push("Y/Y")
                    if (element.QUARTERLY)
                        tempElement.compareFilters.push("Q/Q")
                    if (element.MONTHLY)
                        tempElement.compareFilters.push("M/M")

                    results.push(tempElement)
                })
            }

            resolve({
                totalCount: data.length ? data[0].TOTAL : 0,
                page: page,
                limit: limit,
                data: results
            })

        } catch (err) {
            
            log.error(`<<<<< Exited microservice.official-insights.controller.getOfficialFilters with error ${err} `)
            reject(err);
        }
    });
}




module.exports = {
    getOfficialInsightsById,
    getOfficialFilters,
    getOfficialIndicators
};
