const Logger = require("scad-library").logger;
require("dotenv").config();
const log = new Logger().getInstance();
const axios = require("axios");
const constants = require("../config/constants.json");
const { IFPError } = require("../utils/error");
const mokInsightData = require("./helper/insightReportMockData.json");
const insightConstants = require("./helper/constants.json");
const {
  getSanadkomAccessToken,
  getPublicationId,
  getPriodicityId,
  getIntervalId,
  generatePdf,
  restructureData,
  getServiceID,
  restructureConsolidatedData,
  getQuarterMonths,
} = require("./services/sanadkomAPI.service");
const { updateReportStatus } = require("./services/ai-insight-report.service")
const { regenerate } = require("./services/regenerationOpenaiAPI.service");
const model = require("../database/models");
const { where, or } = require("sequelize");
const puppeteer = require("puppeteer");
const fs = require("fs");
const base64 = require("js-base64");
const SMART_PUBLISHER_BASE_URL =
  "https://data-sharing-api-uat.adsmartsupport.ae";
const PUBLICATION_SECURITY_KEY =
  "842E63EC-051A-47F5-AC40-235683134FA5-17E32357-3AFA-43ED-98DC-02EE0E6C12A5";
const { sendReportEmail } = require("./services/sendEmail.service");
const { getGroupMembers } = require("../services/graph");
const { getBayaanGroups } = require("../services/group.service");
const { runJob } = require("./services/cp4d-job.service");
const { uploadReport } = require("./services/report-s3-storage.service");
const { getDomains } = require("../microservice-cms/services/executeQuery.service");
const config = require("./ai-insight-report.constants.json")[process.env.NODE_ENV || "dev"];

async function getKeyDrivers(req) {
  // function for getting Domain/Consolidate report data
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.getKeyDrivers`
  );
  try {
    let reStructureData = {};
    let lang = `${req.headers["accept-language"]}`;
    const reportData = await model.AiInsightReport.findOne({
          include: [
            {
              model: model.ChatBayaanDomain,
              as: "domain",
              attributes: [
                lang === "en" ? "name_en" : "name_ar",
                "id",
                "cms_id",
              ],
            },
            {
              model: model.DropdownOption,
              as: "report_type",
              attributes: ["value"],
            },
            {
              model: model.DropdownOption,
              as: "status",
              attributes: ["value"],
            },
          ],
          where: {
            id: req.params.id,
          },
        })

    if (!reportData) {
      throw new IFPError(400, "No report found with the given ID.");
    }
    if (reportData.report_type.dataValues.value === "consolidate" && req.user.hasConsolidateGroup == false) {
      // check if user has access to consolidate report
      return {
        "status": 403,
        "access": false
      };
    }
    let keyDriversData = reportData.dataValues.report_data;
    const domain = {
      name: reportData.domain.dataValues[lang === "en" ? "name_en" : "name_ar"],
      id: reportData.domain.dataValues["cms_id"],
    };
    const quarter = `Q${reportData.quarter} Report (${getQuarterMonths(
      reportData.quarter
    )})`;
    if (reportData.report_type.dataValues.value === "consolidate") {
      reStructureData = await restructureConsolidatedData(reportData, lang);

      Object.assign(keyDriversData, {
        quarter: reStructureData.quarter,
        report_id: req.params.id,
        report_type: reportData.report_type.value,
        published_date: reportData.created_at,
        status: reportData.status.value,
      });
    } else {
      if (!reportData.report_data.comparison.headers) {
        reStructureData = await restructureData(
          reportData.report_data["comparison"],
          lang
        );
        Object.assign(keyDriversData, {
          comparison: reStructureData.comparison,
          domain: domain,
          quarter: quarter,
          report_id: req.params.id,
          report_type: reportData.report_type.value,
          published_date: reportData.created_at,
          status: reportData.status.value,
        });
      } else {
        Object.assign(keyDriversData, {
          domain: domain,
          quarter: quarter,
          report_id: req.params.id,
          report_type: reportData.report_type.value,
          published_date: reportData.created_at,
          status: reportData.status.value,
        });
      }
    }
    const resp = { keyDriversData };
    return resp;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function insightReportList(req) {
  // insight report list
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.insightReportList`
  );
  try {
    if (req.query.report_type === "consolidate" && req.user.hasConsolidateGroup == false) {
      // check if user has access to consolidate report
      return {
        "status": 403,
        "access": false
      };
    }
    let domainName = req.user.domainSme;
    let lang = `${req.headers["accept-language"]}`;
    let offset = parseInt(req.query.offset, 10) || 0; // Default to 0 if offset is not provided
    let limit = parseInt(req.query.limit, 10) || 10; // Default to 10 if limit is not provided
    let sort = req.query.sort
      ? req.query.sort.split("-")
      : ["created_at", "DESC"]; // Default to sorting by created_at if not provided
    const reportData = await model.AiInsightReport.findAll({
      include: [
      {
        model: model.DropdownOption,
        as: "report_type",
        attributes: ["display_name"],
        where: {
        value: req.query.report_type,
        },
      },
      {
        model: model.ChatBayaanDomain,
        as: "domain",
        attributes: [lang === "en" ? "name_en" : "name_ar"],
        ...(req.query.report_type === "domain" && domainName && {
        where: {
          name_en: domainName,
        },
        }),
      },
      {
        model: model.DropdownOption,
        as: "status",
        attributes: ["display_name", "color"],
      },
      ],
      where: {
      ...(req.query?.search && {
        [model.Sequelize.Op.or]: [
        {
          report_name: {
          [model.Sequelize.Op.iLike]: `%${req.query.search}%`, // Use iLike for case-insensitive search
          },
        },
        ],
      }),
      ...(req.query?.status && {
        report_status_id: req.query.status,
      }),
      ...(req.query?.quarter && {
        quarter: req.query.quarter,
      }),
      },
      offset: offset,
      limit: limit,
      order: [sort],
    });
    let result = reportData.map((report) => {
      return {
        id: report.id,
        object_id: report.object_id,
        version: report.version,
        report_name: report.report_name,
        quarter: `Q${report.dataValues.quarter}`,
        status: report.status,
        created_date: report.created_at,
      };
    });
    const resp = {
      headers: insightConstants.domainListHeaders,
      data: result,
      count: result.length,
    };
    return resp;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function consolidateTicketCreate(req) {
  // function for consolidate insight Ticket Create using SANADKOM API
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.consolidateTicketCreate`
  );
  try {
    // const token = await getSanadkomAccessToken(req);
    // const publicationId = await getPublicationId(token);
    // const priodicityId = await getPriodicityId(token, publicationId);
    // const IntervalId = await getIntervalId(
    //   token,
    //   publicationId,
    //   priodicityId,
    //   req.body.report_id
    // );
    if (req.user.hasConsolidateGroup == false) {
      // check if user has access to consolidate report
      return {
        "status": 403,
        "access": false
      };
    }
    const genPdf = await generatePdf(req, "consolidate"); // Generate PDF for consolidate report page
    await uploadReport(req.body.report_id, genPdf); // Upload the PDF to S3
    const userEmail = req.user.preferred_username.split("@")[0]; // Get the user email from the request
    const userID = `scad\\${userEmail}`;

    const reportData =  await model.AiInsightReport.findOne({
      where: {
        id: req.body.report_id,
      },
    })
    
    //create payload
    const data = {
      ReportQuarter: reportData.quarter ? reportData.quarter : 1,
      PublicationID: "{{publicationId}}",
      ReleasePriodicityID: "{{priodicityId}}",
      ReleaseIntervalID: "{{IntervalId}}",
      ReferencePeriod: 2025,
      Description: "Quaterly Consolidated Insight Report",
      SecurityKey: PUBLICATION_SECURITY_KEY,
      Email: userID,
      Language: "2",
      RequestorTypeID: 2, // 1:Alteryx, 2: Bayaan
      SupportDocs: [
        {
          Title: `Consolidated Insight Report- Q${reportData.quarter}.pdf`,
          DocTypeID: 4,
          ContentType: "application/pdf",
          // File: `${genPdf}`,
          File: "{{file_string}}",
        },
      ],
    };
    // Ticket create
    // let result = await axios.post(
    //   `${SMART_PUBLISHER_BASE_URL}/Publication/Create`,
    //   data,
    //   {
    //     headers: {
    //       Authorization: `Bearer ${token}`,
    //     },
    //   }
    // );
    let result = await runJob(req.body.report_id, data, "consolidated")
    await updateReportStatus(
      req.body.report_id,
      "",
      "ticket_creation_inprogress"
    ); // update report status
    return result.data;
  } catch (error) {
    log.error(error);
    throw error;
  }
}

async function domainTicketCreate(req) {
  // function for Domain insight Ticket Create using SANADKOM API
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.domainTicketCreate`
  );
  try {
    // const token = await getSanadkomAccessToken(req);
    // const publicationId = await getPublicationId(token);
    // const priodicityId = await getPriodicityId(token, publicationId);
    // const IntervalId = await getIntervalId(
    //   token,
    //   publicationId,
    //   priodicityId,
    //   req.body.report_id
    // );
    const genPdf = await generatePdf(req, "domain"); // Generate PDF for report page
    await uploadReport(req.body.report_id, genPdf); // Upload the PDF to S3
    const userEmail = req.user.preferred_username.split("@")[0]; // Get the user email from the request
    const userID = `scad\\${userEmail}`;
    let lang = `${req.headers["accept-language"]}`;
    const reportData = await model.AiInsightReport.findOne({
      include: [
      {
        model: model.ChatBayaanDomain,
        as: "domain",
        attributes: ["name_en", "name_ar", "id"],
      },
      ],
      where: {
      id: req.body.report_id,
      },
    });

    const domainName = reportData.domain.dataValues[lang === "en" ? "name_en" : "name_ar"];
    const domains = await getDomains(domainName);
    if (!domains.length > 0) {
      throw new IFPError(400, "No domain found with the given ID.");
    }

    //create payload
    const publicationText = `${domainName} Q${reportData.quarter} Report`;
    const data = {
      ReportQuarter: reportData.quarter ? reportData.quarter : 1,
      ServiceID: config.SANADKOM_SERVICE_ID,
      SecurityKey: PUBLICATION_SECURITY_KEY,
      UserID: userID,
      Description: `${domainName} Domain Insight Report Q${reportData.quarter}`,
      Ticket_Attributes: [
        { "Key": 1, "Value": publicationText },   // Publication Text
        { "Key": 2, "Value": `${domains[0].ID}` },                   // Domain
        { "Key": 3, "Value": "3" },                   // Classification ID
        { "Key": 4, "Value": "1" },                   // Is Manager Approval
        { "Key": 5, "Value": "{{IntervalId}}" }                    
      ],
      Ticket_File: [
        {
          FileName: `${domainName} Domain Insight Report Q${reportData.quarter}.pdf`,
          // FileBase64: `${genPdf}`,
          FileBase64: "{{file_string}}",
        },
      ],
    };
    // // Ticket create
    // let result = await axios.post(
    //   `${SMART_PUBLISHER_BASE_URL}/Sanadkom/Create_Ticket`,
    //   data,
    //   {
    //     headers: {
    //       Authorization: `Bearer ${token}`,
    //     },
    //   }
    // );
    let result = await runJob(req.body.report_id, data, "domain")
    await updateReportStatus(
      req.body.report_id,
      "",
      "ticket_creation_inprogress"
    ); // update report status
    return result.data;
  } catch (error) {
    log.error(error);
    if (error.response) {
      log.error(`Error: ${error.response.data}`);
    }
    throw error;
  }
}

//  report status list
async function reportStatusDropdownList(req) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.reportStatusDropdownList`
  );
  try {
    const reportStatusData = model.DropdownOption
      ? await model.DropdownOption.findAll({
          include: [
            {
              model: model.DropdownCategory,
              as: "category",
              attributes: ["name"],
              where: {
                name: "ai_insight_report_status",
              },
            },
          ],
        })
      : [];
    let result = reportStatusData.map((status) => {
      return {
        id: status.id,
        object_id: status.object_id,
        display_name: status.display_name,
        value: status.value,
        color: status.color,
      };
    });

    // Add an 'All' object to the result
    result.unshift({
      id: null,
      object_id: null,
      display_name: "All",
      value: "all",
      color: null,
    });

    const resp = {
      data: result,
      count: result.length,
    };
    return resp;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

// Download PDF
async function downloadGeneratedPdf(req) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.downloadGeneratedPdf`
  );
  try {
    let report_id = parseInt(req.query?.report_id, 10);
    let report_type = req.query?.report_type;
    let webpageUrl = null;
    const reportData = await model.AiInsightReport.findOne({
          include: [
            {
              model: model.DropdownOption,
              as: "report_type",
              attributes: ["value"],
            },
          ],
          where: {
            id: report_id,
          },
        })
    if (reportData.report_type.dataValues.value === "consolidate" && req.user.hasConsolidateGroup == false) {
      // check if user has access to consolidate report
      return {
        "status": 403,
        "access": false
      };
    }
    log.info(`Retrieving report data for ID: ${reportData.dataValues?.id}`);
    let getToken = req.headers.authorization.replace("Bearer ", ""); // Get the token from the request header
    // const browser = await puppeteer.launch({    // Use this line for while you are in (local development)
    //   executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', //Step 1: Launch the browser
    //   headless: true, // Set headless: true for production
    //   args: ["--disable-web-security"],
    // });

    const browser = await puppeteer.launch({
      //Step 1: Launch the browser
      headless: true, // Set headless: true for production
      args: ["--disable-web-security", "--no-sandbox", "--disable-setuid-sandbox"],
    });
    const page = await browser.newPage();
    if (report_type === "domain") {
      await page.setViewport({ width: 2000, height: 10000000 });
      webpageUrl = `${process.env.PLATFORM_BASEPATH}/insight-report-pdf-preview/${report_id}?token=${getToken}`; // Step 2: Navigate to the webpage
    } else {
      await page.setViewport({ width: 2000, height: 10000000 });
      webpageUrl = `${process.env.PLATFORM_BASEPATH}/consolidated-pdf-preview/${report_id}?token=${getToken}`; // Step 2: Navigate to the webpage
    }
    log.info(`Navigating to webpage: ${webpageUrl}`);
    await page.goto(webpageUrl, {
      waitUntil: "networkidle2",
      timeout: 0,
    }); // Wait until the page is fully loaded
    log.info(`Webpage loaded successfully`);
    const elementHeight = await page.evaluate(() => {
      const el = document.querySelector('.ifp-ai-report__main');
      if (!el) return 2000; // fallback in case element is not found
      const rect = el.getBoundingClientRect();
      return rect.height + 50;
    });
    const pdfBuffer = await page.pdf({
      // Step 3: Generate PDF
      // format: "A1", // PDF format
      printBackground: true, // Include background graphics
      height: `${elementHeight}px`,
      width: "2000px",
      timeout: 60000000,
    });
    log.info(`PDF generated successfully`);
    //fs.writeFileSync("Domain-Insight-Report.pdf", pdfBuffer); // Save the PDF as 'report.pdf' for testing purpose
    await browser.close(); // Step 6: Close the browser
    const resp = {
      message: "PDF generated successfully",
      pdfBuffer: pdfBuffer,
      reportName: reportData.report_name,
    };
    return resp;
  } catch (error) {
    console.error("Error:", error);
    log.error(err);
    throw err;
  }
}

// update regenerated  data

async function updateGeneratedData(req) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.updateGeneratedData`
  );
  try {
    let bodyData = req.body;
    let section = bodyData.section;
    let updatedData = bodyData.data;
    const reportData = await model.AiInsightReport.findOne({
          include: [
            {
              model: model.DropdownOption,
              as: "report_type",
              attributes: ["value"],
            },
          ],
          where: {
            id: req.params.id,
          },
        })

    if (!reportData) {
      throw new IFPError(400, "No report found with the given ID.");
    }
    if (reportData.report_type.dataValues.value === "consolidate" && req.user.hasConsolidateGroup == false) {
      // check if user has access to consolidate report
      return {
        "status": 403,
        "access": false
      };
    }
    let keyDriversData = JSON.parse(
      JSON.stringify(reportData.dataValues.report_data)
    );
    if (bodyData.section) {
      if (updatedData) {
        updatedData.forEach((item) => {
          const sectionKey = Object.keys(item)[0]; // Get the key of each item
          if (section === "sentiment") {
            if (reportData.report_type.dataValues.value === "consolidate") {
              const domains = reportData.report_data.domains;
              for (let index = 0; index < domains.length; index++) {
                if (domains[index].domain.report_id === bodyData.domain_id) {
                  keyDriversData.domains[index][section][sectionKey] =
                    item[sectionKey]; // Replace section with the key and its value
                  reportData.update({ report_data: keyDriversData });
                  break; // Exit the loop once the domain is found and updated
                }
              }
            } else {
              keyDriversData[section][sectionKey] = item[sectionKey]; // Replace section with the key and its value
              reportData.update({ report_data: keyDriversData });
            }
          } else {
            if (reportData.report_type.dataValues.value === "consolidate") {
              const domains = reportData.report_data.domains;
              for (let index = 0; index < domains.length; index++) {
                if (domains[index].domain.report_id === bodyData.domain_id) {
                  keyDriversData.domains[index][sectionKey] = item[sectionKey]; // Replace section with the key and its value
                  reportData.update({ report_data: keyDriversData });
                  break; // Exit the loop once the domain is found and updated
                }
              }
            } else {
              keyDriversData[sectionKey] = item[sectionKey]; // Replace section with the key and its value
              reportData.update({ report_data: keyDriversData });
            }
          }
        });
      } else {
        throw new IFPError(400,"Invalid data provided for the section.");
      }
    } else {
      throw IFPError(400,"Invalid section or data provided.");
    }
    const resp = {
      message: `The ${section} data has been successfully updated.`,
    };
    return resp;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

// regenerating pipeline API

async function regenerateReport(req) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.regenerateReport`
  );
  try {
    let bodyData = req.body;
    let section = bodyData.section;
    let subSection = section.split("-")[1] ? section.split("-")[1] : null;
    let userprompt = bodyData.user_prompt;
    let lang = `${req.headers["accept-language"]}`;
    const reportData = await model.AiInsightReport.findOne({
          include: [
            {
              model: model.ChatBayaanDomain,
              as: "domain",
              attributes: [
                lang === "en" ? "name_en" : "name_ar",
                "id",
                "cms_id",
              ],
            },
            {
              model: model.DropdownOption,
              as: "report_type",
              attributes: ["value"],
            },
          ],
          where: {
            id: bodyData.domain_id ? bodyData.domain_id : bodyData.report_id,
          },
        })
    if (!reportData) {
      throw new IFPError(400,"No report found with the given ID.");
    }
    if (reportData.report_type.dataValues.value === "consolidate" && req.user.hasConsolidateGroup == false) {
      // check if user has access to consolidate report
      return {
        "status": 403,
        "access": false
      };
    }
    let regeneratedData = await regenerate(
      section.split("-")[0],
      userprompt,
      reportData,
      reportData.report_type.dataValues.value
    );

    if (section === "all") {
      regeneratedData.domain = {
        name: reportData.domain.dataValues[
          lang === "en" ? "name_en" : "name_ar"
        ],
        id: reportData.domain.dataValues["cms_id"],
      };
      regeneratedData.quarter = `Q${
        reportData.quarter
      } Report (${getQuarterMonths(reportData.quarter)})`;
      regeneratedStructuredData = await restructureData(
        regeneratedData["comparison"],
        lang
      );
      regeneratedData.comparison = regeneratedStructuredData["comparison"];
    } else if (section === "comparison") {
      regeneratedData = await restructureData(regeneratedData, lang);
    }

    if (subSection === null) {
      if (section === "comparison" || section === "key_insights") {
        return (resp = {
          [section]: regeneratedData.comparison
            ? regeneratedData.comparison
            : regeneratedData,
        });
      } else {
        return regeneratedData;
      }
    } else {
      return (resp = { [subSection]: regeneratedData[subSection] });
    }
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function sendAiInsightReportEmail(req) {
  // function for consolidate insight Ticket Create using SANADKOM API
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.sendAiInsightReportEmail`
  );
  try {
    const bayaanGroup = await getBayaanGroups(req.query.group_name);
    const groupSMEs = await getGroupMembers(bayaanGroup[0].ID);
    const data = groupSMEs.map((member) => ({ name: "", email: member.mail }));
    const previewUrl = `${process.env.PLATFORM_BASEPATH}/insight-report-list`;
    const emailData = {
      recipients: data.map((d) => d.email).join(", "),
      previewUrl,
    };
    const sendEmailResponse = await sendReportEmail(emailData);
    return sendEmailResponse;
  } catch (error) {
    log.error(err);
    throw err;
  }
}

async function ticketStatusUpdateWebhook(req) {
  log.debug(
    `>>>>>Entered microservice.ai-insight-report.controller.ticketStatusUpdateWebhook`
  );
  try {
    const reportData = await model.AiInsightReport.findOne({
      include: [
        {
          model: model.DropdownOption,
          as: "report_type",
          attributes: ["value"],
        }
      ],
      where: {
        id: req.body.report_id,
      },
    });
    if (!reportData) {
      throw new IFPError(400, "No report found with the given ID.");
    }
    if (req.body.is_success === false) {
      await updateReportStatus(
        req.body.report_id,
        "",
        "ticket_creation_failed"
      ); // update report status

    } else {
    await updateReportStatus(
      req.body.report_id,
      req.body.ticket_id,
      "inprogress"
    ); // update report status
  }
    return { message: "Report status updated successfully." };
  } catch (error) {
    log.error(error);
    throw error;
  }
}

/**
 * Controller responsible for updating a given report by
 * it's respective Sanadkom Ticket ID.
 * 
 * Primarily called post approval of ticket at sanadkom to update
 * the Bayaan report's status from 'inprogress' to 'approved'.
 * @param {*} req 
 * @returns 
 */
async function updateReportStatusByTicketId(req) {
  try {
    const { sanadkomTicketId } = req.params;

    const reportData = await model.AiInsightReport.findOne({
      where: {
        ticket_id: sanadkomTicketId,
      },
    });

    if (!reportData) {
      throw new IFPError(400, "No report found with the given ticket ID.");
    }

    await updateReportStatus(reportData.id, sanadkomTicketId, "approved");
    return { message: "Report status updated successfully." };
  } catch (error) {
    log.error(error);
    throw error;
  }
}

module.exports = {
  getKeyDrivers,
  insightReportList,
  consolidateTicketCreate,
  domainTicketCreate,
  reportStatusDropdownList,
  downloadGeneratedPdf,
  updateGeneratedData,
  regenerateReport,
  sendAiInsightReportEmail,
  ticketStatusUpdateWebhook,
  updateReportStatusByTicketId,
};
