const newValuesDataFunctions = require('scad-library').valuesDataAnalytical;
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getValuesDataFromQuery, getComboId } = require('./getGraphData.service');
const constants = require('../../config/constants.json');

const { getMetaFromCMS } = require('../../services/common-service');

async function isNewValuesMeta(valuesMetaArr) {
    try {
        let newValuesId = [
            'scad-estimation',
            'latest-date',
            'previous-quarter-index',
            'latest-date-value',
            'quarter-date-value',
            'percentage-change']
        isExist = valuesMetaArr.filter(e => newValuesId.includes(e.id))
        if (isExist.length > 0) {
            return true;
        } else {
            return false;
        }
    }
    catch (err) {
        throw err;
    }
}

const getNewValuesData = async (valuesMeta, userGroups, indicatorDrivers,req={}) => {
    try {
        let iconIds = []; let valueWithData = []
        valuesMeta.forEach(value => {
            if (value.iconId && value.iconId.length > 0) {
                iconIds.push(value.iconId);
            }
        })

        if(!indicatorDrivers)
            throw new Error(`Invalid request body`);

        const valuesPromises = valuesMeta.map( async (value, index) => {
            if (!Object.keys(indicatorDrivers).length > 0) {
                if (!value.isScadProjection && !value.hasDefault) {
                    indicatorDrivers = constants.indicatorDrivers;
                } else if(value.hasDefault) {
                    indicatorDrivers = constants.quarterlyIndicatorDrivers;
                } else{
                    indicatorDrivers = constants.indicatorDriversPopulation;
                }
            }
            if (value.comboIdTable) {
                let comboId = await getComboId(value, indicatorDrivers)
                let data = await getEachValueData(value, comboId)
                valueWithData[index] = data;
            } else {
                let data = await getEachValueData(value, '')
                valueWithData[index] = data;
            }
        });
        await Promise.all(valuesPromises)
        valueWithData = await getIcons(iconIds, valueWithData, userGroups,req)
        return valueWithData
    } catch (err) {
        throw err;
    }
}

const getIcons = async (iconIds, valueWithData, userGroups,req) => {
    try {
        if (iconIds.length < 1)
            return valueWithData;
        let iconCodes = iconIds.map(i => i).join(',');
        const cmsCompareUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_COUNTRY_LIST}${iconCodes}`
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        let iconData = await getMetaFromCMS(req,cmsLoginUrl, cmsCompareUrl, userGroups)
        valueWithData.forEach(value => {
            if (value.iconId && value.iconId.length > 0) {
                iconData.forEach(data => {
                    if (data.country_id === value.iconId) {
                        value.iconId = data.country_flag;
                    }
                })
            }
        })
        return valueWithData;
    } catch (err) {
        throw err;
    }
}

const getEachValueData = async (valuesMetaObj, comboId) => {
    try {
        let valuesData = await getValuesDataFromQuery(valuesMetaObj, comboId)
        let finalResults, scadEstimate;
        if (valuesMetaObj.isScadProjection && comboId !== valuesMetaObj.PARAMETER_COMBO_ID) {
            scadEstimate = valuesData.filter(e => e.PARAMETER_COMBO_ID === valuesMetaObj.PARAMETER_COMBO_ID);
        }
        if (scadEstimate) {
            finalResults = valuesData.filter(e => !scadEstimate.includes(e));
        }
        valuesData = finalResults ? finalResults : valuesData;
        switch (valuesMetaObj.id) {
            case 'scad-estimation': {
                const data = newValuesDataFunctions.scadEstimation(valuesMetaObj, valuesData, scadEstimate);
                valuesMetaObj = Object.assign(valuesMetaObj, data);
                break;
            }
            case 'latest-date': {
                let data = newValuesDataFunctions.latestDate(valuesMetaObj, valuesData);
                valuesMetaObj = Object.assign(valuesMetaObj, data);
                break;
            }
            case 'latest-date-value': {
                let data = newValuesDataFunctions.latestDateValue(valuesMetaObj, valuesData);
                valuesMetaObj = Object.assign(valuesMetaObj, data);
                break;
            }
            case "previous-from-last-index": {
                const data = newValuesDataFunctions.previousFromLastIndex(valuesData);
                valuesMeta = Object.assign(valuesMeta, data);
                break;
            }
            case 'percentage-change': {
                let data = newValuesDataFunctions.percentageChange(valuesMetaObj, valuesData);
                valuesMetaObj = Object.assign(valuesMetaObj, data);
                break;
            }
            default: {
                log.debug(`Values Function not available`);
                break;
            }
        }
        return valuesMetaObj;
    }
    catch (err) {
        throw err
    }
}

module.exports = { isNewValuesMeta, getNewValuesData };