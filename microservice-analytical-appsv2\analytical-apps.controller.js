const Logger = require("scad-library").logger;
require("dotenv").config();
const fs = require("fs");
const moment = require("moment");
const constants = require("../config/constants.json");
const {
  getGraphData,
  getComboId,
} = require("./services/getGraphData.service");
const { getValuesData } = require("./services/getValuesData.service");
const {
  getNewValuesData,
  isNewValuesMeta,
} = require("./services/getNewValuesData.service");
const {
  processLineChartData,
} = require("./services/chart-services/line-chart");
const {
  getMetaFromCMS,
  getIndicatorValuesIcon,
  getColors,
} = require("../services/common-service");
const {
  getTreeSeries,
} = require("./services/chart-services/tree-map-with-change-chart");
const {
  getSunBurstSeries,
} = require("./services/chart-services/sunburst-with-change-chart");
const { getDashboardData } = require("./services/getDashboard.service");

const crypto = require("crypto");
const { setRedis, getRedis } = require("../services/redis.service");
const { IFPError } = require("../utils/error");
const { getFilterOptions, getCorrelationVisualizationData } = require("./services/executeQuery.service");

class AnalyticalApp {
  constructor() {
    this.cmsResponse = {};
    this.indicatorDrivers = {};
    this.log = new Logger().getInstance();
    this.lang = "";
    this.headers = {};
  }

  async getAnalyticalAppById(req) {
    try{
      this.req = req
      this.contentId = req.params.id;
      this.lang = req.headers["accept-language"] === "en" ? "" : `/${req.headers["accept-language"]}`;
      this.indicatorDrivers = req.body.indicatorDrivers
      if(! this.indicatorDrivers){
        throw new IFPError(400,"Please provide indicatorDrivers field")
      }
      const cmsAnalyticalAppsByIdUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsGroupUrl.CMS_ANALYTICAL_APPS_BYID}${this.contentId}`;
      this.cmsResponse = await this.getCMSData(req, cmsAnalyticalAppsByIdUrl);
      this.type = this.cmsResponse.type.toLowerCase();
      if (this.type == 'geospatialuser/1'){
        this.cmsResponse.type = 'correlation'
        this.type = 'correlation'
      }

      // if(this.type == "internal" && Object.keys(this.indicatorDrivers).length==0 && this.cmsResponse.indicatorDrivers){
      //   let isScadProjection = this.cmsResponse.indicatorDrivers.find(driver=>driver.id=="scad-p0000")
      //   if (!isScadProjection){
      //     this.cmsResponse.indicatorDrivers.forEach(driver=>{
      //       let defaultOption = driver.options.find(option=>option.isSelected==true)
      //       if (!defaultOption){
      //         this.indicatorDrivers[driver.id]=driver.options[0].value
      //       }
      //       else{
      //         this.indicatorDrivers[driver.id]=defaultOption.value
      //       }
      //     })
      //   }
      // }

      let cacheKey = this.generateCacheKey()

      let cacheResults = await this.getCache(cacheKey)
      if (cacheResults)
        return cacheResults;

      switch (this.type) {
        case "internal":
          this.cmsResponse.showSecondLevel = true
          let indicatorDriversValues = Object.keys(this.indicatorDrivers)
          let availableDrivers = []
          this.cmsResponse.indicatorDrivers.forEach(driver=>{
            availableDrivers.push(driver.id)
          })

          indicatorDriversValues.forEach(indDriver=>{
            if(!availableDrivers.includes(indDriver))
              throw new IFPError(400,"Please provide a valid driver")
          })

          await Promise.all([
              this.processValuesMeta(), 
              this.processOverviewMeta()
          ]);

          this.cmsResponse = await this.processInternal(this.cmsResponse);
          this.setCache(cacheKey,this.cmsResponse)
          break;

        case "tableau-internal":
          this.cmsResponse.showSecondLevel = false
          await this.processOverviewMeta()
          await this.updateDashboardDataFromDB()
          this.setCache(cacheKey,this.cmsResponse)
          break;

        case "what if":
          this.cmsResponse.showSecondLevel = true
          await Promise.all([
            this.processValuesMeta(), 
            this.processOverviewMeta()
          ]);

          this.cmsResponse = await this.processInternal(this.cmsResponse);
          this.setCache(cacheKey,this.cmsResponse)
          break;

        case "insights-discovery":
          this.cmsResponse.showSecondLevel = true
          await Promise.all([this.processOverviewMeta(),this.processInsightsDiscovery()])
          await this.updateDashboardDataFromDB()
          this.setCache(cacheKey,this.cmsResponse)
          break;
        case "correlation":
          this.cmsResponse.showSecondLevel = true
          await Promise.all([this.processOverviewMeta(),this.processCorrelation()])
          this.setCache(cacheKey,this.cmsResponse)
          break;
        
        default:
          break
      }

      let langCode = this.lang;
      let tableFields = [
        {
          label: langCode == "" ? "INDICATOR ID" : "معرف المؤشر",
          path: "INDICATOR_ID",
        },
        { label: langCode == "" ? "VALUE" : "قيمة", path: "VALUE" },
        { label: langCode == "" ? "DATE" : "تاريخ", path: "OBS_DT" },
      ];
      this.cmsResponse.tableFields = tableFields

      return this.cmsResponse;
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.getAnalyticalAppById ${err}`)
      throw err;
    }
  }

  generateCacheKey(){
    return 'cmsMetaAnalytical_'+ crypto.createHash('md5').update(JSON.stringify(this.cmsResponse)+JSON.stringify(this.indicatorDrivers)).digest("hex")
  }

  async getCache(cacheKey) {
    let cacheData = await getRedis(cacheKey, this.headers);
    if (cacheData) {
      this.log.info(
        `<<<<<Cache found for microservice-analytical-apps.getAnalyticalAppsById`
      );
      return JSON.parse(cacheData);
    }
    return null;
  }

  async setCache(cacheKey, data) {
    setRedis(cacheKey,JSON.stringify(data), constants.redis.cmsResponseTTL, this.headers);
  }

  async getCMSData(req, url) {
    try {
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      let data = await getMetaFromCMS(req, cmsLoginUrl, url, req.user.groups);
      return data;
    } catch (err) {
      this.log.error(`Error while executing AnalyticalApp.getCMSData ${err}`)
      throw err;
    }
  }

  async updateDashboardDataFromDB() {
    try{
        if (this.type === "tableau-internal") {
          let isValidOverviewValuesMeta = this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.overviewValuesMeta
          if (isValidOverviewValuesMeta){
            this.cmsResponse.indicatorValues.overviewValuesMeta.forEach(
                (overview) => {
                if (overview.id == "latest-date-value") {
                    this.cmsResponse.publication_date = moment(overview.dateStart,"YYYY-MM-DD").format("DD/MM/YYYY");
                    this.cmsResponse.updatedDateFromDB = true;
                }
                }
            );
          }
        }
        else if(this.type == 'internal'){
            let isValidVisualizationMeta = this.cmsResponse.indicatorVisualizations && this.cmsResponse.indicatorVisualizations.visualizationsMeta && this.cmsResponse.indicatorVisualizations.visualizationsMeta.length
            if (isValidVisualizationMeta){
                this.cmsResponse.indicatorVisualizations.visualizationsMeta.forEach(meta=>{
                if (meta.type == 'tree-map-with-change-chart'){
                    this.cmsResponse.publication_date = moment(meta.seriesMeta[0].data[0].OBS_DT, 'YYYY-MM-DD').format('DD/MM/YYYY');
                    this.cmsResponse.updatedDateFromDB = true
                }
                else if (meta.type=='line-chart'){
                    this.cmsResponse.publication_date = moment(meta.seriesMeta[0].xMax, 'YYYY-MM-DD').format('DD/MM/YYYY');
                    this.cmsResponse.updatedDateFromDB = true
                }
                else
                    throw Error('No Check')
                })
            }
        }
        else if(this.type == 'insights-discovery'){
          let defaultViz = this.cmsResponse.visualizations.find(viz=>viz.id == this.cmsResponse.default_visualisation)
          if (defaultViz.indicatorVisualizations && defaultViz.indicatorVisualizations.visualizationsMeta && defaultViz.indicatorVisualizations.visualizationsMeta.length){
            defaultViz.indicatorVisualizations.visualizationsMeta.forEach(meta=>{
              if (meta.type=='line-chart'){
                this.cmsResponse.publication_date = moment(meta.seriesMeta[0].xMax, 'YYYY-MM-DD').format('DD/MM/YYYY');
                this.cmsResponse.updatedDateFromDB = true
              }
            })
          }
        }

    }
    catch(err){
        this.log.error(`Error while setting updated date ${err}`)
    }
  }

  async processValuesMeta(){
    try{
      let isValidValuesMeta = this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0;
      if (isValidValuesMeta) {
        let newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.valuesMeta);
        if (newValuesMeta) {
          await getNewValuesData(this.cmsResponse.indicatorValues.valuesMeta, this.req.user.groups, this.indicatorDrivers,this.req);
        }
      } 
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processValuesMetaWithData ${err}`)
      throw err;
    }
  }

  async processValuesMetaWithData(data,scadEstimate=[]){
    try{
      let isValidValuesMeta = this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0;
      if (isValidValuesMeta){
        let newValuesMeta  = await isNewValuesMeta(this.cmsResponse.indicatorValues.valuesMeta);
        if (isValidValuesMeta && !newValuesMeta)
            if (scadEstimate.length)
                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, scadEstimate);
            else
                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data);
      }
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processValuesMetaWithData ${err}`)
      throw err;
    }
  }

  async processMultiDriversWithScadEstimateValuesMeta(data,scadEstimate,driverMetaIndex){
    try{
      let isValidMultiDriverMeta = this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.multiValuesMeta && this.cmsResponse.indicatorValues.multiValuesMeta.length > 0;
      if (isValidMultiDriverMeta && this.cmsResponse.multiDrivers){
        await getValuesData(this.cmsResponse.indicatorValues.multiValuesMeta[driverMetaIndex], data, scadEstimate)
      }
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processMultiDriversWithScadEstimateValuesMeta ${err}`)
      throw err;
    }
  }

  async processOverviewMeta() {
    try {
      let isValidOverview =
        this.cmsResponse.indicatorValues &&
        this.cmsResponse.indicatorValues.overviewValuesMeta &&
        this.cmsResponse.indicatorValues.overviewValuesMeta.length;

      if (isValidOverview) {
        if(this.type == 'insights-discovery'){
          try {
            await getValuesData( this.cmsResponse.indicatorValues.overviewValuesMeta, {}, {});
          } 
          catch (err) {
          }
        }else{

          let newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.overviewValuesMeta);

          if (!newValuesMeta)
            throw new Error(
              "Cannot process overviewMeta with old calculation types"
            );

          await getNewValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, this.req.user.groups,{},this.req);
        }
      }
    } catch (err) {
      this.log.error(`Error while executing AnalyticalApp.processOverviewMeta ${err}`)
      throw err;
    }
  }

  async processLineChart(visualization,comboId,results,vizIndex){
    try{
        let finalResults, scadEstimate;
        if (visualization.isScadProjection && comboId !== 'P0000') {
            scadEstimate = results.filter(e => e.PARAMETER_COMBO_ID === 'P0000');
        }
        if (scadEstimate) {
            finalResults = results.filter(e => !scadEstimate.includes(e));
        }
        results = finalResults ? finalResults : results;
        const data = await processLineChartData(results, visualization, this.cmsResponse.maxPointLimit);
        await Promise.all([
            this.processValuesMetaWithData(data,scadEstimate),
            this.processMultiDriversWithScadEstimateValuesMeta(data,scadEstimate,vizIndex)
        ])
        return data;
    } catch (err) {
        this.log.error(`Error while executing AnalyticalApp.processLineChart ${err}`)
        throw err;
    }
  }

  async processTreeMapChart(visualization,results){
    try{
      let newVisualization = await getTreeSeries(visualization, results);
      await this.processValuesMetaWithData(newVisualization)
      if (newVisualization.colorCodes && newVisualization.colorsRange && newVisualization.selectRangeBy) {
          await getColors(newVisualization);
      }
      return newVisualization
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processTreeMapChart ${err}`)
      throw err
    }
  }

  async processSunBurstChart(visualization, results){
    try{
      let newVisualization = await getSunBurstSeries(visualization, results);
      await this.processValuesMetaWithData(newVisualization)
      if (newVisualization.colorCodes && newVisualization.colorsRange && newVisualization.selectRangeBy) {
        await getColors(newVisualization);
      }
      return newVisualization
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processSunBurstChart ${err}`)
      throw err
    }
  }

  async processInternal() {
    try{
        let isWhatIf = false    
        if (this.cmsResponse.type == 'What If'){
            this.cmsResponse.type = 'Internal'
            isWhatIf = true
        }
        
        let isValid = (this.cmsResponse.type && this.cmsResponse.type.toLowerCase() === 'internal') || (this.cmsResponse[0] && this.cmsResponse[0].type && this.cmsResponse[0].type.toLowerCase() === 'internal');
        if (!isValid)
            throw new Error('Invalid configuration for the type: internal')
        
        let isIndicatorDrivers = Object.keys(this.indicatorDrivers).length > 0

        if (isIndicatorDrivers){
            let driverKeys = Object.keys(this.indicatorDrivers)
            this.cmsResponse.indicatorDrivers.forEach(indicatorDriver =>{
                driverKeys.forEach(driverKey => {
                    if (indicatorDriver.id == driverKey){
                        let options = indicatorDriver.options
                        options.forEach(option => {
                            option.isSelected = option.value == this.indicatorDrivers[driverKey] ?true:false;                
                        })
                    }
                })
            })
        }

        let isValidVisualizationMeta = this.cmsResponse.indicatorVisualizations && 
                                    this.cmsResponse.indicatorVisualizations.visualizationsMeta &&
                                    this.cmsResponse.indicatorVisualizations.visualizationsMeta.length>0;
        if (!isValidVisualizationMeta)
            throw new Error('Not a valid visualizationMeta')

        const indicatorVisualizationsMeta = this.cmsResponse.indicatorVisualizations.visualizationsMeta;
        this.graphData = this.cmsResponse
        this.graphData.indicatorVisualizations.visualizationsMeta = [];

        const visualizationPromises = indicatorVisualizationsMeta.map(async (visualization,vizIndex) => {

            if (this.cmsResponse.minLimitYAxis) {
                visualization["minLimitYAxis"] = this.cmsResponse.minLimitYAxis;
                delete this.cmsResponse.minLimitYAxis;
            }

            //If empty indicator drivers are passed, default values will be used.
            if (!isIndicatorDrivers){
                if (visualization.isInflation) {
                  let defaultInflationDrivers = {}
                  this.cmsResponse.indicatorDrivers.forEach(driver => {
                    const isSel = driver.options.filter(opt => opt.isSelected == true);
                    if (isSel && isSel[0]) {
                      defaultInflationDrivers[driver.id] = isSel[0].value
                    }
                  })
                  this.indicatorDrivers = defaultInflationDrivers;
                } else if (!visualization.isScadProjection && !visualization.hasDefault) {
                    this.indicatorDrivers = constants.indicatorDrivers;
                } else if (visualization.hasDefault) {
                    this.indicatorDrivers = constants.quarterlyIndicatorDrivers;
                } else {
                    this.indicatorDrivers = constants.indicatorDriversPopulation;
                }
            }

            //Retrieves comboId from the table and uses the comboId to fetch the data from the view
            let comboId = await getComboId(visualization, this.indicatorDrivers);
            let results = await getGraphData(comboId, visualization);

            let chartType;
            if (visualization.type === "dual-line-bar-chart" || visualization.type === "sunburst-with-line-chart") {
                chartType = visualization.subtype;
            } else {
                chartType = visualization.type;
            }

            let newVisualization = {}
            switch (chartType) {
                case 'line-chart':
                    newVisualization = await this.processLineChart(visualization,comboId,results,vizIndex);
                    this.graphData.indicatorVisualizations.visualizationsMeta.push(newVisualization);
                    break;
                case "tree-map-with-change-chart": 
                    newVisualization = await this.processTreeMapChart(visualization,results);
                    this.graphData.indicatorVisualizations.visualizationsMeta.push(newVisualization);
                    break;
                case "sunburst-with-change-chart":
                    newVisualization = await this.processSunBurstChart(visualization,results)
                    this.graphData.indicatorVisualizations.visualizationsMeta.push(newVisualization);
                    break;
            }
                    
        })

        await Promise.all(visualizationPromises)

        this.graphData.indicatorVisualizations.visualizationsMeta = this.graphData.indicatorVisualizations.visualizationsMeta.sort((a, b) => a.sortOrder - b.sortOrder);
        this.cmsResponse = this.graphData;

        if (isWhatIf)
            this.cmsResponse.type == 'What If'

        return this.cmsResponse;
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processInternal ${err}`)
        throw err;
    }
  }

  async processInsightsDiscovery() {
    try{
      this.cmsResponse.related_items = this.cmsResponse.related_items.replace(" ","")
      this.cmsResponse.ifp_indicators = this.cmsResponse.related_items.split(',')
      delete this.cmsResponse.related_items;
      this.cmsResponse = await getDashboardData(this.cmsResponse, this.req);
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processInsightsDiscovery ${err}`)
      throw err;
    }

    return this.cmsResponse;
  }

  async processCorrelation(){
    try{
      if (this.cmsResponse.filterPanel) {
        this.cmsResponse.filterPanel = await this.fetchFilterOptions(this.cmsResponse.filterPanel);
      }

      const correlationFilters = this.req.body.indicatorDrivers

      let isValidVisualizationMeta = this.cmsResponse.indicatorVisualizations && 
                                      this.cmsResponse.indicatorVisualizations.visualizationsMeta &&
                                      this.cmsResponse.indicatorVisualizations.visualizationsMeta.length>0;
      if (!isValidVisualizationMeta)
          throw new Error('Not a valid visualizationMeta')

      await Promise.all(this.cmsResponse.indicatorVisualizations.visualizationsMeta.map(async visualization => {
        let visualizationData = await getCorrelationVisualizationData(visualization, correlationFilters,this.cmsResponse.filterPanel);
        visualization.seriesMeta[0].data = visualizationData
      }));
      return this.cmsResponse;
    }
    catch(err){
      this.log.error(`Error while executing AnalyticalApp.processCorrelation ${err}`)
      throw err;
    }
  }

  async fetchFilterOptions(filterPanel) {
    const { viewName, dimension, properties } = filterPanel;
  
    await Promise.all(properties.map(async (property) => {
      const optionsData = await getFilterOptions(viewName, dimension, property);
      property['options'] = optionsData.map(opt => opt.VALUE);
    }));

    return filterPanel;
  }
  

  async getMobileDashboards(req) {
    this.log.debug(`>>>>>Entered AnalyticalApp.getMobileDashboards`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        let page = req.query.page ? req.query.page : 1;
        let limit = req.query.limit ? req.query.limit : 10;
        const cmsMobileDashboardsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_MOBILE_DASHBOARDS_URL}&page=${page}&limit=${limit}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsMobileDashboardsUrl, req.user.groups);
        this.log.debug(`<<<<<Exited AnalyticalApp.getMobileDashboards successfully `);
        return data;
    } catch (err) {
        this.log.error(`<<<<<Exited AnalyticalApp.getMobileDashboards with error ${err}`);
        throw err;
    }
  }

  // Icons for Geo Revamp 
  async getGeoRevampIcons(req) {
    this.log.debug(`>>>>>Entered AnalyticalApp.getGeoRevampIcons`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        let page = req.query.page ? req.query.page : 1;
        let limit = req.query.limit ? req.query.limit : 10;
        const cmsGetGeoRevampIconsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_GeoRevamp_Icons_URL}?page=${page}&limit=${limit}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsGetGeoRevampIconsUrl, req.user.groups);
        this.log.debug(`<<<<<Exited AnalyticalApp.getGeoRevampIcons successfully `);
        return data;
    } catch (err) {
        this.log.error(`<<<<<Exited AnalyticalApp.getGeoRevampIcons with error ${err}`);
        throw err; 
    }
  }

  async getPowerBIDashboards(req) {
    this.log.debug(`>>>>>Entered AnalyticalApp.getPowerBIDashboards`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        let page = req.query.page ? req.query.page : 1;
        let limit = req.query.limit ? req.query.limit : 10;
        const cmsMobileDashboardsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_MOBILE_POWER_BI_URL}&page=${page}&limit=${limit}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsMobileDashboardsUrl, req.user.groups);
        this.log.debug(`<<<<<Exited AnalyticalApp.getPowerBIDashboards successfully `);
        return data;
    } catch (err) {
        this.log.error(`<<<<<Exited AnalyticalApp.getPowerBIDashboards with error ${err}`);
        throw err;
    }
  }
  
}


module.exports = AnalyticalApp