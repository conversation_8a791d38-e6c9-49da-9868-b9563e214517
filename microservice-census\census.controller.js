const Logger = require('scad-library').logger;
const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const constants = require('../config/constants.json');

const log = new Logger().getInstance();
/**
 * function to get whats-new content from CMS
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function getCensus(req) {
  log.debug('>>>>>Entered census-microservice.census.controller.getCensus');
  return new Promise(async (resolve, reject) => {
    try{
      // let lang = req.headers["accept-language"] || "en";
      // // If language is "en", setting it to an empty string
      // const langPrefix = lang === "en" ? "" : lang;
      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;

      const cmsCensusUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CENSUS_URL}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

      // Fetching data from CMS
      const cmsResponse = await getMetaFromCMS(req, cmsLoginUrl, cmsCensusUrl, req.user.groups)

      // Define keys to modify in CMS response
      const cmsPathKeys = ["image", "logo"]
      // Prefixing paths with base path URL
      cmsResponse.forEach(item => {
        Object.keys(item).forEach(key => {
          if (cmsPathKeys.includes(key) && item[key]) {
            item[key] = `${process.env.CMS_BASEPATH_URL}${item[key]}`;
          }
        })
      })
      return resolve(cmsResponse[0])
    }
    catch(err){
      reject(err)
    }
  });
}


async function authCensus(req) {
  log.debug(`>>>>>Entered microservice.census.controller.authCensus`);
  try {
      this.lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      const cmsAnalyticalAppsUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsGroupUrl.CMS_CENSUS_APPS}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      this.cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsAnalyticalAppsUrl, req.user.groups);
      let auth_status = false
      if (this.cmsResponse){
          this.cmsResponse.forEach( element => {
              if (element.type == "Census"){
                  auth_status = true
                  
              }

          })
          if (auth_status)
            return {"message":"Authorized","status":auth_status}

          return {"message":"Unauthorized","status":auth_status}
      }
      else{
          return {"message":"Unauthorized","status":auth_status}
      }
      
  } catch (err) {
      log.error(`<<<<<Exited microservice.census.controller.authCensus on getting CMS data with error ${err}`);
      if (err.status == 404)
          return {"message":"Unauthorized","status":false}
      else{
          throw err;
      }
  }
}

module.exports = { getCensus, authCensus };
