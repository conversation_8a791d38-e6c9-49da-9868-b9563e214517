const express = require('express');
const router = new express.Router();
const interactionController = require('../microservice-interaction/interaction.controller');
const indicatorTcController = require('../microservice-indicator-tc/indicator-tc.controller');
const homeController = require('../microservice-home/home.controller');
const { validateIndicatorTC } = require('./validators/indicator-tc.validator');
const downloadController = require('../microservice-download/download.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/home',async (req,res,next) =>{
  try {
    const data = await homeController.getHome(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } 
  catch (err) {
    
    log.error(`Error fetching data for home content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/interaction', async (req, res, next) => {
    try {
        const data = await interactionController.setInteraction(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for analytical-apps content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/indicator-tc',validateIndicatorTC, async (req, res, next) => {
    try {
      const data = await indicatorTcController.getIndicatorTCStatus(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for indicator-tc content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/indicator-tc/set',validateIndicatorTC, async (req, res, next) => {
    try {
      const data = await indicatorTcController.setIndicatorTC(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for indicator-tc content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });


  router.get('/download', async (req, res, next) => {
    try {
      await downloadController.downloadCMSFile(req,res)
      next();
    } catch (err) {
      
      log.error(`Error fetching data for indicator-tc content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
