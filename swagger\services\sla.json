{"paths": {"/content-type/sla/check": {"get": {"tags": ["SLA"], "summary": "Retrieves SLA status of a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message describing the user's access status to the platform."}, "status": {"type": "boolean", "description": "A boolean indicating the user's access status, where true means the user has access."}}, "required": ["message", "status"], "description": "Schema for a response indicating a user's access status to a platform."}}}}}}}}}