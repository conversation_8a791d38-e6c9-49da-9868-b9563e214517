const { S3Client } = require("@aws-sdk/client-s3");

const awsClient = new S3Client({
  credentials: {
    accessKeyId: process.env.S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.S3_SECRET_KEY,
  },
  region: process.env.S3_REGION,
  endpoint: process.env.S3_ENDPOINT,
  forcePathStyle: true, // ensures path style access which is required to custom s3 compatible storages
});

module.exports = {
  awsClient: awsClient,
};
