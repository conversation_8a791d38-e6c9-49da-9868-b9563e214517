{"paths": {"/content-type/header/": {"get": {"tags": ["Header"], "summary": "Retrieve header information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "The main title of the site or page."}, "sub_title": {"type": "string", "description": "A subtitle providing additional information."}, "site_logo": {"type": "string", "format": "uri", "description": "URL to the site's logo."}, "site_logo_light": {"type": "string", "format": "uri", "description": "URL to the site's light theme logo."}, "navigation_menu": {"type": "array", "items": {"type": "object", "properties": {"menu_label": {"type": "string", "description": "Label for the menu item."}, "menu_link": {"type": "string", "description": "Link for the menu item."}, "show_dropdown": {"type": "string", "enum": ["Yes", "No"], "description": "Indicator if the menu item should show a dropdown."}}, "required": ["menu_label", "menu_link", "show_dropdown"]}, "description": "List of navigation menu items."}, "language": {"type": "array", "items": {"type": "string"}, "description": "List of available languages."}, "notification_icon": {"type": "string", "format": "uri", "description": "URL to the notification icon."}, "notification_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme notification icon."}, "language_icon": {"type": "string", "format": "uri", "description": "URL to the language icon."}, "language_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme language icon."}, "accessibility_icon": {"type": "string", "format": "uri", "description": "URL to the accessibility icon."}, "accessibility_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme accessibility icon."}, "search_button_label": {"type": "string", "description": "Label for the search button."}, "search_icon": {"type": "string", "format": "uri", "description": "URL to the search icon."}, "search_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme search icon."}, "search_placeholder": {"type": "string", "description": "Placeholder text for the search input."}, "dark_theme_dark_icon": {"type": "string", "format": "uri", "description": "URL to the dark theme icon for dark mode."}, "dark_theme_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme icon for dark mode."}, "light_theme_dark_icon": {"type": "string", "format": "uri", "description": "URL to the dark theme icon for light mode."}, "light_theme_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme icon for light mode."}, "show_accessibility": {"type": "boolean", "description": "Indicator if the accessibility options should be shown."}, "show_language": {"type": "boolean", "description": "Indicator if the language switcher should be shown."}, "show_notifications": {"type": "boolean", "description": "Indicator if the notifications should be shown."}, "show_themes": {"type": "boolean", "description": "Indicator if the theme switcher should be shown."}, "site_slogan": {"type": "string", "format": "uri", "description": "URL to the site's slogan image."}, "site_slogan_light": {"type": "string", "format": "uri", "description": "URL to the site's light theme slogan image."}, "languages": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "The language title."}, "value": {"type": "string", "description": "The language value or code."}}, "required": ["title", "value"]}, "description": "List of language options with titles and values."}}, "required": ["title", "sub_title", "site_logo", "site_logo_light", "navigation_menu", "language", "notification_icon", "notification_light_icon", "language_icon", "language_light_icon", "accessibility_icon", "accessibility_light_icon", "search_button_label", "search_icon", "search_light_icon", "search_placeholder", "dark_theme_dark_icon", "dark_theme_light_icon", "light_theme_dark_icon", "light_theme_light_icon", "show_accessibility", "show_language", "show_notifications", "show_themes", "site_slogan", "site_slogan_light", "languages"], "description": "Schema for a response detailing site configuration, including titles, logos, navigation menu, and language options."}}}}}}}}}