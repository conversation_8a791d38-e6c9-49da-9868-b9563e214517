const { getMetaFromCMS, getMetaFromCMSAdmin,getListOfOfficialIndicator } = require('../services/common-service');
require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { 
    addAppsToMyAppsData,
    removeAppsFromMyAppsData,
    removeCompareAppsFromMyAppsData,
    removeCompareAppsFromSourceData,
    draftMyAppsData,
    idListMyAppsData,
    getDraftMyAppsData,
    createDraftMyAppsData,
    getDraftNodesMyAppsData,
    deleteDraftMyAppsNodesData,
    deleteDraftMyAppsData,
    removeAppsFromDraftMyAppsData,
    deleteDraftMasterMyAppsData,
    deleteDraftNodesMyAppsData,
    dragMyAppsData,
    deleteDragMyAppsData,
    storeDragMyAppsData,
    deleteDragByNodesMyAppsData,
    getHierarchyData,
    createDashboardData,
    deleteDashboardData,
    detailDashboardData,
    myAppsFilterData,
    getDashboardsData,
    removeDashboardNodesData,
    createShareApp,
    listMyAppsData,
    createShareDragNodes,
    createShareAppNodes,
    getShareApp,
    dragShareMyAppsData,
    getShareAppsListData,
    createShareNotificationsData,
    createShareMyAppsRequestData,
    getShareMyAppsRequestData,
    deleteShareData,
    deleteShareDragData,
    deleteShareNodesData,
    readShareAppsData,
 } = require('./services/executeQuery.service');
const {
    getInteractionData
} =  require('../services/executeQuery.service');
let constants = require('../config/constants.json');
const uuid = require('uuid');
const moment = require('moment')
const { constructCmsUrl, getLangPrefix } = require('../utils/functions');
const { sendShareMyAppsEmail, requestAccessShareMyAppsEmail } = require('./services/sendEmail.service');
const { getCompareData, createCompareAppData } = require('../microservice-indicator-compare/services/executeQuery');
const { IFPError } = require('../utils/error');
const { validateEmailContent } = require('../services/helpers/helper');

/**
 * function to get notifications
 * @param {*} req 
 */
async function listMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.cntroller.listMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const lang = getLangPrefix(req)
        const language = req.headers["accept-language"].toUpperCase();
        const cmsNodeClassificationUrl = constructCmsUrl(process.env.CMS_BASEPATH,lang,constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST);
        const cmsClassificationList = constructCmsUrl(process.env.CMS_BASEPATH,lang,constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST);
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        let officialNodeMaps = await getListOfOfficialIndicator(req);

        //Asynchronous calls to CMS and Database
        const [trackerData,nodeMapCMS,classificationData,dragData] = await Promise.all([
            getInteractionData(userEmail),
            getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
            getMetaFromCMS(req,cmsLoginUrl, cmsClassificationList, req.user.groups),
            dragMyAppsData(userEmail)
        ]);
        let officialMapper ={}
       
        const trackerNodeIds = new Set(dragData.map(item => item.NODE_ID.toString()));
        officialNodeMaps.forEach(({ node_id, title, domain, theme, subtheme, product }) => {
            const nodeId = node_id.toString();
            if (trackerNodeIds.has(nodeId)) {
                officialMapper[nodeId] = {
                    title,
                    content_type: "scad_official_indicator",
                    domains: [domain],
                    domain,
                    theme,
                    subtheme,
                    product,
                    category: "Official Statistics",
                    content_classification: "Official Statistics",
                    app_type: null,
                };
            }
        });
        // Remove all scad_official_indicator entries from nodeMapCMS
        Object.keys(nodeMapCMS).forEach(key => {
            if (nodeMapCMS[key].content_type === 'scad_official_indicator') {
                delete nodeMapCMS[key];
            }
        });

        // Merge officialMapper into nodeMapCMS
        Object.assign(nodeMapCMS, officialMapper);

        if (!dragData.length)
            return {"stateChange":0,"classificationNodes":{},"data":[]}

        let classificationNodesData = {}
        let stateChange = 0
        let classificationMap = {}
        let nodeClassificationMap = {}

        //Creates a Map for Classification with the keys
        let classifications =Object.values(classificationData.classification)
        classifications.forEach(classification=>{
            nodeClassificationMap[classification.name] = classification.key
            let key = classification.key
            if (key == 'official_statistics')
                classificationMap['official-insights'] = classification.name
            if (key == 'experimental_statistics')
                classificationMap['innovative-insights'] = classification.name
        })

        
        //Assign hierarchy to screener nodes
        let hierarchyInfo = {}
        screenerNodes = dragData.filter(node=>['official-insights','innovative-insights'].includes(node.CONTENT_TYPE)).map(node =>{return node.NODE_ID})
        if (screenerNodes.length){
            let hierarchyData = await getHierarchyData(screenerNodes,language)
            hierarchyData.forEach(hData => {
                hierarchyInfo[hData.INDICATOR_ID] = hData
            })
        }

        //Creates a tracker map for getting the node interaction information
        let trackerMap = {}
        trackerData.forEach(node =>{
            trackerMap[node.NODE_ID] = node.STATUS
        })

        let result = [];
        if (dragData) {
            
            let myApps = {}
            let domainInteractionFlag = {}

            dragData.forEach(app => {

                // Fix for missing values in app
                if (!app['TITLE'] && app['NODE_ID'] in nodeMapCMS) {
                    log.info(`App with NODE_ID ${app['NODE_ID']} does not have a TITLE. Using title from nodeMapCMS.`);
                    app['TITLE'] = nodeMapCMS[app['NODE_ID']].title || "Untitled App";
                }

                let domain, sub_domain, sub_theme, product, classification, map;
                const nodeId = app['NODE_ID'];
                const contentType = app['CONTENT_TYPE'];

                if (nodeId in nodeMapCMS || ['official-insights','innovative-insights','compare-statistics','Spatial-Analytics'].includes(contentType)) {

                    app.INTERACTION_STATUS = trackerMap.hasOwnProperty(nodeId) ? trackerMap[nodeId] : 0;

                    if (contentType == 'compare-statistics'){
                        domain = 'Compare Statistics'
                        classification = 'Compare'
                    }
                    else if (contentType == 'Spatial-Analytics'){
                        domain = app['DOMAIN'] || 'Spatial Analytics'
                        classification = app['CLASSIFICATION'] || 'Spatial Analytics'
                    }

                    else if (['official-insights','innovative-insights'].includes(contentType)){
                        let nodeHierarchy = hierarchyInfo[app['NODE_ID']]
                        domain = nodeHierarchy.TOPIC
                        sub_domain = nodeHierarchy.THEME
                        sub_theme = nodeHierarchy.SUB_THEME
                        product = nodeHierarchy.PRODUCT
                        app['TITLE'] = nodeHierarchy.INDICATOR_NAME
                        classification = classificationMap[contentType]
                        let dynamicClassification =  contentType == 'official-insights'?'Official Insights':'Innovative Insights'

                        if (!(dynamicClassification in classificationNodesData)){
                            classificationNodesData[dynamicClassification] = []
                        }

                        classificationNodesData[dynamicClassification].push(nodeId)
                    }
                    else{
                        map = nodeMapCMS[nodeId]
                        domain = map["domain"]
                        sub_domain = map["theme"]
                        sub_theme = map["subtheme"]
                        product = map["product"]
                        classification = map["content_classification"]
                        // delete app['TITLE']
                        if (!(nodeClassificationMap[classification] in classificationNodesData)){
                            classificationNodesData[nodeClassificationMap[classification]] = []
                        }

                        classificationNodesData[nodeClassificationMap[classification]].push(app.NODE_ID)
                        
                        // Add app type
                        app.APP_TYPE = map.app_type || null;
                    }

                    app['PUBLISH_DATE'] = app['INSERT_DT']

                    const appStateModified = app['MOD_FLAG']
                    if (appStateModified){
                        stateChange = stateChange || appStateModified
                        domain = app['DOMAIN']
                        classification = app['CLASSIFICATION']
                        // app["TITLE"] = app['MOD_TITLE']
                    }
                    
                    if (!domainInteractionFlag.hasOwnProperty(domain)) {
                        domainInteractionFlag[domain] = 0;
                    }
                    
                    if (classification) {
                        
                        try {

                            if (domain && !(domain in myApps))
                                myApps[domain] = { "items": {}, "sub_domains": {} }

                            if (sub_domain && !(sub_domain in myApps[domain]["sub_domains"]))
                                myApps[domain]["sub_domains"][sub_domain] = { "items": {}, "sub_themes": {} }

                            if (sub_theme && !(sub_theme in myApps[domain]["sub_domains"][sub_domain]["sub_themes"]))
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme] = { "items": {}, "products": {} }

                            if (product && !(product in myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"]))
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product] = { "items": {} }                                     


                            if (domain && sub_domain && sub_theme && product) {
                                if (!myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product]["items"][classification])
                                    myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product]["items"][classification] = []
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product]["items"][classification].push(app)                                    
                            }
                            else if (domain && sub_domain && sub_theme) {
                                if (!myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["items"][classification])
                                    myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["items"][classification] = []
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["items"][classification].push(app)
                            }
                            else if (domain && sub_domain) {
                                if (!myApps[domain]["sub_domains"][sub_domain]["items"][classification])
                                    myApps[domain]["sub_domains"][sub_domain]["items"][classification] = []
                                myApps[domain]["sub_domains"][sub_domain]["items"][classification].push(app)
                            }
                            else if (domain) {
                                if (!myApps[domain]["items"][classification])
                                    myApps[domain]["items"][classification] = []
                                myApps[domain]["items"][classification].push(app)
                            }
                            domainInteractionFlag[domain] += app.INTERACTION_STATUS
                        } catch (exp) {

                        }
                    }
                }
            });

            result = Object.entries(myApps).map(([domain_name, domain_value]) => {
                domain_items = Object.entries(domain_value['items']).map(([appType, appValue]) => {
                    const domainItemsObj = {
                        name: appType,
                        nodes: appValue
                    }
                    return domainItemsObj;
                })
                const domainObj = {
                    name: domain_name,
                    items: domain_items,
                    interaction_status: domainInteractionFlag[domain_name],
                    sub_domains: Object.entries(domain_value.sub_domains).map(([sub_domain_name, sub_domain_value]) => {
                        sub_domain_items = Object.entries(sub_domain_value['items']).map(([appType, appValue]) => {
                            const domainItemsObj = {
                                name: appType,
                                nodes: appValue
                            }
                            return domainItemsObj;
                        })
                        const subDomainObj = {
                            name: sub_domain_name,
                            items: sub_domain_items,
                            sub_themes: Object.entries(sub_domain_value.sub_themes).map(([sub_theme_name, sub_theme_value]) => {
                                sub_theme_items = Object.entries(sub_theme_value['items']).map(([appType, appValue]) => {
                                    const domainItemsObj = {
                                        name: appType,
                                        nodes: appValue
                                    }
                                    return domainItemsObj;
                                })
                                const subThemeObj = {
                                    name: sub_theme_name,
                                    items: sub_theme_items,
                                    products: Object.entries(sub_theme_value.products).map(([product_name, product_value]) => {
                                        product_items = Object.entries(product_value['items']).map(([appType, appValue]) => {
                                            const domainItemsObj = {
                                                name: appType,
                                                nodes: appValue
                                            }
                                            return domainItemsObj;
                                        })
                                        const productObj = {
                                            name: product_name,
                                            items: product_items
                                        }
                                        return productObj;
                                    })
                                }
                                return subThemeObj;
                            })
                        }
                        return subDomainObj;
                    })
                }
                return domainObj
            })
        }
        
        return {"stateChange":stateChange,"classificationNodes":classificationNodesData,"data":result}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.listMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to get added app list
 * @param {*} req 
 */
async function idListMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.addSingleAppToMyApps`);
    try {
        const userEmail = req.user.preferred_username
        let data = await idListMyAppsData(userEmail)
        let response = data.map(node => { return node.NODE_ID })
        return response
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.addSingleAppToMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to add a single app
 * @param {*} req 
 */
async function addAppsToMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.addSingleAppToMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const nodes = req.body
        let addData = await addAppsToMyAppsData(nodes, userEmail)
        return addData;
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.addSingleAppToMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to remove a single app
 * @param {*} req 
 */
async function removeAppsFromMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.removeAppsFromMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const nodes = req.body
        let data = await removeAppsFromMyAppsData(nodes, userEmail)

        return data;
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.removeAppsFromMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to remove a single app
 * @param {*} req 
 */
async function removeCompareAppsFromMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.removeCompareAppsFromMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const nodes = req.body
        let data = await removeCompareAppsFromMyAppsData(nodes, userEmail)

        return data
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.removeCompareAppsFromMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to draft apps
 * @param {*} req 
 */
async function draftMyApps(req) {
log.debug(`>>>>>Entered microservice-myapps.controller.removeAppsFromMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const reqData = req.body
        let addData = reqData["add"]

        let removeData = reqData["remove"]
        let reqDraftId = reqData['draftId']
        let draftId;
        if (!(reqDraftId && reqDraftId.length)){
            draftId = String(uuid.v4());
            await createDraftMyAppsData(draftId, userEmail)
        }
        else{
            draftId = reqDraftId
            let deleteData = addData.concat(removeData).map(node => {return node.id})
            await removeAppsFromDraftMyAppsData(deleteData,draftId, userEmail)
        }

        if (addData.length){
            await draftMyAppsData(addData, userEmail,draftId,"ADD")
        }
        if (removeData.length){
            await draftMyAppsData(removeData, userEmail,draftId,"REMOVE")
        }
        
        return {"message":"Drafted successfully"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.removeAppsFromMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to submit apps
 * @param {*} req 
 */
async function submitMyApps(req) {
log.debug(`>>>>>Entered microservice-myapps.controller.submitMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const reqData = req.body
        let addData = reqData["add"]

        let otherData =  reqData["remove"].filter(node => node.type != 'compare')
        let dragOtherData = otherData
        let compareData =  reqData["remove"].filter(node => node.type == 'compare')
        otherData = otherData.map(node =>{ return node.id}).concat(addData.map(node =>{ return node.id}))
        compareData = compareData.map(node =>{ return `'${node.id}'`})

        let reqDraftId = reqData['draftId']
        if (reqDraftId && reqDraftId.length){
            await deleteDraftMyAppsNodesData(reqDraftId, userEmail)
            await deleteDraftMyAppsData(reqDraftId, userEmail)
        }
        if (otherData.length){
            await removeAppsFromMyAppsData(otherData, userEmail)
            if (dragOtherData.length){
                let nodes = dragOtherData.map(node => `'${node.id}'`)
                await deleteDragByNodesMyAppsData(nodes,userEmail)
            }
        }

        if (compareData.length){
                await removeCompareAppsFromMyAppsData(compareData, userEmail)
                await removeCompareAppsFromSourceData(compareData, userEmail)
                await deleteDragByNodesMyAppsData(compareData,userEmail)
        }
        
        if (addData.length){
            await addAppsToMyAppsData(addData, userEmail)
        }
        return {"message":"Your request has been processed","status":"success"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.submitMyApps with error ${err}`);
        throw err;
    }
}

async function domainNodesListMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.domainNodesListMyApps`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsDomainDetailUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAIN_DETAIL}${req.params.id}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsDomainDetailUrl, req.user.groups);

        const analyticalApps = constants.classifications.analyticalApps
        const reports = constants.classifications.reports

        const domain = Object.values(Object.values(data.domains)[0])[0]
        domain.dark_icon = `${process.env.CMS_BASEPATH_URL}${domain.dark_icon}`
        domain.light_icon = `${process.env.CMS_BASEPATH_URL}${domain.light_icon}`

        const detailData = Object.values(data.data).map(classification => {
            const classificationObj = {
                name: classification.classification,
                light_icon: `${process.env.CMS_BASEPATH_URL}${classification.light_icon}`,
                dark_icon: `${process.env.CMS_BASEPATH_URL}${classification.dark_icon}`,
                count: classification.node_count ? classification.node_count : 0,
                nodes: []
            }

            if ([analyticalApps, reports].includes(classification.classification)) {
                classificationObj.nodes = classification.domains.length ? classification.domains[0].nodes : []
                classificationObj.showTree = false
            }
            else {
                classificationObj.showTree = true
                if (classification.domains) {
                    let domain = Object.values(classification.domains)[0]
                    classificationObj.nodes = Object.values(domain.subdomains).map(subdomain => {
                        const subdomainObj = {
                            name: subdomain.name,
                            subthemes: Object.values(subdomain.subthemes).map(subtheme => {
                                const subthemeObj = {
                                    name: subtheme.name,
                                    products: Object.values(subtheme.products).map(product => {
                                        const productObj = {
                                            name: product.name,
                                            nodes: product.nodes
                                        }
                                        return productObj
                                    })
                                }
                                return subthemeObj;
                            })
                        }
                        return subdomainObj
                    })
                }
            }
            return classificationObj;
        })

        return {
            domain: domain,
            data: detailData
        }

    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.domainNodesListMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to fetch draft apps
 * @param {*} req 
 */
async function draftListMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.draftListMyApps`);
    try {
        const userEmail = req.user.preferred_username
        let data = await getDraftMyAppsData(userEmail)
        let counter = 1
        let drafts = data.map(draft => { 
            const draftObj = {
                id: draft.ID,
                name: `Draft ${counter} - ${moment(draft.INSERT_DT, "ddd MMM DD YYYY HH:mm:ss [GMT]ZZ").format("ddd MMM DD YYYY HH:mm")}`
            }
            counter += 1
            return draftObj
        })
        
        return drafts
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.draftListMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to fetch draft apps
 * @param {*} req 
 */
async function draftNodeListMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.draftListMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const id = req.params.id
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const language = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
        const cmsNodeClassificationUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        const [nodeMapCMS,data] = await Promise.all([
            getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
            getDraftNodesMyAppsData(id,userEmail)
        ])
        
        if(data.length<1)
            throw new IFPError(400,'The requested draft not found')

        let hierarchyInfo = {}
        let screenerNodes = data.filter(node=>['official-insights','innovative-insights'].includes(node.CONTENT_TYPE)).map(node =>{return node.NODE_ID})
        if (screenerNodes.length){
            let hierarchyData = await getHierarchyData(screenerNodes,language)
            hierarchyData.forEach(hData => {
                hierarchyInfo[hData.INDICATOR_ID] = hData
            })
        }

        let result = {}
        data.forEach(node => {
            let domain;
            if (node.NODE_ID in nodeMapCMS) {
                nodeMap = nodeMapCMS[node.NODE_ID]
                
                if (nodeMap.domain)
                    domain = nodeMap.domain
            }
            else if (['official-insights','innovative-insights'].includes(node.CONTENT_TYPE)){
                domain = hierarchyInfo[node.NODE_ID].TOPIC
            }
            
            else{
                domain = 'Unknown Domain'
            }

            if (domain) {
                if (!(domain in result)) {
                    result[domain] = []
                }

                result[domain].push(node)
            }

        })
        
        return result
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.draftListMyApps with error ${err}`);
        throw err;
    }
}

/**
 * function to delete draft
 * @param {*} req 
 */
async function draftDeleteMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.draftListMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const draftId = req.params.id
        let data = await getDraftNodesMyAppsData(draftId,userEmail)
        if (data.length < 1){
            throw new IFPError(404,`No draft found with this ID: ${draftId}`)
        }

        await deleteDraftMasterMyAppsData(draftId,userEmail)
        await deleteDraftNodesMyAppsData(draftId,userEmail)
                    
        return {"message":"Draft deleted successfully"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.draftListMyApps with error ${err}`);
        throw err;
    }
}

async function dragStateSaveMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.draftListMyApps`);
    try {
        const userEmail = req.user.preferred_username
        const stateData = []
        const data = req.body
        let count=1
        data.forEach(domain => {
            const classifications = domain.nodeData
            classifications.forEach(classification => {
                classification.nodes.forEach(node =>{
                    const nodeObj = {
                        NODE_ID: node.NODE_ID,
                        TITLE: node.TITLE,
                        DOMAIN: domain.name,
                        CLASSIFICATION: classification.name,
                        SORT_ORDER: count,
                        USER_EMAIL: userEmail
                    }
                    stateData.push(nodeObj)
                    count += 1
                })
            })
        })

        await deleteDragMyAppsData(userEmail)
        await storeDragMyAppsData(stateData)

        return {"message":"State recorded successfully"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.dragStateSaveMyApps with error ${err}`);
        throw err;
    }
}

async function removeDragStateSaveMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.removeDragStateSaveMyApps`);
    try {
        const userEmail = req.user.preferred_username
        await deleteDragMyAppsData(userEmail)

        return {"message":"State removed successfully"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.removeDragStateSaveMyApps with error ${err}`);
        throw err;
    }
}

async function createDashboard(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.createDashboard`);
    try {
        const userEmail = req.user.preferred_username

        const dashboardId = String(uuid.v4());
        const title = req.body.title
        const domains = req.body.nodes
        const cleanedNodes = []
        let counter = 1

        domains.forEach(domain=>{
            const classifications = domain.items
            classifications.forEach(classification => {
                classification.nodes.forEach(node =>{
                    const nodeObj = {
                        ID:dashboardId,
                        TITLE: title,
                        NODE_ID: node.NODE_ID,
                        NODE_TITLE: node.TITLE,
                        DOMAIN: domain.name,
                        CONTENT_TYPE:node.CONTENT_TYPE,
                        CLASSIFICATION: classification.name,
                        SORT_ORDER: counter,
                        USER_EMAIL: userEmail
                    }
                    cleanedNodes.push(nodeObj)
                    counter += 1
                })
            })
        })

        await createDashboardData(cleanedNodes)

        return {"message":"Dashboard created successfully", id: dashboardId}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.createDashboard with error ${err}`);
        throw err;
    }
}

async function editDashboard(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.editDashboard`);
    try {
        const userEmail = req.user.preferred_username

        const dashboardId = req.params.id
        const title = req.body.title
        const domains = req.body.nodes
        const cleanedNodes = []
        let counter = 1

        domains.forEach(domain=>{
            const classifications = domain.items
            classifications.forEach(classification => {
                classification.nodes.forEach(node =>{
                    const nodeObj = {
                        ID:dashboardId,
                        TITLE: title,
                        NODE_ID: node.NODE_ID,
                        NODE_TITLE: node.TITLE,
                        DOMAIN: domain.name,
                        CLASSIFICATION: classification.name,
                        SORT_ORDER: counter,
                        USER_EMAIL: userEmail
                    }
                    cleanedNodes.push(nodeObj)
                    counter += 1
                })
            })
        })

        await deleteDashboardData(dashboardId)
        await createDashboardData(cleanedNodes)

        return {"message":"Dashboard edited successfully"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.editDashboard with error ${err}`);
        throw err;
    }
}

async function removeDashboardNodes(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.removeDashboardNodes`);
    try {
        const userEmail = req.user.preferred_username
        const nodes = req.body.nodes
        const dashboardId = req.params.id
        await removeDashboardNodesData(nodes,dashboardId,userEmail)
        return {"message":"Dashboard nodes removed successfully"}
    }
    catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.removeDashboardNodes with error ${err}`);
        throw err;
    }
}


async function deleteDashboard(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.deleteDashboard`);
    try {
        
        const dashboardId = req.params.id
        await deleteDashboardData(dashboardId)

        return {"message":"Dashboard deleted successfully"}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.deleteDashboard with error ${err}`);
        throw err;
    }
}

async function detailDashboard(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.detailDashboard`);
    try {
        
        const userEmail = req.user.preferred_username
        const dashboardId = req.params.id
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const language = req.headers["accept-language"].toUpperCase();
        const cmsNodeClassificationUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST}`;
        const cmsClassificationList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

        const [trackerData,nodeMapCMS,classificationData,nodeData,filterData] = await Promise.all([
            getInteractionData(userEmail),
            getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
            getMetaFromCMS(req,cmsLoginUrl, cmsClassificationList, req.user.groups),
            detailDashboardData(dashboardId,userEmail),
            myAppsFilterData(dashboardId,userEmail)
        ]);

        let response = {}
        let classificationNodesData = {}
        let classificationMap = {}
        let nodeClassificationMap = {}
        let dashboardApps = {}
        let addApps = {}
        let domainInteractionFlag = {}

        //Creates a Map for Classification with the keys
        let classifications =Object.values(classificationData.classification)
        classifications.forEach(classification=>{
            nodeClassificationMap[classification.name] = classification.key
            let key = classification.key
            if (key == 'official_statistics')
                classificationMap['official-insights'] = classification.name
            if (key == 'experimental_statistics')
                classificationMap['innovative-insights'] = classification.name
            
        })

            //Assign hierarchy to screener nodes
            let hierarchyInfo = {}
            screenerNodes = filterData.filter(node=>['official-insights','innovative-insights'].includes(node.CONTENT_TYPE)).map(node =>{return node.NODE_ID})
            if (screenerNodes.length){
                let hierarchyData = await getHierarchyData(screenerNodes,language)
                hierarchyData.forEach(hData => {
                    hierarchyInfo[hData.INDICATOR_ID] = hData
                })
            }

        //Creates a tracker map for getting the node interaction information
        let trackerMap = {}
        trackerData.forEach(node =>{
            trackerMap[node.NODE_ID] = node.STATUS
        })

        if (filterData) {
            response.title = nodeData[0].TITLE

            filterData.forEach(app=>{
                let domain, map;
                const nodeId = app['NODE_ID']
                const contentType = app['CONTENT_TYPE']

                if ((nodeId in nodeMapCMS) || ['official-insights','innovative-insights','compare-statistics','Spatial-Analytics'].includes(contentType)){
                            
                    if (contentType == 'compare-statistics'){
                        domain = 'Compare Statistics'
                    }
                    else if (contentType == 'Spatial-Analytics'){
                        domain = app['DOMAIN'] || 'Spatial Analytics'
                    }
                    else if (['official-insights','innovative-insights'].includes(contentType)){                            
                        let nodeHierarchy = hierarchyInfo[app['NODE_ID']]
                        domain = nodeHierarchy.TOPIC
                        app['TITLE'] = nodeHierarchy.INDICATOR_NAME
                        let dynamicClassification =  contentType == 'official-insights'?'Official Insights':'Innovative Insights'
                        if (!(dynamicClassification in classificationNodesData)){
                            classificationNodesData[dynamicClassification] = []
                        }
                        classificationNodesData[dynamicClassification].push(nodeId)
                    }
                    else{
                        map = nodeMapCMS[nodeId]
                        domain = map["domain"]
                        let classification = map["content_classification"] ? map["content_classification"] : undefined

                        if (!(nodeClassificationMap[classification] in classificationNodesData)){
                            classificationNodesData[nodeClassificationMap[classification]] = []
                        }
                    
                        classificationNodesData[nodeClassificationMap[classification]].push(nodeId)
                    }   

                    app['PUBLISH_DATE'] = app['CREATED_DATE']

                    if (domain && !(domain in addApps))
                        addApps[domain] = { "items": [] }
                    
                    addApps[domain]["items"].push(app)
                    
                }
            })
        }

        if (nodeData) {
    
            nodeData.forEach(app => {
    
                const nodeId = app['NODE_ID']
                const contentType = app['CONTENT_TYPE']
                const domain = app['DOMAIN']
                const classification = app['CLASSIFICATION']

                if ((nodeId in nodeMapCMS) || ['official-insights','innovative-insights','compare-statistics','Spatial-Analytics'].includes(contentType)){
                    app.INTERACTION_STATUS = trackerMap.hasOwnProperty(nodeId) ? trackerMap[nodeId] : 1;
    
                    if (['official-insights','innovative-insights'].includes(app['CONTENT_TYPE'])){

                        let dynamicClassification =  contentType == 'official-insights'?'Official Insights':'Innovative Insights'
                        if (!(dynamicClassification in classificationNodesData)){
                            classificationNodesData[dynamicClassification] = []
                        }
                        classificationNodesData[dynamicClassification].push(nodeId)
                    }
                    else if (nodeId in nodeMapCMS){
                        let map = nodeMapCMS[nodeId]
                        let nodeClassification = map["content_classification"]

                        if (!(nodeClassificationMap[nodeClassification] in classificationNodesData)){
                            classificationNodesData[nodeClassificationMap[nodeClassification]] = []
                        }

                        classificationNodesData[nodeClassificationMap[nodeClassification]].push(app.NODE_ID)
                    }    
                    
                    app['PUBLISH_DATE'] = app['CREATED_DATE']

                    if (!domainInteractionFlag.hasOwnProperty(domain)) {
                        // domainInteractionFlag[domain] = 0;
                        domainInteractionFlag[domain] = 1;
                    }
                    
                    if (domain && !(domain in dashboardApps))
                        dashboardApps[domain] = { "items": {} }
                    
                    if (domain) {
                        if (!dashboardApps[domain]["items"][classification])
                            dashboardApps[domain]["items"][classification] = []
                        dashboardApps[domain]["items"][classification].push(app)
                        domainInteractionFlag[domain] += app.INTERACTION_STATUS
                    }
                }
                
            });

        }

        response.classificationNodesData = classificationNodesData

        response.addNodes = Object.entries(addApps).map(([domain_name, domain_value]) => {
            const domainObj = {
                name: domain_name,
                items: domain_value['items'],
                interaction_status: 0
            }
            return domainObj
        })

        response.nodes = Object.entries(dashboardApps).map(([domain_name, domain_value]) => {
            domain_items = Object.entries(domain_value['items']).map(([appType, appValue]) => {
                const domainItemsObj = {
                    name: appType,
                    nodes: appValue
                }
                return domainItemsObj;
            })
            const domainObj = {
                name: domain_name,
                items: domain_items,
                interaction_status: domainInteractionFlag[domain_name]
            }
            return domainObj
        })
        
        return response
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.detailDashboard with error ${err}`);
        throw err;
    }
}

async function listDashboards(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.listDashboards`);
    try {
        
        const userEmail = req.user.preferred_username
        let data = await getDashboardsData(userEmail)
        let response = []
        data.forEach(d =>{
            let dashboard = {
                id:d.ID,
                name:d.TITLE,
                createdData:d.CREATED_DATE
            }
            response.push(dashboard)
        })
        return response
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.deleteDashboard with error ${err}`);
        throw err;
    }
}

async function getShareAppsList(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.getShareAppsList`);
    try{
        const type=req.params.type
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const userEmail = req.user.preferred_username

        let data = await getShareAppsListData(type,userEmail,page,limit)
        let result;

        if (type == 'sent'){

            result = data.map(app =>{
                return appObj = {
                    ID: app.ID,
                    NAME:app.NAME,
                    SHARED_DT:app.SHARED_DT,
                    TOKEN: app.TOKEN,
                    RECEPIENTS: app.RECEPIENT_EMAILS.split(';'),
                    NUMBER_OF_RECEPIENTS: app.NUMBER_OF_RECEPIENTS,
                    NUMBER_OF_NODES: app.NUMBER_OF_NODES
                }
            })
        }
        else{
            result = data
        }
        return result
    }
    catch(err){
        log.error(`<<<<<Exited microservice-myapps.controller.getShareAppsList with error ${err}`);
        throw err;
    }

}

async function readShareApp(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.readShareApp`);
    try{
        const id=req.params.id
        const userEmail = req.user.preferred_username
       
        let data = await readShareAppsData(id,userEmail)
        return{
            'message':'The share app has been read successfully',
            'status':'success'
        }
    }
    catch(err){
        log.error(`<<<<<Exited microservice-myapps.controller.readShareApp with error ${err}`);
        throw err;
    }
}

async function shareMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.shareMyApps`);
    try {
        
        const userEmail = req.user.preferred_username
        let appName = req.body.name
        let shareComment = req.body.comment
        const userName = req.user.name
        const shareId = String(uuid.v4());

        let shareList = req.body.shareList?req.body.shareList:[]
        let shareNodes = req.body.shareNodes?req.body.shareNodes:[]

        if (shareList.includes(userEmail))
            throw new Error(`You can't share your app to yourself`)

        if (shareList.length<1)
            throw new Error('Please provide a list of users to be shared')

        if (shareNodes.length<1)
            throw new Error('Please provide a list of nodes to be shared')

        log.info(`${req.user.preferred_username} | Fetching dragMyAppsData for shareId: ${shareId}`);
        let myAppsNodes = await dragMyAppsData(userEmail)
        //Update dragMyAppsData to accept nodes list
        let compareNodes = shareNodes.filter(s=>s.contentType == 'compare-statistics')
        shareNodes = shareNodes.map(s=>String(s.id))
        myAppsNodes = myAppsNodes.filter(mnode => shareNodes.includes(mnode.NODE_ID))

        if (myAppsNodes.length<1){
            throw new Error('The nodes in the shareList is not available for you')
        }

        const notificationData = {
            nodeId: shareId,
            name: `${userName}'s Apps`,
            users: shareList
        }

        appName = appName?appName:`${userName}'s Apps`
        
        log.info(`${req.user.preferred_username} | Creating shareApp record`);
        const createPromises = shareList.map(async recepient=>{
            const shareData = {
                id: shareId,
                name: appName,
                shareEmail: userEmail,
                shareName: userName,
                recepientEmail: recepient,
                token: shareId
            }
            await createShareApp(shareData);
        })


        await Promise.all(createPromises)
        
        log.info(`${req.user.preferred_username} | Created shareApp record with shareId: ${shareId}`);

        let shareNodesData = []
        let shareDragNodesData = []
        myAppsNodes.forEach(node =>{
            const shareNode = {
                nodeId: node.NODE_ID,
                contentType: node.CONTENT_TYPE,
                shareId: shareId
            }
            shareNodesData.push(shareNode)
            if (node.MOD_FLAG){
                shareList.forEach(recepient=>{
                    const shareDragNode = {
                        nodeId: node.NODE_ID,
                        title: node.MOD_TITLE,
                        domain: node.DOMAIN,
                        classification: node.CLASSIFICATION,
                        sortOrder: node.SORT_ORDER,
                        shareId: shareId,
                        recepient: recepient
                    }
                    shareDragNodesData.push(shareDragNode)
                })
            }
        })

        if (!shareNodesData.length){
            log.error(`${req.user.preferred_username} | No nodes selected to share`);
            throw new Error('No share nodes found')
        }
        log.info(`${req.user.preferred_username} | Creating shareNode records`);
        await createShareAppNodes(shareNodesData);
        log.info(`${req.user.preferred_username} | Created shareNode records with shareId: ${shareId}`);

        log.info(`${req.user.preferred_username} | Creating compare records`);
        await Promise.all(compareNodes.map(async cNode=>{
            shareList.map(async recepient=>{
                let cdata = await getCompareData(cNode.id,userEmail)
                let createCompareData = cdata.map(c=>{
                    return {
                        id: c.ID, 
                        title: c.TITLE,
                        indicator: c.NODE,
                        email: recepient
                    }
                })
        
                await createCompareAppData(createCompareData)
            })
        }))
        log.info(`${req.user.preferred_username} | Created compare records with shareId: ${shareId}`);

        if (shareDragNodesData.length){
            log.info(`${req.user.preferred_username} | Creating shareDragNode records`);
            await createShareDragNodes(shareDragNodesData);
            log.info(`${req.user.preferred_username} | Created shareDragNode records with shareId: ${shareId}`);
        }

        // createShareNotificationsData(notificationData)

        shareList.forEach(recepient =>{
            let shareEmailData = {
                recepient: recepient,
                shareName: appName,
                shareComment: shareComment,
                shareLink: `${process.env.PLATFORM_BASEPATH}/my-apps/view/landing?key=${shareId}`,
                sender: userEmail,
                senderName: userName
            }
            validateEmailContent(shareComment)
            sendShareMyAppsEmail(shareEmailData)
        })


        return {message:'Your apps has been shared',token:shareId}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.shareMyApps with error ${err}`);
        throw err;
    }
}


async function getSharedMyApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.getSharedMyApps`);
    try {
        
        const userEmail = req.user.preferred_username
        const shareId = req.query.token

        const [shareData,shareSenderData,shareRequestData] = await Promise.all([
            getShareApp(shareId,userEmail,'recepient'),
            getShareApp(shareId,userEmail,'sender'),
            getShareMyAppsRequestData(userEmail)
        ])
        let recepientAccess = true
        let senderAccess = true

        if (!shareData.length){
            recepientAccess = false   
        }

        if (!shareSenderData.length){
            senderAccess = false
        }

        if (recepientAccess == false && senderAccess == false)
            throw new IFPError(403,`You don't have access to this share app`)

        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const language = req.headers["accept-language"].toUpperCase();
        const cmsNodeClassificationUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST}`;
        const cmsClassificationList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;


        //Asynchronous calls to CMS and Database
        const [nodeMapCMS,classificationData,dragData] = await Promise.all([
            // getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
            getMetaFromCMSAdmin(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
            getMetaFromCMS(req,cmsLoginUrl, cmsClassificationList, req.user.groups),
            dragShareMyAppsData(shareId)
        ]);

        if (!dragData.length)
            return {"stateChange":0,"classificationNodes":{},"data":[]}
                
        let classificationNodesData = {}
        let stateChange = 0
        let classificationMap = {}
        let nodeClassificationMap = {}

        //Creates a Map for Classification with the keys
        let classifications =Object.values(classificationData.classification)
        classifications.forEach(classification=>{
            nodeClassificationMap[classification.name] = classification.key
            let key = classification.key
            if (key == 'official_statistics')
                classificationMap['official-insights'] = classification.name
            if (key == 'experimental_statistics')
                classificationMap['innovative-insights'] = classification.name
        })

        
        //Assign hierarchy to screener nodes
        let hierarchyInfo = {}
        screenerNodes = dragData.filter(node=>['official-insights','innovative-insights'].includes(node.CONTENT_TYPE)).map(node =>{return node.NODE_ID})
        if (screenerNodes.length){
            let hierarchyData = await getHierarchyData(screenerNodes,language)
            hierarchyData.forEach(hData => {
                hierarchyInfo[hData.INDICATOR_ID] = hData
            })
        }

        let result = [];
        if (dragData) {

            shareRequestNodes = shareRequestData.map(s=>s.NODE)
            
            let myApps = {}
            let domainInteractionFlag = {}

            dragData.forEach(app => {

                let domain, sub_domain, sub_theme, product, classification, map;
                const nodeId = app['NODE_ID'];
                const contentType = app['CONTENT_TYPE'];
                app.INTERACTION_STATUS = 0

                if (shareRequestNodes.includes(String(app.NODE_ID)))
                    app.REQUEST_ACCESS = true

                if (nodeId in nodeMapCMS || ['official-insights','innovative-insights','compare-statistics','Spatial-Analytics'].includes(contentType)) {

                    if (contentType == 'compare-statistics'){
                        domain = 'Compare Statistics'
                        classification = 'Compare'
                    }
                    else if (contentType == 'Spatial-Analytics'){
                        domain = app['DOMAIN'] || 'Spatial Analytics'
                        classification = app['CLASSIFICATION'] || 'Spatial Analytics'
                    }

                    else if (['official-insights','innovative-insights'].includes(contentType)){
                        let nodeHierarchy = hierarchyInfo[app['NODE_ID']]
                        domain = nodeHierarchy.TOPIC
                        sub_domain = nodeHierarchy.THEME
                        sub_theme = nodeHierarchy.SUB_THEME
                        product = nodeHierarchy.PRODUCT
                        app['TITLE'] = nodeHierarchy.INDICATOR_NAME
                        classification = classificationMap[contentType]
                        let dynamicClassification =  contentType == 'official-insights'?'Official Insights':'Innovative Insights'

                        if (!(dynamicClassification in classificationNodesData)){
                            classificationNodesData[dynamicClassification] = []
                        }

                        classificationNodesData[dynamicClassification].push(nodeId)
                    }
                    else{
                        map = nodeMapCMS[nodeId]
                        domain = map["domain"]
                        sub_domain = map["theme"]
                        sub_theme = map["subtheme"]
                        product = map["product"]
                        classification = map["content_classification"]
                        // delete app['TITLE']
                        if (!(nodeClassificationMap[classification] in classificationNodesData)){
                            classificationNodesData[nodeClassificationMap[classification]] = []
                        }

                        classificationNodesData[nodeClassificationMap[classification]].push(app.NODE_ID)
                    }

                    app['PUBLISH_DATE'] = app['INSERT_DT']

                    const appStateModified = app['MOD_FLAG']
                    if (appStateModified){
                        stateChange = stateChange || appStateModified
                        domain = app['DOMAIN']
                        classification = app['CLASSIFICATION']
                        // app["TITLE"] = app['MOD_TITLE']
                    }
                    
                    if (!domainInteractionFlag.hasOwnProperty(domain)) {
                        domainInteractionFlag[domain] = 0;
                    }
                    
                    if (classification) {
                        
                        try {

                            if (domain && !(domain in myApps))
                                myApps[domain] = { "items": {}, "sub_domains": {} }

                            if (sub_domain && !(sub_domain in myApps[domain]["sub_domains"]))
                                myApps[domain]["sub_domains"][sub_domain] = { "items": {}, "sub_themes": {} }

                            if (sub_theme && !(sub_theme in myApps[domain]["sub_domains"][sub_domain]["sub_themes"]))
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme] = { "items": {}, "products": {} }

                            if (product && !(product in myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"]))
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product] = { "items": {} }                                     


                            if (domain && sub_domain && sub_theme && product) {
                                if (!myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product]["items"][classification])
                                    myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product]["items"][classification] = []
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product]["items"][classification].push(app)                                    
                            }
                            else if (domain && sub_domain && sub_theme) {
                                if (!myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["items"][classification])
                                    myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["items"][classification] = []
                                myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["items"][classification].push(app)
                            }
                            else if (domain && sub_domain) {
                                if (!myApps[domain]["sub_domains"][sub_domain]["items"][classification])
                                    myApps[domain]["sub_domains"][sub_domain]["items"][classification] = []
                                myApps[domain]["sub_domains"][sub_domain]["items"][classification].push(app)
                            }
                            else if (domain) {
                                if (!myApps[domain]["items"][classification])
                                    myApps[domain]["items"][classification] = []
                                myApps[domain]["items"][classification].push(app)
                            }
                            domainInteractionFlag[domain] += app.INTERACTION_STATUS
                        } catch (exp) {

                        }
                    }
                }
            });

            result = Object.entries(myApps).map(([domain_name, domain_value]) => {
                domain_items = Object.entries(domain_value['items']).map(([appType, appValue]) => {
                    const domainItemsObj = {
                        name: appType,
                        nodes: appValue
                    }
                    return domainItemsObj;
                })
                const domainObj = {
                    name: domain_name,
                    items: domain_items,
                    interaction_status: domainInteractionFlag[domain_name],
                    sub_domains: Object.entries(domain_value.sub_domains).map(([sub_domain_name, sub_domain_value]) => {
                        sub_domain_items = Object.entries(sub_domain_value['items']).map(([appType, appValue]) => {
                            const domainItemsObj = {
                                name: appType,
                                nodes: appValue
                            }
                            return domainItemsObj;
                        })
                        const subDomainObj = {
                            name: sub_domain_name,
                            items: sub_domain_items,
                            sub_themes: Object.entries(sub_domain_value.sub_themes).map(([sub_theme_name, sub_theme_value]) => {
                                sub_theme_items = Object.entries(sub_theme_value['items']).map(([appType, appValue]) => {
                                    const domainItemsObj = {
                                        name: appType,
                                        nodes: appValue
                                    }
                                    return domainItemsObj;
                                })
                                const subThemeObj = {
                                    name: sub_theme_name,
                                    items: sub_theme_items,
                                    products: Object.entries(sub_theme_value.products).map(([product_name, product_value]) => {
                                        product_items = Object.entries(product_value['items']).map(([appType, appValue]) => {
                                            const domainItemsObj = {
                                                name: appType,
                                                nodes: appValue
                                            }
                                            return domainItemsObj;
                                        })
                                        const productObj = {
                                            name: product_name,
                                            items: product_items
                                        }
                                        return productObj;
                                    })
                                }
                                return subThemeObj;
                            })
                        }
                        return subDomainObj;
                    })
                }
                return domainObj
            })
        }
        
        return {"stateChange":stateChange,"classificationNodes":classificationNodesData,"data":result}
    } catch (err) {
        log.error(`<<<<<Exited microservice-myapps.controller.shareMyApps with error ${err}`);
        throw err;
    }
}

async function requestAccessShareApp(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.acceptShareApp`);
    try{
        const id=req.body.id
        const userEmail = req.user.preferred_username
        const userName = req.user.name

        const requestData = {
            requestorEmail: userEmail,
            requestorName: userName,
            nodeId: id
        }
        await createShareMyAppsRequestData(requestData)
        requestAccessShareMyAppsEmail(requestData)
        return{
            'message':'The request has been sent to the IFP',
            'status':'success'
        }
    }
    catch(err){
        log.error(`<<<<<Exited microservice-myapps.controller.requestAccessShareApp with error ${err}`);
        throw err;
    }
}

async function deleteShareApps(req) {
    log.debug(`>>>>>Entered microservice-myapps.controller.acceptShareApp`);
    try{
        const shareId=req.params.id
        const userEmail = req.user.preferred_username
        const type = req.params.type

        switch(type) {
            case 'sent':
                await deleteShareData(shareId,'sender',userEmail)
                await deleteShareDragData(shareId,'sender')
                await deleteShareNodesData(shareId)
                break;
            case 'received':
                await deleteShareData(shareId,'recepient',userEmail)
                await deleteShareDragData(shareId,'recepient',userEmail)
                await deleteShareNodesData(shareId)
                break;
            default:
                throw new Error(`Invalid requestor type: ${type}`) 
          }


        return{
            'message':'The share app has been deleted',
            'status':'success'
        }
    }
    catch(err){
        log.error(`<<<<<Exited microservice-myapps.controller.requestAccessShareApp with error ${err}`);
        throw err;
    }
}




module.exports = { 
    listMyApps, 
    addAppsToMyApps, 
    removeAppsFromMyApps, 
    removeCompareAppsFromMyApps, 
    draftMyApps, 
    submitMyApps, 
    domainNodesListMyApps, 
    idListMyApps, 
    draftListMyApps, 
    draftNodeListMyApps, 
    draftDeleteMyApps, 
    dragStateSaveMyApps, 
    removeDragStateSaveMyApps,
    createDashboard,
    editDashboard,
    deleteDashboard,
    detailDashboard,
    listDashboards,
    removeDashboardNodes,
    shareMyApps,
    readShareApp,
    getShareAppsList,
    getSharedMyApps,
    requestAccessShareApp,
    deleteShareApps
};
