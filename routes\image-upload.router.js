const express = require('express');
const router = new express.Router();
const multer = require('multer');
const { uploadPublicationImage } = require('../controllers/image-upload.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

// Configure multer for memory storage (following your project's pattern)
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { 
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

/**
 * POST /api/upload/image
 * Upload an image for a publication
 * 
 * Body:
 * - publicationId: string (required)
 * - image: file (required) - The image file to upload
 * 
 * Response:
 * {
 *   "success": true,
 *   "message": "Image uploaded successfully",
 *   "data": {
 *     "publicationId": "pub123",
 *     "imageUrl": "https://your-bucket.s3.region.amazonaws.com/publication-images/pub123_uuid.jpg",
 *     "originalName": "image.jpg"
 *   }
 * }
 */
router.post('/image', upload.single('image'), async (req, res, next) => {
  try {
    await uploadPublicationImage(req, res);
  } catch (error) {
    log.error(`Error in image upload route: ${error}`);
    next(error);
  }
});

module.exports = router;
