require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getGlossaryData, getDomainsData, getAlphabetsData } = require('./services/executeQuery.service');

const ARABIC_ALPHABETS = [
  "آ", "إ", "أ", "ا", "ب", "ت", "ث", "ج", "ح", "خ", "د", "ذ", "ر",
  "ز", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ك", "ل",
  "م", "ن", "هـ", "و", "ي",
];

/**
 * function to get whats-new content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getGlossary(req) {
    log.debug(`>>>>>Entered microservice.glossary.controller.getGlossary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        let page = req.query.page?Number(req.query.page):1
        let limit = req.query.limit?Number(req.query.limit):10
        let filters = req.body.filters?req.body.filters:{}
        let sortBy = req.body.sortBy? req.body.sortBy:{}
        let searchTerm = req.query.term

        const [
            data, 
            alphabets
        ] = await Promise.all([
            getGlossaryData(lang.toUpperCase(),page,limit,filters,sortBy,searchTerm),
            getAlphabetsData(lang.toUpperCase(),searchTerm,filters)
        ]);

        let response={}
        
        alphaMap = {}
        if (lang.toUpperCase() == 'EN')
            for (let i = 65; i <= 90; i++) {
                alphaMap[String.fromCharCode(i)] = 0;
            }
        else
            alphaMap = Object.fromEntries(ARABIC_ALPHABETS.map(letter => [letter, 0]));

        alphabets.forEach(({ CHARACTER, COUNT }) => {
            if (alphaMap.hasOwnProperty(CHARACTER)) {
                alphaMap[CHARACTER] = +COUNT;
            }
        })
        
        if (data.length){
            response ={
                'totalCount': +data[0].TOTAL,
                'page':page,
                'limit':limit,
                'alphabets':alphaMap,
                'results': data
            }
        }else{
            response ={
                'totalCount':0,
                'page':page,
                'limit':limit,
                'alphabets':alphaMap,
                'results': data
            }
        }
        return response
    } catch (err) {
        log.error(`<<<<<Exited microservice.glossary.getGlossary on getting data with error ${err}`);
        throw err;
    }
}

async function getGlossaryFilters(req) {
    log.debug(`>>>>>Entered microservice.glossary.controller.getGlossary`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? 'en' : `${req.headers["accept-language"]}`;
        const domains = await getDomainsData(lang.toUpperCase())
        
        domainMap = {}
        domains.forEach(domain=>{
            if (!(domain.TOPIC in domainMap))
                domainMap[domain.TOPIC] = {}
            if (!(domain.THEME in domainMap[domain.TOPIC]))
                domainMap[domain.TOPIC][domain.THEME] = []
            domainMap[domain.TOPIC][domain.THEME].push(domain.TYPE)
        })

        domainMap = Object.entries(domainMap).map(([domain,themes]) =>{
            const domainObj = {
                name: domain,
                items: Object.entries(themes).map(([theme,types]) =>{
                    const themeObj = {
                        name: theme,
                        items: types
                    }
                    return themeObj
                })
            }
            return domainObj
        })
        return {
            "domains":domainMap
        }
    } catch (err) {
        log.error(`<<<<<Exited microservice.glossary.getGlossary on getting data with error ${err}`);
        throw err;
    }
}

module.exports = { getGlossary, getGlossaryFilters };
