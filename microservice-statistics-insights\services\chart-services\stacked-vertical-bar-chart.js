const scadLib = require('scad-library');

const { util } = scadLib;
const Logger = scadLib.logger;
const log = new Logger().getInstance();
const getStackedVerticalBarData = async (results, visualization, maxPointLimit) => {
  log.debug('>>>>> Enter services.chart-services.stacked-vertical-bar-chart.getStackedVerticalBarData');

  return new Promise((resolve, reject) => {
    try {
      const listOfObj = [];
      const finalObj = [];
      // getting dtae field from all object
      results.map((obj) => {
        listOfObj.push(obj.OBS_DT);
      });

      const uniqueList = [...new Set(listOfObj)]; // to get unique list of date
      uniqueList.map((date) => {
        results.forEach((item) => {
          Object.entries(item).forEach(([key, value]) => {
            if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
              delete item[key];
            }
          });
          const obj = {};
          // grouping all indicator title : indicator value for each date
          if (date === item.OBS_DT) {
            const key = item.INDICATOR_ID;
            obj.OBS_DT = util.convertDate(`${item.OBS_DT}`);
            obj[key] = item.VALUE;
          }
          if (Object.keys(obj).length !== 0) finalObj.push(obj);
        });
      });

      // Removing duplicate object
      finalObj.map((item) => {
        const date = item.OBS_DT;
        finalObj.map((obj, index) => {
          if (obj.OBS_DT === date) {
            finalObj[index] = Object.assign(item, obj);
          }
        });
      });

      visualization.seriesMeta[0].data = [...new Set(finalObj)];
      if (maxPointLimit) {
        visualization.seriesMeta[0].data = visualization.seriesMeta[0].data.splice(`-${maxPointLimit}`)
      }
      log.debug('<<<<< Exit services.chart-services.stacked-vertical-bar-chart.getStackedVerticalBarData successfully');
      resolve(visualization);
    } catch (err) {
      log.error(`<<<<< Exit services.chart-services.stacked-vertical-bar-chart.getStackedVerticalBarData with error ${err}`);
      reject(err);
    }
  });
};

module.exports = {
  getStackedVerticalBarData,
};
