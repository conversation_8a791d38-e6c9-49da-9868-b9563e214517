const valuesDataFunctions = require('scad-library').valuesData;
const newValuesDataFunctions = require('scad-library').valuesDataAnalytical;
const { getValuesDataFromDB } = require('./getGraphData.service');
const Logger = require('scad-library').logger;

const log = new Logger().getInstance();

async function getValuesData(valuesData, data, scadEstimate) {
    try {
        let scadData = [];
        let coiData = [];
        if (data.seriesMeta) {
            data.seriesMeta.forEach(series => {
                if (series.id.includes('-forecast')) {
                    coiData.push(series.data);
                }
                else if (!series.id.includes('-forecast')) {
                    scadData.push(series.data);
                }
            })
        } else {
            scadData = data;
        }
        const valuesPromises = valuesData.map(async value => {
            switch (value.id) {
                case "total-population-value": {
                    const data = valuesDataFunctions.population(coiData);
                    value = Object.assign(value, data);
                    break;
                }
                case "scad-projection": {
                    const data = valuesDataFunctions.scadEstimate(coiData, scadEstimate);
                    value = Object.assign(value, data);
                    break;
                }
                case "current": {
                    const data = valuesDataFunctions.currentDate();
                    value = Object.assign(value, data);
                    break;
                }
                case "current-index": {
                    const data = valuesDataFunctions.todayIndex(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case "current-percentage": {
                    const data = valuesDataFunctions.todayPercentage(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case "quarter-index": {
                    const data = valuesDataFunctions.quarterIndex(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case "quarter-percentage": {
                    const data = valuesDataFunctions.quarterPercentage(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case "official-date": {
                    const data = valuesDataFunctions.officialDate(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case "estimate": {
                    const data = valuesDataFunctions.estimatePercentage(scadData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case 'year-to-date-value': {
                    let dbData = await getValuesDataFromDB(value);
                    const data = valuesDataFunctions.yearToDateValue(value, dbData);
                    value = Object.assign(value, data);
                    break;
                }
                case "previous-quarter-index": {
                    const data = valuesDataFunctions.previousQuarterIndex(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case "previous-quarter-percentage": {
                    const data = valuesDataFunctions.previousQuarterPercentage(scadData[0], coiData[0]);
                    value = Object.assign(value, data);
                    break;
                }
                case 'year-to-date': {
                    let dbData = await getValuesDataFromDB(value);
                    const data = valuesDataFunctions.yearToDate(value, dbData);
                    value = Object.assign(value, data);
                    break;
                }
                case 'percentage-change': {
                    let dbData = await getValuesDataFromDB(value);
                    let data = newValuesDataFunctions.percentageChange(value, dbData);
                    value = Object.assign(value, data);
                    break;
                }
                default: {
                    reject(`Values Function not available`);
                    break;
                }
            }
        })
        await Promise.all(valuesPromises)
    }
    catch (err) {
        throw err;
    }
}

module.exports = { getValuesData }