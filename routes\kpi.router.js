const express = require('express');
const Logger = require('scad-library').logger;
const router = new express.Router();
const kpiController = require('../microservice-kpi/kpi.controller');
const { getBayaanGroupMatrix } = require("../services/group.service");
const log = new Logger().getInstance();
const { validateQueryStrings } = require('./validators/kpi.validator');

router.post('/log/:type', async (req, res, next) => {
    try {
        // Call the KPI controller to handle the logging
        const data = await kpiController.updateKpiSession(req);

        // Set response headers and send data
        res.set('Content-Type', 'application/json');
        res.status(200).json(data);

    } catch (err) {
        // Log the error and pass it to the next middleware
        log.error(`Error updating KPI session log, ERROR: ${err}`);
        next(err);
    }
});


async function kpiDashboardPermissionCheck(req, res, next) {
	try {
    if (process.env.NODE_ENV === "prod" || process.env.NODE_ENV === "demo") {
      const userGroups = req.user.groups;

      // Await the resolution of groupMatrix
      const groupMatrix = await getBayaanGroupMatrix();

      // Define required roles
      const requiredRoles = ["IFP_Approvers", "UAT_Content"];

      // Check if any user group ID matches a required role in groupMatrix
      const hasPermission = userGroups.some((groupId) => {
        const role = groupMatrix[groupId];
        return requiredRoles.includes(role);
      });

      if (!hasPermission) {
        return res.status(403).json({ status: false });
      }
    }

    next(); // Continue to the next middleware/controller
  } catch (error) {
    console.error("Error retrieving group matrix:", error);
    res.status(500).json({ status: false, error: "Internal server error." });
  }
}

router.use(kpiDashboardPermissionCheck);

router.get("/access-check", (req, res) => {
	res.json({ status: true });
});

router.post("/users", kpiController.getUserDataController);
router.post("/users/status", kpiController.getUsersByStatusController);
router.post("/users/usage", kpiController.getTopUsersByUsageController);
router.post("/users/usage-by-hour", kpiController.getPlatformUsageByHourController);
router.post("/users/by-group", validateQueryStrings, kpiController.getUserWithGroupController);
router.post("/entities", kpiController.getEntitiesDataController);
router.post("/entities/usage", kpiController.getTopEntitiesByUsageController);
router.post("/domains", kpiController.getOverallDomainsDataController);
router.post("/official-statistics", kpiController.getCommonStatisticsDataController);
router.post("/experimental-statistics", kpiController.getExperimentalStatisticsDataController);
router.post("/reports", kpiController.getReportsDataController);
router.post("/analytical-apps", kpiController.getAnalyticalAppsDataController);
router.get("/entities-list", kpiController.getEntityListController);
router.post("/self-service-tools", kpiController.getSelfServiceToolsController);
router.post("/self-service-tools/user", validateQueryStrings, kpiController.getSelfServiceToolsUserController);
router.post("/geo-spatial", kpiController.getGeoSpatialController);
router.post("/geo-spatial/user", kpiController.getGeoSpatialUserController);

// new routes 
router.get("/superusers", kpiController.getPrimarySecondaryUsers);
router.get("/users-count", kpiController.getTypeOfUsersCount);
router.get("/entity-classification", kpiController.getEntityClassificationOverview);
router.post("/nodes-list", kpiController.getProductAndNodeFromCMS);
router.post("/users-list", kpiController.getEntityUsersByClassification);
router.post("/active/users/list", kpiController.getActiveusersList);
router.post("/active/entity/list", kpiController.getActiveEntities);
router.post("/geospatil/overview/list", kpiController.getGeospatialOverviewList);
router.post("/tools/overview/list", kpiController.getToolsOverviewList);
router.post("/download/indicators", kpiController.getDownloadIndicators);
router.post("/notification/indicators", kpiController.getEnabledIndicatorsList);
router.get("/entity/users/count", validateQueryStrings, kpiController.getEntityandUsersList);
router.get("/users/detail", validateQueryStrings, kpiController.getUsersListWithDetails);
router.get("/entity/domain", kpiController.getEntityDomains);
router.get("/entity/domain/products", kpiController.getDomainProducts);



module.exports = router;
