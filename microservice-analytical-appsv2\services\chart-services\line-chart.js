const { max } = require('moment');
const scadLibrary = require('scad-library');
const Logger = scadLibrary.logger;

const log = new Logger().getInstance();
const { util } = scadLibrary;

/**
 * Function to process data for line chart according to how the data structure provided by UI
 * @param {*} data object
 * @param {*} visualization object
 */
const processLineChartData = (data, visualization, maxPointLimit) => {
  //adding data array inside each series

  return new Promise((resolve, reject) => {
    try {
      // visualization.seriesMeta.forEach(series => {
      //   series["data"] = [];
      // });
      if (data && data.length > 0) {
        /*formatting data received from db by mapping id to values 
        based on dimension object provided within json through CMS*/
        visualization.seriesMeta.forEach(series => {
          //adding data array inside each series
          series.data = [];
          // Removes all null data
          data = data.filter( d => d );
          let matchingValueObject = series.dimension;
          let result = data.filter(function (item) {
            item.OBS_DT = util.convertDate(item.OBS_DT.toString());
            for (var key in matchingValueObject) {
              let itemColumn = item[key] === null ? '' : item[key].toUpperCase();
              let dimensionValue = matchingValueObject[key] === null ? '' : matchingValueObject[key].toUpperCase();
              if (item[key] === undefined || itemColumn != dimensionValue) {
                return false;
              }
            }
            Object.keys(item).forEach((k) => item[k] == null && delete item[k] && delete item['INSERT_DT'] && delete item['INSERT_USER_ID']);
            return true;
          });

          if (result.length >= 1) series.data = result;

        })
      }
      else {
        log.error(`Data not available in DB for visualization ${visualization}`);
        reject([404, `Data not available in DB`]);
      }
      //logic to find last scad element from all scad series
      let scadLastElement = [];
      visualization.seriesMeta.filter(series => {
        if (maxPointLimit && !series.id.includes('-forecast')) {
          series.data = series.data.slice(`-${maxPointLimit}`);
        }
        if (!series.id.includes('-forecast')) {
          scadLastElement.push({ id: `${series.id}-forecast`, data: series.data[series.data.length - 1] });
          // Assigning scad series last element's OBS_DT as markers data (DATE)
          if (visualization.markersMeta.length > 0) {
            visualization.markersMeta[0].data = { "DATE": series.data[series.data.length - 1][series.xAccessor.path] }
          }
          else {
            log.debug(`markersMeta unavailable`);
          }
        }
        //creating array of x-axis values and y-axis values to calculate xMin xMax and yMin yMax
        //x axis value is OBS_DT (DATE format)
        let xFields = series.data.map( e => new Date(e[series.xAccessor.path]) );
        let yFields = series.data.map( e => e[series.yAccessor.path] );

        if (yFields.length > 0) {
          //calculating min and max values
          series.yMax = Math.max(...yFields);
          series.yMin = Math.min(...yFields);
        }
        if (xFields.length > 0) {
          //calculating min and max date and converting to HTML5 format
          series.xMin = util.convertDate(Math.min.apply(null, xFields));
          series.xMax = util.convertDate(Math.max.apply(null, xFields));
        }

      })
      //logic to append scad series last element to coi series as first element 
      visualization.seriesMeta.filter(series => {
        if (series.id.includes('-forecast')) {
          scadLastElement.filter(element => {
            if (series.id === element.id) {
              series.data.unshift(element.data);
            }
          })
        }
      });

      if (!visualization.showPointLabels) {
        visualization["showPointLabels"] = false;
      }

      resolve(visualization);
    } catch (err) {
      
      log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
      reject([422, err]);
    }
  });
}

module.exports = { processLineChartData };
