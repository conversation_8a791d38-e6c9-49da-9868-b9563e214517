const { IFPError } = require("../../utils/error");

const validateNotificationList = (req, res, next) => {
    if (req.query.status)
        if (! ['READ','UNREAD'].includes(req.query.status))
            throw new IFPError(400,`${req.query.status} is not a valid status`)
    next()
  };

const validateNotificationSubscribe = (req, res, next) => {
    if (!('id' in req.body))
        throw new IFPError(400,`Please provide an id`)
    
    if (!('contentType' in req.body))
        throw new IFPError(400,`Please provide a contentType`)

    if (!req.body.contentType)
        throw new IFPError(400,`Please provide a valid contentType`)

    next()
};


const validateNotificationSubscribeEmail = (req, res, next) => {
    if (!('id' in req.body))
        throw new IFPError(400,`Please provide an id`)
    next()
};

const validateNotificationUnSubscribe = (req, res, next) => {
    if (!('id' in req.body))
        throw new IFPError(400,`Please provide an id`)
    next()
};

const validateNotificationUnSubscribeEmail = (req, res, next) => {
    if (!('id' in req.body))
        throw new IFPError(400,`Please provide an id`)
    next()
};

const validateNotificationRead = (req, res, next) => {
    if (!('id' in req.body))
        throw new IFPError(400,`Please provide an id`)
    next()
};

module.exports = {
    validateNotificationList,
    validateNotificationSubscribe,
    validateNotificationSubscribeEmail,
    validateNotificationUnSubscribe,
    validateNotificationUnSubscribeEmail,
    validateNotificationRead
}