const valuesDataFunctions = require('scad-library').valuesData;

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getValuesData(valuesData, data, visualization, results) {
    return new Promise((resolve, reject) => {
        try {
            let scadData = '';
            let coiData = '';
            if (data.seriesMeta) {
                data.seriesMeta.forEach(series => {
                    if (series.data && series.data.length > 0) {
                        if (series.id.includes('-forecast')) {
                            coiData = series.data;
                        }
                        else {
                            scadData = series.data;
                        }
                    }
                })
            } else {
                scadData = data;
            }

            valuesData.forEach(value => {
                switch (value.id) {
                    case "official": {
                        const data = valuesDataFunctions.official(scadData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "current-index": {
                        const data = valuesDataFunctions.todayIndex(scadData, coiData);
                        value = Object.assign(value, data);
                        break;
                    }
                    case "quarter-index": {
                        const data = valuesDataFunctions.quarterIndex(scadData, coiData);
                        value = Object.assign(value, data);
                        break;
                    }
                    case "quarter-percentage": {
                        const data = valuesDataFunctions.quarterPercentage(scadData, coiData);
                        value = Object.assign(value, data);
                        break;
                    }
                    case "previous-quarter-index": {
                        const data = valuesDataFunctions.previousQuarterIndex(scadData, coiData);
                        value = Object.assign(value, data);
                        break;
                    }
                    case "previous-quarter-percentage": {
                        const data = valuesDataFunctions.previousQuarterPercentage(scadData, coiData);
                        value = Object.assign(value, data);
                        break;
                    }
                    case "estimate": {
                        const data = valuesDataFunctions.estimatePercentage(scadData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "index-current": {
                        const data = valuesDataFunctions.current(coiData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "forecast": {
                        const data = valuesDataFunctions.currentPercentage(coiData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "nowcast-forecast": {
                        const data = valuesDataFunctions.nowCastForeCastPercentage(scadData, coiData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "official-date": {
                        const data = valuesDataFunctions.officialDate(scadData, coiData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "official-day": {
                        const data = valuesDataFunctions.officialDateWithDay(scadData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "official-percentage": {
                        const data = valuesDataFunctions.officialPercentage(scadData, coiData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "latest-date": {
                        const data = valuesDataFunctions.latestDate(scadData);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "percentage-change": {
                        const data = valuesDataFunctions.percentageChange(scadData, value, visualization);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "percentage-change-multi": {
                        const result = valuesDataFunctions.percentageChangeMulti(value, results);
                        value = Object.assign(value, result)
                        break;
                    }
                    case "latest-data": {
                        const data = valuesDataFunctions.latestData(scadData, valuesData, visualization, results, value);
                        value = Object.assign(value, data)
                        break;
                    }
                    case "percentage-change-quarter": {
                        const data = valuesDataFunctions.percentageChangeQuarter(results, value);
                        value = Object.assign(value, data)
                        break;
                    }
                    default: {
                        log.debug(`Values Function not available`);
                        break;
                    }
                }
                resolve();
            })
        }
        catch (err) {
            
            log.error(`<<<<< Exited microservice-statistics-insights.services.getValuesData.service with error ${err}`);
            reject([422, err]);
        }
    });
}

module.exports = { getValuesData }