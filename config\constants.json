{"__comment1__": "Default indicator drivers values for economy sector chart", "indicatorDrivers": {"parameter_1_range": "medium", "parameter_2_range": "medium", "parameter_3_range": "medium", "parameter_4_range": "medium"}, "__comment2__": "Default indicator drivers values for population projection chart", "indicatorDriversPopulation": {"parameter_1_range": "no_range", "parameter_2_range": "no_range", "parameter_3_range": "no_range", "parameter_4_range": "no_range"}, "quarterlyIndicatorDrivers": {"parameter_1_range": "1q"}, "__comment3__": "Default error messages to be sent to UI", "errorMessage": {"400": "Bad Request", "401": "Unauthorized", "404": "Data Not found", "500": "Interal server error", "422": "Unable to process the Entity / Restricted Content", "424": "Error generating query Error in input json/request body"}, "cmsUrl": {"CMS_PUBLICATIONS_URL": "/api/security/publications?field_id_value=", "CMS_INFOGRAPHIC_REPORTS_URL": "/api/security/infographic-reports?field_id_value=", "CMS_STATISTICS_INSIGHTS_URL": "/api/security/statics-insight?field_id_value=", "CMS_STATISTICS_INSIGHTS_URL_BYID": "/api/security/statics-insight/content?field_id_value=", "CMS_WHATS_NEW_URL": "/api/security/whats-new?field_id_value=", "CMS_ANALYTICAL_APPS": "/api/security/analytics-apps?field_id_value=", "CMS_ANALYTICAL_APPS_BYID": "/api/security/analytics-apps/content?field_id_value=", "CMS_SEARCH_URL": "/api/security/search?field_id_value=", "CMS_COUNTRY_LIST": "/api/security/country/", "CMS_LOGIN_URL": "/user/login?_format=json", "CMS_DOMAINS_URL": "/api/security/domain-list", "CMS_TAGS_URL": "/api/security/tag-color-code-list", "CMS_CONTENTTYPE_URL": "/api/security/content-type-list", "CMS_GEOSPATIAL_URL": "/api/security/geospatial-analysis?field_id_value=", "CMS_SURVEY_URL": "/api/survey", "CMS_ABOUT_US_URL": "/api/page-content/about-us", "CMS_PRODUCT_URL": "/api/page/products", "CMS_PRIVACY_POLICY_URL": "/api/page/privacy-policy", "CMS_TC_URL": "/api/page/terms-and-conditions", "CMS_USER_SYNC_URL": "/api/user/sync", "CMS_HOME_URL": "/api/homepage-sections/", "CMS_PUBLICATIONS_CREATE_URL": "/api/node/add/publications", "CMS_NODE_LIST_URL": "/api/nodes", "CMS_MAINTANANCE_URL": "/api/maintenance", "CMS_INDICATOR_LIST": "/api/indicatorslist", "CMS_PRODUCT_LIST": "/api/group/category-list"}, "cmsGroupUrl": {"CMS_PUBLICATIONS_URL": "/api/group/publications", "CMS_PUBLICATION_URL": "/api/group/publication", "CMS_INFOGRAPHIC_REPORTS_URL": "/api/group/infographic-reports", "CMS_INFOGRAPHIC_REPORT_URL": "/api/group/infographic-report", "CMS_STATISTICS_INSIGHTS_URL": "/api/group/statistics-insights", "CMS_OFFICIAL_STATISTICS_URL": "/api/group/statistics-insights?classification_key=official_statistics", "CMS_RECOMMENDATIONS_INDICATOR_URL": "/api/group/nodes/scad_official_indicator?fields=[\"nid\", \"title\",\"parent_domain\",\"body\"]&filters={\"is_screener\":\"0\", \"content_classification\": \"official_statistics\"}", "CMS_FORECASTS_URL": "/api/group/statistics-insights?category=Forecast&classification_key=analytical_apps", "CMS_STATISTICS_INSIGHTS_URL_BYID": "/api/group/statistics-insights/content?nid=", "CMS_WHATS_NEW_URL_LEFT": "/api/group/whats-new", "CMS_WHATS_NEW_URL_RIGHT_HIGHLIGHTS": "/api/group/whats-new-highlights", "CMS_WHATS_NEW_V2": "/api/group/whatsnew", "CMS_ANALYTICAL_APPS": "/api/group/analytical-apps", "CMS_ANALYTICAL_APPSV2": "/api/group/analytical-apps-with-config", "CMS_HEADER_URL": "/api/group/header", "CMS_DYANAMIC_HEADER_URL": "/api/group/dyanamic-header", "CMS_FOOTER_URL": "/api/group/footer", "CMS_ANALYTICAL_APPS_BYID": "/api/group/analytical-apps/content?nid=", "CMS_GEOSPATIAL_URL": "/api/group/geospatial-analysis?nid=", "CMS_SEARCH_URL": "/api/group/search?keys=", "CMS_SEARCH_URL_V2": "/api/group/search-new?keys=", "CMS_PAGES_URL": "/api/group/page-list", "CMS_MENU_LIST_URL": "/api/group/menu-list", "CMS_PAGES_BYID_DOMAIN_URL": "/api/group/page-content/domainpage?nid=", "CMS_PAGES_BYID_LISTING_URL": "/api/group/page-content/listingpage?nid=", "CMS_PAGES_BYID_HOME_URL": "/api/group/page-content/homepage?nid=", "CMS_PAGES_BYID_WHATS_NEW_URL": "/api/group/page-content/whats-new", "CMS_PAGES_BYID_TC_URL": "/api/group/page-content/tcpage?nid=", "CMS_PAGES_BYID_SUB_DOMAIN_URL": "/api/group/subdomain-content-list/", "CMS_PAGES_BYID_DOMAIN_URL_V2": "/api/group/domain-content-list/", "CMS_PAGES_BYID_CATEGORY_URL": "/api/group/category-content-list/", "CMS_DOMAINS_LIST_BY_CATEGORY": "/api/group/navigation-content-list/", "CMS_DOMAINS_LIST_BY_CLASSIFICATION": "/api/group/classification-content-list/", "CMS_NAVIGATION_BY_CLASSIFICATION": "/api/group/classification-content-lists/0", "CMS_NAVIGATION_V2": "/api/group/navigation", "CMS_NAVIGATION_V3": "/api/group/navigation/v3", "CMS_DOMAINS_LIST": "/api/group/filter-list", "CMS_DOMAINS_LIST_V2": "/api/group/domain-list", "CMS_DOMAIN_DETAIL": "/api/group/my-apps-domain-content-lists/", "CMS_SUBDOMAIN_DETAIL": "/api/group/my-apps-sub-domain-content-lists/", "CMS_SUBTHEME_DETAIL": "/api/group/my-apps-sub-theme-content-lists/", "CMS_CATEGORY_LIST": "/api/group/navigation-content-list/0", "CMS_CLASSIFICATION_LIST": "/api/group/classification-content-list/0", "CMS_NODE_CLASSIFICATION_LIST": "/api/group/my-apps-list", "CMS_SPATIAL_ANALYTICS_LIST": "/api/group/spatial-analytics", "CMS_ECI_APPS": "/api/group/analytical-apps-v2?app_type=eci_insights", "CMS_BASKET_APPS": "/api/group/analytical-apps-v2?app_type=basket_insights", "CMS_CENSUS_APPS": "/api/group/analytical-apps-v2?app_type=census", "CMS_CLASSIFICATIONS_BY_DOMAIN": "/api/group/my-apps-domain-content-aggregated/", "CMS_CLASSIFICATIONS_BY_DOMAIN_FILTERS": "/api/group/my-apps-domain-content-dropdown/", "CMS_DOMAIN_NODES_LIST": "/api/group/my-apps-domain-content-lists-paginated/", "CMS_USER_JOURNEY": "/api/user-journey", "CMS_WHATS_NEW_OPT_URL": "/api/group/whatsnew", "CMS_CENSUS_URL": "/api/group/census", "CMS_NEWSLETTER_URL": "/api/group/newsletters/", "CMS_MOBILE_DASHBOARDS_URL": "/api/group/analytical-apps-v2?app_type=tableau_internal_mobile", "CMS_MOBILE_POWER_BI_URL": "/api/group/analytical-apps-v2?app_type=power_bi", "CMS_ACCESS_CHECK_URL": "/api/group/access-check", "CMS_NODE_ACCESS_CHECK_URL": "/api/group/node-access-check", "CMS_GeoRevamp_Icons_URL": "/api/group/nodes/spatial_analytics_icon"}, "favoriteTableName": "COI_FAVOURITES", "favoriteColumnNames": ["EMAIL_ID", "CONTENT_ID", "TIMESTAMP", "INSERT_USER_ID", "CONTENT_TYPE"], "insertUserId": "DEVCOI", "imagePath": "/api/images", "supportEmailId": "<EMAIL>", "supportCCEmailIds": ["<EMAIL>", "<EMAIL>"], "emailResponseUser": "Thank you for contacting the Bayaan Team! We highly appreciate feedback and suggestions.<br/>The Bayaan Team will review your message shortly and get back to you within 3 Business Days.<br/><br/><br/>All the Best!<br/>", "visualizationCharts": {"group-1": [{"label": "Line Chart", "id": "line-chart"}, {"label": "Bar Chart", "id": "single-bar-chart"}, {"label": "Table View", "id": "table-view"}], "group-2": [{"label": "Tree Map Chart", "id": "tree-map-with-change-chart"}, {"label": "Horizontal Bar Chart", "id": "single-horizontal-bar-chart"}, {"label": "Circular Bar Chart", "id": "single-circular-bar-chart"}, {"label": "Table View", "id": "table-view"}]}, "redis": {"cmsResponseTTL": 1800, "dataResponseTTL": 1800, "catalogTTL": 28800, "enabled": true, "cacheExcludeTables": ["IFP2_SURVEY_RESPONSE", "FACT_PLAT_USRS_TC", "COI_FAVOURITES", "IFP_USER_ACCESS_SLA", "IFP_INSIGHTS", "IFP_CHART_INSIGHTS", "IFP_USER_OPTIONS", "IFP_MY_APPS", "VW_IFP_NOTIFICATIONS", "IFP_USER_NOTIFICATION_MAP", "IFP_NOTIFICATION_READ_STATUS", "IFP_INTERACTION_MAP", "IFP_INDICATOR_TC", "IFP_COMPARE_NODES", "IFP_MY_APPS_ORDER", "IFP_MY_APPS_DASHBOARD", "IFP_GLOSSARY", "IFP_USER_JOURNEY", "IFP_USERS_TC", "IFP_CMS_SESSION", "IFP_POPULAR_STATS", "IFP_USER_ACCESS", "IFP_SHARE_MY_APPS", "IFP_SHARE_APPS_NOTIFICATIONS", "IFP_SHARE_MY_APPS_ORDER", "IFP_SHARE_MY_APPS_NODES", "IFP_APPS_REQUEST", "IFP_DASHBOARDS", "IFP_DASHBOARD_NODES", "IFP_SHARE_DASHBOARD", "IFP_SHARE_DASHBOARD_NODES", "IFP_PUB_PERIODICITY", "IFP_DOMAINS", "IFP_DATA_CLASSIFICATIONS", "IFP_ENTITY_LOOKUP", "IFP_DISS_ACCESS_POLICY", "IFP_FLOW_USERS", "IFP_INVITATIONS", "IFP_USER_ACCESS_APPROVALS", "IFP_USER_ACCESS_REQUEST", "IFP_USER_ACCESS", "IFP_FLOW_USERS_V2", "IFP_INVITATIONS_V2", "IFP_EID_REQUEST_MAPPING", "USER_FEATURE_FLAG", "BAYAAN_PLATFORM_OPS"]}, "dynamicConfiguration": {"officialTag": "official_statistics", "ifpTag": "experimental_statistics"}, "classifications": {"officialStatistics": "official_statistics", "innovativeStatistics": "experimental_statistics", "analyticalApps": "analytical_apps", "reports": "reports"}, "classificationKeys": {"Analytical Apps": "analytical_apps", "Experimental Statistics": "experimental_statistics", "Official Statistics": "official_statistics", "Reports": "reports"}, "screenerClassificationKeys": ["experimental_statistics", "official_statistics"], "popStatCount": 6, "clickhouseTables": ["VW_VISA_DOMESTIC_SPENDING", "VW_VISA_DOMESTIC_SPENDING_YR", "VW_L_CONSUMER_SPEND_ATTRIBUTES", "VW_CONSUMER_DOMESTIC_SPEND", "VW_MEED_PROJECTS_AWARDED_MON", "VW_MEED_PROJECTS_AWARDED_YR", "VW_MEED_TRENDS", "VW_L_MEED_ATTRIBUTES", "VW_STR_ATTRIBUTES_S", "VW_STR_INDICATORS", "VW_STR_MONTHLY_KPIS_CITY", "VW_STR_YEARLY_KPIS_CITY", "L_PMI_ATTRIBUTES", "VW_PMI_AD", "VW_PMI_AD_YEARLY", "VW_PMI_COMPANY_SIZE", "VW_PMI_COMPANY_SIZE_YEARLY", "VW_PMI_TRENDS", "VW_PMI_UAE", "VW_PMI_UAE_YEARLY", "VW_PMI_YEARLY", "L_CONSUMER_SPEND_ATTRIBUTES", "L_NI_ATTRIBUTES", "VW_NI_HIGHLIGHTS", "VW_NI_PANEL_AVG", "VW_NI_PANEL_AVG_YEARLY", "VW_NI_PANEL_CNT", "VW_NI_PANEL_CNT_YEARLY", "VW_NI_PANEL_SUM", "VW_NI_PANEL_SUM_YEARLY", "VW_CPI_PANEL_L1", "VW_CPI_PANEL_L2", "VW_CPI_PANEL_L1_YEARLY", "VW_CPI_PANEL_L2_YEARLY", "L_CPI_ATTRIBUTES", "VW_CPI_HIGHLIGHTS", "CONSUMER_DOMESTIC_SPEND_TRENDS", "VW_REAL_ESTATE_YEARLY_RENT", "VW_REAL_ESTATE_INDICATORS_RENT", "VW_LK_REAL_ESTATE_ATTRIBUTES_R", "VW_REAL_ESTATE_MONTHLY_RENT", "VW_REAL_ESTATE_INDICATORS_SALE", "VW_LK_REAL_ESTATE_ATTRIBUTES_S", "VW_REAL_ESTATE_YEARLY_SALE", "VW_REAL_ESTATE_MONTHLY_SALE", "VW_PP_PROJECTION_V2", "DIM_PP_PARAM_COMBO", "DS_RI_SCE_COMBO_CURRENT", "VW_RI_GDP_AUG21_INDCNST_1Q", "VW_RI_GDP_AUG21_INDCRNT_1Q", "DS_GDP_SECTORS_COMBO_CNST", "VW_RI_GDP_SECTOR_CNST_IND_PCT", "VW_DS_RI_INFLATION", "VW_RI_GDP_AUG21_INDCNST_2Q", "DS_RI_SCE_COMBO_CONSTANT", "VW_RI_GDP_AUG2021_IND_CNST", "VW_STAT_INDICATORS", "FACT_BLOOMBERG_ECO_ANALYSIS", "VW_IFP_IND_STR_CARD_VALUES", "VW_IFP_IND_POP_CARD_VALUES", "VW_IFP_IND_VISA_CARD_VALUES", "VW_IFP_IND_MEED_CARD_VALUES", "VW_IFP_IND_NI_CARD_VALUES", "VW_IFP_IND_PMI_CARD_VALUES", "IFP_STAT_OVERVIEW", "VW_STATISTICAL_INDICATORS", "VW_NON_OIL_TRANS_SUMMARY", "VW_NON_OIL_TB_TG_INDICATORS", "DS_WHAT_IF_NEW", "VW_DS_WHAT_IF_PARAMS", "VW_RI_GDP_SECTOR_CRNT_IND_PCT", "DS_GDP_SECTORS_COMBO_CRNT", "VW_TABLEAU_CARDS", "VW_STAT_IND_FILTER_ATTRIBUTES", "VW_STAT_IND_SCREENER", "VW_RI_GDP_AUG21_INDCNST_2Q1Q", "VW_RI_GDP_AUG21_INDCRNT_2Q1Q", "VW_RI_GDP_AUG21_INDCRNT_2Q", "VW_RI_GDP_FORECAST_YEARLY", "VW_DS_UNEMPLOYMENT_FORECAST", "VW_VISA_SPENDING_AD", "VW_VISA_SPENDING_AD_YEARLY", "VW_VISA_SPENDING_NON_AD", "VW_VISA_SPENDING_NON_AD_YEARLY", "SCENARIO_TABLE_NEW_SCHEMA", "COMBINATION_DF_NEW_SCHEMA", "VW_INFLATION_SCENARIO", "VW_IFP_IND_CENSUS_POP_C_VALUES", "VW_IFP_IND_CENSUS_LF_C_VALUES", "VW_IFP_IND_CENSUS_BLD_C_VALUES", "VW_IFP_IND_CENSUS_UNT_C_VALUES", "VW_FORECAST_BASELINE_IFP_SCHEMA", "VW_NI_TRANSACTION", "VW_FORECAST_BASELINE_IFP_SCHEMA", "VW_L_VISA_SPEND_ATTRIBUTES", "VW_VISA_SPENDING_AD3", "VW_VISA_SPENDING_NON_AD3", "VW_L_VISA_SPEND_ATTRIBUTES", "VW_CB_RISK", "VW_CB_CREDIT", "VW_L_CB_ATTRIBUTES", "VW_CREDIT_BUREAU", "VW_LF_INDICATORS", "VW_L_NI_TRANSACTION_ATTRIBUTES", "VW_DS_AD_EMP_RATE", "VW_DS_AD_UNEMP_PEOPLE", "VW_LF_INDICATORS", "VW_L_VISA_ECOMMERCE_ATTRIBUTES", "VW_VISA_ECOMMERCE", "VW_DATA_GOV_OVERVIEW", "VW_T_DS_INDICATORS", "VW_T_USECASE", "VW_T_SOURCE", "VW_T_MODELS", "VW_ALTERYX_WORKFLOW_STATUS", "VW_IFP_SV_REPORT", "VW_SV_SUMMARY", "VW_STATISTICAL_COMPARISON_REPORT", "VW_CONSUMER_SPEND_PERC_GDP", "VW_DS_POP_FORE"], "AD": {"url": {"users": "/v1.0/users", "invitations": "/v1.0/invitations", "groups": "/v1.0/groups"}}, "swagger": {"configs": ["analyticalApps", "statisticsInsights", "experimentalStatistics", "officialStatistics", "domains", "survey", "termsAndConditions", "whatsNew", "settings", "notifications", "publications", "sla", "glossary", "header", "footer", "category", "classifications", "indicatorCompare", "spatialAnalytics", "eciBasketAuth", "userJourney", "recommendations", "compute", "reports", "myapps"]}, "censusViewMap": {"VW_IFP_IND_CENSUS_POP_C_VALUES": {"hideExpandView": true, "thresholdValue": 500}, "VW_IFP_IND_CENSUS_LF_C_VALUES": {"hideExpandView": true, "thresholdValue": 500}, "VW_IFP_IND_CENSUS_BLD_C_VALUES": {"hideExpandView": true, "thresholdValue": 30}, "VW_IFP_IND_CENSUS_UNT_C_VALUES": {"hideExpandView": true, "thresholdValue": 100}}, "censusIndicators": ["IFP_IND_CENSUS_POP", "IFP_IND_CENSUS_LF", "IFP_IND_CENSUS_RE_BLD", "IFP_IND_CENSUS_RE_UNT"], "censusNodeDuplicationMap": {"6630": "3496", "6632": "3497", "6631": "3376", "6629": "3527", "6633": "3379"}, "alteryx": {"API_KEY": "8DB82C3F3863F04ed53c8d20ece5c4aa03fc127cd076924d95c11ff", "API_SECRET": "7063b09b9a131920b5a603115ce52372f25adf606e64ca08f884b657f9bbba8c"}, "uaePass": {"REDIRECT_PATH": "/auth/upass/callback", "ACCESS_TOKEN_PATH": "/idshub/token", "USER_INFO_PATH": "/idshub/userinfo"}, "featureGroupMap": {"49997a9f-eb80-457f-a268-8d31e8609818": "genAi", "ac75e827-43bf-42f6-a53b-af01ab60086c": "selfService"}, "dataGovernanceCategory": {"HOME": "home", "USE_CASE": "use_case", "DATA_SCIENCE": "data_science", "WORKFLOW_DETAILS": "workflow_details", "BAYAAN_SVS": "bayaan_svs", "STATISTICAL_INDICATORS": "statistical_indicators", "DATA_SOURCE": "data_source"}, "dataClassifications": {"1": ["OPEN"], "2": ["OPEN", "CONFIDENTIAL"], "3": ["OPEN", "CONFIDENTIAL", "SENSITIVE"], "4": ["OPEN", "CONFIDENTIAL", "SENSITIVE", "SECRET"]}, "classificationProducts": [{"name": "Official Statistics", "key": "Official Statistics"}, {"name": "Experimental Statistics", "key": "Innovative Statistics"}, {"name": "Insight Discovery", "key": "Insight Discovery"}, {"name": "Forcast", "key": "Forcasts"}, {"name": "Scenario Drivers", "key": "<PERSON><PERSON><PERSON>"}, {"name": "Correlation", "key": "Correlation"}], "cmsClassifications": {"OPEN": "0 - Open", "CONFIDENTIAL": "1 - Confidential", "SENSITIVE": "2 - <PERSON><PERSON>", "SECRET": "3 - Secret"}, "dxpUrls": {"PRODUCT": {"CREATE_DATA_SHARE": "/v5/product-data-shares"}, "ASSET": {"DETAIL": "/v5/assets"}, "DATA_SHARE": {"DETAIL": "/v5/data-shares"}, "DELTA_SHARE": {"ACTIVATE_DELTA_SHARE": "/api/2.0/unity-catalog/public/data_sharing_activation/"}, "DATA_PRODUCT_DETAIL": "/v5/products", "LIST_ORGANIZATIONS": "/v1/organizations", "USER_SUBSCRIPTIONS": "/v1/licenses/user"}, "classificationRanking": {"OPEN": 1, "CONFIDENTIAL": 2, "SENSITIVE": 3, "SECRET": 4}, "geospatialAccessGroupsBKP": {"population": ["815316be-989e-4f00-873e-8c04308ee971", "f81af515-cbef-4790-89ea-5ecd60e0d5a5", "643fcd42-10a3-4d7d-a49b-797bbf67f54e", "7ab595c3-bc6c-4676-9e1b-7f1d145abd74"], "realEstate": ["815316be-989e-4f00-873e-8c04308ee971", "52cc3eee-1e14-4a02-a452-0c07a4c6b4c4", "a456f6c6-8af0-4380-8df4-5cfd34966ec8", "1bfe86ce-bf85-401d-a650-2545d694771d"], "labourForce": ["815316be-989e-4f00-873e-8c04308ee971", "6ef68eb3-f6bf-4d02-b94e-9887642b3606", "6f437b36-da26-428e-bb93-4092934daac8", "0b9daf3d-e169-41b8-a4a9-8b29b5cbb77a"], "community": "c369df50-e67c-4c98-a2c1-ab36c3f317eb", "nationality": "c369df50-e67c-4c98-a2c1-ab36c3f317eb", "religion": "c369df50-e67c-4c98-a2c1-ab36c3f317eb"}, "geospatialAccessGroups": {"population": ["fda09427-bb7f-4c36-98a9-0846cad243c4"], "realEstate": ["fda09427-bb7f-4c36-98a9-0846cad243c4"], "labourForce": ["fda09427-bb7f-4c36-98a9-0846cad243c4"], "community": "c369df50-e67c-4c98-a2c1-ab36c3f317eb", "nationality": "c369df50-e67c-4c98-a2c1-ab36c3f317eb", "religion": "c369df50-e67c-4c98-a2c1-ab36c3f317eb"}, "censusOfficialIndicators": ["3496", "3379", "3527", "3497"], "footnote": {"en": {"text": "The 2024 population statistics are based on Abu Dhabi's integrated administrative registers. The figures presented for Abu Dhabi Census 2023 on this page reflect the revised data, in line with our revision policy. For more information about the policy and the methodology used in the Abu Dhabi Census, please {link}. \n To request the non-revised Census 2023(R0) results, please contact us via email at {email}", "email": {"type": "mail", "content": "<EMAIL>"}, "link": {"type": "link", "linkText": "click here", "content": "https://uat-census.scad.gov.ae/Home/About?tab=RevisionPolicy&lang=en"}}, "ar": {"text": " \n للحصول على النسخة الأولى (غير المُحدّثة) من نتائج تعداد 2023 (R0)، يُرجى التواصل معنا عبر البريد الإلكتروني على العنوان {email} * تم إعداد بيانات السكان المقيمين في أبوظبي لعام 2024 بالاعتماد الكامل على السجلات الإدارية في الإمارة. وتعكس الأرقام المعروضة لتعداد أبوظبي 2023 في هذه الصفحة النسخة المُحدّثة من بيانات عام 2023، وفقًا لسياسة التحديث والتنقيح الخاصة بالمركز، للمزيد من المعلومات حول هذه السياسة والمنهجية المعتمدة في التعداد السكاني {link}.", "email": {"type": "mail", "content": "<EMAIL>"}, "link": {"type": "link", "linkText": "اضغط هنا", "content": "https://uat-census.scad.gov.ae/Home/About?tab=RevisionPolicy&lang=ar"}}}}