const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
let constants = require('../config/constants.json');
/**
 * function to get categories content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getClassification(req) {
    log.debug(`>>>>>Entered microservice.classification.controller.getClassification`);
    try {
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsClassificationsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        const data = await getMetaFromCMS(req,cmsLoginUrl, cmsClassificationsUrl, req.user.groups);
        log.debug(`<<<<<Exited microservice.classification.controller.getCategories successfully `);
        const classifications = Object.values(data.classification).map((classification) => {
            const classificationObj = {
                id: classification.id,
                name: classification.name,
                icon_path: `${process.env.CMS_BASEPATH_URL}${classification.icon_path}`,
                light_icon_path: `${process.env.CMS_BASEPATH_URL}${classification.light_icon_path}`,
                node_count: classification.nodes_count,
                isSelected: false
            }
            return classificationObj;
        })
        return classifications;
    } catch (err) {
        log.error(`<<<<<Exited microservice.classification.controller.getClassification on getting CMS data with error ${err}`);
        throw err;
    }
}
module.exports = { getClassification };
