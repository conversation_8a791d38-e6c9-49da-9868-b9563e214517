const Logger = require('scad-library').logger;
const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const constants = require('../config/constants.json');

const log = new Logger().getInstance();
/**
 * function to get infographic-reports content from CMS
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function getInfographicReportsById(req) {
  log.debug('>>>>>Entered infographic-reports-microservice.infographic-reports.controller.getInfographicReports');
  return new Promise(async (resolve, reject) => {
    try{
      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      const cmsInfographicReportUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_INFOGRAPHIC_REPORT_URL}/${req.params.id}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      const cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsInfographicReportUrl, req.user.groups)

      const response = cmsResponse[0]
      
      return resolve(response)
    }
    catch(err){
      return reject(err)
    }
  });
}

async function getInfographicReports(req) {
  log.debug('>>>>>Entered infographic-reports-microservice.infographic-reports.controller.getInfographicReports');
  return new Promise(async (resolve, reject) => {
    try{
      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      const cmsInfographicReportUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_INFOGRAPHIC_REPORTS_URL}?content_classification_key=reports`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      const cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsInfographicReportUrl, req.user.groups)
      let response = {}
      cmsResponse.forEach(report=>{
        if (report.domain){
          if (!response[report.domain])
            response[report.domain] = []
          response[report.domain].push(report)
        }
      })
      response = Object.entries(response).map(([domain,nodes])=>{
        const domainObj = {
          domain:domain,
          nodes:nodes
        }
        return domainObj
      })
      
      return resolve(response)
    }
    catch(err){
      return reject(err)
    }
  });
}

module.exports = { getInfographicReportsById, getInfographicReports };
