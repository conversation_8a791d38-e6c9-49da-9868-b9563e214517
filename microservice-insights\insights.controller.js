require('dotenv').config();
const Logger = require('scad-library').logger;
const moment = require('moment')

const log = new Logger().getInstance();
const { listInsightData, addInsightData, getInsightData, updateInsightData, approveInsightData, rejectInsightData, deleteInsightData, getSubmitRequestInsightsData, updateSubmitRequestInsightsData, getInsightsData, requestEditInsightData } = require('./services/executeQuery.service');
const { sendInsightsEmail } = require('./services/sendEmail.service');
const { validateEmailContent } = require('../services/helpers/helper');

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function listInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.listInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const nodeId = req.params.id           
            const insightUsers = process.env.INSIGHT_USERS.split(',')
            let isInsightUser = insightUsers.includes(req.user.preferred_username)?true:false
            let showSubmitForApproval = (process.env.INSIGHT_APPROVER == req.user.preferred_username)?false:insightUsers.includes(req.user.preferred_username)?true:false
            let isApprover = (process.env.INSIGHT_APPROVER == req.user.preferred_username)?true:false
            let enableSubmitForApproval = false
            let enableRequestForEdit = false
            listInsightData(nodeId)
                .then(results => {
                    if (process.env.INSIGHT_APPROVER == req.user.preferred_username){
                        log.debug(`List Insights: Request user is an approver`);
                        results = results.filter(insight => {return insight.EMAILSENT == 1})
                        results.forEach(insight => {
                                insight.isEdit = true
                                insight.showApprove = true
                                insight.showApprove = insight.STATUS == 'PENDING'?insight.showApprove:false
                                insight.showDelete = true
                                enableRequestForEdit = enableRequestForEdit || insight.STATUS == 'PENDING'
                            }
                        )
                    }
                    else if(insightUsers.includes(req.user.preferred_username)){
                        log.debug(`List Insights: Request user is an SME`);
                        results = results.filter(insight => {return (insight.STATUS == 'PENDING' && insight.EMAIL != req.user.preferred_username)?false:true})
                        results.forEach(insight => {
                                insight.isEdit = insight.EMAIL == req.user.preferred_username?true:false
                                insight.showApprove = false
                                insight.showDelete = insight.EMAIL == req.user.preferred_username?true:false
                                enableSubmitForApproval = enableSubmitForApproval || (insight.EMAILSENT == 0)                         }
                        )
                    }
                    else{
                        log.debug(`List Insights: Request user is a reqular user`);
                        results = results.filter(insight => {return insight.STATUS == 'PENDING'?false:true})
                        results.forEach(insight => {
                                insight.isEdit = false
                                insight.showApprove = false
                                insight.showDelete = false                            
                            }
                        )
                    }
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.listInsights successfully `);
                    
                    return resolve(
                        {
                            "data":results,
                            "isInsightUser":isInsightUser,
                            "isApprover": isApprover,
                            "showSubmitForApproval": showSubmitForApproval,
                            "enableSubmitForApproval":enableSubmitForApproval,
                            "enableRequestForEdit":enableRequestForEdit,
                            "enableApprove":enableRequestForEdit,
                            "enableReject":enableRequestForEdit
                        }
                    );
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.listInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.listInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function addInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.addInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const reqBody = req.body
            
            if (!process.env.INSIGHT_USERS.split(',').includes(req.user.preferred_username)){
                log.debug(`Add Insights: Request user is a regular user`);
                return reject("You don't have permission to add insights")
            }

            validateEmailContent(reqBody.insight)

            log.debug(`Add Insights: Request user is an SME`);

            insightData = {}
            insightData.email = req.user.preferred_username;
            insightData.user = req.user.name;
            insightData.insight = reqBody.insight;
            insightData.nodeId = reqBody.nodeId;
            insightData.nodeTitle = reqBody.nodeTitle;
            insightData.nodeLink = reqBody.nodeLink;
            insightData.date = moment().format('DD/MM/YYYY')
            addInsightData(insightData)
                .then(results => {
                    sendInsightsEmail(req,'ADD')
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.addInsights successfully `);
                    return resolve({
                        "message": "Insights added successfully. Your insight will appear once approved",
                        "status": "success"
                    });
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.addInsights not successfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.addInsights with error ${err}`);
            reject(err);
        }
    })
}

async function sendInsightsForApproval(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.addInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            if (!process.env.INSIGHT_USERS.split(',').includes(req.user.preferred_username)){
                log.debug(`Add Insights: Request user is a regular user`);
                return reject("You don't have permission to request approval insights")
            }

            log.debug(`Submit for Approval Insights: Request user is an SME`);
            const insightId = req.body.ids
            let insightEmail = req.user.preferred_username
            const insights = await getInsightsData(insightId)
            if (!Array.isArray(insightId) || insightId.length === 0) {
                log.debug(`<<<<<Exited insights-microservice.insights.controller.sendInsightsForApproval successfully `);
                return resolve({
                    "message": "No insight IDs provided for approval",
                    "status": "failed"
                });
            }
            
            pending_insights = await getSubmitRequestInsightsData(insightEmail, insightId)

            if (!pending_insights.length){
                log.debug(`<<<<<Exited insights-microservice.insights.controller.sendInsightsForApproval successfully `);
                return resolve({
                    "message": "Approve request already sent",
                    "status": "success"
                });
            }


            insight_data = {}
            insight_data.nodeTitle = pending_insights[0].NODE_TITLE;
            insight_data.nodeLink = pending_insights[0].NODE_LINK;
            
            updateSubmitRequestInsightsData(insightEmail, insightId)
                .then(results => {
                    sendInsightsEmail(req,'SUBMIT_REQUEST',pending_insights)
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.sendInsightsForApproval successfully `);
                    return resolve({
                        "message": "Approve request sent successfully",
                        "status": "success"
                    });
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.sendInsightsForApproval not successfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.sendInsightsForApproval with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function updateInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.updateInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const id = req.params.id
            const reqBody = req.body
            let insight = await getInsightData(id)
            insight = insight.length?insight[0]:resolve("No insight found with given id")
            if (!insight.EMAIL == req.user.preferred_username)
                return resolve("You don't have permission to update this insight")
            
            const date = moment().format('DD/MM/YYYY')
            validateEmailContent(reqBody.insight)

            insightData = {}
            insightData.id = insight.ID
            insightData.email = insight.EMAIL;
            insightData.insight = reqBody.insight;
            insightData.nodeId = reqBody.nodeId;
            insightData.nodeTitle = reqBody.nodeTitle;
            insightData.nodeLink = reqBody.nodeLink;
            insightData.prevInsight = insight.INSIGHT;
            insightData.date = date
            insightData.status = "PENDING"

            updateInsightData(insightData)
                .then(async results => {
                    sendInsightsEmail(req,'UPDATE',insightData)
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.updateInsights successfully `);
                    return resolve({"message":"Insight update","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.updateInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.updateInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function deleteInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.approveInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            
            const id = req.params.id
            let insight = await getInsightData(id)
            insight = insight[0]
            if (![insight.EMAIL,process.env.INSIGHT_APPROVER].includes(req.user.preferred_username))
                return resolve("You don't have permission to delete insights")

            deleteInsightData(id)
                .then(async results => {
                    sendInsightsEmail(req,'DELETE',insight)
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.deleteInsights successfully `);
                    return resolve({"message":"Insight deleted","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.deleteInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.deleteInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function approveInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.approveInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            if (!(process.env.INSIGHT_APPROVER == req.user.preferred_username))
                return resolve("You don't have permission to approve insights")
            // const id = req.params.id
            const ids = req.body.ids
            const date = moment().format('DD/MM/YYYY')
            approveInsightData(ids,date)
                .then(async results => {
                    const insights = await getInsightsData(ids)
                    if (!insights.length)
                        return reject("No insight with given id found!")
                    let tempEmails = []
                    let userRecords = {}
                    insights.forEach(element => {
                        tempEmails.push(element.EMAIL)
                        userRecords[element.EMAIL] = element.USER_NAME
                    })
                    let emails = [...new Set(tempEmails)]
                    emails.forEach(email => {
                        sendInsightsEmail(req,'APPROVE',insights,email,userRecords[email])
                    })
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.approveInsights successfully `);
                    return resolve({"message":"Insight approved","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.approveInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.approveInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function rejectInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.rejectInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            if (!process.env.INSIGHT_APPROVER == req.user.preferred_username)
                return resolve("You don't have permission to reject insights")
            // const id = req.params.id
            const ids = req.body.ids
            const date = moment().format('DD/MM/YYYY')
            rejectInsightData(ids,date)
                .then(async results => {
                    const insights = await getInsightsData(ids)
                    if (!insights.length)
                        return reject("No insight with given id found!")
                    let tempEmails = []
                    let userRecords = {}
                    insights.forEach(element => {
                        tempEmails.push(element.EMAIL)
                        userRecords[element.EMAIL] = element.USER_NAME
                    })
                    let emails = [...new Set(tempEmails)]
                    emails.forEach(email => {
                        sendInsightsEmail(req,'REJECT',insights,email,userRecords[email])
                    })

                    log.debug(`<<<<<Exited insights-microservice.insights.controller.rejectInsights successfully `);
                    return resolve({"message":"Insight rejected","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.rejectInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.rejectInsights with error ${err}`);
            reject(err);
        }
    })
}


/**
 * function to get terms-and-conditions content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function requestEditInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insights.controller.requestEditInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            if (!(process.env.INSIGHT_APPROVER == req.user.preferred_username))
                return resolve("You don't have permission to request edit")
            // const id = req.params.id
            const ids = req.body.ids
            const date = moment().format('DD/MM/YYYY')
            requestEditInsightData(ids,date)
                .then(async results => {
                    const insights = await getInsightsData(ids)
                    if (!insights.length)
                        return reject("No insight with given id found!")
                    let tempEmails = []
                    let userRecords = {}
                    insights.forEach(element => {
                        tempEmails.push(element.EMAIL)
                        userRecords[element.EMAIL] = element.USER_NAME
                    })
                    let emails = [...new Set(tempEmails)]
                    emails.forEach(email => {
                        sendInsightsEmail(req,'REQUEST_EDIT',insights,email,userRecords[email])
                    })

                    log.debug(`<<<<<Exited insights-microservice.insights.controller.requestEditInsights successfully `);
                    return resolve({"message":"Requested edit for insights","status":"success"});
                })
                .catch((err) => {
                    
                    log.debug(`<<<<<Exited insights-microservice.insights.controller.requestEditInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.insights.controller.requestEditInsights with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { listInsights, addInsights, approveInsights, updateInsights, deleteInsights, rejectInsights, sendInsightsForApproval, requestEditInsights };
