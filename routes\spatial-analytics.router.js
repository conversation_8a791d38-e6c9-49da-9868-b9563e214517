const express = require('express');
const router = new express.Router();
const spatialAnalyticsController = require('../microservice-spatial-analytics/spatial-analytics.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
  try {
    const data = await spatialAnalyticsController.getSpatialAnalytics(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
