const scadLib = require('scad-library');
const Logger = scadLib.logger;
const log = new Logger().getInstance();
const getPyramidData = async (results, visualization,type) => {
    log.debug('>>>>> Enter services.chart-services.stacked-vertical-bar-chart.getPyramidData');

    if (type == 'geospatial') {
        return visualization;
    } else {
        return new Promise((resolve, reject) => {
            try {
                const listOfObj = [];
                const finalObj = [];
                // getting dtae field from all object
                let groupKey = visualization.groupByColumn;
                results.map((obj) => {
                    let pushObj = {};
                    pushObj[groupKey] = obj[groupKey];
                    listOfObj.push(pushObj);
                });

                const uniqueList = [...new Set(listOfObj)]; // to get unique list of date
                uniqueList.map((obj1) => {
                    results.forEach((item) => {
                        Object.entries(item).forEach(([key, value]) => {
                            if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                                delete item[key];
                            }
                        });
                        const obj = {};
                        // grouping all indicator title : indicator value for each date
                        if (obj1[groupKey] === item[groupKey]) {
                            const key = item[visualization.dbColumn];
                            obj[groupKey] = item[groupKey];
                            obj[key] = item.VALUE;
                        }
                        if (Object.keys(obj).length !== 0) finalObj.push(obj);
                    });
                });

                // Removing duplicate object
                finalObj.map((item) => {
                    finalObj.map((obj, index) => {
                        if (obj[groupKey] === item[groupKey]) {
                            finalObj[index] = Object.assign(item, obj);
                        }
                    });
                });

                visualization.seriesMeta[0].series[0].data = [...new Set(finalObj)];
                log.debug('<<<<< Exit services.chart-services.stacked-vertical-bar-chart.getPyramidData successfully');
                resolve(visualization);
            } catch (err) {
                log.error(`<<<<< Exit services.chart-services.stacked-vertical-bar-chart.getPyramidData with error ${err}`);
                reject(err);
            }
        });
    };
};

module.exports = {
    getPyramidData
};
