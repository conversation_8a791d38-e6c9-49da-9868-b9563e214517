const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const nunjucks = require('nunjucks');

async function sendShareMyAppsEmail(data) {
    new Promise((resolve, reject) => {

        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });


        const recepient = data.recepient
        const sender = data.senderName
        

        var shareMyAppsMailOptionsResponse = {
            from: process.env.SYSTEM_MAILID,
            to: recepient,
            subject: `Apps shared by ${sender}`,
            attachments: [
                {
                    filename: "new-message-banner.png",
                    path: process.cwd() + "/emails/email-new-message-banner.png",
                    cid: "new-message-banner"
                },
            ],
            html: nunjucks.render(
                "microservice-myapps/app-share-email-notification.njk",
                { ...data, emailBanner: "new-message-banner" }
            )
        };

        transporter.sendMail(shareMyAppsMailOptionsResponse, function (error, info) {
            if (error) {
                reject(error);
            }

            log.info(`Email sent successfully to ${recepient} \n${info.response}`);
            resolve();
        })
    });
}


async function requestAccessShareMyAppsEmail(data) {
    new Promise((resolve, reject) => {

        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });


        const requestorEmail = data.requestorEmail
        const requestorName = data.requestorName
        

        var requestAccessShareMyAppsMailOptionsResponse = {
            from: requestorEmail,
            to: process.env.SYSTEM_MAILID,
            subject: `Request Access By ${requestorName}`,
            attachments: [
                {
                    filename: "new-message-banner.png",
                    path: process.cwd() + "/emails/email-new-message-banner.png",
                    cid: "new-message-banner"
                },
            ],
            html: nunjucks.render(
                "microservice-myapps/request-node-access.njk",
                { ...data, emailBanner: "new-message-banner" }
            )
        };

        transporter.sendMail(requestAccessShareMyAppsMailOptionsResponse, function (error, info) {
            if (error) {
                reject(error);
            }

            log.info(`Email sent successfully to ${requestorEmail} \n${info.response}`);
            resolve();
        })
    });
}

module.exports = {sendShareMyAppsEmail,requestAccessShareMyAppsEmail}