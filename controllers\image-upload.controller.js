const { uploadImage, isValidImageType, isValidImageSize } = require("../services/image-upload.service");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

/**
 * Controller to handle image upload for publications
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
async function uploadPublicationImage(req, res) {
  log.debug(`>>>>>Entered image-upload.controller.uploadPublicationImage`);
  
  try {
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No image file provided"
      });
    }

    const { buffer, originalname, mimetype } = req.file;
    const { publicationId } = req.body;

    // Validate required fields
    if (!publicationId) {
      return res.status(400).json({
        success: false,
        message: "Publication ID is required"
      });
    }

    // Validate image type
    if (!isValidImageType(mimetype)) {
      return res.status(400).json({
        success: false,
        message: "Invalid image type. Allowed types: JPEG, PNG, GIF, WebP"
      });
    }

    // Validate image size (5MB limit)
    if (!isValidImageSize(buffer, 5)) {
      return res.status(400).json({
        success: false,
        message: "Image size too large. Maximum size is 5MB"
      });
    }

    // Upload image to S3
    const imageUrl = await uploadImage(buffer, originalname, mimetype, publicationId);

    if (!imageUrl) {
      return res.status(500).json({
        success: false,
        message: "Failed to upload image"
      });
    }

    log.info(`Image uploaded successfully for publication ${publicationId}: ${imageUrl}`);

    // Return success response with image URL
    res.status(200).json({
      success: true,
      message: "Image uploaded successfully",
      data: {
        publicationId,
        imageUrl,
        originalName: originalname
      }
    });

  } catch (error) {
    log.error(`Error in uploadPublicationImage: ${error}`);
    res.status(500).json({
      success: false,
      message: "Internal server error"
    });
  }
}

module.exports = {
  uploadPublicationImage
};
