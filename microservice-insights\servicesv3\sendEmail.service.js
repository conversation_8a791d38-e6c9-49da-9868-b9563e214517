const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const nunjucks = require('nunjucks');

async function sendChartInsightsEmail(req, purpose, insight = {}, email = "", business_name = "", approver_list, approver_name_list) {
    new Promise((resolve, reject) => {
        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: false,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });

        let approverActName;
        const approveRequesterName = req.user.name;
        const setComments = req.body.comments
        let businessMailOptionsResponse;
        let approverMailOptions;
        let approveInsights;
        let insights;
        let requestEditInsights;
        switch (purpose) {
            case 'SUBMIT_REQUEST':
                approverActName = approver_name_list[0]
                businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: req.user.preferred_username,
                    subject: `Approval Request Sent!`,
                    attachments: [
                        {
                            filename: 'email-new-message-banner.png',
                            path: process.cwd() + '/emails/email-new-message-banner.png',
                            cid: 'email-new-message-banner'
                        },
                    ],
                    html: nunjucks.render(
                        "microservice-chart-insights/sent-for-approval.njk",
                        {
                            name: approveRequesterName,
                            approverName: approverActName,
                            nodeLink: insight[0].NODE_LINK,
                            nodeTitle: insight[0].NODE_TITLE,
                            insightsList: insight,
                            emailBanner: "email-new-message-banner"
                        }
                    )
                };
                approverMailOptions = {
                    from: process.env.SYSTEM_MAILID,
                    to: approver_list[0],
                    subject: `Approval process for Insight!`,
                    attachments: [
                        {
                            filename: 'email-new-message-banner.png',
                            path: process.cwd() + '/emails/email-new-message-banner.png',
                            cid: 'email-new-message-banner'
                        },
                    ],
                    html: nunjucks.render(
                        "microservice-chart-insights/new-approval-request.njk",
                        {
                            approverName: approverActName,
                            businessName: approveRequesterName,
                            nodeLink: insight[0].NODE_LINK,
                            nodeTitle: insight[0].NODE_TITLE,
                            insightsList: insight,
                            emailBanner: "email-new-message-banner"
                        }
                    )
                };
                transporter.sendMail(approverMailOptions, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                            if (error) {
                                reject(error);
                            }
                            log.info(`Email sent successfully to user ${req.user.preferred_username} \n${info?.response}`);
                            resolve();
                        })
                    }
                    log.info(`Email sent successfully to Approver \n${info?.response}`);
                    resolve();
                })
                break;
            
            case 'APPROVE':
                approverActName = approver_name_list
                approveInsights = insight.filter(element => element.EMAIL == email)
                businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: email,
                    subject: `Insight Approval`,
                    attachments: [
                        {
                            filename: 'email-new-message-banner.png',
                            path: process.cwd() + '/emails/email-new-message-banner.png',
                            cid: 'email-new-message-banner'
                        },
                    ],
                    html: nunjucks.render(
                        "microservice-chart-insights/insights-approved.njk",
                        {
                            businessName: business_name,
                            chartTitle: insight[0].NODE_TITLE,
                            approverName: approverActName,
                            insightsList: approveInsights,
                            chartLink: insight[0].NODE_LINK,
                            emailBanner: "email-new-message-banner"
                        }
                    )
                };
                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        log.info(`Email sent successfully to user ${email} \n${info?.response}`);
                        resolve();
                    }
                })
                break;
         
            case 'REJECT':
                approverActName = approver_name_list
                insights = insight.filter(element => element.EMAIL == email)
                businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: email,
                    subject: `Insight Rejected`,
                    attachments: [
                        {
                            filename: 'email-new-message-banner.png',
                            path: process.cwd() + '/emails/email-new-message-banner.png',
                            cid: 'email-new-message-banner'
                        },
                    ],
                    html: nunjucks.render(
                        "microservice-chart-insights/insights-rejected.njk",
                        {
                            businessName: business_name,
                            chartTitle: insight[0].NODE_TITLE,
                            approverName: approverActName,
                            insightsList: insights,
                            chartLink: insight[0].NODE_LINK,
                            emailBanner: "email-new-message-banner"
                        }
                    )
                };
                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        log.info(`Email sent successfully to user ${email} \n${info?.response}`);
                        resolve();
                    }
                })
                break;
            
            case 'REQUEST_EDIT':
                approverActName = approver_name_list
                requestEditInsights = insight.filter(element => element.EMAIL == email)
                businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: email,
                    subject: `Insight Edit Request`,
                    attachments: [
                        {
                            filename: 'email-new-message-banner.png',
                            path: process.cwd() + '/emails/email-new-message-banner.png',
                            cid: 'email-new-message-banner'
                        },
                    ],
                    html: nunjucks.render(
                        "microservice-chart-insights/insights-edit-request.njk",
                        {
                            businessName: business_name,
                            chartTitle: insight[0].NODE_TITLE,
                            approverName: approverActName,
                            insightsList: requestEditInsights,
                            chartLink: insight[0].NODE_LINK,
                            setComments: setComments,
                            emailBanner: "email-new-message-banner"
                        }
                    )
                };
                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        log.info(`Email sent successfully to user ${email} \n${info?.response}`);
                        resolve();
                    }
                })
                break;
        }
    });
}


module.exports = { sendChartInsightsEmail }