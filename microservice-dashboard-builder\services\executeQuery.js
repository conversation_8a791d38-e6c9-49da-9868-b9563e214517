const db = require('../../services/database.service');
const { createDashboardQuery, getDashboardDetailQuery, getDashboardQuery, getDashboardListQuery, deleteDashboardQuery, editDashboardQuery, createDashboardNodesQuery, deleteDashboardNodesQuery, getDashboardDetailWithThumbnailQuery, createShareDashboardQuery, createShareDashboardNodesQuery, getDashboardShareDetailQuery, getShareDashboardListQuery, getUserDashboardListDataQuery, getDashboardRequestorQuery, deleteShareDashboardQuery, getAppsRequestQuery } = require('./getQuery.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

/**
 * Retrieves list of dashboards available to the user.
 * @param {Object} userEmail The email of the user.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard list fails.
 */
async function getDashboardListData(userEmail, page, limit, search,sort) {
  try {
    const { query, binds, options } = await getDashboardListQuery(userEmail, page, limit, search,sort);
    const result = await getData(query, binds, options);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard list data:${err}`);
    throw err;
  }
}

/**
 * Retrieves list of dashboards available to the user.
 * @param {Object} userEmail The email of the user.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard list fails.
 */
async function getShareDashboardListData(type,userEmail, page, limit, search,sort) {
  try {
    const { query, binds, options } = await getShareDashboardListQuery(type,userEmail, page, limit, search,sort);
    const result = await getData(query, binds, options);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard list data:${err}`);
    throw err;
  }
}

/**
 * Retrieves list of dashboards available to the user.
 * @param {Object} userEmail The email of the user.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard list fails.
 */
async function getUserDashboardListData(userEmail) {
  try {
    const { query, binds, options } = await getUserDashboardListDataQuery(userEmail);
    const result = await getData(query, binds, options);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard list data:${err}`);
    throw err;
  }
}


/**
 * Retrieves dashboard information including name and owner.
 * @param {Object} dashboard The object containing id and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard information fails.
 */
async function getDashboardData(dashboard) {
  try {
    const { query, binds } = await getDashboardQuery(dashboard);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard data:${err}`);
    throw err;
  }
}

/**
 * Retrieves dashboard information including name and owner.
 * @param {Object} dashboard The object containing id and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard information fails.
 */
async function getDashboardRequestorData(dashboardId,userEmail,requestor) {
  try {
    const { query, binds } = await getDashboardRequestorQuery(dashboardId,userEmail,requestor);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard data:${err}`);
    throw err;
  }
}

/**
 * Retrieves detailed dashboard information including name and its nodes.
 * @param {Object} dashboard The object containing id and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard information fails.
 */
async function getDashboardDetailData(dashboard) {
  try {
    const { query, binds, options } = await getDashboardDetailQuery(dashboard);
    const result = await getData(query, binds, options);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard detail data:${err}`);
    throw err;
  }
}

/**
 * Retrieves detailed dashboard information including name and its nodes.
 * @param {Object} dashboard The object containing id and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard information fails.
 */
async function getDashboardShareDetailData(dashboard,requestor) {
  try {
    const { query, binds, options } = await getDashboardShareDetailQuery(dashboard,requestor);
    const result = await getData(query, binds, options);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard detail data:${err}`);
    throw err;
  }
}

/**
 * Retrieves detailed dashboard information including name and its nodes.
 * @param {Object} dashboard The object containing id and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if retrieving dashboard information fails.
 */
async function getDashboardDetailWithThumbnailData(dashboard) {
  try {
    const { query, binds, options } = await getDashboardDetailWithThumbnailQuery(dashboard);
    const result = await getData(query, binds, options);
    return result;
  } catch (err) {
    log.error(`Error getting dashboard detail data:${err}`);
    throw err;
  }
}

/**
 * Creates a record with the provided dashboard information.
 * @param {Object} dashboard The object containing id, name and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if creating dashboard fails.
 */
async function createDashboardData(dashboard) {
  try {
    const { query, binds } = await createDashboardQuery(dashboard);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error creating dashboard data:${err}`);
    throw err;
  }
}

/**
 * Creates a record with the provided dashboard information.
 * @param {Object} dashboard The object containing id, name and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if creating dashboard fails.
 */
async function createShareDashboardData(dashboard) {
  try {
    const { query, binds } = await createShareDashboardQuery(dashboard);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error creating dashboard data:${err}`);
    throw err;
  }
}

/**
 * Edit the record of the provided dashboard.
 * @param {Object} dashboard The object containing id, name and owner of the dashboard.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if editing dashboard fails.
 */
async function editDashboardData(dashboard) {
  try {
    const { query, binds } = await editDashboardQuery(dashboard);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error editing dashboard data:${err}`);
    throw err;
  }
}

/**
 * Delete the record of the provided dashboard.
 * @param {string} dashboardId The unique id of the dashboard to be deleted.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if deleting dashboard fails.
 */
async function deleteDashboardData(dashboard) {
  try {
    const { query, binds } = await deleteDashboardQuery(dashboard);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error deleting dashboard data:${err}`);
    throw err;
  }
}

/**
 * Creates nodes data for the provided dashboard.
 * @param {string} dashboardId The unique id of the dashboard of which nodes should be created.
 * @param {Array} nodes An array of nodes to be created.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if creating nodes fail.
 */
async function createDashboardNodesData(dashboardId, nodes) {
  try {
    const { query, binds } = await createDashboardNodesQuery(dashboardId, nodes);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error creating dashboard nodes data:${err}`);
    throw err;
  }
}

/**
 * Creates nodes data for the provided dashboard.
 * @param {string} dashboardId The unique id of the dashboard of which nodes should be created.
 * @param {Array} nodes An array of nodes to be created.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if creating nodes fail.
 */
async function createShareDashboardNodesData(dashboardId, nodes) {
  try {
    const { query, binds } = await createShareDashboardNodesQuery(dashboardId, nodes);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error creating dashboard nodes data:${err}`);
    throw err;
  }
}

/**
 * Deletes nodes for the provided dashboard.
 * @param {string} dashboardId The unique id of the dashboard of which nodes to be deleted.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if deleting nodes fail.
 */
async function deleteDashboardNodesData(dashboardId) {
  try {
    const { query, binds } = await deleteDashboardNodesQuery(dashboardId);
    const result = await getData(query, binds);
    return result;
  } catch (err) {
    log.error(`Error deleting dashboard nodes data:${err}`);
    throw err;
  }
}

async function deleteShareDashboardData(shareId,requestor,userEmail) {
  try {
    const {query,binds} = await deleteShareDashboardQuery(shareId,requestor,userEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.deleteShareData with error ${err}`);
    throw err;
  }
}

async function getAppsRequestData(requestorEmail){
  try {
    const {query,binds} = await getAppsRequestQuery(requestorEmail);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-dashboard-builder.executeQuery.service.getAppsRequestData with error ${err}`);
    throw err;
  }
}

/**
 * Executes the provided query with bind variables and options.
 * @param {string} query The query to be executed.
 * @param {Object} binds The bind variables, if any for the query.
 * @param {Object} options The options, if any to be passed into execute method.
 * @returns {Promise<Object>} A Promise resolving to the fetched data.
 * @throws {Error} Throws an error if deleting nodes fail.
 */
async function getData(query, binds,options={}) {
  try {
    log.debug(`>>>>> Enter microservice-dashboard-builder.services.executeQuery.getData`);
    let data = await db.simpleExecute(query, binds,options);
    log.debug(`<<<<< Exit microservice-dashboard-builder.services.executeQuery.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-dashboard-builder.services.executeQuery.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getDashboardListData,
  getShareDashboardListData,
  getDashboardData,
  getDashboardDetailData,
  getDashboardShareDetailData,
  getDashboardDetailWithThumbnailData,
  createDashboardData,
  createShareDashboardData,
  editDashboardData,
  deleteDashboardData,
  createDashboardNodesData,
  createShareDashboardNodesData,
  deleteDashboardNodesData,
  getUserDashboardListData,
  getDashboardRequestorData,
  deleteShareDashboardData,
  getAppsRequestData
};
