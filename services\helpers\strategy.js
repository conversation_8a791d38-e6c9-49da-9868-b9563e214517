const passport = require('passport-strategy');
const util = require('util');
const { IFPError } = require('../../utils/error');

function ApiKeyStrategy(verify) {
    passport.Strategy.call(this);
    this.name = 'apikey';
    this._verify = verify;
    this._passReqToCallback = true;
}

util.inherits(ApiKeyStrategy, passport.Strategy);

ApiKeyStrategy.prototype.authenticate = function(req, options) {
    try{
        var apiKey = req.headers['x-api-key'] || req.query.apiKey;

        if (!apiKey) {
            // return this.fail({ message: options.badRequestMessage || 'Missing API Key' }, 400);
            throw new IFPError(400,'Missing API Key')
        }

        const self = this;

        function verified(err, user, info) {
            if (err) {
                return self.error(err);
            }
            if (!user) {
                return self.fail(info);
            }
            self.success(user, info);
        }

        try {
            this._verify(api<PERSON><PERSON>, verified);
        } catch (exp) {
            return self.error(exp);
        }
    }
    catch(error){
        throw error;
    }
};


module.exports = {
    ApiKeyStrategy: ApiKeyStrategy
}