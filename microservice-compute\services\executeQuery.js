const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const {getComputedDataQuery, getComputeNodeInfoQuery, getIndicatorConfigurationQuery} = require('./getQuery.service')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getComputedData(indicatorId, viewName,dimensions,operation) {
  try {
    let {query,binds} = await getComputedDataQuery(indicatorId, viewName, dimensions, operation)
      
      let data = await clkdb.simpleExecute(query,binds)
      log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData successfully`);
      return data
          
  }
  catch(err){
    log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.createCompareAppData with error ${err}`);
    throw err;
  }
}

async function getIndicatorConfiguration(indicatorId) {
  try {
    let {query,binds} = await getIndicatorConfigurationQuery(indicatorId)
      
      let data = await clkdb.simpleExecute(query,binds)
      log.debug(`<<<<< Exit microservice-compute.services.executeQuery.getIndicatorConfiguration successfully`);
      return data
          
  }
  catch(err){
    log.error(`<<<<< Exited microservice-compute.services.executeQuery.getIndicatorConfiguration with error ${err}`);
    throw err;
  }
}

async function getComputeNodeInfo(node,lang) {
  return new Promise((resolve, reject) => {
    getComputeNodeInfoQuery(node,lang).then((results) => {
      getData(results.query,results.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.addAppsToMyAppsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.addAppsToMyAppsData with error ${err}`);
      reject(err);
    })
  })
}

async function getData(query,binds={}) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-indicator-compare.services.executeQuery.service.getData`);
    if (query.includes('VW_INDICATOR_MAP')){
      clkdb.simpleExecute(query,binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
    }
    else{
      db.simpleExecute(query,binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
      }
  });
}


module.exports = {
  getComputedData,
  getIndicatorConfiguration,
  getComputeNodeInfo
}