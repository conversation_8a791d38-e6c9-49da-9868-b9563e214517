const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
let constants = require('../config/constants.json');
/**
 * Retrieves header content from CMS
 */
async function getHeader(req) {
  log.debug(`>>>>>Entered header-microservice.header.controller.getHeader`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsHeaderUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_HEADER_URL}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req, cmsLoginUrl, cmsHeaderUrl, req.user.groups);

    const boolKeys = [
      "show_accessibility",
      "show_language",
      "show_notifications",
      "show_themes"
    ]

    const iconKeys = [
      "notification_icon",
      "notification_light_icon",
      "language_icon",
      "language_light_icon",
      "accessibility_icon",
      "accessibility_light_icon",
      "search_icon",
      "search_light_icon",
      "dark_theme_dark_icon",
      "dark_theme_light_icon",
      "light_theme_dark_icon",
      "light_theme_light_icon",
      "site_logo",
      "site_logo_light",
      "site_slogan_light",
      "site_slogan"
    ]
    data.forEach(element => {
      Object.keys(element).forEach(key => {
        if (boolKeys.includes(key)) {
          element[key] = ['Yes', 'True'].includes(element[key]) ? true : false;
        }
        if (iconKeys.includes(key)) {
          if (element[key])
            element[key] = `${process.env.CMS_BASEPATH_URL}${element[key]}`;
        }

      })
    })
    log.debug(`<<<<<Exited header-microservice.header.controller.getHeader successfully `);
    return data[0];
  } catch (err) {
    log.error(`<<<<<Exited header-microservice.header.controller.getHeader on getting CMS data with error ${err}`);
    throw err;
  }
}

/**
 * Retrieves dyanamic header content from CMS
 */
async function getDyanamicHeader(req) {
  log.debug(`>>>>>Entered header-microservice.header.controller.getDyanamicHeader`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsHeaderUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DYANAMIC_HEADER_URL}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req, cmsLoginUrl, cmsHeaderUrl, req.user.groups);

    const iconKeys = [
      "dark_icon",
      "light_icon"
    ]
    const basePath = process.env.CMS_BASEPATH_URL;
    const processItems = (items) =>
      items.map(({ children = [], ...item }) => ({
        ...item,
        ...Object.fromEntries(
          iconKeys.map(key => [
            key,
            item[key] ? `${basePath}${item[key]}` : item[key]
          ])
        ),
        children: processItems(children),
      }));
    data.primary = processItems(data.primary);
    data.secondary = processItems(data.secondary);

    log.debug(`<<<<<Exited header-microservice.header.controller.getDyanamicHeader successfully `);
    return data;
  } catch (err) {
    log.error(`<<<<<Exited header-microservice.header.controller.getDyanamicHeader on getting CMS data with error ${err}`);
    throw err;
  }
}

module.exports = { getHeader, getDyanamicHeader };
