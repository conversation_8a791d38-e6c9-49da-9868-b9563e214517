const clkdb = require("../../../services/clk-database.service");
const db = require('../../../services/database.service');
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const {
  getUsersDataQuery,
  getUsersByStatusQuery,
  getTopUsersByUsageQuery,
  getAverageUsageDurationQuery,
  getPlatformUsageByHourQuery,
  getUserWithGroupQuery,
  generateUsersCountQuery,
  generateUserQuery,
  generateTotalEntitiesCountandSensitieve,
  generateActiveAndNonActiveUsersQuery,
  getEntityDomains,
  generateUserAccessQuery,
  generateActiveusersQuery,
  generateConsolidatedActiveUsers,
  generateGeospatialOverviewQuery,
  generateToolsOverviewQuery,
  generateUsersListQuery,
  generateEntityTotalCount,
  generateActiveUsersListBasedOnEntity
} = require("./getQuery.service");

async function getUsersData(filter) {
  const { query, binds } = getUsersDataQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getUsersData with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getUsersByStatus(filter) {
  const { query, binds } = getUsersByStatusQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    const activeCount =
      Number(data.find((d) => d.status == "Active")?.user_count) || 0;
    const inActiveCount =
      Number(data.find((d) => d.status == "Inactive")?.user_count) || 0;
    return {
      active: activeCount,
      inActive: inActiveCount,
      total: activeCount + inActiveCount,
    };
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getUsersByStatus with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getTopUsersByUsage(filter) {
  const { query, binds } = getTopUsersByUsageQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getTopUsersByUsage with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getAverageUsageDuration(filter) {
  const { query, binds } = getAverageUsageDurationQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getAverageUsageDuration with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getPlatformUsageByHour(filter) {
  const { query, binds } = getPlatformUsageByHourQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getPlatformUsageByHour with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getUserWithGroup(filter, limit, offset, isPaginated, selectedColumns = []) {
  const { query, binds } = getUserWithGroupQuery(filter, limit, offset, isPaginated, selectedColumns);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return {
      totalCount: data[0]?.totalCount || 0,
      result: data
    };
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getUserWithGroup with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getAllTypeUsersCount(entityId) {
  try {
    const entityDomain = getEntityDomains(entityId)
    let domains = await db.simpleExecute(entityDomain.query, entityDomain.binds)
    const activeUserQuery = generateActiveAndNonActiveUsersQuery(domains.map(x => x.domain))
    const entityTotalCountQuery = generateEntityTotalCount(domains.map(x => x.domain))
    const countQuery = generateUsersCountQuery();
    const entityCountQuery = generateTotalEntitiesCountandSensitieve()
    const results = await Promise.all([
      db.simpleExecute(countQuery.query, countQuery.binds),
      db.simpleExecute(entityCountQuery.query, entityCountQuery.binds),
      db.simpleExecute(entityTotalCountQuery.query, entityTotalCountQuery.binds),
      clkdb.simpleExecute(activeUserQuery.query, activeUserQuery.binds)
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getAllTypeUsersCount with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getEntityUsers(limit, offset, filter) {
  const { query, binds } = generateUserQuery(limit, offset, filter);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getEntityUsers with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getTotalUsers(filter) {
  const { query, binds } = generateUserAccessQuery(filter);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getTotalUsers with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getEntityDomainsList(entityId) {
  const { query, binds } = getEntityDomains(entityId);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getEntityDomainsList with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getActiveUserSplitCount(filter) {
  const { query, binds } = generateActiveusersQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getActiveUserSplitCount with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getConsolidatedActiveUsers(filter) {
  const { query, binds } = generateConsolidatedActiveUsers(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getConsolidatedActiveUsers with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function generateGeospatialOverview(filter) {
  try {
    const { query, binds } = generateGeospatialOverviewQuery(filter);
    const overviewList = await clkdb.simpleExecute(query, binds);
    return overviewList;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-kpi.executeQuery.service.generateGeospatialOverview with error ${err}`
    );
    throw err;
  }
}

async function generateToolsOverview(filter) {
  try {
    const { query, binds } = generateToolsOverviewQuery(filter);
    const overviewList = await clkdb.simpleExecute(query, binds);
    return overviewList;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-kpi.executeQuery.service.generateToolsOverview with error ${err}`
    );
    throw err;
  }
}


async function generateUsersList(offset, limit, entityId, external, selectedColumns, isPaginated) {
  try {
    const { query, binds } = generateUsersListQuery(offset, limit, entityId, external, selectedColumns, isPaginated);
    const overviewList = await db.simpleExecute(query, binds);
    return overviewList;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-kpi.executeQuery.service.generateUsersList with error ${err}`
    );
    throw err;
  }
}

async function getActiveUsersList(domain) {
  try {
    const { query, binds } = generateActiveUsersListBasedOnEntity(domain);
    const overviewList = await clkdb.simpleExecute(query, binds);
    return overviewList;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-kpi.executeQuery.service.generateUsersList with error ${err}`
    );
    throw err;
  }
}




module.exports = {
  getUsersData,
  getUsersByStatus,
  getTopUsersByUsage,
  getPlatformUsageByHour,
  getAverageUsageDuration,
  getUserWithGroup,
  getAllTypeUsersCount,
  getEntityUsers,
  getTotalUsers,
  getEntityDomainsList,
  getActiveUserSplitCount,
  getConsolidatedActiveUsers,
  generateGeospatialOverview,
  generateToolsOverview,
  generateUsersList,
  getActiveUsersList
};
