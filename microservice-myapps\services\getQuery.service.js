const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const uuid = require('uuid');


async function listMyAppsDataQuery(userEmail) {
    try {
        let query = `SELECT * FROM IFP_MY_APPS WHERE USER_EMAIL = '${userEmail}' AND STATUS='SAVE'`
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.listMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function addAppsToMyAppsDataQuery(nodes, userEmail) {
    try {
        let binds = {};
        let valuesClause = [];

        nodes.forEach((node, index) => {
            // Prefix to ensure unique bind variable names
            let prefix = `node${index}`;

            // Default values for optional fields
            node.topic = node.topic || '';
            node.theme = node.theme || '';
            node.subtheme = node.subtheme || '';
            node.product = node.product || '';
            node.viewName = node.viewName || '';

            // Construct bind variables for each node
            binds[`${prefix}_id`] = node.id;
            binds[`${prefix}_title`] = node.title;
            binds[`${prefix}_contentType`] = node.contentType;
            binds[`${prefix}_userEmail`] = userEmail; // Assuming userEmail is constant for all nodes
            binds[`${prefix}_status`] = 'SAVE'; // Assuming status is always 'SAVE'
            binds[`${prefix}_topic`] = node.topic;
            binds[`${prefix}_theme`] = node.theme;
            binds[`${prefix}_subtheme`] = node.subtheme;
            binds[`${prefix}_product`] = node.product;
            binds[`${prefix}_viewName`] = node.viewName;

            // Append to the values clause for the insert query
            valuesClause.push(`INTO IFP_MY_APPS (NODE_ID, TITLE, CONTENT_TYPE, USER_EMAIL, STATUS, TOPIC, THEME, SUBTHEME, PRODUCT, SOURCE_NAME) VALUES (:${prefix}_id, :${prefix}_title, :${prefix}_contentType, :${prefix}_userEmail, :${prefix}_status, :${prefix}_topic, :${prefix}_theme, :${prefix}_subtheme, :${prefix}_product, :${prefix}_viewName)`);
        });

        // Combine all parts to form the full insert query
        let query = `INSERT ALL ${valuesClause.join(' ')} SELECT 1 FROM DUAL`;

        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited addAppsToMyAppsDataQuery with error: ${err}`);
        throw err;
    }
}


async function removeAppsFromMyAppsDataQuery(nodes, userEmail) {
    try {
        let binds = {};
        let nodeIdPlaceholders = nodes.map((node, index) => {
            let placeholder = `:nodeId${index}`;
            binds[placeholder.substring(1)] = node; // Remove the ':' when setting the key
            return placeholder;
        });

        // Add the userEmail to the bind variables
        binds['userEmail'] = userEmail;

        let query = `DELETE FROM IFP_MY_APPS WHERE NODE_ID IN (${nodeIdPlaceholders.join(',')}) AND USER_EMAIL = :userEmail`;

        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited removeAppsFromMyAppsDataQuery with error: ${err}`);
        throw err;
    }
}

async function removeCompareAppsFromSourceDataQuery(nodes,userEmail) {
    try {
        let query = `DELETE FROM IFP_COMPARE_NODES WHERE ID IN (${nodes.join(',')}) AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.removeCompareAppsFromMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function removeCompareAppsFromMyAppsDataQuery(nodes,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS WHERE NODE_ID IN (${nodes.join(',')}) AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.removeAppsFromDraftMyAppsDataQuery with error ${err} `);
        throw err;
    }
}


async function draftMyAppsDataQuery(nodes, userEmail, draftId, type) {
    try {
        let binds = {};
        let valuesClause = [];
        nodes.forEach((node, index) => {
            // Prefix to ensure unique bind variable names for each node
            let prefix = `node${index}`;

            // Construct bind variables for each property
            binds[`${prefix}_id`] = node.id;
            binds[`${prefix}_title`] = node.title?node.title:'';
            binds[`${prefix}_contentType`] = node.contentType?node.contentType:'delete';
            // Assuming userEmail, draftId, and type are constant for all nodes, no prefix needed
            binds[`userEmail`] = userEmail;
            binds[`draftId`] = draftId;
            binds[`type`] = type;

            // Append to the values clause for the insert query using placeholders
            valuesClause.push(`INTO IFP_MY_APPS (NODE_ID, TITLE, CONTENT_TYPE, USER_EMAIL, STATUS, DRAFT_ID, TYPE) VALUES (:${prefix}_id, :${prefix}_title, :${prefix}_contentType, :userEmail, 'DRAFT', :draftId, :type)`);
        });

        // Combine all parts to form the full insert query
        let query = `INSERT ALL ${valuesClause.join(' ')} SELECT 1 FROM DUAL`;


        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.draftMyAppsDataQuery with error ${err} `);
        throw err;
    }
}


async function updateDraftStatusMyAppsDataQuery(userEmail) {
    try {
        let query = `UPDATE IFP_MY_APPS SET STATUS = 'SAVE' WHERE USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.updateDraftStatusMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function idListMyAppsDataQuery(userEmail) {
    try {
        let query = `SELECT NODE_ID FROM IFP_MY_APPS WHERE STATUS = 'SAVE' AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.idListMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getDraftMyAppsDataQuery(userEmail) {
    try {
        let query = `SELECT * FROM IFP_MY_APPS_DRAFT WHERE USER_EMAIL = '${userEmail}' ORDER BY INSERT_DT ASC`
        return query;
    } catch (err) {
        
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getDraftMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getDraftNodesMyAppsDataQuery(id,userEmail) {
    try {
        let query = `SELECT * FROM IFP_MY_APPS WHERE USER_EMAIL = '${userEmail}' AND STATUS = 'DRAFT' AND DRAFT_ID = '${id}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getDraftNodesMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function createDraftMyAppsDataQuery(draftId,userEmail) {
    try {
        let query = `INSERT INTO IFP_MY_APPS_DRAFT (ID,USER_EMAIL) VALUES('${draftId}','${userEmail}')`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.createDraftMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteDraftMyAppsDataQuery(draftId,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS_DRAFT WHERE ID = '${draftId}' AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteDraftMyAppsNodesDataQuery(draftId,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS WHERE DRAFT_ID = '${draftId}' AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftMyAppsNodesDataQuery with error ${err} `);
        throw err;
    }
}

async function removeAppsFromDraftMyAppsDataQuery(nodes,draftId,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS WHERE NODE_ID IN (${nodes.join(',')}) AND DRAFT_ID = '${draftId}' AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.removeAppsFromDraftMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteDraftMasterMyAppsDataQuery(draftId,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS_DRAFT WHERE ID = '${draftId}' AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftMasterMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteDraftNodesMyAppsDataQuery(draftId,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS WHERE DRAFT_ID = '${draftId}' AND USER_EMAIL = '${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftNodesMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function dragMyAppsDataQuery(userEmail) {
    try {
        let query = `SELECT * FROM (SELECT IFP_MY_APPS.NODE_ID,\
            IFP_MY_APPS.CONTENT_TYPE,\
            IFP_MY_APPS.STATUS,\
            IFP_MY_APPS.USER_EMAIL,\
            IFP_MY_APPS.INSERT_DT,\
            IFP_MY_APPS.TITLE,\
            A.TITLE     AS MOD_TITLE,\
            A.DOMAIN,\
            A.CLASSIFICATION,\
            CASE A.MOD_FLAG WHEN 1 THEN 1 ELSE 0 END AS MOD_FLAG,\
            A.SORT_ORDER,\
            IFP_MY_APPS.TOPIC,\
            IFP_MY_APPS.THEME,\
            IFP_MY_APPS.SUBTHEME,\
            IFP_MY_APPS.PRODUCT,\
            IFP_MY_APPS.SOURCE_NAME
        FROM IFP_MY_APPS\
            LEFT OUTER JOIN IFP_MY_APPS_ORDER A\
                ON     A.NODE_ID = IFP_MY_APPS.NODE_ID\
                    AND IFP_MY_APPS.USER_EMAIL = A.USER_EMAIL) WHERE USER_EMAIL='${userEmail}' AND STATUS='SAVE' ORDER BY SORT_ORDER ASC, INSERT_DT DESC
        `
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftNodesMyAppsDataQuery with error ${err} `);
        throw err;
    }

}


async function deleteDragMyAppsDataQuery(userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS_ORDER WHERE USER_EMAIL='${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteDragByNodesMyAppsDataQuery(nodes,userEmail) {
    try {
        let query = `DELETE FROM IFP_MY_APPS_ORDER WHERE NODE_ID in (${nodes.join(',')}) AND USER_EMAIL='${userEmail}'`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function storeDragMyAppsDataQuery(stateData) {
    try {
        let insertQuery = ''
        stateData.forEach(app => {
            insertQuery = `${insertQuery}INTO IFP_MY_APPS_ORDER ( NODE_ID, TITLE, DOMAIN, CLASSIFICATION, USER_EMAIL, SORT_ORDER ) VALUES ( '${app.NODE_ID}','${app.TITLE}','${app.DOMAIN}','${app.CLASSIFICATION}', '${app.USER_EMAIL}', ${app.SORT_ORDER})\ `
        })
        let query = `INSERT ALL ${insertQuery} SELECT 1 FROM DUAL`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getHierarchyDataQuery(nodes,language) {
    try {
        let hLang = language == 'EN'?'ENGLISH':'ARABIC'
        let query = `
        SELECT
            DATA_SOURCE,
            INDICATOR_ID,
            INDICATOR_NAME_${language} AS INDICATOR_NAME,
            INDICATOR_TYPE,
            PRODUCT_NAME_${hLang} AS PRODUCT,
            SOURCE_TABLE,
            SUB_THEME_NAME_${hLang} AS SUB_THEME,
            THEME_NAME_${hLang} AS THEME,
            TOPIC_NAME_${hLang} AS TOPIC,
            UNIT
        FROM
        VW_INDICATOR_MAP WHERE INDICATOR_ID IN (${nodes.join(',')})`

        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}
async function createDashboardDataQuery(dashboardData) {
    try {
        let insertQuery = ''
        dashboardData.forEach(app => {
            insertQuery = `${insertQuery}INTO IFP_MY_APPS_DASHBOARD ( ID, TITLE, NODE_ID, NODE_TITLE, DOMAIN, CONTENT_TYPE, CLASSIFICATION, USER_EMAIL, SORT_ORDER ) VALUES ('${app.ID}', '${app.TITLE}', '${app.NODE_ID}','${app.NODE_TITLE}','${app.DOMAIN}','${app.CONTENT_TYPE}','${app.CLASSIFICATION}', '${app.USER_EMAIL}', ${app.SORT_ORDER})\ `
        })
        let query = `INSERT ALL ${insertQuery} SELECT 1 FROM DUAL`
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.createDashboardDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteDashboardDataQuery(dashboardId) {
    try {
        let query = `DELETE FROM IFP_MY_APPS_DASHBOARD WHERE ID='${dashboardId}'`
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDashboardDataQuery with error ${err} `);
        throw err;
    }
}

async function detailDashboardDataQuery(dashboardId,userEmail) {
    try {
        let query = `SELECT T.ID,
                        T.TITLE,
                        T.NODE_ID,
                        T.NODE_TITLE,
                        T.DOMAIN,
                        T.CLASSIFICATION,
                        T.USER_EMAIL,
                        T.SORT_ORDER,
                        T.CREATED_DATE,
                        T.CONTENT_TYPE,
                        S.TOPIC
                    FROM IFP_MY_APPS_DASHBOARD  T
                        LEFT JOIN IFP_MY_APPS S ON T.NODE_ID = S.NODE_ID
                    WHERE     T.USER_EMAIL = '${userEmail}'
                        AND S.USER_EMAIL = '${userEmail}' AND T.ID='${dashboardId}' ORDER BY T.SORT_ORDER`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.detailDashboardDataQuery with error ${err} `);
        throw err;
    }
}

async function myAppsFilterDataQuery(dashboardId,userEmail) {
    try {
        let query = `SELECT * FROM IFP_MY_APPS WHERE USER_EMAIL='${userEmail}' AND NODE_ID NOT IN (SELECT NODE_ID FROM IFP_MY_APPS_DASHBOARD WHERE ID='${dashboardId}')`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.myAppsFilterDataQuery with error ${err} `);
        throw err;
    }
}

async function getDashboardsDataQuery(userEmail) {
    try {
        let query = `SELECT DISTINCT ID,TITLE,CREATED_DATE FROM IFP_MY_APPS_DASHBOARD WHERE USER_EMAIL='${userEmail}'`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.myAppsFilterDataQuery with error ${err} `);
        throw err;
    }
}

async function removeDashboardNodesDataQuery(nodes,dashboardId,userEmail) {
    try {
        nodes = nodes.map(node => `'${node}'`);
        let query = `DELETE FROM IFP_MY_APPS_DASHBOARD WHERE ID='${dashboardId}' AND NODE_ID IN (${nodes.join(',')}) AND USER_EMAIL = '${userEmail}'`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.removeDashboardNodesDataQuery with error ${err} `);
        throw err;
    }
}

async function createShareAppQuery(shareApp) {
    try {
        let query = `INSERT INTO IFP_SHARE_MY_APPS (ID,NAME,SHARE_EMAIL,SHARE_NAME,RECEPIENT_EMAIL,TOKEN) VALUES(:id,:name,:shareEmail,:shareName,:recepientEmail,:token)`;
        return {query:query,binds:shareApp};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.createShareApp with error ${err} `);
        throw err;
    }
}

async function getShareAppQuery(shareId,userEmail,requestor='recepient') {
    try {
        let binds = {
            shareId: shareId
        };
        let query;
        if (requestor == 'recepient'){
            binds.recepientEmail=userEmail
            query = `SELECT * FROM IFP_SHARE_MY_APPS WHERE ID=:shareId AND RECEPIENT_EMAIL=:recepientEmail`;
        }
        else if (requestor == 'sender'){
            binds.senderEmail=userEmail
            query = `SELECT * FROM IFP_SHARE_MY_APPS WHERE ID=:shareId AND SHARE_EMAIL=:senderEmail`;
        }
        else
            throw new Error('Invalid requestor type')
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getShareAppQuery with error ${err} `);
        throw err;
    }
}

async function getShareAppsListQuery(type,userEmail,page,limit){
    try {
        const offset = (page - 1) * limit;

        let binds = {
            userEmail: userEmail,
            offset: offset,
            limit: limit
        };
        let query;

        if (type == 'received')
            // query = `SELECT ID,NAME,SHARE_EMAIL,SHARE_NAME,INSERT_DT AS SHARED_DT,TOKEN,READ FROM IFP_SHARE_MY_APPS WHERE RECEPIENT_EMAIL=:userEmail ORDER BY INSERT_DT DESC`;
            query = `            
                WITH NodeCounts AS (
                    SELECT 
                        SHARE_ID, 
                        COUNT(*) AS NUMBER_OF_NODES
                    FROM 
                        IFP_SHARE_MY_APPS_NODES
                    GROUP BY 
                        SHARE_ID
                )
                SELECT 
                    A.ID,
                    A.NAME,
                    A.SHARE_EMAIL,
                    A.SHARE_NAME,
                    A.INSERT_DT AS SHARED_DT,
                    A.TOKEN,
                    A.READ,
                    COALESCE(NC.NUMBER_OF_NODES, 0) AS NUMBER_OF_NODES
                FROM 
                    IFP_SHARE_MY_APPS A
                LEFT JOIN NodeCounts NC ON A.ID = NC.SHARE_ID
                WHERE 
                    A.RECEPIENT_EMAIL = :userEmail
                ORDER BY 
                    A.INSERT_DT DESC
                    OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY`
        else if(type == 'sent'){
            // query = `SELECT ID,NAME,RECEPIENT_EMAIL, INSERT_DT AS SHARED_DT,TOKEN,READ FROM IFP_SHARE_MY_APPS WHERE SHARE_EMAIL=:userEmail ORDER BY INSERT_DT DESC`;
            query = `
            WITH RecipientCounts AS (
                SELECT 
                    ID, 
                    COUNT( DISTINCT RECEPIENT_EMAIL) AS NUMBER_OF_RECEPIENTS,
                    RTRIM(DBMS_LOB.SUBSTR(XMLAGG(XMLELEMENT(e, RECEPIENT_EMAIL || ';').EXTRACT('//text()') ORDER BY RECEPIENT_EMAIL).GetClobVal(), 4000, 1), ';') AS RECEPIENT_EMAILS
                FROM 
                    IFP_SHARE_MY_APPS
                WHERE SHARE_EMAIL = :userEmail
                GROUP BY 
                    ID
            ),
            NodeCounts AS (
                SELECT 
                    SHARE_ID, 
                    COUNT(*) AS NUMBER_OF_NODES
                FROM 
                    IFP_SHARE_MY_APPS_NODES
                GROUP BY 
                    SHARE_ID
            ),
            Pagination AS (
                SELECT 
                    A.ID,
                    MAX(A.NAME) AS NAME,
                    MAX(A.INSERT_DT) AS SHARED_DT,
                    MAX(A.TOKEN) AS TOKEN,
                    RC.NUMBER_OF_RECEPIENTS,
                    RC.RECEPIENT_EMAILS,
                    NVL(NC.NUMBER_OF_NODES, 0) AS NUMBER_OF_NODES,
                    COUNT(*) OVER() AS TOTAL_COUNT
                FROM 
                    IFP_SHARE_MY_APPS A
                LEFT JOIN RecipientCounts RC ON A.ID = RC.ID
                LEFT JOIN NodeCounts NC ON A.ID = NC.SHARE_ID
                WHERE 
                    A.SHARE_EMAIL = :userEmail
                GROUP BY 
                    A.ID, RC.NUMBER_OF_RECEPIENTS, RC.RECEPIENT_EMAILS, NC.NUMBER_OF_NODES
            )
            SELECT 
                ID,
                NAME,
                SHARED_DT,
                TOKEN,
                NUMBER_OF_RECEPIENTS,
                RECEPIENT_EMAILS,
                NUMBER_OF_NODES,
                TOTAL_COUNT
            FROM 
                Pagination
            ORDER BY 
                SHARED_DT DESC
            OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY`
        }
        else
            throw new Error(`Invalid type ${type}`);
        return {query:query,binds:binds};
    }
    catch(err){
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getShareAppsListQuery with error ${err} `);
        throw err;
    }
}

async function readShareAppsQuery(id,userEmail){
    try {
        let binds = {
            userEmail: userEmail,
            id: id
        };
        let query = `UPDATE IFP_SHARE_MY_APPS SET READ=1 WHERE RECEPIENT_EMAIL=:userEmail AND ID=:id`;
        return {query:query,binds:binds};
    }
    catch(err){
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.readShareAppsQuery with error ${err} `);
        throw err;
    }
}

async function createShareNodesQuery(shareNodes) {
    try {
        let insertQuery = '';
        shareNodes.forEach(node => {               
            insertQuery = `${insertQuery}INTO IFP_SHARE_MY_APPS_NODES ( NODE_ID, CONTENT_TYPE, SHARE_ID ) VALUES ( '${node.nodeId}','${node.contentType}','${node.shareId}' )\ `;
        });
        let query = `INSERT ALL ${insertQuery} SELECT 1 FROM DUAL`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.addSingleAppToMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function dragShareDataQuery(userEmail) {
    try {
        let query = `SELECT * FROM IFP_SHARE_MY_APPS_ORDER WHERE USER_EMAIL='${userEmail}' ORDER BY SORT_ORDER ASC`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftNodesMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function storeDragShareMyAppsDataQuery(shareDragData) {
    try {
        let insertQuery = '';
        shareDragData.forEach(app => {
            insertQuery = `${insertQuery}INTO IFP_SHARE_MY_APPS_ORDER ( NODE_ID, TITLE, DOMAIN, CLASSIFICATION, SHARE_ID, SORT_ORDER, USER_EMAIL ) VALUES ( '${app.nodeId}','${app.title}','${app.domain}','${app.classification}', '${app.shareId}', ${app.sortOrder}, '${app.recepient}')\ `;
        });
        let query = `INSERT ALL ${insertQuery} SELECT 1 FROM DUAL`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.storeDragShareMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function dragShareMyAppsDataQuery(shareId) {
    try {
        let query = `
        SELECT 
            DISTINCT MAIN.NODE_ID,
            MAIN.CONTENT_TYPE,
            MAIN.SHARE_ID,
            MAIN.INSERT_DT,
            COALESCE(CAST(CN.TITLE AS VARCHAR2(1000)), MAIN.TITLE) AS TITLE,
            MAIN.MOD_TITLE,
            MAIN.DOMAIN,
            MAIN.CLASSIFICATION,
            MAIN.MOD_FLAG,
            MAIN.SORT_ORDER
        FROM (
            SELECT 
                IFP_SHARE_MY_APPS_NODES.NODE_ID,
                IFP_SHARE_MY_APPS_NODES.CONTENT_TYPE,
                IFP_SHARE_MY_APPS_NODES.SHARE_ID,
                IFP_SHARE_MY_APPS_NODES.INSERT_DT,
                IFP_SHARE_MY_APPS_NODES.TITLE,
                A.TITLE AS MOD_TITLE,
                A.DOMAIN,
                A.CLASSIFICATION,
                CASE A.MOD_FLAG WHEN 1 THEN 1 ELSE 0 END AS MOD_FLAG,
                A.SORT_ORDER
            FROM (
                SELECT * FROM IFP_SHARE_MY_APPS_NODES
                WHERE SHARE_ID = '${shareId}'
            ) IFP_SHARE_MY_APPS_NODES
            LEFT OUTER JOIN IFP_SHARE_MY_APPS_ORDER A ON A.NODE_ID = IFP_SHARE_MY_APPS_NODES.NODE_ID
            AND IFP_SHARE_MY_APPS_NODES.SHARE_ID = A.SHARE_ID
        ) MAIN
        LEFT JOIN (
            SELECT ID, MAX(TITLE) AS TITLE
            FROM IFP_COMPARE_NODES
            GROUP BY ID
        ) CN ON MAIN.NODE_ID = CN.ID
        ORDER BY MAIN.SORT_ORDER ASC, MAIN.INSERT_DT DESC`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDraftNodesMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteShareQuery(shareId,requestor,userEmail) {
    try {
        if (!userEmail)
            throw new Error('Please provide a valid email')
        let binds = {
            shareId: shareId,
            userEmail: userEmail
        }
        let query;
        switch(requestor) {
            case 'sender':
                query = `DELETE FROM IFP_SHARE_MY_APPS WHERE ID=:shareId AND SHARE_EMAIL=:userEmail`;
                break;
            case 'recepient':
                query = `DELETE FROM IFP_SHARE_MY_APPS WHERE ID=:shareId AND RECEPIENT_EMAIL=:userEmail`;
                break;
            default:
                throw new Error(`Invalid requestor type: ${requestor}`)   
          }
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteShareDragQuery(shareId,requestor,userEmail=null) {
    try {
        let binds = {
            shareId:shareId
        }
        let query;
        switch(requestor) {
            case 'sender':
                query = `DELETE FROM IFP_SHARE_MY_APPS_ORDER WHERE SHARE_ID=:shareId`;
                break;
            case 'recepient':
                if (!userEmail)
                    throw new Error('Please provide a valid email for requestor type: recepient')
                binds.userEmail = userEmail
                query = `DELETE FROM IFP_SHARE_MY_APPS_ORDER WHERE SHARE_ID=:shareId AND USER_EMAIL=:userEmail`;
                break;
            default:
                throw new Error(`Invalid requestor type: ${requestor}`)   
          }
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function deleteShareNodesQuery(shareId) {
    try {
        let binds = {
            shareId: shareId
        }
        let query = `DELETE FROM IFP_SHARE_MY_APPS_NODES WHERE SHARE_ID=:shareId`;
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function createShareNotificationsQuery(notificationData) {
    try {
        let binds = {
            nodeId: notificationData.nodeId,
            name: notificationData.name
        }
        let insertQuery = '';
        notificationData.users.forEach((user, index) => {
            binds[`user${index}`] = user
            binds[`id${index}`] = String(uuid.v4())
            insertQuery = `${insertQuery}INTO IFP_SHARE_APPS_NOTIFICATIONS (
                USER_EMAIL,NOTIFICATION_ID,NODE_ID,CONTENT_NAME,CONTENT_DESCRIPTION,CONTENT_NAME_AR,CONTENT_DESCRIPTION_AR) VALUES (
                    :user${index},
                    :id${index},
                    :nodeId,
                    :name,
                    :name,
                    :name,
                    :name
                )\ `;
        });
        let query = `INSERT ALL ${insertQuery} SELECT 1 FROM DUAL`;
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function createShareMyAppsRequestQuery(requestData) {
    try {
        let binds = {
            id: requestData.nodeId,
            userEmail: requestData.requestorEmail
        }
        let query = `INSERT INTO IFP_APPS_REQUEST (NODE,USER_EMAIL) VALUES (:id,:userEmail)`;
        
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getShareMyAppsRequestQuery(requestorEmail) {
    try {
        let binds = {
            userEmail: requestorEmail
        }
        let query = `SELECT NODE FROM IFP_APPS_REQUEST WHERE USER_EMAIL=:userEmail`;
        
        return {query:query,binds:binds};
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
        throw err;
    }
}



module.exports = { 
    listMyAppsDataQuery,
    addAppsToMyAppsDataQuery,
    removeAppsFromMyAppsDataQuery,
    removeCompareAppsFromMyAppsDataQuery,
    removeCompareAppsFromSourceDataQuery,
    draftMyAppsDataQuery,
    updateDraftStatusMyAppsDataQuery,
    idListMyAppsDataQuery,
    getDraftMyAppsDataQuery,
    createDraftMyAppsDataQuery,
    getDraftNodesMyAppsDataQuery,
    deleteDraftMyAppsDataQuery,
    deleteDraftMyAppsNodesDataQuery,
    removeAppsFromDraftMyAppsDataQuery,
    deleteDraftMasterMyAppsDataQuery,
    deleteDraftNodesMyAppsDataQuery,
    deleteDragMyAppsDataQuery,
    dragMyAppsDataQuery,
    storeDragMyAppsDataQuery,
    deleteDragByNodesMyAppsDataQuery,
    getHierarchyDataQuery,
    createDashboardDataQuery,
    deleteDashboardDataQuery,
    detailDashboardDataQuery,
    myAppsFilterDataQuery,
    getDashboardsDataQuery,
    removeDashboardNodesDataQuery,
    createShareAppQuery,
    getShareAppsListQuery,
    readShareAppsQuery,
    createShareNodesQuery,
    getShareAppQuery,
    storeDragShareMyAppsDataQuery,
    dragShareDataQuery,
    dragShareMyAppsDataQuery,
    createShareNotificationsQuery,
    createShareMyAppsRequestQuery,
    getShareMyAppsRequestQuery,
    deleteShareQuery,
    deleteShareDragQuery,
    deleteShareNodesQuery
 };
