require('dotenv').config();

module.exports = {
  "local": {
    "username": "postgres",
    "password": "mysecretpassword",
    "database": "postgres",
    "host": "127.0.0.1",
    "dialect": "postgres"
  },
  "dev": {
    "username": "data_ingestion_user",
    "password": "NEKh7uGhk35Za4hUHKPq7K",
    "database": "bayaan_genai_db",
    "host": "***********",
    "dialect": "postgres"
  },
  "test": {
    "username": "genai",
    "password": "n7Kaj2WKiV0XVqlyaTp5ED",
    "database": "bayaan_genai_db",
    "host": "***********",
    "dialect": "postgres"
  },
  "prod": {
    "username": "gena<PERSON><PERSON>",
    "password": "n7Kaj2WKiV0XVqlyaTp5ED",
    "database": "bayaan_genai_prod",
    "host": "***********",
    "dialect": "postgres"
  }
}
