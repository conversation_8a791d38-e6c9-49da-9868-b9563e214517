const lineChart = require("../../../services/chart-services/line-chart");

describe('Service', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    test('should get json response from processLineChartData - success', async () => {
        const mockData = [
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 84.68047105714966,
                "VALUE_LL": 81.81949518725062,
                "VALUE_UL": 91.52070701506828,
                "OBS_DT": "2012-01-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Oil"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 84.88203883695701,
                "VALUE_LL": 82.01425288975234,
                "VALUE_UL": 91.73855683910833,
                "OBS_DT": "2012-02-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Oil"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 85.08360661676438,
                "VALUE_LL": 82.20901059225405,
                "VALUE_UL": 91.95640666314836,
                "OBS_DT": "2012-03-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Oil"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 86.53306162438773,
                "VALUE_LL": 82.40219819464674,
                "VALUE_UL": 92.07018342635438,
                "OBS_DT": "2012-01-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Total Non -Oil GDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 86.57233274499981,
                "VALUE_LL": 82.43959461404131,
                "VALUE_UL": 92.11196744751621,
                "OBS_DT": "2012-02-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Total Non -Oil GDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 86.73866761949292,
                "VALUE_LL": 82.59798909399346,
                "VALUE_UL": 92.28894584302537,
                "OBS_DT": "2012-03-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Total Non -Oil GDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 98.51670980215367,
                "VALUE_LL": 93.81377815844736,
                "VALUE_UL": 104.82064741239799,
                "OBS_DT": "2020-12-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Total Non -Oil GDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 101.19808964831701,
                "VALUE_LL": 94.11422337293489,
                "VALUE_UL": 113.341860406115,
                "OBS_DT": "2021-01-01",
                "TYPE": "FORECAST",
                "SECTOR": "Total Non -Oil GDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 101.373767228229,
                "VALUE_LL": 94.27760352225256,
                "VALUE_UL": 113.538619295616,
                "OBS_DT": "2021-02-01",
                "TYPE": "FORECAST",
                "SECTOR": "Total Non -Oil GDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 100.90669506182802,
                "VALUE_LL": 97.49750736980059,
                "VALUE_UL": 109.057637012669,
                "OBS_DT": "2020-12-01",
                "TYPE": "NOWCAST",
                "SECTOR": "Oil"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 99.98819521931993,
                "VALUE_LL": 92.98902155396755,
                "VALUE_UL": 118.985952310991,
                "OBS_DT": "2021-01-01",
                "TYPE": "FORECAST",
                "SECTOR": "Oil"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210201",
                "INSERT_DT": "20210201",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210201",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 99.94415616290067,
                "VALUE_LL": 92.94806523149762,
                "VALUE_UL": 118.93354583385201,
                "OBS_DT": "2021-02-01",
                "TYPE": "FORECAST",
                "SECTOR": "Oil"
            }
        ];

        const mockVisualization = {
            "id": "line-chart-economy-sector-indicator",
            "type": "line-chart",
            "sortOrder": 2,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "oil",
                    "label": "Oil Production",
                    "color": "#26A6BF",
                    "type": "solid",
                    "dimension": {
                        "SECTOR": "OIL",
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "non-oil",
                    "label": "Non Oil Production",
                    "color": "#983EFF",
                    "type": "solid",
                    "dimension": {
                        "SECTOR": "TOTAL NON -OIL GDP",
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "oil-forecast",
                    "label": "Oil Production",
                    "color": "#26A6BF",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "SECTOR": "OIL",
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "non-oil-forecast",
                    "label": "Non Oil Production",
                    "color": "#983EFF",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "SECTOR": "TOTAL NON -OIL GDP",
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                }
            ],
            "markersMeta": [
                {
                    "id": "economic-sector-index_real-vs-forecast",
                    "color": "#ffffff",
                    "type": "line-with-label",
                    "labelText": "Forecast",
                    "axis": "x",
                    "accessor": {
                        "type": "date",
                        "path": "DATE",
                        "specifier": "%Y-%m-%d"
                    }
                }
            ],
            "showLegend": true,
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.05,
            "yAxisExtraStepMax": 0.05,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "number_1.2-2"
        };

        const expectedResponse = {
            "id":"line-chart-economy-sector-indicator",
            "type":"line-chart",
            "sortOrder":2,
            "viewName":"VW_RI_FORECAST_CONSTANT",
            "comboIdTable":"DIM_RI_PARAM_COMBO",
            "seriesMeta":[
               {
                  "id":"oil",
                  "label":"Oil Production",
                  "color":"#26A6BF",
                  "type":"solid",
                  "dimension":{
                     "SECTOR":"OIL",
                     "TYPE":"NOWCAST"
                  },
                  "xAccessor":{
                     "type":"date",
                     "path":"OBS_DT",
                     "specifier":"%Y-%m-%d"
                  },
                  "yAccessor":{
                     "type":"value",
                     "path":"VALUE"
                  },
                  "data":[
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":84.68047105714966,
                        "VALUE_LL":81.81949518725062,
                        "VALUE_UL":91.52070701506828,
                        "OBS_DT":"2012-01-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Oil"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":84.88203883695701,
                        "VALUE_LL":82.01425288975234,
                        "VALUE_UL":91.73855683910833,
                        "OBS_DT":"2012-02-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Oil"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":85.08360661676438,
                        "VALUE_LL":82.20901059225405,
                        "VALUE_UL":91.95640666314836,
                        "OBS_DT":"2012-03-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Oil"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":100.90669506182802,
                        "VALUE_LL":97.49750736980059,
                        "VALUE_UL":109.057637012669,
                        "OBS_DT":"2020-12-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Oil"
                     }
                  ],
                  "yMax":100.90669506182802,
                  "yMin":84.68047105714966,
                  "xMin":"2012-01-01",
                  "xMax":"2020-12-01"
               },
               {
                  "id":"non-oil",
                  "label":"Non Oil Production",
                  "color":"#983EFF",
                  "type":"solid",
                  "dimension":{
                     "SECTOR":"TOTAL NON -OIL GDP",
                     "TYPE":"NOWCAST"
                  },
                  "xAccessor":{
                     "type":"date",
                     "path":"OBS_DT",
                     "specifier":"%Y-%m-%d"
                  },
                  "yAccessor":{
                     "type":"value",
                     "path":"VALUE"
                  },
                  "data":[
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":86.53306162438773,
                        "VALUE_LL":82.40219819464674,
                        "VALUE_UL":92.07018342635438,
                        "OBS_DT":"2012-01-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Total Non -Oil GDP"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":86.57233274499981,
                        "VALUE_LL":82.43959461404131,
                        "VALUE_UL":92.11196744751621,
                        "OBS_DT":"2012-02-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Total Non -Oil GDP"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":86.73866761949292,
                        "VALUE_LL":82.59798909399346,
                        "VALUE_UL":92.28894584302537,
                        "OBS_DT":"2012-03-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Total Non -Oil GDP"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":98.51670980215367,
                        "VALUE_LL":93.81377815844736,
                        "VALUE_UL":104.82064741239799,
                        "OBS_DT":"2020-12-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Total Non -Oil GDP"
                     }
                  ],
                  "yMax":98.51670980215367,
                  "yMin":86.53306162438773,
                  "xMin":"2012-01-01",
                  "xMax":"2020-12-01"
               },
               {
                  "id":"oil-forecast",
                  "label":"Oil Production",
                  "color":"#26A6BF",
                  "type":"forecast-with-arrow",
                  "dimension":{
                     "SECTOR":"OIL",
                     "TYPE":"FORECAST"
                  },
                  "xAccessor":{
                     "type":"date",
                     "path":"OBS_DT",
                     "specifier":"%Y-%m-%d"
                  },
                  "yAccessor":{
                     "type":"value",
                     "path":"VALUE"
                  },
                  "data":[
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":100.90669506182802,
                        "VALUE_LL":97.49750736980059,
                        "VALUE_UL":109.057637012669,
                        "OBS_DT":"2020-12-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Oil"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":"E0000",
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":99.98819521931993,
                        "VALUE_LL":92.98902155396755,
                        "VALUE_UL":118.985952310991,
                        "OBS_DT":"2021-01-01",
                        "TYPE":"FORECAST",
                        "SECTOR":"Oil"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":"E0000",
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":99.94415616290067,
                        "VALUE_LL":92.94806523149762,
                        "VALUE_UL":118.93354583385201,
                        "OBS_DT":"2021-02-01",
                        "TYPE":"FORECAST",
                        "SECTOR":"Oil"
                     }
                  ],
                  "yMax":99.98819521931993,
                  "yMin":99.94415616290067,
                  "xMin":"2021-01-01",
                  "xMax":"2021-02-01"
               },
               {
                  "id":"non-oil-forecast",
                  "label":"Non Oil Production",
                  "color":"#983EFF",
                  "type":"forecast-with-arrow",
                  "dimension":{
                     "SECTOR":"TOTAL NON -OIL GDP",
                     "TYPE":"FORECAST"
                  },
                  "xAccessor":{
                     "type":"date",
                     "path":"OBS_DT",
                     "specifier":"%Y-%m-%d"
                  },
                  "yAccessor":{
                     "type":"value",
                     "path":"VALUE"
                  },
                  "data":[
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":null,
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":98.51670980215367,
                        "VALUE_LL":93.81377815844736,
                        "VALUE_UL":104.82064741239799,
                        "OBS_DT":"2020-12-01",
                        "TYPE":"NOWCAST",
                        "SECTOR":"Total Non -Oil GDP"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":"E0000",
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":101.19808964831701,
                        "VALUE_LL":94.11422337293489,
                        "VALUE_UL":113.341860406115,
                        "OBS_DT":"2021-01-01",
                        "TYPE":"FORECAST",
                        "SECTOR":"Total Non -Oil GDP"
                     },
                     {
                        "INDICATOR_ID":"RAPID",
                        "PARAMETER_COMBO_ID":"E0000",
                        "RUN_SEQ_ID":13,
                        "RUN_DT":"20210201",
                        "INSERT_DT":"20210201",
                        "INSERT_USER_ID":"ETL_USER",
                        "LAST_UPD_DT":"20210201",
                        "LAST_UPD_USER_ID":"ETL_USER",
                        "LANGUAGE_CD":"EN",
                        "VALUE":101.373767228229,
                        "VALUE_LL":94.27760352225256,
                        "VALUE_UL":113.538619295616,
                        "OBS_DT":"2021-02-01",
                        "TYPE":"FORECAST",
                        "SECTOR":"Total Non -Oil GDP"
                     }
                  ],
                  "yMax":101.373767228229,
                  "yMin":101.19808964831701,
                  "xMin":"2021-01-01",
                  "xMax":"2021-02-01"
               }
            ],
            "markersMeta":[
               {
                  "id":"economic-sector-index_real-vs-forecast",
                  "color":"#ffffff",
                  "type":"line-with-label",
                  "labelText":"Forecast",
                  "axis":"x",
                  "accessor":{
                     "type":"date",
                     "path":"DATE",
                     "specifier":"%Y-%m-%d"
                  },
                  "data":{
                     "DATE":"2020-12-01"
                  }
               }
            ],
            "showLegend":true,
            "showInterval":false,
            "xAxisLabel":null,
            "yAxisLabel":null,
            "yAxisExtraStepMin":0.05,
            "yAxisExtraStepMax":0.05,
            "xAxisFormat":"date_y",
            "yAxisFormat":"d3-number",
            "tooltipTitleFormat":"date_MMM y",
            "tooltipValueFormat":"number_1.2-2"
         }
        jest.spyOn(lineChart,"processLineChartData");
        const result = await lineChart.processLineChartData(mockData, mockVisualization);
        //expect(result).toEqual(expectedResponse);
    });
    test('should get json response from processLineChartData - failure', async () => {
        const mockData = [{
            "INDICATOR_ID": "RAPID",
            "PARAMETER_COMBO_ID": null,
            "RUN_SEQ_ID": 13,
            "RUN_DT": "20210201",
            "INSERT_DT": "20210201",
            "INSERT_USER_ID": "ETL_USER",
            "LAST_UPD_DT": "20210201",
            "LAST_UPD_USER_ID": "ETL_USER",
            "LANGUAGE_CD": "EN",
            "VALUE": 84.68047105714966,
            "VALUE_LL": 81.81949518725062,
            "VALUE_UL": 91.52070701506828,
            "OBS_DT": "2012-01-01",
            "TYPE": null,
            "SECTOR": null
        },null];

        const mockVisualization = {
            "id": "line-chart-economy-sector-indicator",
            "type": "line-chart",
            "sortOrder": 2,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "oil",
                    "label": "Oil Production",
                    "color": "#26A6BF",
                    "type": "solid",
                    "dimension": {
                        "SECTOR": "OIL",
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "non-oil",
                    "label": "Non Oil Production",
                    "color": "#983EFF",
                    "type": "solid",
                    "dimension": {
                        "SECTOR": "TOTAL NON -OIL GDP",
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "oil-forecast",
                    "label": "Oil Production",
                    "color": "#26A6BF",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "SECTOR": "OIL",
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "non-oil-forecast",
                    "label": "Non Oil Production",
                    "color": "#983EFF",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "SECTOR": "TOTAL NON -OIL GDP",
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                }
            ],
            "markersMeta": [],
            "showLegend": true,
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.05,
            "yAxisExtraStepMax": 0.05,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "number_1.2-2"
        };
        try{
        jest.spyOn(lineChart,"processLineChartData");
        await lineChart.processLineChartData(mockData, mockVisualization);
        
        }catch(err){
            expect(err).toEqual(404);
        }
    });
    test('should get json response from processLineChartData - failure', async () => {
        const mockData = [];

        const mockVisualization = {
            "id": "line-chart-economy-sector-indicator",
            "type": "line-chart",
            "sortOrder": 2,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "seriesMeta": [
                {
                    "id": "oil",
                    "label": "Oil Production",
                    "color": "#26A6BF",
                    "type": "solid",
                    "dimension": {
                        "SECTOR": "OIL",
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "non-oil",
                    "label": "Non Oil Production",
                    "color": "#983EFF",
                    "type": "solid",
                    "dimension": {
                        "SECTOR": "TOTAL NON -OIL GDP",
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "oil-forecast",
                    "label": "Oil Production",
                    "color": "#26A6BF",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "SECTOR": "OIL",
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "non-oil-forecast",
                    "label": "Non Oil Production",
                    "color": "#983EFF",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "SECTOR": "TOTAL NON -OIL GDP",
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                }
            ],
            "markersMeta": [
                {
                    "id": "economic-sector-index_real-vs-forecast",
                    "color": "#ffffff",
                    "type": "line-with-label",
                    "labelText": "Forecast",
                    "axis": "x",
                    "accessor": {
                        "type": "date",
                        "path": "DATE",
                        "specifier": "%Y-%m-%d"
                    }
                }
            ],
            "showLegend": true,
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.05,
            "yAxisExtraStepMax": 0.05,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "number_1.2-2"
        };
        const mockError  = new TypeError(`Cannot read property 'forEach' of undefined`)
        try{
        jest.spyOn(lineChart,"processLineChartData");
        await lineChart.processLineChartData(mockData, mockVisualization);
        
        }catch(err){
            expect(err).toEqual([404, "Data not available in DB"]);
        }
    });
});



