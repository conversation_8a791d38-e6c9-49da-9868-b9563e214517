const Logger = require('scad-library').logger;
const {filterQuery} = require('../services/helper')

const log = new Logger().getInstance();

async function getComboIdQuery(indicatorDrivers, comboIdTable) {
    try {
        let query = `SELECT PARAMETER_COMBO_ID FROM ${comboIdTable} WHERE `;
        if(indicatorDrivers){
            const indicatorCount = Object.keys(indicatorDrivers).length;
            let counter = [];
            Object.keys(indicatorDrivers).forEach((key) => {
                query = `${query} ${counter.length === 0 ? '':'AND '}LOWER(${key.toUpperCase()})='${indicatorDrivers[key]}'` 
                counter.push('1');
            });
        }
        return query;
    } catch (err) {
        
        log.error(`Error building getComboIdQuery: ERROR ${err}`)
        return ([424, err]);
    }
}

async function getTreeMapQuery(visualization) {
    try {
        let query = `SELECT VALUE AS SECTOR_SIZE, SECTOR, OBS_DT FROM ${visualization.sectorSizeViewName} WHERE INDICATOR_ID = '${visualization.sectorSizeIndicatorId}' AND OBS_DT = (SELECT MAX(OBS_DT) FROM ${visualization.sectorSizeViewName} WHERE INDICATOR_ID = '${visualization.sectorSizeIndicatorId}')`;
        return query;
    } catch (err) {
        
        log.error(`Error building getTreeMapQuery: ERROR ${err}`)
        return ([424, err]);
    }

}
async function getQuery(visualization, comboId) {
    try {
        let query = `SELECT * FROM ${visualization.viewName} WHERE (UPPER(PARAMETER_COMBO_ID) = '${comboId}'  OR PARAMETER_COMBO_ID IS NULL) `
        if (visualization.filterBy)
            query = filterQuery(visualization.filterBy, query)
        if (visualization.isScadProjection) {
            query = `${query} OR UPPER(PARAMETER_COMBO_ID) = '${visualization.scadComboId}' ORDER BY OBS_DT`
        } else {
            query = `${query} ORDER BY OBS_DT`
        }
        return query;
    } catch (err) {
        
        log.error(`Error building getTreeMapQuery: ERROR ${err}`)
        return ([424, err]);
    }

}

async function getValuesDataFromDBQuery(value) {
    return new Promise((resolve, reject) => {
        try {
            let query = '';
            querywithWhere = `SELECT * FROM ${value.viewName} WHERE`
            if (value.dimension) {
                Object.entries(value.dimension).forEach(([column, value]) => {
                    if (Array.isArray(value) && value.length > 1) {
                        let dimensionValues = [];
                        value.forEach(element => {
                            dimensionValues.push(element);
                        });
                        let dimensionValue = dimensionValues.map(i => `'${i}'`).join(',');
                        query = `${querywithWhere} UPPER(${column}) IN (${dimensionValue})`
                    } else {
                        query = `${querywithWhere} UPPER(${column}) = '${value}'`
                    }
                })
            } else {
                query = `SELECT * FROM ${value.viewName}`;
            }
            if (value.filterBy && Object.keys(value.filterBy).length > 0) {
                Object.entries(value.filterBy).forEach(([column, columnValue]) => {
                    if (query.includes('WHERE')) {
                        query = `${query} AND UPPER(${column}) = '${columnValue}'`
                    } else {
                        query = `${querywithWhere} UPPER(${column}) = '${value}'`
                    }
                })
            }
            query = `${query} ORDER BY OBS_DT`;
            resolve(query);
        } catch (err) {
            
            reject(([424, err]));
        }
    })
}

async function getAccuracyMetricsDataQuery(meta, id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM ${meta.viewName} WHERE ${meta.dbColumn} = '${id}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getAccuracyMetricsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getValuesQuery(valuesMeta,comboId) {
    return new Promise((resolve, reject) => {
        try {
            let viewName = valuesMeta.viewName;
            let query = `SELECT * FROM ${viewName}`;
            if ((Object.keys(valuesMeta.dimension)).length > 0) {
                Object.entries(valuesMeta.dimension).forEach(([columnValue, value]) => {
                    if(query.includes('WHERE')){
                        query = `${query} AND UPPER(${columnValue}) = '${value}'`
                    } else {
                        query = `${query} WHERE UPPER(${columnValue}) = '${value}'`
                    }
                });
                if(comboId && valuesMeta.unit !== 'nowcast-forecast'){
                    if(query.includes('WHERE')){
                        query = `${query} AND UPPER(PARAMETER_COMBO_ID) = '${comboId}'`
                    } else {
                        query = `${query} WHERE UPPER(PARAMETER_COMBO_ID) = '${comboId}'`
                    } 
                }
                if(valuesMeta.unit === 'nowcast-forecast'){
                    if(query.includes('WHERE')){
                        query = `${query} AND (UPPER(PARAMETER_COMBO_ID) = '${comboId}' OR UPPER(PARAMETER_COMBO_ID) is null)`
                    } else {
                        query = `${query} WHERE (UPPER(PARAMETER_COMBO_ID) = '${comboId}' OR UPPER(PARAMETER_COMBO_ID) is null)`
                    } 
                }

                if (valuesMeta.isScadProjection && valuesMeta.PARAMETER_COMBO_ID) {
                    query = `${query} OR UPPER(PARAMETER_COMBO_ID) = '${valuesMeta.PARAMETER_COMBO_ID}'`
                }
                query = `${query} ORDER BY OBS_DT`;
            } else {
                query = `SELECT * from ${viewName} ORDER BY OBS_DT`;
            }
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getValuesQuery with error ${err} `);
            reject([424, err]);
        }
    })
}

module.exports = { getQuery, getComboIdQuery, getTreeMapQuery, getValuesDataFromDBQuery, getAccuracyMetricsDataQuery, getValuesQuery }