const express = require('express');
const router = new express.Router();
const inflationController = require('../microservice-inflation/inflation.controller');
const { validateInnovativeInsights, validateInnovativeInsightsList } = require('./validators/innovative-insights.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/yearly-baseline-forecast', async (req, res, next) => {
    try {
        const data = await inflationController.getYearlyForecastBaseline(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for inflation by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/metrics-model', async (req, res, next) => {
    try {
        const data = await inflationController.getMetricsModel(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for inflation by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/metrics-train-test', async (req, res, next) => {
    try {
        const data = await inflationController.getMetricsTrainTest(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for inflation by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/forecast', async (req, res, next) => {
    try {
        const data = await inflationController.getForecastData(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for inflation by id content-type, ERROR: ${err}`);
        next(err);
    }
});

module.exports = router;
