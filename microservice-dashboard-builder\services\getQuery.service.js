const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const oracledb = require('oracledb');

async function getDashboardListQuery(userEmail,page,limit,search,sort='desc') {
  try {
    const offset = (page - 1) * limit;
    let binds = {
      userEmail: userEmail,
      offset: offset,
      limit: limit
    };

    let whereClauses = []
    whereClauses.push(`USER_EMAIL=:userEmail`)

    if (search){
      binds.search = `%${search.toUpperCase()}%`
      whereClauses.push(`UPPER(NAME) LIKE :search`) 
    }


    
    let query = `SELECT ID AS "id", NAME AS "name", INSERT_DT AS "createdDate", THUMBNAIL_LIGHT AS "thumbnailLight", THUMBNAIL_LIGHT_TYPE AS "thumbnailLightType",THUMBNAIL_DARK AS "thumbnailDark", THUMBNAIL_DARK_TYPE AS "thumbnailDarkType", LOGO AS "logo", LOGO_TYPE AS "logoType", COUNT(*) OVER() AS "totalCount" FROM IFP_DASHBOARDS WHERE ${whereClauses.join(' AND ')} ORDER BY INSERT_DT ${sort} OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY`;
    let options = { fetchInfo: { thumbnailLight: { type: oracledb.BUFFER },thumbnailDark: { type: oracledb.BUFFER } ,logo: { type: oracledb.BUFFER } } }
    return { query: query, binds: binds, options: options };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardListQuery with error ${err} `);
    throw err;
  }
}

async function getShareDashboardListQuery(type,userEmail,page,limit,search,sort="desc") {
  try {
    const offset = (page - 1) * limit;
    let binds = {
      userEmail: userEmail,
      offset: offset,
      limit: limit
    };

    let whereClauses = []
    whereClauses.push(`USER_EMAIL=:userEmail`)

    let searchClause = null

    if (search){
      binds.search = `%${search.toUpperCase()}%`
      searchClause=`UPPER(NAME) LIKE :search`
    }


    if (type == "received"){
      query = `
      SELECT
        A.ID AS "id",
        A.NAME AS "name",
        A.SHARE_EMAIL AS "shareEmail",
        A.SHARE_NAME AS "shareName",
        A.INSERT_DT AS "sharedDate",
        A.THUMBNAIL_LIGHT AS "thumbnailLight",
        A.THUMBNAIL_LIGHT_TYPE AS "thumbnailLightType",
        A.THUMBNAIL_DARK AS "thumbnailDark",
        A.THUMBNAIL_DARK_TYPE AS "thumbnailDarkType",
        A.LOGO AS "logo",
        A.LOGO_TYPE AS "logoType",
        A.TOKEN AS "token",
        COUNT(*) OVER() AS "totalCount"
      FROM
        IFP_SHARE_DASHBOARD A
      WHERE
        A.RECEPIENT_EMAIL = :userEmail ${searchClause?`AND ${searchClause}`:''}
      ORDER BY
        A.INSERT_DT ${sort} OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY
        `
    }
    else {
      query = `
      WITH RecipientCounts AS (
        SELECT
          ID,
          COUNT(DISTINCT RECEPIENT_EMAIL) AS NUMBER_OF_RECEPIENTS,
          RTRIM(DBMS_LOB.SUBSTR(XMLAGG(XMLELEMENT(e, RECEPIENT_EMAIL || ';').EXTRACT('//text()') ORDER BY RECEPIENT_EMAIL).GetClobVal(), 4000, 1), ';') AS RECEPIENT_EMAILS
        FROM
          IFP_SHARE_DASHBOARD
        WHERE
          SHARE_EMAIL = :userEmail ${searchClause?`AND ${searchClause}`:''}
        GROUP BY
          ID
            ),
            TotalCount AS (
        SELECT
          COUNT(DISTINCT ID) AS TOTAL_COUNT
        FROM
          IFP_SHARE_DASHBOARD
        WHERE
          SHARE_EMAIL = :userEmail ${searchClause?`AND ${searchClause}`:''}
            ),
            Pagination AS (
        SELECT
          A.ID,
          A.NAME,
          A.INSERT_DT AS SHARED_DT,
          A.TOKEN,
          RC.NUMBER_OF_RECEPIENTS,
          RC.RECEPIENT_EMAILS,
          A.THUMBNAIL_LIGHT,
          A.THUMBNAIL_LIGHT_TYPE,
          A.THUMBNAIL_DARK,
          A.THUMBNAIL_DARK_TYPE,
          A.LOGO,
          A.LOGO_TYPE,
          ROW_NUMBER() OVER (PARTITION BY A.ID
        ORDER BY
          A.INSERT_DT DESC) AS RN,
          TC.TOTAL_COUNT
        FROM
          IFP_SHARE_DASHBOARD A
        LEFT JOIN RecipientCounts RC ON
          A.ID = RC.ID
        CROSS JOIN TotalCount TC
        WHERE
          A.SHARE_EMAIL = :userEmail ${searchClause?`AND ${searchClause}`:''}
            )
        SELECT
          ID AS "id",
          NAME AS "name",
          SHARED_DT AS "sharedDate",
          TOKEN AS "token",
          NUMBER_OF_RECEPIENTS AS "numberOfRecepients",
          RECEPIENT_EMAILS AS "recepientEmails",
          TOTAL_COUNT AS "totalCount",
          THUMBNAIL_LIGHT AS "thumbnailLight",
          THUMBNAIL_LIGHT_TYPE AS "thumbnailLightType",
          THUMBNAIL_DARK AS "thumbnailDark",
          THUMBNAIL_DARK_TYPE AS "thumbnailDarkType",
          LOGO AS "logo",
          LOGO_TYPE AS "logoType"
        FROM
          Pagination
        WHERE
          RN = 1
        ORDER BY
          SHARED_DT ${sort}
        OFFSET :offset ROWS FETCH NEXT :limit ROWS ONLY`
    }
    
    let options = { fetchInfo: { thumbnailLight: { type: oracledb.BUFFER },thumbnailDark: { type: oracledb.BUFFER } ,logo: { type: oracledb.BUFFER } } }
    return { query: query, binds: binds, options: options };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardListQuery with error ${err} `);
    throw err;
  }
}

async function getDashboardQuery(dashboard) {
  try {
    let binds = {
      dashboardId: dashboard.id,
      userEmail: dashboard.userEmail
    };

    let query = `SELECT * FROM IFP_DASHBOARDS WHERE ID = :dashboardId AND USER_EMAIL = :userEmail `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardQuery with error ${err} `);
    throw err;
  }
}

async function getDashboardRequestorQuery(dashboardId,userEmail,requestor) {
  try {
    let binds = {
      dashboardId: dashboardId,
      userEmail: userEmail
    };

    let query;
    if (requestor == 'sender')
      query = `SELECT * FROM IFP_SHARE_DASHBOARD WHERE ID = :dashboardId AND SHARE_EMAIL = :userEmail `;
    else
      query = `SELECT * FROM IFP_SHARE_DASHBOARD WHERE ID = :dashboardId AND RECEPIENT_EMAIL = :userEmail `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardRequestorQuery with error ${err} `);
    throw err;
  }
}

async function getDashboardDetailQuery(dashboard) {
  try {
    let binds = {
      dashboardId: dashboard.id,
      userEmail: dashboard.userEmail
    };

    let query = `SELECT D.ID AS "id", D.NAME AS "name",D.INSERT_DT AS "createdDate",D.USER_EMAIL AS "userEmail",D.LOGO AS "logo", D.LOGO_TYPE AS "logoType", N.ID AS "nodeId", N.CONTENT_TYPE AS "contentType",N.NODE_GROUP AS "nodeGroup",N.PROPERTIES AS "properties"
    FROM IFP_DASHBOARDS D
    LEFT JOIN IFP_DASHBOARD_NODES N ON D.ID = N.DASHBOARD_ID
    WHERE D.ID = :dashboardId AND D.USER_EMAIL = :userEmail
    `;

    let options = { fetchInfo: { properties: { type: oracledb.BUFFER }, logo: { type: oracledb.BUFFER }, thumbnail: { type: oracledb.BUFFER } } }
    return { query: query, binds: binds,options:options };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardDetailQuery with error ${err} `);
    throw err;
  }
}

async function getDashboardShareDetailQuery(dashboard,requestor) {
  try {
    let binds = {
      dashboardId: dashboard.id,
      userEmail: dashboard.userEmail
    };
    let query;
    if (requestor == 'sender')
      query = `
      SELECT
        D.*,
        N.ID AS "nodeId",
        N.CONTENT_TYPE AS "contentType",
        N.NODE_GROUP AS "nodeGroup",
        N.PROPERTIES AS "properties"
      FROM
        (
        SELECT
          *
        FROM
          (
          SELECT
            ID AS "id",
            NAME AS "name",
            INSERT_DT AS "createdDate",
            SHARE_EMAIL AS "shareEmail",
            SHARE_NAME AS "shareName",
            RECEPIENT_EMAIL AS "recepientEmail",
            LOGO AS "logo",
            LOGO_TYPE AS "logoType",
            ROW_NUMBER() OVER (PARTITION BY ID
          ORDER BY
            ID) AS RN
          FROM
            IFP_SHARE_DASHBOARD
          WHERE
            ID = :dashboardId
            AND SHARE_EMAIL = :userEmail)
        WHERE
          RN = 1)D
      LEFT JOIN IFP_SHARE_DASHBOARD_NODES N ON
        D."id" = N.SHARE_ID`;
    else
      query = `SELECT D.ID AS "id", D.NAME AS "name",D.INSERT_DT AS "createdDate",D.SHARE_EMAIL AS "shareEmail",D.SHARE_NAME AS "shareName",D.RECEPIENT_EMAIL AS "recepientEmail",D.LOGO AS "logo", D.LOGO_TYPE AS "logoType", N.ID AS "nodeId", N.CONTENT_TYPE AS "contentType",N.NODE_GROUP AS "nodeGroup",N.PROPERTIES AS "properties"
        FROM IFP_SHARE_DASHBOARD D
        LEFT JOIN IFP_SHARE_DASHBOARD_NODES N ON D.ID = N.SHARE_ID
        WHERE D.ID = :dashboardId AND D.RECEPIENT_EMAIL = :userEmail
        `;
    let options = { fetchInfo: { properties: { type: oracledb.BUFFER }, logo: { type: oracledb.BUFFER }} }
    return { query: query, binds: binds,options:options };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardDetailQuery with error ${err} `);
    throw err;
  }
}

async function getDashboardDetailWithThumbnailQuery(dashboard) {
  try {
    let binds = {
      dashboardId: dashboard.id,
      userEmail: dashboard.userEmail
    };

    let query = `SELECT D.ID AS "id", D.NAME AS "name",D.INSERT_DT AS "createdDate",D.USER_EMAIL AS "userEmail",D.LOGO AS "logo", D.LOGO_TYPE AS "logoType",D.THUMBNAIL_DARK AS "thumbnailDark", D.THUMBNAIL_DARK_TYPE AS "thumbnailDarkType",D.THUMBNAIL_LIGHT AS "thumbnailLight", D.THUMBNAIL_LIGHT_TYPE AS "thumbnailLightType", N.ID AS "nodeId", N.CONTENT_TYPE AS "contentType",N.NODE_GROUP AS "nodeGroup",N.PROPERTIES AS "properties"
    FROM IFP_DASHBOARDS D
    LEFT JOIN IFP_DASHBOARD_NODES N ON D.ID = N.DASHBOARD_ID
    WHERE D.ID = :dashboardId AND D.USER_EMAIL = :userEmail
    `;

    let options = { 
      fetchInfo: { 
        properties: { type: oracledb.BUFFER }, 
        logo: { type: oracledb.BUFFER }, 
        thumbnailDark: { type: oracledb.BUFFER },
        thumbnailLight: { type: oracledb.BUFFER } 
      } 
    }
    return { query: query, binds: binds,options:options };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getDashboardDetailQuery with error ${err} `);
    throw err;
  }
}

async function createDashboardQuery(dashboard) {
  try {
    let binds = {
      id:dashboard.id,
      name:dashboard.name,
      thumbnailLightContent: dashboard.thumbnailLight.buffer,
      thumbnailLightType: dashboard.thumbnailLight.type,
      thumbnailDarkContent: dashboard.thumbnailDark.buffer,
      thumbnailDarkType: dashboard.thumbnailDark.type,
      userEmail:dashboard.userEmail
    };
    let query;
    if (dashboard.logo){
      binds.logoContent = dashboard.logo.buffer
      binds.logoType = dashboard.logo.type
      query = `INSERT INTO IFP_DASHBOARDS (ID, NAME,USER_EMAIL,THUMBNAIL_LIGHT,THUMBNAIL_LIGHT_TYPE,THUMBNAIL_DARK,THUMBNAIL_DARK_TYPE,LOGO,LOGO_TYPE) VALUES (:id, :name, :userEmail,:thumbnailLightContent,:thumbnailLightType,:thumbnailDarkContent,:thumbnailDarkType,:logoContent,:logoType)`;
    }
    else
      query = `INSERT INTO IFP_DASHBOARDS (ID, NAME,USER_EMAIL,THUMBNAIL_LIGHT,THUMBNAIL_LIGHT_TYPE,THUMBNAIL_DARK,THUMBNAIL_DARK_TYPE) VALUES (:id, :name, :userEmail,:thumbnailLightContent,:thumbnailLightType,:thumbnailDarkContent,:thumbnailDarkType)`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.createDashboardQuery with error ${err} `);
    throw err;
  }
}

async function createShareDashboardQuery(dashboard) {
  try {
    let binds = {
      id:dashboard.id,
      name:dashboard.name,
      thumbnailLightContent: dashboard.thumbnailLight.buffer,
      thumbnailLightType: dashboard.thumbnailLight.type,
      thumbnailDarkContent: dashboard.thumbnailDark.buffer,
      thumbnailDarkType: dashboard.thumbnailDark.type,
      logoContent: dashboard.logo.buffer,
      logoType: dashboard.logo.type,
      shareEmail: dashboard.shareEmail,
      shareName: dashboard.shareName,
      recepientEmail: dashboard.recepientEmail,
      token: dashboard.token
    };
    let query = `INSERT INTO IFP_SHARE_DASHBOARD (ID, NAME,THUMBNAIL_LIGHT,THUMBNAIL_LIGHT_TYPE,THUMBNAIL_DARK,THUMBNAIL_DARK_TYPE,LOGO,LOGO_TYPE,SHARE_EMAIL,SHARE_NAME,RECEPIENT_EMAIL,TOKEN)\
     VALUES (:id, :name, :thumbnailLightContent,:thumbnailLightType,:thumbnailDarkContent,:thumbnailDarkType,:logoContent,:logoType,:shareEmail,:shareName,:recepientEmail,:token)`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.createDashboardQuery with error ${err} `);
    throw err;
  }
}

async function editDashboardQuery(dashboard) {
  try {
    let binds = {
      dashboardId: dashboard.id,
      name: dashboard.name,
      thumbnailLightContent: dashboard.thumbnailLight.buffer,
      thumbnailLightType: dashboard.thumbnailLight.type,
      thumbnailDarkContent: dashboard.thumbnailDark.buffer,
      thumbnailDarkType: dashboard.thumbnailDark.type,
      userEmail: dashboard.userEmail
    };
    
    let query;

    if (dashboard.logo){
      binds.logoContent = dashboard.logo.buffer
      binds.logoType = dashboard.logo.type
      query = `UPDATE IFP_DASHBOARDS SET NAME=:name,THUMBNAIL_LIGHT=:thumbnailLightContent, THUMBNAIL_LIGHT_TYPE=:thumbnailLightType,THUMBNAIL_DARK=:thumbnailDarkContent, THUMBNAIL_DARK_TYPE=:thumbnailDarkType, LOGO=:logoContent, LOGO_TYPE=:logoType WHERE ID=:dashboardId AND USER_EMAIL=:userEmail`;
    }
    else
      query = `UPDATE IFP_DASHBOARDS SET NAME=:name, THUMBNAIL_LIGHT=:thumbnailLightContent, THUMBNAIL_LIGHT_TYPE=:thumbnailLightType,THUMBNAIL_DARK=:thumbnailDarkContent, THUMBNAIL_DARK_TYPE=:thumbnailDarkType WHERE ID=:dashboardId AND USER_EMAIL=:userEmail`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.editDashboardQuery with error ${err} `);
    throw err;
  }
}

async function deleteDashboardQuery(dashboard) {
  try {
    let binds = {
      dashboardId: dashboard.id,
      userEmail: dashboard.userEmail
    };

    let query = `DELETE FROM IFP_DASHBOARDS WHERE ID=:dashboardId AND USER_EMAIL=:userEmail`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.deleteDashboardQuery with error ${err} `);
    throw err;
  }
}

async function createDashboardNodesQuery(dashboardId,nodes) {
  try {
    let binds = {};
    let insertQueries = [];

    nodes.forEach((node, index) => {
        
        const propertiesString = Buffer.from(JSON.stringify(node.properties), 'utf-8');
        insertQueries.push(`SELECT :id${index}, :contentType${index}, :nodeGroup${index}, :dashboardId${index}, :properties${index} FROM dual`);
        binds = {
          ...binds,
            [`id${index}`]: node.id,
            [`contentType${index}`]: node.contentType,
            [`nodeGroup${index}`]: null, 
            [`dashboardId${index}`]: dashboardId,
            [`properties${index}`]: propertiesString 
        }
    });

    const query = `
        INSERT INTO IFP_DASHBOARD_NODES (ID, CONTENT_TYPE, NODE_GROUP, DASHBOARD_ID, PROPERTIES)
        WITH nodes AS (
          ${insertQueries.join(' UNION ALL ')}
        )
        SELECT * FROM nodes
    `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.createDashboardNodesQuery with error ${err} `);
    throw err;
  }
}

async function createShareDashboardNodesQuery(dashboardId,nodes) {
  try {
    let binds = {};
    let insertQueries = [];

    nodes.forEach((node, index) => {
        
        const propertiesString = Buffer.from(JSON.stringify(node.properties), 'utf-8');
        insertQueries.push(`SELECT :id${index}, :contentType${index}, :nodeGroup${index}, :dashboardId${index}, :properties${index} FROM dual`);
        binds = {
          ...binds,
            [`id${index}`]: node.id,
            [`contentType${index}`]: node.contentType,
            [`nodeGroup${index}`]: node.nodeGroup, 
            [`dashboardId${index}`]: dashboardId,
            [`properties${index}`]: node.properties 
        }
    });

    const query = `
        INSERT INTO IFP_SHARE_DASHBOARD_NODES (ID, CONTENT_TYPE, NODE_GROUP, SHARE_ID, PROPERTIES)
        WITH nodes AS (
          ${insertQueries.join(' UNION ALL ')}
        )
        SELECT * FROM nodes
    `;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.createDashboardNodesQuery with error ${err} `);
    throw err;
  }
}

async function deleteDashboardNodesQuery(dashboardId) {
  try {
    let binds = {
      dashboardId:dashboardId
    };
    const query = `DELETE FROM IFP_DASHBOARD_NODES WHERE DASHBOARD_ID = :dashboardId`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.deleteDashboardNodesQuery with error ${err} `);
    throw err;
  }
}

async function deleteShareDashboardQuery(shareId,requestor,userEmail) {
  try {
      if (!userEmail)
          throw new Error('Please provide a valid email')
      let binds = {
          shareId: shareId,
          userEmail: userEmail
      }
      let query;
      switch(requestor) {
          case 'sender':
              query = `DELETE FROM IFP_SHARE_DASHBOARD WHERE ID=:shareId AND SHARE_EMAIL=:userEmail`;
              break;
          case 'recepient':
              query = `DELETE FROM IFP_SHARE_DASHBOARD WHERE ID=:shareId AND RECEPIENT_EMAIL=:userEmail`;
              break;
          default:
              throw new Error(`Invalid requestor type: ${requestor}`)   
        }
      return {query:query,binds:binds};
  } catch (err) {
      log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.deleteDragMyAppsDataQuery with error ${err} `);
      throw err;
  }
}

async function getUserDashboardListDataQuery(userEmail) {
  try {
    let binds = {
      userEmail:userEmail
    };
    const query = `SELECT ID FROM IFP_DASHBOARDS WHERE USER_EMAIL = :userEmail`;

    return { query: query, binds: binds };
  } catch (err) {
    log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getUserDashboardListDataQuery with error ${err} `);
    throw err;
  }
}

async function getAppsRequestQuery(requestorEmail) {
  try {
      let binds = {
          userEmail: requestorEmail
      }
      let query = `SELECT NODE FROM IFP_APPS_REQUEST WHERE USER_EMAIL=:userEmail`;
      
      return {query:query,binds:binds};
  } catch (err) {
      log.error(`<<<<<< Exited microservice-dashboard-builder.services.getQuery.service.getAppsRequestQuery with error ${err} `);
      throw err;
  }
}

module.exports = {
  getDashboardListQuery,
  getShareDashboardListQuery,
  getDashboardQuery,
  getDashboardDetailQuery,
  getDashboardShareDetailQuery,
  getDashboardDetailWithThumbnailQuery,
  createDashboardQuery,
  createShareDashboardQuery,
  editDashboardQuery,
  deleteDashboardQuery,
  createDashboardNodesQuery,
  createShareDashboardNodesQuery,
  deleteDashboardNodesQuery,
  getUserDashboardListDataQuery,
  getDashboardRequestorQuery,
  deleteShareDashboardQuery,
  getAppsRequestQuery
};
