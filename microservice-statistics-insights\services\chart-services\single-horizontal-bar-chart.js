const scadLib = require('scad-library');

const { util } = scadLib;
const Logger = scadLib.logger;
const log = new Logger().getInstance();


const getSingleHorizontalBar = async (data, visualization, type) => {
  log.debug('>>>>> Enter services.chart-services.single-horizontal-bar-chart.getSingleHorizontalBar');
  if (type == 'geospatial') {
    return visualization;
  } else {
    visualization.seriesMeta.forEach(series => {
      series["data"] = [];
    })
    return new Promise((resolve, reject) => {
      try {
        if (data && data.length > 0) {
          data.forEach((element) => {
            if (element) {
              element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
              if (element.OLD_DATE) element.OLD_DATE = util.convertDate(`${element.OLD_DATE}`);
              visualization.seriesMeta.forEach(series => {
                if (series.dimensionValue.toLowerCase() === (element[visualization.dimensionColumn]).toLowerCase()) {
                  Object.entries(element).forEach(([key, value]) => {
                    if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                      delete element[key];
                    }
                  });
                  series.data.push(element);
                }
              })
            } else {
              log.debug('Received null values in db result')
            }
          });
        } else {
          log.error(`Data not available in DB for visualization ${visualization}`);
          reject([404, `Data not available in DB`]);
        }
        let values = [];
        visualization.seriesMeta.forEach(series => {
          series.data.sort((a, b) => a.OBS_DT - b.OBS_DT);
          let dataLen = series.data.length;
          if (dataLen > 0) {
            const lastestObj = series.data[dataLen - 1];
            series.xMin = lastestObj[series.valueAccessor.path];
            series.xMax = lastestObj[series.valueAccessor.path];
            series.data = [];
            series.data.push(lastestObj);
            values.push(lastestObj[series.valueAccessor.path])
          } else {
            log.debug(`Data is null for series ${JSON.stringify(series.id)}`);
          }
        })
        // Adding proportion Accessor value for horizontal bar chart 
        let proportionValue = values.reduce((prev, curr) => prev + curr, 0);
        visualization.seriesMeta.forEach(series => {
          if ( proportionValue && series.proportionAccessor && ! series.data[0][series.proportionAccessor.path] ) {
            series.data[0][series.proportionAccessor.path] = ( series.data[0][series.valueAccessor.path] / proportionValue ) * 100;
          }
        })
        let sortObj = {
          sortField: 'xMin',
          sortType: visualization.sortOrder,
          filterId: visualization.staticInSortField,
        };
        util.sortArray(visualization.seriesMeta, sortObj);
        resolve(visualization);
      } catch (err) {
        log.error(`<<<<< Exit services.chart-services.single-horizontal-bar-chart.getSingleHorizontalBar with error ${err}`);
        reject(err);
      }
    });
  }
};

module.exports = { getSingleHorizontalBar };
