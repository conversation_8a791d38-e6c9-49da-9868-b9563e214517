const newValuesDataFunctions = require('scad-library').valuesDataAnalytical;
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { getValuesData } = require('./getGraphData');
const constants = require('../../config/constants.json');

async function isNewValuesMeta(valuesMetaArr) {
    return new Promise((resolve, reject) => {
        try {
            let newValuesId = [
                'latest-date',
                'earliest-date',
                'earliest-date-value',
                'latest-date-value',
                'percentage-change',
                'previous-latest-date-value',
                'whatsnew'
            ]
            isExist = valuesMetaArr.filter(e => newValuesId.includes(e.id))
            if (isExist.length > 0) {
                resolve(true);
            } else {
                resolve(false);
            }
        }
        catch (err) {
            
            reject(err);
        }
    })
}

async function getNewValuesData(valuesMeta, userGroups) {
    try {
        let iconIds = []; let valueWithData = []
        valuesMeta.forEach(value => {
            if (value.iconId && value.iconId.length > 0) {
                iconIds.push(value.iconId);
            }
        })
        const promises = valuesMeta.map(async (value, index) => {
            try {
              const data = await getEachValueData(value);
                valueWithData[index] = data;
            } catch (err) {
              console.error("Error fetching data for value:", value, err);
              throw err;
            }
        });
        
        await Promise.all(promises);
        return valueWithData
    } catch (err) {
        
        throw err;
    }

}


const getEachValueData = (valuesMetaObj) => {
    return new Promise((resolve, reject) => {
        try {
            getValuesData(valuesMetaObj).then(async (valuesData) => {
                switch (valuesMetaObj.id) {
                    case 'earliest-date': {
                        let data = newValuesDataFunctions.earliestDate(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'latest-date': {
                        let data = newValuesDataFunctions.latestDate(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'earliest-date-value': {
                        let data = newValuesDataFunctions.earliestDateValue(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'latest-date-value': {
                        let data = newValuesDataFunctions.latestDateValue(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'percentage-change': {
                        let data = newValuesDataFunctions.percentageChange(valuesMetaObj, valuesData);
                        valuesMetaObj = Object.assign(valuesMetaObj, data);
                        break;
                    }
                    case 'previous-latest-date-value':
                        {
                            let data = newValuesDataFunctions.previousFromLastIndex(valuesMetaObj, valuesData);
                            valuesMetaObj = Object.assign(valuesMetaObj, data);
                            break;
                        }
                    case 'whatsnew':
                        {
                            let data = newValuesDataFunctions.overViewData(valuesMetaObj, valuesData);
                            valuesMetaObj = Object.assign(valuesMetaObj, data);
                            break;
                        }

                    default: {
                        log.debug(`Values Function not available`);
                        break;
                    }
                }
                resolve(valuesMetaObj);
            })
        }
        catch (err) {
            
            reject([422, err]);
        }
    });
}

module.exports = { isNewValuesMeta, getNewValuesData };