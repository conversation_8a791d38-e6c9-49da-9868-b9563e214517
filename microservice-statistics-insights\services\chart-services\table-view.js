const util = require('scad-library').util;
const moment = require('moment');
async function getTableSeries(visualization, data) {
    return new Promise((resolve, reject) => {
        try {
            let filteredData = [];
            data.forEach(element => {
                element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
                element['YEAR'] = element.YEAR ? element.YEAR : moment(`${element.OBS_DT}`).format('YYYY');
                filteredData.push(element);
            })
            visualization.seriesMeta[0]["data"] = filteredData;
            resolve(visualization);
        } catch (err) {
            reject(err);
        }
    })
}


module.exports = { getTableSeries }