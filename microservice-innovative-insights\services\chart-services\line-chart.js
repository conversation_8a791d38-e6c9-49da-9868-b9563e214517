const scadLibrary = require('scad-library');

const Logger = scadLibrary.logger;
const log = new Logger().getInstance();
const { util } = scadLibrary;
const moment = require('moment');

/**
 * Function to process data according to how the UI needs it
 * @param {*} data object
 * @param {*} seriesData object
 * @param {*} seriesValues object
 */
const processLineChartData = (data, series) => {
  try {
    series["data"] = [];

    try {
      if (data.length > 0) {
        series.data = data
        series.yMax = series.data[0][`MAX_${series.yAccessor.path}`];
        series.yMin = series.data[0][`MIN_${series.yAccessor.path}`];
        series.xMin = series.data[0][`MIN_${series.xAccessor.path}`];
        series.xMax = series.data[0][`MAX_${series.xAccessor.path}`];

      }
      else {
        log.error(`Data not available in DB for visualization ${visualization}`);
        reject([404, `Data not available in DB`]);
      }

      return series
    } catch (err) {
      
      log.error(`<<<<< Exit services.chart-services.line-chart.processData with error ${err}`);
      reject([422, err]);
    }

  } catch (err) {
    
    log.error(`Error in microservice-statistics-insights.services.chart-services.line-chart.processLineChartData ${err}`);
    reject([422, err]);
  }
}

module.exports = { processLineChartData };
