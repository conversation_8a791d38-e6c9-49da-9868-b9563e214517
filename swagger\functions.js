const constants = require('../config/constants.json')
const swaggerBase = require('./swaggerBase.json')
const s = require('./services/analyticalApps.json')

deleteEndpoints = [
    "/content-type/statistics-insights/",
    "/content-type/innovative-insights/compare",
    "/content-type/domains/subdomain/detail/{id}",
    "/content-type/domains/subdomain/subtheme/detail/{id}",
    "/content-type/terms-and-conditions/update-lang-preferrence",
    "/content-type/notifications/map-list",
    "/content-type/indicator-compare/list"    
]

function importSwaggerJson(){
    let swaggerJson = {...swaggerBase}
    constants.swagger.configs.forEach(config=>{
        let json = require(`./services/${config}.json`)
        deleteEndpoints.forEach(endpoint=>{
            try{
                delete json.paths[endpoint]
            }
            catch(exp){
            }
        })
        if (json.paths)
        swaggerJson.paths=Object.assign({}, swaggerJson.paths, json.paths);
        if (json.components && json.components.schemas)
            swaggerJson.components.schemas = Object.assign({}, swaggerJson.components.schemas, json.components.schemas);
    })
    return swaggerJson
}

module.exports = {
    importSwaggerJson
}