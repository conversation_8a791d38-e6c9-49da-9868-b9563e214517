
const { getCompareQuery } = require('./getQuery.service');
const { getSeriesData } = require('./getGraphData');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getCompareSeries(compareMeta) {
    try {
        let query = await getCompareQuery(compareMeta);
        return new Promise((resolve, reject) => {
            try {
                getSeriesData(query).then(data => {
                    log.debug(`<<<<<Exited microservice-statistics-insights.services.getCompareData successfully`);
                    resolve(data);
                });
            } catch (err) {
                
                log.error(`<<<<<Exited microservice-statistics-insights.services.getCompareData with error ${err}`);
                reject(err);
            }
        })
    } catch (err) {
        
        log.error(`<<<<<Exited microservice-statistics-insights.services.getCompareData with error ${err}`);
        reject(err);
    }
}

module.exports = { getCompareSeries }