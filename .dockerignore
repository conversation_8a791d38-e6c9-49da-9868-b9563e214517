npm-debug.log

# See http://help.github.com/ignore-files/ for more about ignoring files.
# compiled output
/dist
/build
/tmp
/out-tsc

/coverage
/junit.xml
/logs
/.nyc_output
# Only exists if <PERSON><PERSON> was run
/bazel-out

# profiling files
chrome-profiler-events*.json
speed-measure-plugin*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
junit.xml
npm_install.sh
dev_docker_build.sh
dev_env.sh
healthCheck.xml
az-*.yml
Readme.md
MultistageDocker_backup*
jest.config.*
.gitignore
.env
*/test
.npmrc
node_modules