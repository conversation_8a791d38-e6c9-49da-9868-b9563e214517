require('dotenv').config();
const { getMetaFromCMS } = require('../services/common-service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { listNotificationsData, getNotificationMappingsData, subscribeNotificationsData, subscribeEmailNotificationsData, unsubscribeNotificationsData, 
    unsubscribeEmailNotificationsData, readNotificationData, getCompareNodesById, getIndicatorsMetaData, getMappingsData, getMappingsNoPageData, getNotificationCount } = require('./services/executeQuery.service');
let constants = require('../config/constants.json');


/**
 * function to get notifications
 * @param {*} req 
 */
async function listNotifications(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.listNotifications`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username
            const status = req.query.status
            const page = req.query.page ? req.query.page : 1
            const limit = req.query.limit ? req.query.limit: 10
            const lang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
            const [ data, count ] = await Promise.all([listNotificationsData(userEmail, status,lang, page,limit), getNotificationCount(userEmail)])
            resolve({
                "totalCount": count[0]['NOTIFICATION_COUNT'],
                "unreadCount": count[0]['UNREAD_NOTIFICATION_COUNT'],
                "readCount": count[0]['READ_NOTIFICATION_COUNT'],
                "data": data
            })

        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.listNotifications with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to get node notifications mappings
 * @param {*} req 
 */
async function getNotificationsMappings(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.getNotificationsMappings`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username
            let data = await getNotificationMappingsData(userEmail)
            let result = {}
            data.forEach(obj => {
                result[obj.NODE_ID] = {
                    isNotification: true,
                    isEmail: obj.IS_EMAIL
                }
            });
            resolve(result)
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.getNotificationsMappings with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to subscribe notifications
 * @param {*} req 
 */
async function subscribeNotifications(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.subscribeNotifications`);
    return new Promise(async (resolve, reject) => {
        try {
            const isCompare = req.query.compare?req.query.compare:false
            const userEmail = req.user.preferred_username

            if (isCompare){
                const compareId = req.body.id
                const contentType = 'scad_official_indicator'
                const appType = 'scad'
                const isEmail = req.body.isEmail?1:0
                const nodes = await getCompareNodesById(compareId)
                nodes.forEach(async node => {
                    await subscribeNotificationsData(node.NODE, userEmail, contentType, appType,'',isEmail)
                })
            }
            else{
                const nodeId = req.body.id
                const contentType = req.body.contentType
                const appType = req.body.appType?req.body.appType:['innovative-insights','official-insights'].includes(contentType)?'scad':''
                const viewName = req.body.viewName?req.body.viewName:''
                const isEmail = req.body.isEmail?1:0
                await subscribeNotificationsData(nodeId, userEmail, contentType, appType,viewName, isEmail)
            }
            
            
            resolve({ "message": "Mapping created successfully", "status": "success" })
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.subscribeNotifications with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to subscribe notifications
 * @param {*} req 
 */
async function subscribeEmailNotifications(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.subscribeNotifications`);
    return new Promise(async (resolve, reject) => {
        try {
            const isCompare = req.query.compare?req.query.compare:false
            const userEmail = req.user.preferred_username

            if (isCompare){
                const compareId = req.body.compareId
                const contentType = 'scad_official_indicator'
                const appType = 'scad'
                const isEmail = req.body.isEmail?1:0
                const nodes = await getCompareNodesById(compareId)
                nodes.forEach(async node => {
                    await subscribeEmailNotificationsData(node.NODE, userEmail, contentType, appType, isEmail)
                })
            }
            else{
                const nodeId = req.body.id
                const contentType = req.body.contentType
                const appType = req.body.appType
                const isEmail = req.body.isEmail?1:0
                await subscribeEmailNotificationsData(nodeId, userEmail, contentType, appType, isEmail)
            }
            resolve({ "message": "Email flag mapping updated successfully", "status": "success" })
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.subscribeNotifications with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to unsubscribe notifications
 * @param {*} req 
 */
async function unsubscribeNotifications(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.unsubscribeNotifications`);
    return new Promise(async (resolve, reject) => {
        try {
            const isCompare = req.query.compare?req.query.compare:false
            const userEmail = req.user.preferred_username

            if (isCompare){
                const compareId = req.body.compareId
                const nodes = await getCompareNodesById(compareId)
                nodes.forEach(async node => {
                    await unsubscribeNotificationsData(node.NODE, userEmail)
                })
            }
            else{
                const nodeId = req.body.id
                await unsubscribeNotificationsData(nodeId, userEmail)
            }
            
            resolve({ "message": "Mapping created successfully", "status": "success" })
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.unsubscribeNotifications with error ${err}`);
            reject(err);
        }
    })
}

/**
 * function to subscribe notifications
 * @param {*} req 
 */
async function unsubscribeEmailNotifications(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.subscribeNotifications`);
    return new Promise(async (resolve, reject) => {
        try {
            const isCompare = req.query.compare?req.query.compare:false
            const userEmail = req.user.preferred_username

            if (isCompare){
                const compareId = req.body.compareId
                const nodes = await getCompareNodesById(compareId)
                nodes.forEach(async node => {
                    await unsubscribeEmailNotificationsData(node.NODE, userEmail)
                })
            }
            else{
                const nodeId = req.body.id
                await unsubscribeEmailNotificationsData(nodeId, userEmail)
            }
            
            resolve({ "message": "Email flag mapping updated successfully", "status": "success" })
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.subscribeNotifications with error ${err}`);
            reject(err);
        }
    })
}


/**
 * function to read a notification
 * @param {*} req 
 */
async function readNotification(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.readNotification`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username
            const notificationId = req.body.id
            let isShareApp = req.query.share?req.query.share:false
            if (!['true',false].includes(isShareApp)){
                throw new Error('Please provide a valid entry for share')
            }
            let data = await readNotificationData(notificationId, userEmail,isShareApp)
            resolve(data)
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.readNotification with error ${err}`);
            reject(err);
        }
    })
}

async function mapList(req) {
    log.debug(`>>>>>Entered insights-microservice.notifications.controller.mapList`);
    return new Promise(async (resolve, reject) => {
        try {
            const userEmail = req.user.preferred_username

            const langCode = req.headers["accept-language"]
            const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
            const page = req.query.page?req.query.page:1
            const limit = req.query.limit?req.query.limit:10
            const cmsClassificationList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST}`;
            const cmsNodeClassificationUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST}`;
            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

            let classificationMap = {}
            let classificatioNameMap = {}


            const [
                mapData, 
                nodeCMSData,
                classificationData
              ] = await Promise.all([
                getMappingsNoPageData(userEmail,page,limit),
                getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
                getMetaFromCMS(req,cmsLoginUrl, cmsClassificationList, req.user.groups)  
              ]);

            if (!mapData.length){
                return resolve({'message':'User has no notification subscription','status':false})
            }

            let classifications =Object.values(classificationData.classification)
            classifications.forEach(classification => {
                let key = classification.key
                classificatioNameMap[classification.name] = classification.key
                if (key == 'official_statistics')
                    classificationMap['official-insights'] = classification.name
                else if (key == 'experimental_statistics')
                    classificationMap['innovative-insights'] = classification.name
                else
                    classificationMap[classification.key] = classification.name
            })

            const nodes = mapData.map(node=>node.NODE_ID)
            let indicatorsMetaData = await getIndicatorsMetaData(nodes,langCode)
            let metaMap = {}
            indicatorsMetaData.forEach(indicator=>{
                metaMap[indicator.INDICATOR_ID] = indicator
            })

            classificationNodeResult = {}

            mapData.forEach(node=>{
                let item = {
                    id: node.NODE_ID,
                    isEmaill: node.IS_EMAIL
                }
                let nodeClassification;
                let nodeDomain;
                let nodeTheme;
                let nodeSubTheme;
                let nodeProduct;

                if(node.CONTENT_TYPE == 'Spatial-Analytics'){
                    nodeClassification = 'Analytical Apps'
                    nodeDomain = 'Spatial Analytics'
                    item = {
                        ...item,
                        ...{
                            title:'Spatial Analytics',
                            content_type: node.CONTENT_TYPE,
                            domain: 'Spatial Analytics',
                        }   
                    }
                }
                else if(['official-insights','innovative-insights'].includes(node.CONTENT_TYPE)){
                    let meta = metaMap[node.NODE_ID]
                    nodeClassification = classificationMap[node.CONTENT_TYPE]
                    nodeDomain = meta.TOPIC_NAME
                    nodeTheme = meta.THEME_NAME
                    nodeSubTheme = meta.SUB_THEME_NAME
                    nodeProduct = meta.PRODUCT_NAME
                    item = {
                            ...item,
                            ...{
                                title: meta.INDICATOR_NAME,
                                content_type: node.CONTENT_TYPE,
                                domain:meta.TOPIC_NAME,
                            }    
                        }
                }
                else if(Object.keys(nodeCMSData).includes(node.NODE_ID)){
                    let meta = nodeCMSData[node.NODE_ID]
                    nodeClassification = meta.content_classification
                    nodeDomain = meta.domain
                    if (['official_statistics','experimental_statistics'].includes(classificatioNameMap[nodeClassification])){
                        nodeTheme = meta.theme
                        nodeSubTheme = meta.subtheme
                        nodeProduct = meta.product
                    }


                    item = {
                        ...item,
                        ...{
                            title: meta.title,
                            content_type: node.CONTENT_TYPE,
                            classification: meta.content_classification,
                            domain:meta.domain,
                        }    
                    }
                }
                
                item.classification = nodeClassification

                if(nodeDomain && !Object.keys(classificationNodeResult).includes(nodeDomain)){
                    classificationNodeResult[nodeDomain] = {
                        name: nodeDomain,
                        classifications:{}
                    }
                    classifications.forEach(classification=>{
                        classificationNodeResult[nodeDomain].classifications[classification.name]={
                            ...classification,
                            nodeCount: 0
                        }
                        if (classificationNodeResult[nodeDomain].classifications[classification.name].key == 'analytical_apps')
                            classificationNodeResult[nodeDomain].classifications[classification.name].categories=[]
                        else if (['official_statistics','experimental_statistics'].includes(classificationNodeResult[nodeDomain].classifications[classification.name].key))
                            classificationNodeResult[nodeDomain].classifications[classification.name].nodes={}
                        else if (classificationNodeResult[nodeDomain].classifications[classification.name].key == 'reports')
                            classificationNodeResult[nodeDomain].classifications[classification.name].nodes=[]
                    })
                    

                    if (nodeTheme && !Object.keys(classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes).includes(nodeTheme)){
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme]={
                            name:nodeTheme,
                            subthemes:{},
                            nodes:[]
                        }
                    }

                    if (nodeSubTheme && !Object.keys(classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].subthemes).includes(nodeSubTheme)){
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].subthemes[nodeSubTheme]={
                            name:nodeSubTheme,
                            products:{}
                        }
                    }

                    if (nodeProduct && !Object.keys(classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].subthemes[nodeSubTheme].products).includes(nodeProduct) ){
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].subthemes[nodeSubTheme].products[nodeProduct]={
                            name:nodeProduct,
                            nodes:[]
                        }
                    }

                    if(classificatioNameMap[nodeClassification] == 'official_statistics' && item.content_type == 'official-insights')
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].subthemes[nodeSubTheme].products[nodeProduct].nodes.push(item)
                    else if(classificatioNameMap[nodeClassification] == 'experimental_statistics' && item.content_type == 'innovative-insights')
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].nodes.push(item)
                    else if(classificatioNameMap[nodeClassification] == 'official_statistics')
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].subthemes[nodeSubTheme].products[nodeProduct].nodes.push(item)
                    else if(classificatioNameMap[nodeClassification] == 'experimental_statistics')
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes[nodeTheme].nodes.push(item)
                    else if(classificatioNameMap[nodeClassification] == 'analytical_apps')
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].categories.push(item)
                    else if(classificatioNameMap[nodeClassification] == 'reports')
                        classificationNodeResult[nodeDomain].classifications[nodeClassification].nodes.push(item)

                    classificationNodeResult[nodeDomain].classifications[nodeClassification].nodeCount +=1
                }

                
            })

            let dropDownList = []
            const result = Object.entries(classificationNodeResult).map(([domain,value])=>{
                dropDownList.push(domain)
                const domainObj = {
                    name: domain,
                    classifications: Object.entries(value.classifications).map(([key,classification]) =>{
                        const classificationObj = {
                            id: classification.id,
                            name: classification.name,
                            datkIconPath: `${process.env.CMS_BASEPATH_URL}${classification.icon_path}`,
                            key: classification.key,
                            lightIconPath: `${process.env.CMS_BASEPATH_URL}${classification.light_icon_path}`,
                            nodeCount: classification.nodeCount
                        }
                        if(classification.key == 'analytical_apps'){
                            classificationObj.categories = classification.categories
                        }
                        else if(classification.key == 'experimental_statistics'){
                            classificationObj.items = Object.values(classification.nodes).map(theme=>{
                                const themeObj = {
                                    name: theme.name,
                                    nodes: theme.nodes
                                }
                                return themeObj;
                            })
                        }
                        else if(classification.key == 'official_statistics'){
                            classificationObj.items = Object.values(classification.nodes).map(theme=>{
                                const themeObj = {
                                    name: theme.name,
                                    subthemes: Object.values(theme.subthemes).map(subTheme=>{
                                        const subThemeObj = {
                                            name: subTheme.name,
                                            products: Object.values(subTheme.products).map(product=>{
                                                const productObj = {
                                                    name: product.name,
                                                    nodes: product.nodes
                                                }
                                                return productObj;
                                            })
                                        }
                                        return subThemeObj;
                                    })
                                }
                                return themeObj;
                            })
                        }
                        else if(classification.key == 'reports'){
                            classificationObj.items = classification.nodes
                        }
                        return classificationObj;
                    })
                }
                return domainObj;
            })
            
            resolve({domains:dropDownList,results:result})
            
        } catch (err) {
            
            log.error(`<<<<<Exited insights-microservice.notifications.controller.mapList with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { listNotifications, getNotificationsMappings, subscribeNotifications, subscribeEmailNotifications, unsubscribeNotifications, unsubscribeEmailNotifications, readNotification, mapList };

