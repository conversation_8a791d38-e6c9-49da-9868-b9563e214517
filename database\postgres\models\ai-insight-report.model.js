const { Sequelize, DataTypes, Model } = require("sequelize");
const ChatBayaanDomain = require("./bayaan-domain.model");
const DropdownOption = require("./drop-down-option.model");

/**
 * Model to store AI generated insight reports along with
 * all necessary identification fields like version, quatrer etc
 * along with metadata like report type and approval related
 * fields like status.
 * 
 * Contains relations to already existing tables: chat_bayaandomains and
 * common_dropdownoption.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const AiInsightReport = sequelize.define(
    'AiInsightReport',
    {
      id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      object_id: {
        type: DataTypes.UUID,
        allowNull: false,
        defaultValue: DataTypes.UUIDV4,
      },
      report_name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      version: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      quarter: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      report_data: {
        type: DataTypes.JSON,
        allowNull: false,
      },
      ticket_id: {
        type: DataTypes.STRING,
      },
    },
    {
      tableName: 'ai_insight_report',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  AiInsightReport.belongsTo(ChatBayaanDomain(sequelize, DataTypes), {
    foreignKey: 'domain_id',
    targetKey: 'id',
    as:'domain',
  });

  AiInsightReport.belongsTo(DropdownOption(sequelize, DataTypes), {
    foreignKey: 'report_type_id',
    targetKey: 'id',
    as: 'report_type'
  });

  AiInsightReport.belongsTo(DropdownOption(sequelize, DataTypes), {
    foreignKey: 'report_status_id',
    targetKey: 'id',
    as:'status',
  });

  return AiInsightReport
}
module.exports = model;
