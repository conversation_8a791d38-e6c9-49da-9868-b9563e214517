'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('ai_insight_report', {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      object_id: {
        type: Sequelize.UUID,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4,
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      quarter: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      report_data: {
        type: Sequelize.JSON,
        allowNull: false,
      },
      ticket_id: {
        type: Sequelize.STRING,
      },
      domain_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'chat_bayaandomains',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      report_type_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'common_dropdownoption',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      report_status_id: {
        type: Sequelize.INTEGER,
        references: {
          model: 'common_dropdownoption',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ai_insight_report');
  }
};
