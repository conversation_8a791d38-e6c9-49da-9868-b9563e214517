const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const oracledb = require('oracledb');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {  getUserJourneyStatusQuery,attendUserJourneyQuery } = require('./getQuery.service');


async function getUserJourneyStatusData(email) {
  return new Promise((resolve, reject) => {
    getUserJourneyStatusQuery(email).then((results) => {
      log.debug(`>>>>> Enter microservice-user-journey.services.getGraphData.getUserJourneyStatusData`);

      db.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-user-journey.services.getGraphData.getUserJourneyStatusData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-user-journey.services.getGraphData.getUserJourneyStatusData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-user-journey.executeQuery.service.getUserJourneyStatusData with error ${err}`);
      reject(err);
    })
  })
}

async function updateUserJourneyStatus(email,status) {
  return new Promise((resolve, reject) => {
    attendUserJourneyQuery(email,status).then((results) => {
      log.debug(`>>>>> Enter microservice-user-journey.services.getGraphData.updateUserJourneyStatus`);

      db.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-user-journey.services.getGraphData.updateUserJourneyStatus successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-user-journey.services.getGraphData.updateUserJourneyStatus with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-user-journey.executeQuery.service.updateUserJourneyStatus with error ${err}`);
      reject(err);
    })
  })
}

module.exports = {
  getUserJourneyStatusData,
  updateUserJourneyStatus
}