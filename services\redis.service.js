const redisClient = require('../redis-con');
const constants = require('../config/constants.json');
const Logger = require('scad-library').logger;
const { IFPError } = require('../utils/error');
const log = new Logger().getInstance();
const ioRedis = require('ioredis'); // Required for flush and pattern-based operations
const axios = require('axios');

function getRedisClient() {
    return redisClient;
}

async function setRedis(key, value, exp, headers = {}, query = undefined) {
    try {
        key = `${process.env.NODE_ENV}_${key}`;
        if (constants.redis.enabled) {
            // Cache-Control headers logic can be re-added if needed
            await redisClient.set(key, value, 'EX', exp, 'NX');
            log.info(`Cache set with key: ${key}`);
        }
    } catch (err) {
        throw new IFPError(500, 'Error setting Redis Cache');
    }
}

async function getRedis(key, headers = {}) {
    try {
        key = `${process.env.NODE_ENV}_${key}`;
        if (constants.redis.enabled) {
            return redisClient.get(key);
        }
        return null;
    } catch (err) {
        throw new IFPError(500, 'Error reading Redis Cache');
    }
}

// Always enabled Redis
async function setGlobalRedis(key, value, exp) {
    try {
        await redisClient.set(key, value, 'EX', exp);
        log.info(`Cache set with key: ${key}`);
    } catch (err) {
        throw new IFPError(500, 'Error setting Redis Cache');
    }
}

// Always enabled Redis
async function getGlobalRedis(key) {
    try {
        return redisClient.get(key);
    } catch (err) {
        throw new IFPError(500, 'Error reading Redis Cache');
    }
}

// Always enabled Redis
async function delGlobalRedis(key) {
    try {
        return redisClient.del(key);
    } catch (err) {
        throw new IFPError(500, 'Error deleting Redis Cache');
    }
}

// Function to flush keys by pattern
function flushKeys(pattern) {
    const redis = new ioRedis({
        port: process.env.REDIS_PORT,
        host: process.env.REDIS_HOST,
    });

    return redis.keys(pattern).then(function (keys) {
        const pipeline = redis.pipeline();
        keys.forEach(function (key) {
            pipeline.del(key);
        });
        return pipeline.exec();
    }).finally(function () {
        redis.quit();
    });
}

async function flushGatewayCache(pattern) {
    const gatewayEndpoint = process.env.GATEWAY_ENDPOINT?process.env.GATEWAY_ENDPOINT:'http://bayaan-gateway:4000';
    const url = `${gatewayEndpoint}/redis/flush`;
    const headers = {
        'Content-Type': 'application/json'
    };
    const data = {
        pattern: pattern
    };

    try {
        const response = await axios.post(url, data, { headers });
        log.info(`Flush gateway cache response: ${response.data}`);
        return true
    } catch (error) {
        log.error(`Error flushing gateway cache: ${error.message}`);
        throw new IFPError(500, 'Error flushing gateway cache');
    }
}

// Function to get keys by pattern
const getRedisKeys = (pattern) => {
    const redis = new ioRedis({
        port: process.env.REDIS_PORT,
        host: process.env.REDIS_HOST,
    });

    return redis.keys(pattern).finally(() => redis.quit());
};

module.exports = { 
    setRedis, 
    getRedis, 
    setGlobalRedis, 
    getGlobalRedis, 
    delGlobalRedis, 
    flushKeys, 
    getRedisKeys, 
    getRedisClient,
    flushGatewayCache
};
