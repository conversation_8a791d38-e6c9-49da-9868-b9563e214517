const { getIndicatorConfiguration } = require("../../microservice-compute/services/executeQuery");
const { IFPError } = require("../../utils/error");

const validateCompute = async (req, res, next) => {
    try{
        if (!Number(req.body.id))
            throw new IFPError(400,`Please input a valid indicator id`)

        if (!req.body.operation)
            throw new IFPError(400,`Please specify an operation`)

        if(!['+','-','/','*'].includes(req.body.operation))
            throw new IFPError(400,`${req.body.operation} is not a valid operation`)

            
        const { dimensions } = req.body;
            
            
        if (!dimensions) 
            throw new IFPError(400,'Dimensions are missing in the input')
        
        const arrayCount = Object.values(dimensions).reduce((count, value) => {
            return count + (Array.isArray(value) ? 1 : 0);
        }, 0);
        
        if (arrayCount !== 1) {
            throw new IFPError(400,"Exactly one dimension should be an array, and the rest should be single values, if any.");
        }

        let indicatorConfiguration = await getIndicatorConfiguration(req.body.id)
        if (indicatorConfiguration.length<1)
            throw new IFPError(404,'Provided indicator not found')

        indicatorConfiguration = JSON.parse(indicatorConfiguration[0].CONFIGURATION)[0]

        if (!indicatorConfiguration.isMultiDimension)
            throw new IFPError(400,`The provided indicator doesn't support compute. Please try with a multidimensional indicator`)

        filterPanelData = indicatorConfiguration.filterPanelData
        language = req.headers['accept-language'].toUpperCase()
        requiredDimensions = filterPanelData.properties.filter(property=>property.lang == language).map(property=>property.path)
        
        if (!(requiredDimensions.every(element => Object.keys(dimensions).includes(element))))
            throw new IFPError(400,`Please provide all the dimension of the indicator, required: [${requiredDimensions.join(',')}]`)
        console.log(indicatorConfiguration)
        
        next()
    }
    catch(err){
        next(err)
    }
  };

module.exports = {
    validateCompute
}