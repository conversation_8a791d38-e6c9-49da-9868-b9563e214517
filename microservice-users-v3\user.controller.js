const Logger = require("scad-library").logger;
require("dotenv").config();
const log = new Logger().getInstance();
const path = require('node:path');

const { IFPError } = require("../utils/error");

const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require("uuid");
var XLSX = require("xlsx");

const {
  getEntityDetails,
  createInvitationData,
  getEntityList,
  getInvitationData,
  getInvitationDataV2,
  createUserData,
  setUserActivationStatus,
  getActiveSuperuser,
  updateInvitationStatus,
  updateUserStatus,
  setUserDeleteStatus,
  createUserAccess,
  removeUserAccess,
  getUserData,
  updateUserAccessApproval,
  createUserAccessApproval,
  getActiveUserByEmail,
  getActiveUserById,
  getUserDataV2,
  getDisseminationAccessPolicy,
  createUserAccessRequest,
  updateUserAccessRequestStatus,
  getApprovedAccess,
  getIntraIDPendingAccess,
  getActiveDGUser,
  getDGPendingAccess,
  getUserAccessApprovals,
  getUserAccessLevels,
  getPENewAccess,
  getActivePEUser,
  getSUPendingAccess,
  createEIDUser,
  createEIDInvitationMapping,
  getEIDInvitationMapping,
  getDGNewAccess,
  getDGExistingAccess,
  getPEExistingAccess,
  getSUExistingAccess,
  getSUNewAccess,
  deleteUserAccess,
  deleteUserData,
  deleteUserAccessRequests,
  getSecondarySU,
  updatePrimarySU,
  getSecondaryPE,
  updatePrimaryPE,
  updateSecondarySU,
  updateSecondaryPE,
  updateUserData,
  listPendingInvitations,
  deleteInvitation,
  updateInvitation,
  getDGOnboardingStatus,
  getAllMappingsData,
  getMappingByRequestIdsData,
  getMappingByEmailsData,
  bulkDeleteMappingsData,
  getEntityUsersForExport,
  getUserDataV3,
  getSensitiveAccessData,
  getDeletedUsersList,
  getUserAccessV2,
  getRequestIDsforPendingAccess,
  deleteInvitationByEmail,
  resetUserData,
  deleteUserEIDMapping,
} = require("./services/executeQuery");
const { verifyOTP, generateOTP, sendOTP, deleteVerifyFlag } = require("./services/otp");
const { generateInvitationToken, maskEmail, maskPhone, hashEmiratesId, formatAccess, parseAccessData, getRoleName, cleanEIDName, filterApprovedUserAccess } = require("./helper");
const { updateInvitationStatusQuery } = require("./services/getQuery");
const { sendEmail } = require("./services/email/sendEmail.service");
const { getGlobalRedis } = require("../services/redis.service");
const { getBayaanExternalGroupMatrix, getDomainMatrixMap } = require("../services/group.service");
const { encryptEmail, decryptEmail, encryptPhone, decryptPhone } = require("../services/encryption.service");
const { toTitleCase } = require("../services/helpers/helper");
const { deleteMobileUser, syncBayaanMobile } = require("./services/mobile");
const { getUserInfo, unassignUserFromGroup } = require("../services/graph");
const UAEPassService = require("../services/uae-pass.service");
const message = require("./responseMessages.json");

class RoleController {
  constructor() {

  }

  async getDGOnboardingStatus(entityId) {
    try {
      const dgUser = await getUserDataV2({
        ROLE: "DG",
        ENTITY_ID: entityId,
        STATUS: "REGISTERED"
      });

      if (dgUser.length > 0) {

        // User pending AD activation
        if (dgUser[0].ACTIVATION_FLAG == "PENDING") {
          return "INVITE_SENT";
        }

        // DG User exists
        switch (dgUser[0].EXISTING_USER) {
          case "EXISTING_UNLINKED":
            return "INVITE_PENDING";
          case "EXISTING_LINK_INITIATED":
            return "INVITE_SENT";
          case "EXISTING_LINKED":
          case null: // STATUS=ACTIVE & EXISTING_USER=NULL is active new registration DG
            return "REGISTERED";
          default:
            return "INVITE_PENDING";
        }
      }

      // User does not exist, check status via invitation
      const dgInvitation = await getInvitationDataV2({
        ENTITY_ID: entityId,
        INVITE_TYPE: "DG",
      });
      if (dgInvitation.length > 0 && dgInvitation[0].STATUS === "PENDING") {
        return "INVITE_SENT";
      }
      return "INVITE_PENDING";
    } catch (error) {
      log.error(`Something went wrong when getting DG Status: ${error}`);
      throw error;
    }
  }

  async getUserRole(req) {
    try {
      const userEmail = req.user.preferred_username
      const decryptedEmail = encryptEmail(userEmail)
      const data = await getUserData(decryptedEmail)
      const entityData = await getEntityDetails(data.ENTITY_ID)
      const accessPolicy = await getDisseminationAccessPolicy(data.ENTITY_ID)
      const dgRequired = accessPolicy.some(policy => 
        policy.CLASSIFICATION === "SENSITIVE" || policy.CLASSIFICATION === "SECRET"
      );
      let roleData = {
        "role": data.ROLE,
        "designation": data.DESIGNATION,
        "entity": {
          "id": entityData.ID,
          "name": entityData.NAME,
          "domains": entityData.DOMAIN.split(',')
        },
        "dg_required": dgRequired
      };

      if (['PRIMARY_SUPERUSER','SECONDARY_SUPERUSER'].includes(data.ROLE) && dgRequired){
        try {
          roleData.dg_status = await this.getDGOnboardingStatus(entityData.ID);
        } catch (error) {
          log.error(`DG Records for the entity: ${entityData.ID} not found.`);
        }
      }
      return roleData;
    } catch (error) {
      throw error;
    }
  }

}

class EntityController {
  constructor() {

  }

  async listEntity(req) {
    try {
      let data = await getEntityList()
      for (let entity of data) {
        entity.DOMAINS = entity.DOMAINS.split(',')
      }
      return data;
    } catch (error) {
      throw error;
    }
  }

  async accessPolicy(req) {
    try {
      let superUserData = req.superuser
      let data = await getDisseminationAccessPolicy(superUserData.ENTITY_ID)
      let domainMap = {}
      const toTitleCase = (str) => {
        return str.toLowerCase().split(' ').map(function (word) {
          return word.charAt(0).toUpperCase() + word.slice(1);
        }).join(' ');
      }

      data.forEach(d => {
        const domainValue = d.DOMAIN.toLowerCase()
        const classificationValue = d.CLASSIFICATION.toLowerCase()
        if (!(domainValue in domainMap)) {
          domainMap[domainValue] = {
            "open": false,
            "confidential": false,
            "sensitive": false,
            "secret": false
          }
        }
        domainMap[domainValue][classificationValue] = true

      })

      let result = Object.entries(domainMap).map(([domain, access]) => {
        return {
          "name": toTitleCase(domain),
          "value": domain,
          "access": Object.entries(access).map(([classification, flag]) => {
            return {
              "name": toTitleCase(classification),
              "value": classification,
              "enabled": flag
            }
          })
        }
      })
      return result;
    } catch (error) {
      log.error("Error while getting access policy")
      throw error;
    }
  }
}

class ProductEngagementController {
  constructor() {
    this.secretKey = process.env.APP_SECRET_KEY;
  }

  async getUserAccessRequests(req) {
    try {
      const type = req.params.type
      const page = req.query.page ? req.query.page : 1
      const limit = req.query.limit ? req.query.limit : 10
      const search = req.query.search
      const sortBy = req.query.sortBy
      const order = req.query.order
      const entity = req.query.entity
      const userType = req.query.userType;

      let data = [];
      let usersList;

      if (type == "new") {
        usersList = await getPENewAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "existing") {
        usersList = await getPEExistingAccess(page, limit, search, entity, sortBy, order, userType);
      }
      else if (type == "approved") {
        usersList = await getApprovedAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "intra-id-pending") {
        usersList = await getIntraIDPendingAccess(page, limit, search, entity, sortBy, order);
      }
      else{
        throw new IFPError(400,"Invalid type")
      }

      data = await Promise.all(usersList.map(async user => {
        user.isLinked = user.isLinked == "EXISTING_UNLINKED" ? false : true;
        user.email =
          type == "existing"
            ? decryptEmail(user.email)
            : maskEmail(decryptEmail(user.email));
        try {
          const userAccessRecords = await getUserAccessV2(
            user.id,
            type,
            req.peUser.ROLE,
            req.peUser.ROLE
          );
          if (!userAccessRecords.length) {
            return null;
          }
          const { formattedAccess, maxClassification, revertInfo } =
            formatAccess(userAccessRecords);
          user.access = formattedAccess;
          user.maxClassification = maxClassification;
          Object.assign(user, revertInfo);
          return user;
        } catch (e) {
          log.error(`Something went wrong getting access for user ${user.id}: ${e}`)
          return null;
        }
      }));

      data = data.filter((user) => user !== null);

      let result = {}
      if(data.length > 0) {
        result = {
          total: data[0].totalCount,
          data: data          
        }
      }
      return result;

    } catch (error) {
      throw error
    }
  }

  async makePrimary(req){
    try{
      const {id} = req.body
      const peUser = req.peUser
      const secondaryPE = await getSecondaryPE()
      if (secondaryPE.length == 0){
        throw new IFPError(400,"No secondary product engagement user found")
      }
      const secondaryPEData = secondaryPE[0]
      if (id != secondaryPEData.ID){
        throw new IFPError(400,"Invalid secondary product engagement user id")
      }

      await updatePrimaryPE(id)
      await updateSecondaryPE(peUser.ID)
      return {message:"Primary product engagement updated"}
    }
    catch(error){
      throw error
    }
  }

  async createInvitations(req) {
    try {
      const { entityId, superusers } = req.body;

      const newUserRecepients = [];
      // check if existing user
      for (const user of superusers) {
        const encryptedEmail = encryptEmail(user.email);
        const userData = await getUserDataV2({ EMAIL: encryptedEmail, STATUS: "REGISTERED" });
        if (!userData.length > 0) {
          newUserRecepients.push(user)
          continue;
        }
        if (userData[0].EXISTING_USER.includes("EXISTING")) {
          const mobileSyncStatus = user.onBoardMobile
            ? 'PENDING'
            : 'NA_ONLY_WELCOME_EMAIL';
          await updateUserData("EMAIL", encryptedEmail, {
            ROLE:
              user.role.toLowerCase() == "primary superuser"
                ? "PRIMARY_SUPERUSER"
                : "SECONDARY_SUPERUSER",
            PHONE_NUMBER: encryptPhone(user.phone),
            EXISTING_USER: "EXISTING_LINK_INITIATED",
            DESIGNATION: user.designation,
            MOBILE_SYNC_STATUS: mobileSyncStatus
          });
          const emailData = {
            userType: 'PRODUCT_ENGAGEMENT',
            subject: `Bayaan Access | ${getRoleName("SUPERUSER")} UAE Pass Linking`,
            emailType: 'EXISTING_LINK_INIT_REQUEST',
            recepientEmail: user.email,
            recepientName: userData[0].NAME,
            loginLink: `${process.env.PLATFORM_BASEPATH}/login`,
            userRole: "Superuser"
          }
          await sendEmail(emailData);
        } else {
          newUserRecepients.push(user);
        }
      }

      const entityData = await getEntityDetails(entityId);
      const accessPolicy = await getDisseminationAccessPolicy(entityId);
      if (accessPolicy.length < 1) {
        throw new IFPError(404, "No access policies defined for this entity. Update the policy to continue")
      }
      const entityName = entityData.NAME;
      const entityID = entityData.ID;
      const invitations = [];

      for (const user of newUserRecepients) {
        const requestId = uuidv4();
        const userEmail = user.email
        const encryptedEmail = encryptEmail(userEmail)
        const phone = user.phone
        const encrptedPhone = encryptPhone(phone);
        const designation = user.designation;
        const emailMasked = maskEmail(userEmail);
        const maskedPhone = maskPhone(phone);
        const platformAccess = user.onBoardMobile ? 'all' : 'web';
        const token = generateInvitationToken(
          requestId,
          emailMasked,
          maskedPhone,
          user.role,
          entityName,
          entityID,
          accessPolicy,
          platformAccess,
          designation,
        );

        await createInvitationData(requestId, encrptedPhone, encryptedEmail, emailMasked, entityID, user.role, designation, platformAccess);

        const invitationLink = `${process.env.PLATFORM_BASEPATH}/pre-register-auth?token=${token}`
        invitations.push({
          id: requestId,
          email: userEmail,
          invitation_link: invitationLink
        });

        const emailData = {
          userType: 'SUPERUSER',
          subject: `Bayaan Access | ${getRoleName("SUPERUSER")} Registration`,
          emailType: 'ENTITY_INVITE',
          recepientEmail: userEmail,
          recepientName: "User",
          registrationLink: invitationLink
        }

        await sendEmail(emailData);

      }

      return {
        message: "Invitations created and emails sent",
      };
    } catch (error) {
      log.error(`Error creating invitations for users`, error);
      throw error;
    }
  }

  async listInvitations(req) {
    try {
      const { entityId } = req.query;
      const pendingInvitations = await listPendingInvitations(entityId, ["Primary Superuser", "Secondary Superuser"]);
      const formattedData = pendingInvitations.map((data) => {
        return {
          ...data,
          entityDomain: data.entityDomain.split(','),
          inviteePhone: decryptPhone(data.inviteePhone),
          inviteeEmail: decryptEmail(data.inviteeEmail),
        }
      })
      return formattedData;
    } catch (error) {
      log.error(`Error listing pending invitations: ${error}`);
      throw error;
    }
  }

  async activateUser(req) {
    const { userId } = req.body
    try {
      await setUserActivationStatus(userId, 'ACTIVE')
      return { "message": "Status has been updated" }
    } catch (error) {
      log.error(`Error activating access for user:${userId}`, error);
      throw error;
    }
  }

  async deActivateUser(req) {
    const { userId } = req.body
    try {
      await setUserActivationStatus(userId, 'INACTIVE')
      return { "message": "Status has been updated" }
    } catch (error) {
      lof.error(`Error activating access for user:${userId}`, error);
      throw error
    }
  }

  async revokeSuperUser(req) {
    const { userId } = req.body
    try {
      await setUserActivationStatus(userId, 'REVOKED')
      return { "message": "Status has been updated" }
    } catch (error) {
      log.error(`Error revoking access for user:${userId}`, error);
      throw error;
    }
  }

  async exportUsers(req) {
    try {
      const { entityId, userType } = req.query;

      let entityName = "";
      if (entityId) {
        const entityDetails = await getEntityDetails(entityId);
        entityName = entityDetails.NAME;
      }

      let users = await getEntityUsersForExport(entityId, userType);
      users.forEach((user) => {
        user.Email = decryptEmail(user.Email);
        user.Role = getRoleName(user.Role);
        if (user.Mobile != "NA") {
          user.Mobile = decryptPhone(user.Mobile);
        }
      });

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(users);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });
      return { buffer, entityName };
    } catch (error) {
      log.error(`Something went wrong during users export: ${error}`);
      throw error;
    }
  }
}

class SuperuserController {
  constructor() {
    this.secretKey = process.env.APP_SECRET_KEY;
  }

  async getUserAccessRequests(req) {
    try {
      const type = req.params.type
      const page = req.query.page ? req.query.page : 1
      const limit = req.query.limit ? req.query.limit : 10
      const search = req.query.search
      const sortBy = req.query.sortBy
      const order = req.query.order
      const userType = req.query.userType;

      // // Check if the user is a superuser
      // const superUserData = await getActiveSuperuser(req.user.preferred_username)
      // if (superUserData.length === 0) {
      //   throw new IFPError(403, 'The requestor is not a superuser');
      // }
      // const superuserEmail = req.user.preferred_username
      const superUser = req.superuser 
      const entity = superUser.ENTITY_ID
      let data = [];
      let usersList;

      if (type == "new") {
        usersList = await getSUNewAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "pending") {
        usersList = await getSUPendingAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "existing") {
        usersList = await getSUExistingAccess(page, limit, search, entity, sortBy, order, superUser.ROLE, userType);
      }
      else if (type == "intra-id-pending") {
        usersList = await getIntraIDPendingAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "approved") {
        usersList = await getApprovedAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "intra-id-pending") {
        usersList = await getIntraIDPendingAccess(page, limit, search, entity, sortBy, order);
      }

      data = await Promise.all(usersList.map(async user => {
        user.isLinked = user.isLinked == "EXISTING_UNLINKED" ? false : true;
        user.email = decryptEmail(user.email);
        try {
          const userAccessRecords = await getUserAccessV2(
            user.id,
            type,
            superUser.ROLE,
            superUser.ROLE
          );
          if (!userAccessRecords.length) {
            return null;
          }
          const {
            formattedAccess,
            maxClassification,
            approvalLevel,
            revertInfo,
          } = formatAccess(userAccessRecords);
          user.access = formattedAccess;
          user.maxClassification = maxClassification;
          user.approvalLevel = approvalLevel;
          Object.assign(user, revertInfo);
          return user;
        } catch (e) {
          log.error(
            `Something went wrong when getting user access with user id = ${user.id}: ${e} `
          );
          return null;
        }
      }));

      data = data.filter((user) => user !== null);

      //Add sensitive access ids to the existing records
      if(type == 'existing' &&  data.length > 0 ){
        const existingUsers = data.map(d => d.id)
        const sensitiveRequests = await getSensitiveAccessData(existingUsers);
        const sensitiveRequestsMap = sensitiveRequests.reduce((acc, request) => {
            if (!acc[request.USER_ID]) acc[request.USER_ID] = [];
            acc[request.USER_ID].push(request);
            return acc;
        }, {});

        data.forEach(userData => {
          const userSensitiveRequests = sensitiveRequestsMap[userData.id];
          if (userSensitiveRequests) {
              userSensitiveRequests.forEach(sensitiveRequest => {
                  userData.access.forEach(accessEntry => {
                      accessEntry.levels.forEach(level => {
                          if (
                              accessEntry.name.toUpperCase() === sensitiveRequest.DOMAIN.toUpperCase() &&
                              level.classification.toUpperCase() === 'SENSITIVE'
                          ) {
                              level.accessId = sensitiveRequest.ACCESS_ID;
                              level.isPending = true;
                          }
                      });
                  });
              });
          }
        });
      }

      let result = {}
      if(data.length > 0) {
        result = {
          total: data[0].totalCount,
          data: data          
        }
      }
      return result;

    } catch (error) {
      throw error
    }
  }

  async makePrimary(req){
    try{
      const {id} = req.body
      const superuser = req.superuser
      const secondarySuperuser = await getSecondarySU(superuser.ENTITY_ID)
      if (secondarySuperuser.length == 0){
        throw new IFPError(400,"No secondary superuser found")
      }
      const secondarySuperuserData = secondarySuperuser[0]
      if (id != secondarySuperuserData.ID){
        throw new IFPError(400,"Invalid secondary superuser id")
      }

      await updatePrimarySU(id)
      await updateSecondarySU(superuser.ID)

      // Email notifications
      const dgUser = await getUserDataV2({
        ROLE: "DG",
        ENTITY_ID: superuser.ENTITY_ID,
      });
      const peUser = await getUserDataV2({ ROLE: "PRIMARY_PE_USER" });
      const entityDetails = await getEntityDetails(superuser.ENTITY_ID);
      const notifySecondarySuperuser = {
        userType: "SUPERUSER",
        emailType: "SECONDARY_SUPERUSER_ROLE_CHANGE",
        subject: "Activation of Secondary Super user as Primary Super User",
        recepientEmail: decryptEmail(secondarySuperuserData.EMAIL),
        recepientName: secondarySuperuserData.NAME,
        entityName: entityDetails.NAME,
      };
      const notifyDg = {
        userType: "DG",
        emailType: "SECONDARY_SUPERUSER_ROLE_CHANGE",
        subject: "Activation of Secondary Super user as Primary Super User",
        recepientEmail: decryptEmail(dgUser[0].EMAIL),
        recepientName: dgUser[0].NAME,
        secondaryUserEmail: decryptEmail(secondarySuperuserData.EMAIL),
        entityName: entityDetails.NAME,
      };
      const notifyPe = {
        userType: "PRODUCT_ENGAGEMENT",
        emailType: "SECONDARY_SUPERUSER_ROLE_CHANGE",
        subject: "Activation of Secondary Super user as Primary Super User",
        recepientEmail: decryptEmail(peUser[0].EMAIL),
        recepientName: peUser[0].NAME,
        secondaryUserEmail: decryptEmail(secondarySuperuserData.EMAIL),
        entityName: entityDetails.NAME,
      };
      await Promise.all([
        sendEmail(notifySecondarySuperuser),
        sendEmail(notifyDg),
        sendEmail(notifyPe),
      ]);
      return {message:"Primary superuser updated"}
    }
    catch(error){
      throw error
    }
  }

  async deleteUser(req){
    try {
      const userId = req.params.id;

      // Validate users exists
      const userData = await getUserDataV2({ ID: userId });
      if (!userData) {
        console.error("Record for 'USER' not found");
        throw new IFPError("User not found");
      }

      // Validate user exist in EntraID
      const encryptedEmail = userData[0]["EMAIL"];
      const email = decryptEmail(encryptedEmail);
      const entraIdUser = await getUserInfo(email);
      if (!entraIdUser) {
        console.error("User not part of Bayaan Entra ID");
      }

      // Fetch currently assigned access levels, access levels
      // set to REVOKE will be handled by group-assign cron
      let currentAccessLevels = await getUserAccessLevels({
        USER_ID: userId,
        ACCESS_OPERATION: "GRANT",
      });
      if (currentAccessLevels.length <= 0) {
        console.log("No groups apporved for user.");
      }
      currentAccessLevels = currentAccessLevels.map((access) => ({
        domain: access.DOMAIN,
        classification: access.ACCESS_LEVEL,
      }));

      // create new access request for group revokation via group-assign cron
      const requestId = uuidv4();
      for (const userAccess of currentAccessLevels) {
        const domain = userAccess.domain;
        const classification = userAccess.classification;

        await deleteUserAccess(userId, {
          DOMAIN: domain,
          ACCESS_LEVEL: classification.toUpperCase(),
        });
        const userAccessId = uuidv4();
        await createUserAccess(
          requestId,
          userAccessId,
          userId,
          domain,
          classification,
          "REVOKE"
        );
        await createUserAccessApproval(uuidv4(), userAccessId, "REVOKED", null);
      }
      await createUserAccessRequest(requestId, userId);
      await updateUserAccessRequestStatus(requestId, "COMPLETED");

      // Unassign platform access group
      let platformAccessGroup;
      if (process.env.NODE_ENV === "prod") {
        if (email.includes("@scad.gov.ae")) {
          platformAccessGroup = "ad85f6c4-04af-48ad-aed6-c63e7f9f768d"; //Prod Internal
        } else {
          platformAccessGroup = "a0d83e83-22c0-4406-b0b8-6eb7246aa436"; //Prod External
        }
      } else {
        platformAccessGroup = "93732bd5-d769-4b9a-bb0e-2e414a50b463"; //Staging Internal
      }
      await unassignUserFromGroup(entraIdUser.id, platformAccessGroup);

      try {
        const dgUser = await getUserDataV2({
          ROLE: "DG",
          ENTITY_ID: req.superuser.ENTITY_ID,
        });
        const peUser = await getUserDataV2({ ROLE: "PRIMARY_PE_USER" });
        const entityDetails = await getEntityDetails(req.superuser.ENTITY_ID);
        const userDeleteNotificationToDG = {
          userType: "DG",
          emailType: "USER_DELETE_NOTIFICATION",
          subject: "Access to Bayaan Platform",
          recepientEmail: decryptEmail(dgUser[0].EMAIL),
          recepientName: dgUser[0].NAME,
          userName: userData[0].NAME,
          actor: { en: 'your entity', ar: 'جهتكم'}
        };
        const userDeleteNoitifcationToPE = {
          userType: "PRODUCT_ENGAGEMENT",
          emailType: "USER_DELETE_NOTIFICATION",
          subject: "Access to Bayaan Platform",
          recepientEmail: decryptEmail(peUser[0].EMAIL),
          recepientName: peUser[0].NAME,
          userEmail: decryptEmail(userData[0].EMAIL),
          actor: { en: entityDetails.NAME, ar: entityDetails.NAME}
        };
        const userDeleteNoitifcationToUser = {
          userType: "USER",
          emailType: "USER_DELETE_NOTIFICATION",
          subject: "Access to Bayaan Platform",
          recepientEmail: decryptEmail(userData[0].EMAIL),
          recepientName: userData[0].NAME,
        };
        await Promise.all([
          sendEmail(userDeleteNotificationToDG),
          sendEmail(userDeleteNoitifcationToPE),
          sendEmail(userDeleteNoitifcationToUser),
        ]);
      } catch (error) {
        log.error(`Error sending email notifications: ${error}`);
      }
      // Set user is deleted data in the database
      await setUserDeleteStatus(userId, req.superuser.ID);
      await deleteInvitationByEmail(encryptedEmail);
      await deleteUserEIDMapping(encryptedEmail);

      // delete from mobile
      await deleteMobileUser(userId);
      return { message: "User deleted" };
    } catch (error) {
      throw error;
    }
  }

  async setDG(req) {
    try {
      const { name, email } = req.body;

      // Check if the inviter is a superuser
      const superUserData = await getActiveSuperuser(req.user.preferred_username)
      if (superUserData.length === 0) {
        throw new IFPError(403, 'The requestor is not a superuser');
      }
      const superUser = superUserData[0];
      const superUserEntity = superUser.ENTITY_ID

      const invitations = [];
      const superuserEmail = req.user.preferred_username
      const entityDomain = superuserEmail.split('@')[1]

      const dgEmail = email
      const dgName = toTitleCase(name)
      const role = 'DG'

      const dgDomain = dgEmail.split('@')[1]
      if (dgDomain != entityDomain)
        throw new IFPError(403, "You can only add users from your organization", { "reason": `Tried adding the user '${dgEmail}'` })

      await createUserData(uuidv4(), dgName, dgEmail, null, null, role, superUserEntity, "Director General")
      const emailData = {
        recepientEmail: dgEmail,
        subject: "Bayaan Access | Entity DG Onboarding",
        userType: "DG",
        emailType: "REGISTER_COMPLETE",
        recepientName: dgName
      }
      await sendEmail(emailData);

      return {
        message: 'DG user has been created and email sent',
        invitations
      };
    } catch (error) {
      throw error
    }
  }

  async inviteUser(req) {
    try {
      const { type, recepients } = req.body;

      const newUserRecepients = [];
      // check if existing user
      for (const user of recepients) {
        const encryptedEmail = encryptEmail(user.email);
        const userData = await getUserDataV2({ EMAIL: encryptedEmail, STATUS: "REGISTERED" });
        if (!userData.length > 0) {
          newUserRecepients.push(user)
          continue;
        }
        if (userData[0].EXISTING_USER.includes("EXISTING")) {
          let userRole;
          if (type.toLowerCase() == "dg") {
            userRole = user.designation;
            const hashedEid = hashEmiratesId(user.eid);
            await createEIDInvitationMapping(
              uuidv4(),
              hashedEid,
              encryptedEmail
            );
            const mobileSyncStatus = user.onBoardMobile
              ? 'PENDING'
              : 'NA_ONLY_WELCOME_EMAIL';
            await updateUserData("EMAIL", encryptedEmail, {
              ROLE: "DG",
              DESIGNATION: user.designation,
              EXISTING_USER: "EXISTING_LINK_INITIATED",
              MOBILE_SYNC_STATUS: mobileSyncStatus
            });
            const emailData = {
              userType: 'SUPERUSER',
              subject: `Bayaan Access | ${userRole} UAE Pass Linking`,
              emailType: 'EXISTING_LINK_INIT_REQUEST',
              recepientEmail: user.email,
              recepientName: userData[0].NAME,
              loginLink: `${process.env.PLATFORM_BASEPATH}/login`,
              userRole: userRole
            }
            await sendEmail(emailData);
          }
        } else {
          newUserRecepients.push(user);
        }
      }

      const superUser = req.superuser
      const superUserEntity = superUser.ENTITY_ID
      const entityData = await getEntityDetails(superUserEntity);
      const accessPolicy = await getDisseminationAccessPolicy(superUserEntity);
      if (accessPolicy.length < 1) {
        throw new IFPError(404, "No access policies defined for this entity. Update the policy to continue")
      }

      const invitations = [];
      const superuserEmail = req.user.preferred_username
      const entityName = entityData.NAME
      const entityDomains = entityData.DOMAIN.split(",");

      for (const recepient of newUserRecepients) {
        const userEmail = recepient.email
        // const phone = recepient.phone
        const eid = recepient.eid
        const designation = recepient.designation
        const userDomain = userEmail.split('@')[1]
        if (!entityDomains.includes(userDomain)) {
          throw new IFPError(403, "You can only add users from your organization", { "reason": `Tried adding the user '${userEmail}'` })
        }

        const requestId = uuidv4();
        const entityID = superUser.ENTITY_ID;
        const emailMasked = maskEmail(userEmail);
        // const phoneMasked = maskPhone(phone);
        const role = type.toUpperCase()

        if (role == 'DG') {
          recepient.access = accessPolicy.map(policy => {
            return { "domain": policy.DOMAIN, "classification": policy.CLASSIFICATION }
          })
        }

        const normalizeObject = (obj) => {
          return {
            domain: obj.domain || obj.DOMAIN,
            classification: obj.classification || obj.CLASSIFICATION
          };
        };

        const existsInDisseminationPolicy = (obj, secondArray) => {
          const normalizedObj = normalizeObject(obj);
          return secondArray.some((item) => {
            const normalizedItem = normalizeObject(item);
            return (
              normalizedObj.domain.toUpperCase() === normalizedItem.domain.toUpperCase() &&
              normalizedObj.classification.toUpperCase() === normalizedItem.classification.toUpperCase()
            );
          });
        };

        const validateAccess = (firstArray, secondArray) => {
          for (const item of firstArray) {
            if (
              item.classification.toLowerCase() !== "open" &&
              item.classification.toLowerCase() !== "confidential"
            ) {
              if (!existsInDisseminationPolicy(item, secondArray)) {
                throw new IFPError(
                  400,
                  `Access not found in ${entityName}'s Access Policy: ${JSON.stringify(
                    item
                  )}`
                );
              }
            }
          }
        };

        validateAccess(recepient.access, accessPolicy);

        const invitationAccessPolicy = role == "DG" ? null : JSON.stringify(recepient.access)
        const encryptedEID = hashEmiratesId(eid)
        const encryptedEmail = encryptEmail(userEmail)
        const platformAccess = recepient.onBoardMobile ? "all" : "web";
        const superUserId = superUser.ID
        await createInvitationData(requestId, null, encryptedEmail, emailMasked, entityID, role, designation, platformAccess, invitationAccessPolicy);
        await createEIDInvitationMapping(requestId, encryptedEID,encryptedEmail)
        const token = generateInvitationToken(requestId, emailMasked, "", role, entityName, entityID, recepient.access, platformAccess, designation, recepient.justification, superUserId);
        const invitationLink = `${process.env.PLATFORM_BASEPATH}/pre-register-auth?token=${token}`;

        // Send invitation email to the user
        const emailData = {
          userType: role,
          subject: `Bayaan Access | ${role == "DG" ? designation : getRoleName(role)} Registration`,
          emailType: 'PLATFORM_INVITE',
          recepientEmail: userEmail,
          recepientName: userEmail.split('@')[0],
          registrationLink: invitationLink
        }
        await sendEmail(emailData);
        invitations.push({
          userEmail,
          invitationLink
        });
      }

      return {
        message: 'Invitations created and email sent'
      };
    } catch (error) {
      throw error
    }
  }

  async listInvitations(req) {
    try {
      const { entityId } = req.query;
      const pendingInvitations = await listPendingInvitations(entityId, ["USER", "DG"]);
      const formattedData = pendingInvitations.map((data) => {
        return {
          ...data,
          entityDomain: data.entityDomain.split(','),
          inviteeEmail: decryptEmail(data.inviteeEmail),
        }
      })
      return formattedData;
    } catch (error) {
      log.error(`Error listing pending invitations: ${error}`);
      throw error;
    }
  }

  async resendInvitation(req) {
    const { invitationId } = req.params;
    const senderEmail = req.user.preferred_username

    let senderData = await getUserDataV3({
      EMAIL: encryptEmail(senderEmail)
    })
    
    senderData = senderData[0]
    if (!(['PRIMARY_PE_USER','PRIMARY_SUPERUSER','SECONDARY_SUPERUSER'].includes(senderData.ROLE))) {
      throw new IFPError(403, "You are not authorized to perform this action")
    }

    // Get invitation data
    const invitationData = await getInvitationData(invitationId);
    let {
      EMAIL_MASKED: emailMasked,
      INVITE_TYPE: role,
      ENTITY_ID: entityID,
      INVITEE_DESIGNATION: designation,
      NAME: entityName,
      REQUESTED_ACCESS: requestedAccess,
      PHONE: encryptedPhone,
      PLATFORM_ACCESS: platformAccess
    } = invitationData;

    // prepare accesspolicy incase of dg or superuser invites
    if (
      ["dg", "primary superuser", "secondary superuser"].includes(
        role.toLowerCase()
      )
    ) {
      const accessPolicy = await getDisseminationAccessPolicy(entityID);
      if (accessPolicy.length < 1) {
        throw new IFPError(
          404,
          "No access policies defined for this entity. Update the policy to continue"
        );
      }
      requestedAccess = accessPolicy.map((policy) => {
        return { domain: policy.DOMAIN, classification: policy.CLASSIFICATION };
      });
    }

    // Parse stored requested Access
    if (!Array.isArray(requestedAccess)) {
      requestedAccess = JSON.parse(requestedAccess)
    }
    // Recreate token
    const token = generateInvitationToken(
      invitationId,
      emailMasked,
      encryptedPhone || "",
      role,
      entityName,
      entityID,
      requestedAccess,
      platformAccess,
      designation,
      '',
      senderData.ID
    );
    const userEmail = decryptEmail(invitationData.USER_EMAIL);
    const invitationLink = `${process.env.PLATFORM_BASEPATH}/pre-register-auth?token=${token}`;

    // Normalize roles names & emailType for email notification
    let emailType = "PLATFORM_INVITE";
    if (["primary superuser", "secondary superuser"].includes(role.toLowerCase())) {
      role = "Superuser";
      emailType = "ENTITY_INVITE"
    }
    const emailData = {
      userType: role.toUpperCase(),
      subject: `Bayaan Access | ${getRoleName(role)} Registration`,
      emailType: emailType,
      recepientEmail: userEmail,
      recepientName: userEmail.split("@")[0],
      registrationLink: invitationLink,
    };
    await sendEmail(emailData);

    return {message: message.SUCCESS.INVITE_RESENT};
  }

  async verifyToken(req) {
    try {
      const { token } = req.query;
      const { valid, decoded, error } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      return {
        message: "Token verified",
        user: {
          email: decoded.userId,
          role: decoded.role,
          entity: decoded.entity
        },
      };
    } catch (error) {
      throw error
    }
  }

  async requestOTP(req) {
    try {
      const token = req.headers['x-token'];
      if (!token)
        throw new IFPError(400, "Token not found in the headers")

      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      const invitation = await getInvitationData(decoded.requestId)
      const decryptedEmail = decryptEmail(invitation.USER_EMAIL)
      const otp = generateOTP();
      const emailData = {
        userType: "SUPERUSER",
        recepientEmail: decryptedEmail,
        subject: `Bayaan Access | OTP to your Bayaan account`,
        emailType: 'REGISTER_OTP',
        otp: otp
      }
      await sendOTP(emailData, otp);

      return {
        message: 'OTP sent to email',
      };
    } catch (error) {
      throw error
    }
  }

  async verifySUOTP(req) {
    try {
      const token = req.headers['x-token'];
      const { otp } = req.body;
      if (!token)
        throw new IFPError(400, "Token not found in the headers")

      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      const invitation = await getInvitationData(decoded.requestId)
      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)
      const validOtp = await verifyOTP(decryptedEmail, otp, decoded.requestId)
      if (!validOtp) {
        throw new IFPError(400, "Invalid OTP");
      }

      return {
        message: 'OTP is valid',
        status: true
      };
    } catch (error) {
      throw error
    }
  }

  async register(req) {
    try {
      const token = req.headers['x-token']
      const { signed_nda } = req.body;
      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, "Invalid or expired token");
      }
      const invitation = await getInvitationData(decoded.requestId)
      const decryptedPhone = decryptPhone(invitation.PHONE)
      if (decryptedPhone != req.user.mobile) {
        throw new IFPError(400, "Your phone number does not match with this invitation record");
      }
      if (!signed_nda) {
        throw new IFPError(400, "Please sign the NDA to proceed");
      }
      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)
      // Verify OTP (dummy verification here, implement actual verification logic)
      const verifiedKey = `verified_OTP_${process.env.NODE_ENV}_${decryptedEmail}`;
      const otpVerify = await getGlobalRedis(verifiedKey);
      if (!otpVerify) {
        throw new IFPError(400, "Please verify OTP");
      }
      //Deletes verification flag after successful verification
      await deleteVerifyFlag(decryptedEmail)

      const userName = cleanEIDName(req.user.fullnameEN)
      // const eid = req.user.idn;
      // const maskedEid = hashEmiratesId(eid)
      const userUUID = req.user.uuid;
      await resetUserData(userUUID);
      const mobileSyncStatus = decoded.platformAccess == "all" ? "PENDING" : "NA_ONLY_WELCOME_EMAIL";
      await createUserData(userUUID, userName, userEmail, invitation.PHONE, decoded.role, decoded.entityId, mobileSyncStatus, decoded.designation)
      // await createEIDUser(userUUID, maskedEid, userEmail)

      // await sendADInvite(decoded.email);
      const emailData = {
        recepientEmail: decryptedEmail,
        subject: `Bayaan Access | ${getRoleName("SUPERUSER")} Registration`,
        userType: "SUPERUSER",
        emailType: "REGISTER_COMPLETE",
        recepientName: userName
      }
      await sendEmail(emailData);
      await updateInvitationStatus(decoded.requestId, 'INVALID')
      return {
        message: "Registration completed and Active Directory invite sent",
      };
    } catch (error) {
      throw error
    }

  }

  async editAccess(req) {
    try {
      const superUserData = req.superuser;
      const userUUID = req.body.userId;
      const entityId = superUserData.ENTITY_ID
      const accessPolicy = await getDisseminationAccessPolicy(entityId)
      const accessInfo = req.body.accessInfo
      //User validation
      let userData;
      try{
        userData = await getUserDataV3(
          {ID: userUUID,ENTITY_ID:entityId}
        )
        userData = userData[0]
      }
      catch{
        throw new IFPError(400, "User not found")
      }

      //validate access levels
      let grantAccessLevels = accessInfo['grant']
      const removeAccessLevels = accessInfo['remove']

      log.info("Access policies fetched for entity:", entityId)

      // Define the order of classification levels
      const classificationOrder = ["open", "confidential", "sensitive", "secret"];

      // Function to get classification rank
      const getClassificationRank = (classification) => {
        return classificationOrder.indexOf(classification.trim().toLowerCase());
      };

      // Convert accessPolicies to a map for quick lookup
      const policyMap = {};
      accessPolicy.forEach(policy => {
        const domain = policy.DOMAIN.toLowerCase();
        const classification = policy.CLASSIFICATION.trim().toLowerCase();
        if (!policyMap[domain]) {
          policyMap[domain] = classification;
        } else {
          if (getClassificationRank(classification) > getClassificationRank(policyMap[domain])) {
            policyMap[domain] = classification;
          }
        }
      });

      // Group access levels by domain and find the highest level requested for each domain
      const groupAccessLevelsByDomain = (accessLevels) => {
        const groupedAccessClassifications = {};

        accessLevels.forEach(access => {
          const domain = access.domain.toLowerCase();
          const requestedClassification = access.classification.toLowerCase();

          if (!groupedAccessClassifications[domain]) {
            groupedAccessClassifications[domain] = requestedClassification;
          } else {
            const currentMaxClassification = groupedAccessClassifications[domain];
            if (getClassificationRank(requestedClassification) > getClassificationRank(currentMaxClassification)) {
              groupedAccessClassifications[domain] = requestedClassification;
            }
          }
        });

        return groupedAccessClassifications;
      };

      // Validate access levels
      const validateAccessLevels = (groupedAccessLevels, policyMap) => {
        const invalidAccesses = [];

        for (const domain in groupedAccessLevels) {
          const requestedLevel = groupedAccessLevels[domain];

          if (policyMap[domain] === undefined) {
            invalidAccesses.push({ domain, reason: "Domain not found in entity policies" });
          } else {
            const maxAllowedLevel = policyMap[domain];
            if (getClassificationRank(requestedLevel) > getClassificationRank(maxAllowedLevel)) {
              invalidAccesses.push({ domain, reason: `Requested level ${requestedLevel} exceeds maximum allowed level ${maxAllowedLevel}` });
            }
          }
        }

        return invalidAccesses;
      };

      const accessLevels = accessPolicy
      let userAccessData = await getUserAccessV2(userUUID, "existing", "", "");
      userAccessData = filterApprovedUserAccess(userAccessData);

      const groupedAccessLevels = groupAccessLevelsByDomain(grantAccessLevels);
      const result = validateAccessLevels(groupedAccessLevels, policyMap);
      if (result.length) {
        log.info("Invalid access levels found");
        throw new IFPError(403, "Invalid access levels found", { "errors": result });
      }
      log.info("All requested access levels are valid.");

      const classificationLevels = ["open", "confidential", "sensitive", "secret"];
      const classificationRank = {
          open: 1,
          confidential: 2,
          sensitive: 3,
          secret: 4,
      };


      //Process grantAccessLevels
      let lowerGrants = []
      grantAccessLevels = grantAccessLevels.map(access => ({
        domain: access.domain.toUpperCase(),
        classification: access.classification.toUpperCase(),
      }));
    
      grantAccessLevels.forEach(access => {
          const currentDomain = access.domain;
          const currentClassificationRank = classificationRank[access.classification.toLowerCase()];
      
          // Generate lower classifications for the current classification rank
          const lowerClassifications = classificationLevels.slice(0, currentClassificationRank - 1);
      
          // existing levels for domain
          const existingLevels = userAccessData
            .filter(u => u.domain.toUpperCase() == currentDomain && u.accessOperation == 'GRANT')
            .map(u => u.classification.toLowerCase());
          const filteredLClassifications = lowerClassifications
            .filter(l => !existingLevels.includes(l))

          filteredLClassifications.forEach(lowerClassification => {
            lowerGrants.push({
              domain: currentDomain,
              classification: lowerClassification.toUpperCase(),
            });
          })
      });
    
      // Merge lowerGrants into grantAccessLevels
      grantAccessLevels.push(...lowerGrants);

      const secLevelAccessGroupA = grantAccessLevels
        .filter(access=>access.classification.toUpperCase() == "CONFIDENTIAL" || access.classification.toUpperCase() == "OPEN")
      if (secLevelAccessGroupA.length > 0){
        const groupARequestId = uuidv4();
        await createUserAccessRequest(groupARequestId, userUUID)
        log.info("Group A User access request created")
        for (const access of secLevelAccessGroupA) {
          let { domain, classification } = access;
          const userAccessId = uuidv4();
          await createUserAccess(groupARequestId, userAccessId, userUUID, domain, classification)
          log.info(`User access ${domain} - ${classification} created`)
          await createUserAccessApproval(uuidv4(), userAccessId, 'APPROVED', null)
          log.info(`User access ${domain} - ${classification} pre-approved`)        
        }
        //Update the request status to COMPLETED to be proccessed by Entra ID Group assign cron
        await updateUserAccessRequestStatus(groupARequestId, 'COMPLETED')
      }

      const secLevelAccessGroupB = grantAccessLevels
        .filter(access=>access.classification.toUpperCase() == "SENSITIVE" || access.classification.toUpperCase() == "SECRET")
      if (secLevelAccessGroupB.length > 0) {
        const groupBRequestId = uuidv4();
        await createUserAccessRequest(groupBRequestId, userUUID)
        log.info("Group B User access request created")
        for (const access of secLevelAccessGroupB) {
          let { domain, classification } = access;
          const userAccessId = uuidv4();
          await createUserAccess(groupBRequestId, userAccessId, userUUID, domain, classification)
          await createUserAccessApproval(uuidv4(), userAccessId, 'PENDING', "DG")
          log.info(`User access ${domain} - ${classification} DG approval request created`)
        }
        
        // Mail notifications for secLevelAccessGroupB
        const entityDg = await getUserDataV2({ ROLE: "DG", ENTITY_ID: entityId });
        if (entityDg.length > 0) {
          try {
            const emailData = {
              userType: "DG",
              emailType: "NEW_ACCESS_REQUEST",
              recepientEmail: decryptEmail(entityDg[0].EMAIL),
              subject: "Action: Access to Sensitive Data",
              recepientName: entityDg[0].NAME,
              requestorName: superUserData.NAME
            };
            await sendEmail(emailData);
          } catch (error) {
            log.error(`Something went wrong went sending email: ${error}`);
          }
        }
      }

      if(removeAccessLevels.length<1){
        return {
          message: message.SUCCESS.USER.UPDATE_SUCCESS,
        };
      }

      // Check if any access being removed is still pending
      const allUserPendingAccess = await getRequestIDsforPendingAccess(
        userUUID,
        removeAccessLevels
      );

      // Remove pending access records from said requests.
      for (const access of allUserPendingAccess) {
        const requestAccess = await getUserAccessLevels({
          REQUEST_ID: access.requestId,
        });
        const isOnlyAccessInRequest = !requestAccess.some(
          (a) => a.ACCESS_ID != access.accessId
        );
        await deleteUserAccess(userUUID, {
          REQUEST_ID: access.requestId,
          ACCESS_ID: access.accessId,
        });
        if (isOnlyAccessInRequest) {
          await updateUserAccessRequestStatus(access.requestId, "COMPLETED");
        }
      }

      let removeAccessData = userAccessData.filter(u => removeAccessLevels.includes(u.accessId));
      if (removeAccessData.length < 1) {
        return { message: message.SUCCESS.APPROVAL_CYCLE_UPDATE.PENDING_ACCESS_REMOVED};
      }

      removeAccessData.forEach(removeAccess=>{
        let currentDomain = removeAccess.domain;
        let currentClassification = removeAccess.classification.toLowerCase();
        let currentClassificationRank = classificationRank[currentClassification];
        let classifications = Object.keys(classificationRank);
        let higherClassificationRanks = Object.values(classificationRank).slice(currentClassificationRank);
        for (const rank of higherClassificationRanks){
           let higherLevel = userAccessData.find(userAccess => userAccess.classification == classifications[rank-1].toUpperCase() && userAccess.domain == currentDomain)
           if (higherLevel)
              removeAccessData.push(higherLevel)
        }
        
       })
      removeAccessData = Array.from(new Set(removeAccessData))
      let userAccessApprovalsData = []
      userAccessApprovalsData = await getUserAccessApprovals({
        "USER_ACCESS_ID": removeAccessData.map(u => u.accessId)
      });

      

      const requestId = uuidv4();
      for (const userAccess of removeAccessData) {
        const domain = userAccess.domain;
        const accessLevel = userAccess.classification;

        const userAccessId = uuidv4();
        await createUserAccess(requestId, userAccessId, userUUID, domain, accessLevel.toUpperCase(), "REVOKE");
        await createUserAccessApproval(uuidv4(), userAccessId, "REVOKED", null);
      }
      await createUserAccessRequest(requestId, userUUID)
      await updateUserAccessRequestStatus(requestId, 'COMPLETED');

      // Mail notifications for access 
      try {
        const user = await getUserDataV2({ ID: userUUID });
        const dgUser = await getUserDataV2({
          ROLE: "DG",
          ENTITY_ID: entityId,
        });
        const peUser = await getUserDataV2({ ROLE: "PRIMARY_PE_USER" });
        const userAccessRevokeNotification = {
          userType: "USER",
          emailType: "ACCESS_DOWNGRADE_NOTIFICATION",
          subject: "Access to Data",
          recepientEmail: decryptEmail(user[0].EMAIL),
          recepientName: user[0].NAME,
          userEmail: decryptEmail(user[0].EMAIL)
        };
        const dgAccessRevokeNotification = {
          userType: "DG",
          emailType: "ACCESS_DOWNGRADE_NOTIFICATION",
          subject: "Access to Data",
          recepientEmail: decryptEmail(dgUser[0].EMAIL),
          recepientName: dgUser[0].NAME,
          userEmail: decryptEmail(user[0].EMAIL),
          actorEmail: decryptEmail(superUserData.EMAIL)
        };        
        const peUserAccessRevokeNotification = {
          userType: "PRODUCT_ENGAGEMENT",
          emailType: "ACCESS_DOWNGRADE_NOTIFICATION",
          subject: "Access to Data",
          recepientEmail: decryptEmail(peUser[0].EMAIL),
          recepientName: peUser[0].NAME,
          userEmail: decryptEmail(user[0].EMAIL),
          actorEmail: decryptEmail(superUserData.EMAIL)
        };
        const notificationPromises = [
          sendEmail(userAccessRevokeNotification),
          sendEmail(dgAccessRevokeNotification),
          sendEmail(peUserAccessRevokeNotification)
        ];
        await Promise.all(notificationPromises);
      } catch (error) {
        log.error(`Something went wrong when sending notification: ${error}`);
      }

      return {
        message: message.SUCCESS.USER.UPDATE_SUCCESS,
      };
    } catch (error) {
      throw error
    }

  }

  async approveUserRegistration(req) {
    try {
      const { userId, accessLevels } = req.body;

      // Check if the inviter is a superuser
      const superUserEmail = req.user.preferred_username
      const superUserDomain = superUserEmail.split('@')[1]
      const superUserData = await getActiveSuperuser(superUserEmail)
      if (superUserData.length === 0) {
        throw new IFPError(403, 'The requestor is not a superuser');
      }

      // const userData = await getActiveUserById(userId)
      let userData = await getUserDataV2({
        ID: userId,
        ACTIVATION_FLAG: "PENDING"
      })
      userData = userData[0]
      const userEmail = userData.EMAIL
      const userDomain = userEmail.split('@')[1]

      if (userDomain != superUserDomain)
        throw new IFPError(403, "You can only manager access of users from your organization")

      const accessPolicies = await getDisseminationAccessPolicy(userData.ENTITY_ID)

      // Define the order of classification levels
      const classificationOrder = ["open", "confidential", "sensitive", "secret"];

      // Function to get classification rank
      const getClassificationRank = (classification) => {
        return classificationOrder.indexOf(classification.trim().toLowerCase());
      };

      // Convert accessPolicies to a map for quick lookup
      const policyMap = {};
      accessPolicies.forEach(policy => {
        const domain = policy.DOMAIN.toLowerCase();
        const classification = policy.CLASSIFICATION.trim().toLowerCase();
        if (!policyMap[domain]) {
          policyMap[domain] = classification;
        } else {
          if (getClassificationRank(classification) > getClassificationRank(policyMap[domain])) {
            policyMap[domain] = classification;
          }
        }
      });

      // Group access levels by domain and find the highest level requested for each domain
      const groupAccessLevelsByDomain = (accessLevels) => {
        const groupedAccessLevels = {};

        accessLevels.forEach(access => {
          const domain = access.domain.toLowerCase();
          const requestedLevel = access.level.toLowerCase();

          if (!groupedAccessLevels[domain]) {
            groupedAccessLevels[domain] = requestedLevel;
          } else {
            const currentMaxLevel = groupedAccessLevels[domain];
            if (getClassificationRank(requestedLevel) > getClassificationRank(currentMaxLevel)) {
              groupedAccessLevels[domain] = requestedLevel;
            }
          }
        });

        return groupedAccessLevels;
      };

      // Validate access levels
      const validateAccessLevels = (groupedAccessLevels, policyMap) => {
        const invalidAccesses = [];

        for (const domain in groupedAccessLevels) {
          const requestedLevel = groupedAccessLevels[domain];

          if (policyMap[domain] === undefined) {
            invalidAccesses.push({ domain, reason: "Domain not found in entity policies" });
          } else {
            const maxAllowedLevel = policyMap[domain];
            if (getClassificationRank(requestedLevel) > getClassificationRank(maxAllowedLevel)) {
              invalidAccesses.push({ domain, reason: `Requested level ${requestedLevel} exceeds maximum allowed level ${maxAllowedLevel}` });
            }
          }
        }

        return invalidAccesses;
      };

      const groupedAccessLevels = groupAccessLevelsByDomain(accessLevels);
      const result = validateAccessLevels(groupedAccessLevels, policyMap);

      if (result.length) {
        log.info("Invalid access levels found");
        throw new IFPError(403, "Invalid access levels found", { "errors": result });
      }
      log.info("All access levels are valid.");

      // Update user status to 'APPROVED'
      await removeUserAccess(userId)
      // Insert access levels
      const requestId = uuidv4();
      await createUserAccessRequest(requestId, userId)
      let accessStatusApprovalFlag = true
      for (const access of accessLevels) {
        const { domain, level } = access;
        const userAccessId = uuidv4();
        await createUserAccess(requestId, userAccessId, userId, domain, level)
        if (['Sensitive', 'Secret'].includes(level)) {
          await createUserAccessApproval(uuidv4(), userAccessId, 'PENDING', "DG")
          accessStatusApprovalFlag = accessStatusApprovalFlag && false
        }
        else {
          await createUserAccessApproval(uuidv4(), userAccessId, 'APPROVED', null)
          accessStatusApprovalFlag = accessStatusApprovalFlag && true
        }
      }
      //Checks for all the access level approval.
      if (accessStatusApprovalFlag) {
        await updateUserAccessRequestStatus(requestId, 'APPROVED')
        // await updateUserStatus(userId,'ACCESS_APPROVED')
        await setUserActivationStatus(userEmail, 'ACCESS_APPROVED')
      }
      else {
        //If all the access hasn't been processed, set an intermediate status Superuser Approval
        // await updateUserStatus(userId,'SU_APPROVED')
        await setUserActivationStatus(userEmail, 'SU_APPROVED')
      }
      return { message: 'User access approved' };
    } catch (error) {
      throw error
    }
  }

  async rejectUserRegistration(req, res) {
    try {
      const { superUserId, userId } = req.body;

      // Check if the superuser is valid and active
      const superUserQuery = `
        SELECT ID, ROLE FROM USERS WHERE ID = :superUserId AND STATUS = 'ACTIVE' AND (ROLE = 'PRIMARY_SUPERUSER' OR ROLE = 'SECONDARY_SUPERUSER')
      `;
      const superUserBinds = { superUserId };
      const superUserResult = await db.simpleExecute(superUserQuery, superUserBinds);

      if (superUserResult.rows.length === 0) {
        return res.status(403).json({ message: 'Only active superusers can reject registrations' });
      }

      // Delete the user record from USERS table
      const deleteUserQuery = `
        DELETE FROM USERS WHERE ID = :userId
      `;
      const deleteUserBinds = { userId };
      await db.simpleExecute(deleteUserQuery, deleteUserBinds);

      // Also delete the associated invitations
      const deleteInvitationQuery = `
        DELETE FROM IFP_INVITATIONS WHERE USER_ID = :userId
      `;
      await db.simpleExecute(deleteInvitationQuery, deleteUserBinds);

      res.status(200).json({ message: 'User registration rejected and user record deleted' });
    } catch (error) {
      res.status(500).json({ message: error.message });
    }
  }
  //Helper functions
  verifyInvitationToken(token) {
    try {
      const decoded = jwt.verify(token, this.secretKey);
      return { valid: true, decoded };
    } catch (error) {
      return { valid: false, error };
    }
  }

  async exportUsers(req) {
    try {
      const { entityId, userType } = req.query;

      if (!entityId) {
        throw new IFPError(400, message.ERROR.ENTITY_REQUIRED)
      }

      const entityDetails = await getEntityDetails(entityId);
      const entityName = entityDetails.NAME;

      let users = await getEntityUsersForExport(entityId, userType);
      users.forEach((user) => {
        user.Email = decryptEmail(user.Email);
        user.Role = getRoleName(user.Role);
        if (user.Mobile != "NA") {
          user.Mobile = decryptPhone(user.Mobile);
        }
      });

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(users);
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });
      return { buffer, entityName };
    } catch (error) {
      log.error(`Something went wrong during users export: ${error}`);
      throw error;
    }
  }
}

class DGController {
  constructor() {
    this.secretKey = process.env.APP_SECRET_KEY;
  }

  async getUserAccessRequests(req) {
    try {
      const type = req.params.type
      const page = req.query.page ? req.query.page : 1
      const limit = req.query.limit ? req.query.limit : 10
      const search = req.query.search
      const sortBy = req.query.sortBy
      const order = req.query.order

      const dgUser = req.dg
      const entity = dgUser.ENTITY_ID
      let data = [];
      let usersList;

      if (type == "new") { //Pending with DG
        usersList = await getDGNewAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "pending") { //Pending with Product Engagement
        usersList = await getDGPendingAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "existing") { //Existing users of the entity
        usersList = await getDGExistingAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "approved") {
        usersList = await getApprovedAccess(page, limit, search, entity, sortBy, order);
      }
      else if (type == "intra-id-pending") {
        usersList = await getIntraIDPendingAccess(page, limit, search, entity, sortBy, order);
      }
      else{
        throw new IFPError(400,"Invalid type")
      }

      data = await Promise.all(usersList.map(async user => {
        user.isLinked = user.isLinked == "EXISTING_UNLINKED" ? false : true;
        user.email = maskEmail(decryptEmail(user.email));
        try {
          const userAccessRecords = await getUserAccessV2(
            user.id,
            type,
            dgUser.ROLE,
            dgUser.ROLE
          );
          if (!userAccessRecords.length) {
            return null;
          }
          const { formattedAccess, maxClassification, approvalLevel, revertInfo } =
            formatAccess(userAccessRecords);
          user.access = formattedAccess;
          user.maxClassification = maxClassification;
          user.approvalLevel = approvalLevel;
          Object.assign(user, revertInfo);
          return user;
        } catch (e) {
          log.error(`Something went wrong getting access for user ${user.id}: ${e}`)
          return null;
        }
      }));

      data = data.filter((user) => user !== null);

      let result = {}
      if(data.length > 0) {
        result = {
          total: data[0].totalCount,
          data: data          
        }
      }
      return result;

    } catch (error) {
      throw error
    }
  }

  async requestOTP(req) {
    try {
      const token = req.headers['x-token'];
      if (!token)
        throw new IFPError(400, "Token not found in the headers")

      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      const invitation = await getInvitationData(decoded.requestId)
      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)
      const otp = generateOTP();
      const emailData = {
        userType: "DG",
        recepientEmail: decryptedEmail,
        subject: `Bayaan Access | OTP to your Bayaan account`,
        emailType: 'REGISTER_OTP',
        otp: otp
      }
      await sendOTP(emailData, otp);

      return {
        message: 'OTP sent to email',
      };
    } catch (error) {
      throw error
    }
  }

  async verifyDGOTP(req) {
    try {
      const token = req.headers['x-token'];
      const { otp } = req.body;
      if (!token)
        throw new IFPError(400, "Token not found in the headers")

      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      const invitation = await getInvitationData(decoded.requestId)
      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)
      const validOtp = await verifyOTP(decryptedEmail, otp, decoded.requestId)
      if (!validOtp) {
        throw new IFPError(400, "Invalid OTP");
      }

      return {
        message: 'OTP is valid',
        status: true
      };
    } catch (error) {
      throw error
    }
  }

  async register(req) {
    try {
      const token = req.headers['x-token']
      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, "Invalid or expired token");
      }
      const invitation = await getInvitationData(decoded.requestId)
      log.info("Invitation data fetched with requestId:", decoded.requestId)
      if (invitation.STATUS != 'PENDING') {
        throw new IFPError(400, "The invite is already used once. If you didn't register, report to your superuser")
      }
      let invitationEIDMapping = await getEIDInvitationMapping(decoded.requestId)
      if (invitationEIDMapping.length == 0) {
        throw new IFPError(400, "No EID mapping found for this invitation")
      }
      const hashedInvitationEID = invitationEIDMapping[0].EID
      const hashedEID = hashEmiratesId(req.user.idn)
      if (hashedInvitationEID != hashedEID) {
        throw new IFPError(400, "Your EID does not match with this invitation record");
      }
      log.info(`EID number matched with the invitation record for requestId:`, decoded.requestId)

      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)
      const verifiedKey = `verified_OTP_${process.env.NODE_ENV}_${decryptedEmail}`;
      const otpVerify = await getGlobalRedis(verifiedKey);
      if (!otpVerify) {
        throw new IFPError(400, "Please verify OTP");
      }
      //Deletes verification flag after successful verification
      await deleteVerifyFlag(userEmail)
      log.info("OTP verified successfully for requestId:", decoded.requestId)

      const userName = cleanEIDName(req.user.fullnameEN)
      const userUUID = req.user.uuid;
      await resetUserData(userUUID);
      const mobileSyncStatus = decoded.platformAccess == "all" ? "PENDING" : "NA_ONLY_WELCOME_EMAIL";
      await createUserData(userUUID, userName, userEmail, "NA", decoded.role, decoded.entityId, mobileSyncStatus, decoded.designation)
      log.info("User data created for requestId:", decoded.requestId)
      // await createEIDUser(userUUID, maskedEid, userEmail)
      // log.info("EID user created for requestId:", decoded.requestId)

      // await sendADInvite(decoded.email);
      const emailData = {
        recepientEmail: decryptedEmail,
        subject: `Bayaan Access | ${decoded.designation} Registration`,
        userType: "DG",
        emailType: "REGISTER_COMPLETE",
        recepientName: userName
      }
      await sendEmail(emailData);
      await updateInvitationStatus(decoded.requestId, 'INVALID')
      return {
        message: "Registration completed and Active Directory invite sent",
      };
    } catch (error) {
      throw error
    }

  }

  //Helper functions
  verifyInvitationToken(token) {
    try {
      const decoded = jwt.verify(token, this.secretKey);
      return { valid: true, decoded };
    } catch (error) {
      return { valid: false, error };
    }
  }
}


class UserController {
  constructor() {
    this.secretKey = process.env.APP_SECRET_KEY;
  }

  async verifyToken(req) {
    try {
      const { token } = req.query;
      const { valid, decoded, error } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      return {
        message: "Token verified",
        user: {
          email: decoded.email,
          role: decoded.role,
          entity: decoded.entity
        },
      };
    } catch (error) {
      throw new IFPError
    }
  }

  async requestOTP(req, res) {
    try {
      const token = req.headers['x-token'];
      if (!token)
        throw new IFPError(400, "Token not found in the headers")

      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      const invitation = await getInvitationData(decoded.requestId)
      const entryptedEmail = invitation.USER_EMAIL
      const userEmail = decryptEmail(entryptedEmail)
      const otp = generateOTP();
      const emailData = {
        userType: "USER",
        recepientEmail: userEmail,
        subject: `Bayaan Access | OTP to your Bayaan account`,
        emailType: 'REGISTER_OTP',
        otp: otp
      }
      await sendOTP(emailData, otp);

      return {
        message: 'OTP sent to email',
      };
    } catch (error) {
      throw error
    }
  }

  async verifyUserOTP(req, res) {
    try {
      const token = req.headers['x-token'];
      const { otp } = req.body;
      if (!token)
        throw new IFPError(400, "Token not found in the headers")

      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, 'Invalid or expired token');
      }
      const invitation = await getInvitationData(decoded.requestId)
      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)
      const validOtp = await verifyOTP(decryptedEmail, otp, decoded.requestId)
      if (!validOtp) {
        throw new IFPError(400, "Invalid OTP");
      }

      return {
        message: 'OTP is valid',
        status: true
      };
    } catch (error) {
      throw error
    }
  }

  async register(req, res) {
    try {
      const token = req.headers['x-token']
      const { otp } = req.body;
      const { valid, decoded } = this.verifyInvitationToken(token);
      if (!valid) {
        throw new IFPError(400, "Invalid or expired token");
      }

      if (!['DG', 'USER'].includes(decoded.role))
        throw new IFPError(400, "Invalid request")

      const invitation = await getInvitationData(decoded.requestId)
      log.info("Invitation data fetched with requestId:", decoded.requestId)
      if (invitation.STATUS != 'PENDING') {
        throw new IFPError(400, "The invite is already used once. If you didn't register, report to your superuser")
      }
      let invitationEIDMapping = await getEIDInvitationMapping(decoded.requestId)
      if (invitationEIDMapping.length == 0) {
        throw new IFPError(400, "No EID mapping found for this invitation")
      }
      const hashedInvitationEID = invitationEIDMapping[0].EID
      const hashedEID = hashEmiratesId(req.user.idn)
      if (hashedInvitationEID != hashedEID) {
        throw new IFPError(400, "Your EID does not match with this invitation record");
      }
      log.info(`EID number matched with the invitation record for requestId:`, decoded.requestId)

      const userEmail = invitation.USER_EMAIL
      const decryptedEmail = decryptEmail(userEmail)

      // Verify OTP (dummy verification here, implement actual verification logic)
      const verifiedKey = `verified_OTP_${process.env.NODE_ENV}_${decryptedEmail}`;
      const otpVerify = await getGlobalRedis(verifiedKey);
      if (!otpVerify) {
        throw new IFPError(400, "Please verify OTP");
      }
      //Deletes verification flag after successful verification
      await deleteVerifyFlag(userEmail)

      log.info("OTP verified successfully for requestId:", decoded.requestId)

      const entityId = decoded.entityId
      const accessPolicies = await getDisseminationAccessPolicy(entityId)
      log.info("Access policies fetched for entity:", entityId)

      // Define the order of classification levels
      const classificationOrder = ["open", "confidential", "sensitive", "secret"];

      // Function to get classification rank
      const getClassificationRank = (classification) => {
        return classificationOrder.indexOf(classification.trim().toLowerCase());
      };

      // Convert accessPolicies to a map for quick lookup
      const policyMap = {};
      accessPolicies.forEach(policy => {
        const domain = policy.DOMAIN.toLowerCase();
        const classification = policy.CLASSIFICATION.trim().toLowerCase();
        if (!policyMap[domain]) {
          policyMap[domain] = classification;
        } else {
          if (getClassificationRank(classification) > getClassificationRank(policyMap[domain])) {
            policyMap[domain] = classification;
          }
        }
      });

      // Group access levels by domain and find the highest level requested for each domain
      const groupAccessLevelsByDomain = (accessLevels) => {
        const groupedAccessClassifications = {};

        accessLevels.forEach(access => {
          const domain = access.domain.toLowerCase();
          const requestedClassification = access.classification.toLowerCase();

          if (!groupedAccessClassifications[domain]) {
            groupedAccessClassifications[domain] = requestedClassification;
          } else {
            const currentMaxClassification = groupedAccessClassifications[domain];
            if (getClassificationRank(requestedClassification) > getClassificationRank(currentMaxClassification)) {
              groupedAccessClassifications[domain] = requestedClassification;
            }
          }
        });

        return groupedAccessClassifications;
      };

      // Validate access levels
      const validateAccessLevels = (groupedAccessLevels, policyMap) => {
        const invalidAccesses = [];

        for (const domain in groupedAccessLevels) {
          const requestedLevel = groupedAccessLevels[domain];

          if (policyMap[domain] === undefined) {
            invalidAccesses.push({ domain, reason: "Domain not found in entity policies" });
          } else {
            const maxAllowedLevel = policyMap[domain];
            if (getClassificationRank(requestedLevel) > getClassificationRank(maxAllowedLevel)) {
              invalidAccesses.push({ domain, reason: `Requested level ${requestedLevel} exceeds maximum allowed level ${maxAllowedLevel}` });
            }
          }
        }

        return invalidAccesses;
      };

      const accessLevels = decoded.accessPolicy
      const groupedAccessLevels = groupAccessLevelsByDomain(accessLevels);
      const result = validateAccessLevels(groupedAccessLevels, policyMap);

      if (result.length) {
        log.info("Invalid access levels found");
        throw new IFPError(403, "Invalid access levels found", { "errors": result });
      }
      log.info("All requested access levels are valid.");

      const userName = cleanEIDName(req.user.fullnameEN)
      const eid = req.user.idn;
      const maskedEid = hashEmiratesId(eid)
      const userUUID = req.user.uuid;
      await resetUserData(userUUID);
      const mobileSyncStatus = decoded.platformAccess == "all" ? "PENDING" : "NA_ONLY_WELCOME_EMAIL";
      await createUserData(userUUID, userName, userEmail, "NA", decoded.role, entityId, mobileSyncStatus, decoded.designation, "", decoded.requestJustification)
      log.info("User data created for requestId:", decoded.requestId)
      await updateInvitationStatus(decoded.requestId, 'REGISTERED')
      log.info("Invitation status updated to REGISTERED for requestId:", decoded.requestId)
      // await createEIDUser(userUUID, maskedEid, userEmail)
      // log.info("EID user created for requestId:", decoded.requestId)

      // const groupMatrix = await getBayaanExternalGroupMatrix()
      // const domainMatrixMap = await getDomainMatrixMap()
      // const invDomainMatrixMap = Object.entries(domainMatrixMap).reduce((acc, [key, value]) => {
      //   acc[value] = key;
      //   return acc;
      // }, {});
      // // Default access levels as per dissemination policy
      // const defaultAccess = Object.values(groupMatrix).filter(g=>g.includes("CONFIDENTIAL"))
      // let defaultAccessLevels = defaultAccess.map(access=>{
      //     return {domain:invDomainMatrixMap[access.split("__")[1]],classification:access.split("__")[2]}
      // })

      // Add default conf access for domains with sensitive access request
      const defaultLevels = [];
      for (const requestedAccess of accessLevels) {
        if (requestedAccess.classification.toLowerCase() === "sensitive") {
          defaultLevels.push(
            {
              domain: requestedAccess.domain,
              classification: "Confidential",
            },
            {
              domain: requestedAccess.domain,
              classification: "Open",
            }
          );
        } else if (
          requestedAccess.classification.toLowerCase() === "confidential"
        ) {
          defaultLevels.push({
            domain: requestedAccess.domain,
            classification: "Open",
          });
        }
      }
      let combinedAccess = accessLevels.concat(defaultLevels);
      combinedAccess = combinedAccess.map(accessMap => {
          return {domain:accessMap.domain.toUpperCase(),classification:accessMap.classification.toUpperCase()}
      })
      //Remove duplicates
      combinedAccess = Array.from(
        new Set(combinedAccess.map(JSON.stringify))
      ).map(JSON.parse);

      //Remove existing user access levels
      await removeUserAccess(userUUID)
      //Open and Confidential access levels (group A)
      const secLevelAccessGroupA = combinedAccess
        .filter(access=>access.classification.toUpperCase() == "CONFIDENTIAL" || access.classification.toUpperCase() == "OPEN")
      if (secLevelAccessGroupA.length > 0){
        const groupARequestId = uuidv4();
        await createUserAccessRequest(groupARequestId, userUUID)
        log.info("Group A User access request created for requestId:", decoded.requestId)
        for (const access of secLevelAccessGroupA) {
          let { domain, classification } = access;
          const userAccessId = uuidv4();
          await createUserAccess(groupARequestId, userAccessId, userUUID, domain, classification)
          log.info(`User access ${domain} - ${classification} created for requestId:`, decoded.requestId)
          await createUserAccessApproval(uuidv4(), userAccessId, 'APPROVED', null)
          log.info(`User access ${domain} - ${classification} pre-approved for requestId:`, decoded.requestId)        
        }
        //Update the request status to COMPLETED to be proccessed by Entra ID Group assign cron
        await updateUserAccessRequestStatus(groupARequestId, 'COMPLETED')
      }

      //Sensitive and Secret access levels (group B)
      const secLevelAccessGroupB = combinedAccess
        .filter(access=>access.classification.toUpperCase() == "SENSITIVE" || access.classification.toUpperCase() == "SECRET")
      if (secLevelAccessGroupB.length > 0){
        const groupBRequestId = uuidv4();
        await createUserAccessRequest(groupBRequestId, userUUID)
        log.info("Group B User access request created for requestId:", decoded.requestId)
        for (const access of secLevelAccessGroupB) {
          let { domain, classification } = access;
          const userAccessId = uuidv4();
          await createUserAccess(groupBRequestId, userAccessId, userUUID, domain, classification)
          await createUserAccessApproval(uuidv4(), userAccessId, 'PENDING', "DG")
          log.info(`User access ${domain} - ${classification} DG approval request created for requestId:`, decoded.requestId)
        }
        const entityDg = await getUserDataV2({
          ENTITY_ID: entityId,
          ROLE: "DG",
        });
        if (entityDg.length > 0) {
          try {

            let entitySuperuser;
            try{
              entitySuperuser = await getUserDataV2({
                ROLE: ["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER"],
                ID: decoded.suid,
                ENTITY_ID: entityId
              });
            }
            catch(e){
              entitySuperuser = []
            }

            const emailData = {
              userType: "DG",
              emailType: "NEW_ACCESS_REQUEST",
              recepientEmail: decryptEmail(entityDg[0].EMAIL),
              subject: "Action: Access to Sensitive Data",
              recepientName: entityDg[0].NAME,
              requestorName: entitySuperuser[0].NAME,
              requestJustification: decoded.requestJustification
            };
            await sendEmail(emailData);
          } catch (error) {
            log.error(`Something went wrong went sending email: ${error}`);
          }
        }
      }

      // //Checks for all the access level approval.
      // if (accessStatusApprovalFlag) {
        
      //   // await updateUserStatus(userId,'ACCESS_APPROVED')
      //   // await setUserActivationStatus(userEmail,'ACCESS_APPROVED')
      // }
      // else {
      //   //If all the access hasn't been processed, set an intermediate status Superuser Approval
      //   // await updateUserStatus(userId,'SU_APPROVED')
      //   // await setUserActivationStatus(userEmail,'SU_APPROVED')
      // }

      const emailData = {
        recepientEmail: decryptedEmail,
        subject: `Bayaan Access | ${getRoleName("USER")} Registration`,
        userType: "USER",
        emailType: "REGISTER_COMPLETE",
        recepientName: userName
      }
      await sendEmail(emailData);
      return {
        message: "User registration completed",
      };
    } catch (error) {
      throw error
    }

  }

  async update(req, res) {
    try {
      const { emiratesId, designation } = req.body;
      const { userId } = req.params;
      const userData = await getUserDataV2({ ID: userId, EXISTING_USER: "EXISTING_UNLINKED" });
      if (!userData.length > 0) {
        throw IFPError(404, `'Unlinked Existing User' with ${userId} not found`);
      }

      let updateData = {};

      // Existing users update
      if (emiratesId && designation) {
        const encryptedEID = hashEmiratesId(emiratesId);
        const encryptedEmail = userData[0].EMAIL;
        await createEIDInvitationMapping(
          uuidv4(),
          encryptedEID,
          encryptedEmail
        );
        updateData = {
          DESIGNATION: designation,
          ACTIVATION_FLAG: "ACTIVE",
          EXISTING_USER: "EXISTING_LINK_INITIATED",
        };
      }

      if (Object.keys(updateData).length > 0) {
        await updateUserData('ID', userId, updateData);
        return 204;
      }

      return 200;
    } catch (error) {
      throw error;
    }
  }

  async getNdaAcceptanceStatus(req, res) {
    try {
      const userEmail = req.user.preferred_username;
      const hashedEmail = encryptEmail(userEmail);
      const userData = await getUserDataV2({ EMAIL: hashedEmail });
      if (!userData.length > 0) {
        throw new IFPError(404, `User with email: ${userEmail} not found.`);
      }

      let isNdaAccepted = false;
      if (userData[0].IS_NDA_ACCEPTED == 1) {
        isNdaAccepted = true
      }
      return { isNdaAccepted, role: userData[0].ROLE };
    } catch (error) {
      throw error;
    }
  }

  async acceptNda(req, res) {
    try {
      const { isNdaAccepted } = req.body;
      if (isNdaAccepted != true) {
        throw new IFPError(400, "NDA Operation not allowed");
      }

      const userEmail = req.user.preferred_username;
      const hashedEmail = encryptEmail(userEmail);
      const userData = await getUserDataV2({ EMAIL: hashedEmail });
      if (!userData.length > 0) {
        throw new IFPError(404, `User with email: ${userEmail} not found.`);
      }

      await updateUserData("ID", userData[0].ID, {
        IS_NDA_ACCEPTED: 1,
      });
    } catch (error) {
      throw error;
    }
  }

  //Helper functions
  verifyInvitationToken(token) {
    try {
      const decoded = jwt.verify(token, this.secretKey);
      return { valid: true, decoded };
    } catch (error) {
      return { valid: false, error };
    }
  }
}

class ApprovalController {
  constructor() {

  }

  async approveUserAccess(req, type) {
    try {
      let { userAccessIds, action, reason ,send_to, userId } = req.body;

      if (userAccessIds.length == 0) {
        throw new IFPError(400, "Access request cannot be empty.");
      }

      if (send_to){
        if (send_to == 'DG'){
          send_to = 'DG'
        }
        else if(send_to == 'PRIMARY_PE_USER'){
          send_to = 'PRODUCT_ENGAGEMENT'
        }

      }

      let userInfo = {}
      if (type == 'DG')
        userInfo = req.dg
      else if (type == 'PRIMARY_PE_USER')
        userInfo = req.peUser
      else if (type == 'PRIMARY_SUPERUSER')
        userInfo = req.superuser
      else if (type == 'SECONDARY_SUPERUSER')
        userInfo = req.superuser

      if (type != userInfo.ROLE)
        throw new IFPError(403, "You don't have sufficient permissions to perform this action")

      const approvalLevels = {
        PRIMARY_PE_USER: "PRODUCT_ENGAGEMENT",
        DG: "DG",
        PRIMARY_SUPERUSER: "SUPERUSER",
        SECONDARY_SUPERUSER: "SUPERUSER"
      };

      const approvalLevel = approvalLevels[userInfo.ROLE];
      if (!approvalLevel) {
        throw new IFPError(404, "Approval levels are not defined for this user role");
      }

      const requestType = action == "revoke" ? "existing" : "new"
      let userAccessData = await getUserAccessV2(userId, requestType, userInfo.ROLE, userInfo.ROLE)
      if (requestType == "existing") {
        userAccessData = filterApprovedUserAccess(userAccessData)
        userAccessData = userAccessData.filter(access => access.classification == "SENSITIVE")
      }

      if (userAccessData.length < 1) {
        throw new IFPError(400, `Access Requests with the provided data not found`);
      }

      let filteredUserAccessData = userAccessData;

      if (action === 'approve' && userAccessIds && userAccessIds.length > 0) {
        filteredUserAccessData = userAccessData.filter(u => userAccessIds.includes(u.accessId));
      }

      if (filteredUserAccessData.length < 1) {
        throw new IFPError(400, "Given access levels are not part of the request");
      }

      const userAccessRequestIds = Array.from(new Set(userAccessData.map(userAccess=>{return userAccess.requestId})))
      let userAccessApprovalsData = []
      if (action == 'revoke'){
        userAccessApprovalsData = await getUserAccessApprovals({
          "USER_ACCESS_ID": filteredUserAccessData.map(u => u.accessId),
          // "APPROVAL_LEVEL": approvalLevel,

        });
      }
      else{

        userAccessApprovalsData = await getUserAccessApprovals({
          "USER_ACCESS_ID": filteredUserAccessData.map(u => u.accessId),
          "APPROVAL_LEVEL": approvalLevel,
          "STATUS": "PENDING"
        });
      }

      if (userAccessApprovalsData.length < 1) {
        throw new IFPError(400, "No pending approvals available for the provided access levels");
      }

      let nextApprovalLevel = null;
      let updateStatus = null;

      if (action === 'approve') {
        if (approvalLevel === 'DG') {
          nextApprovalLevel = 'PRODUCT_ENGAGEMENT';
          updateStatus = 'DG_APPROVED';
        } else if (approvalLevel === 'PRODUCT_ENGAGEMENT') {
          nextApprovalLevel = null;
          updateStatus = 'APPROVED';
        }
        else if (approvalLevel === 'SUPERUSER') {
          if (send_to){
            nextApprovalLevel = send_to;
          }
          else{
            nextApprovalLevel = 'DG';
          }
          updateStatus = 'APPROVED';
          if (reason)
            reason = `${reason}_:${userInfo.ROLE}`
        }

        // Approve filteredUserAccessData and reject the rest
        for (const userAccess of userAccessData) {
          if (userAccessIds.includes(userAccess.accessId)) {
            await updateUserAccessApproval(userAccess.accessId, updateStatus, approvalLevel);

            if (nextApprovalLevel) {
              await createUserAccessApproval(
                uuidv4(),
                userAccess.accessId,
                'PENDING',
                nextApprovalLevel,
                null,
                reason || null
              );
            }
          } else {
            // Reject the rest
            await updateUserAccessApproval(userAccess.accessId, 'REJECTED', approvalLevel);
          }
        }
        if (approvalLevel == "PRODUCT_ENGAGEMENT"){
          await Promise.all(
            userAccessRequestIds.map(async requestId => {
              await updateUserAccessRequestStatus(requestId, "COMPLETED")
            })
          )
        }
        // Notification
        if (approvalLevel === 'DG') {

          // PE, you have new request
          try {
            const user = await getUserDataV2({ ID: userId });
            const peUser = await getUserDataV2({ ROLE: "PRIMARY_PE_USER" });
            const requestingEntity =await getEntityDetails(user[0].ENTITY_ID)
            const peNewRequestEmailData = {
              userType: "PRODUCT_ENGAGEMENT",
              emailType: "NEW_ACCESS_REQUEST",
              recepientEmail: decryptEmail(peUser[0].EMAIL),
              subject: "Action: Access to Sensitive Data",
              recepientName: peUser[0].NAME,
              requestingEntity: requestingEntity.NAME,
              requestJustification: user[0].REQUEST_JUSTIFICATION
            };

            // SU, your request has been approved.
            const superUsers = await getUserDataV2({
              ROLE: ["PRIMARY_SUPERUSER", "SECONDARY_SUPERUSER"],
              ENTITY_ID: req.dg.ENTITY_ID,
            });
            // const suRequestApprovedEmailData = {
            //   userType: "SUPERUSER",
            //   emailType: "REQUEST_STATUS_UPDATE",
            //   subject: "Bayaan Access | Request Approved",
            //   recepientEmail: decryptEmail(superUser[0].EMAIL),
            //   recepientName: superUser[0].NAME,
            //   action: "approved",
            //   accessLevel: { en: "sensitive", ar: "الحساسة"},
            //   actorRole: userInfo.DESIGNATION // get Director General or Under Secretarys
            // };
            
            // await Promise.all([
            //   sendEmail(peNewRequestEmailData),
            //   sendEmail(suRequestApprovedEmailData),
            // ]);
            const suEmails = superUsers.map(superUser => ({
              userType: "SUPERUSER",
              emailType: "REQUEST_STATUS_UPDATE",
              subject: "Bayaan Access | Request Approved",
              recepientEmail: decryptEmail(superUser.EMAIL),
              recepientName: superUser.NAME,
              action: "approved",
              accessLevel: { en: "sensitive", ar: "الحساسة" },
              actorRole: userInfo.DESIGNATION
            }));
            
            await Promise.all([
              sendEmail(peNewRequestEmailData),
              ...suEmails.map(emailData => sendEmail(emailData))
            ]);
          } catch (error) {
            log.error(`Something went wrong when sending emails: ${error}`);
          }
        } else if (approvalLevel === 'PRODUCT_ENGAGEMENT') {
          try {
            const user = await getUserDataV2({ ID: userId });
            const superUsers = await getUserDataV2({
              ROLE: ["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER"],
              ENTITY_ID: user[0].ENTITY_ID,
            });
            const dgUser = await getUserDataV2({
              ROLE: "DG",
              ENTITY_ID: user[0].ENTITY_ID,
            });
            // const suRequestApprovedEmailData = {
            //   userType: "SUPERUSER",
            //   emailType: "REQUEST_STATUS_UPDATE",
            //   subject: "Bayaan Access | Request Approved",
            //   recepientEmail: decryptEmail(superUser[0].EMAIL),
            //   recepientName: superUser[0].NAME,
            //   action: "approved",
            //   accessLevel: { en: "sensitive", ar: "الحساسة"},
            //   actorRole: "Statistics Centre - Abu Dhabi"
            // };
            
            const suRequestApprovedEmailData = superUsers.map(superUser => ({
              userType: "SUPERUSER",
              emailType: "REQUEST_STATUS_UPDATE",
              subject: "Bayaan Access | Request Approved",
              recepientEmail: decryptEmail(superUser.EMAIL),
              recepientName: superUser.NAME,
              action: "approved",
              accessLevel: { en: "sensitive", ar: "الحساسة" },
              actorRole: "Statistics Centre - Abu Dhabi"
            }));
            
            const dgRequestApprovedEmailData = {
              userType: "DG",
              emailType: "REQUEST_STATUS_UPDATE",
              subject: "Bayaan Access | Request Approved",
              recepientEmail: decryptEmail(dgUser[0].EMAIL),
              recepientName: dgUser[0].NAME,
              action: "approved",
              accessLevel: { en: "sensitive", ar: "الحساسة"},
              actorRole: "Statistics Centre - Abu Dhabi"
            };
            await Promise.all([
              // sendEmail(suRequestApprovedEmailData),
              ...suRequestApprovedEmailData.map(emailData => sendEmail(emailData)),
              sendEmail(dgRequestApprovedEmailData),
            ]);
          } catch (error) {
            log.error(
              `Something went wrong when sending notification: ${error}`
            );
          }
        }
      } else if (action === 'reject') {
        updateStatus = 'REJECTED';

        // Reject all userAccessData
        for (const userAccess of userAccessData) {
          await updateUserAccessApproval(userAccess.accessId, updateStatus, approvalLevel);
        }

        // Update the parent request status to REJECTED
        await Promise.all(
          userAccessRequestIds.map(async requestId => {
            await updateUserAccessRequestStatus(requestId, 'COMPLETED');
          })
        )

        // Notify SU of request rejection
        if (approvalLevel == 'DG') {
          try {
            const superUsers = await getUserDataV2({
              ROLE: ["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER"],
              ENTITY_ID: req.dg.ENTITY_ID,
            });
            const user = await getUserDataV2({ ID: userId });
            // const suRequestRejectedEmailData = {
            //   userType: "SUPERUSER",
            //   emailType: "REQUEST_STATUS_UPDATE",
            //   subject: "Access to Sensitive Data",
            //   recepientEmail: decryptEmail(superUser[0].EMAIL),
            //   recepientName: superUser[0].NAME,
            //   recepientRole: approvalLevel,
            //   requestorName: user[0].NAME,
            //   action: "rejected",
            //   actor: { en: "your entity", ar: "جهتكم"},
            //   accessLevel: { en: "sensitive", ar: "الحساسة" }
            // };
            // // await sendEmail(suRequestRejectedEmailData);
            const suRequestRejectedEmailData = superUsers.map(superUser => ({
              userType: "SUPERUSER",
              emailType: "REQUEST_STATUS_UPDATE",
              subject: "Access to Sensitive Data",
              recepientEmail: decryptEmail(superUser.EMAIL),
              recepientName: superUser.NAME,
              recepientRole: approvalLevel,
              requestorName: user[0].NAME,
              action: "rejected",
              actor: { en: "your entity", ar: "جهتكم" },
              accessLevel: { en: "sensitive", ar: "الحساسة" }
            }));
            
            await Promise.all(suRequestRejectedEmailData.map(emailData => sendEmail(emailData)));
          
          } catch (error) {
            log.error(
              `Something went wrong when sending notification: ${error}`
            );
          }
        } else if (approvalLevel == 'PRODUCT_ENGAGEMENT') {
          try {
            const user = await getUserDataV2({ID: userId});
            const superUsers = await getUserDataV2({
              ROLE: ["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER"],
              ENTITY_ID: user[0].ENTITY_ID,
            });
            const dgUser = await getUserDataV2({
              ROLE: "DG",
              ENTITY_ID: user[0].ENTITY_ID,
            });
            // const suRequestRejectedEmailData = {
            //   userType: "SUPERUSER",
            //   emailType: "REQUEST_STATUS_UPDATE",
            //   subject: "Access to Sensitive Data",
            //   recepientEmail: decryptEmail(superUser[0].EMAIL),
            //   recepientName: superUser[0].NAME,
            //   requestorName: user[0].NAME,
            //   action: 'rejected',
            //   actor: { en: "Statistics Centre - Abu Dhabi", ar: "مركز الإحصاء - أبوظبي"},
            //   accessLevel: { en: "sensitive", ar: "الحساسة" }
            // };
            const suRequestRejectedEmailData = superUsers.map(superUser => ({
              userType: "SUPERUSER",
              emailType: "REQUEST_STATUS_UPDATE",
              subject: "Access to Sensitive Data",
              recepientEmail: decryptEmail(superUser.EMAIL),
              recepientName: superUser.NAME,
              requestorName: user[0].NAME,
              action: "rejected",
              actor: { en: "Statistics Centre - Abu Dhabi", ar: "مركز الإحصاء - أبوظبي" },
              accessLevel: { en: "sensitive", ar: "الحساسة" }
            }));
            const dgRequestRejectedEmailData = {
              userType: "DG",
              emailType: "REQUEST_STATUS_UPDATE",
              subject: "Access to Sensitive Data",
              recepientEmail: decryptEmail(dgUser[0].EMAIL),
              recepientName: dgUser[0].NAME,
              requestorName: user[0].NAME,
              action: 'rejected',
              actor: { en: "Statistics Centre - Abu Dhabi", ar: "مركز الإحصاء - أبوظبي"},
              accessLevel: { en: "sensitive", ar: "الحساسة" }
            };
            await Promise.all([
              // sendEmail(suRequestRejectedEmailData),
              ...suRequestRejectedEmailData.map(emailData => sendEmail(emailData)),
              sendEmail(dgRequestRejectedEmailData)
            ]);
          } catch (error) {
            log.error(`Something went wrong when sending email: ${error}`)
          }
        }
      } else if (action === 'revert') {
        if (send_to){
          nextApprovalLevel = send_to;
        }
        else{
          nextApprovalLevel = 'SUPERUSER';
        }
        updateStatus = `${userInfo.ROLE}_REVERTED`;

        // Revert the specified userAccessIds or all if not specified
        for (const userAccess of userAccessData) {
          if (!userAccessIds || userAccessIds.includes(userAccess.accessId)) {
            await updateUserAccessApproval(userAccess.accessId, updateStatus, approvalLevel);
            
            //Create a new user access with the same data
            let userAccessId = uuidv4()
            // Create a new approval for the Superuser to handle the reverted request
            await createUserAccessApproval(
              uuidv4(),
              userAccess.accessId,
              'PENDING',
              // 'PRIMARY_PE_USER',
              nextApprovalLevel,
              null,
              reason?`${reason}_:${userInfo.ROLE}`:reason || null
            );
          }
        }

        // Notification
        try {
          const user = await getUserDataV2({ ID: userId });
          const superUsers = await getUserDataV2({
            ROLE: ["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER"],
            ENTITY_ID: user[0].ENTITY_ID,
          });
          const superUserObj = req.superuser?req.superuser:superUsers[0]
          if (approvalLevel == "SUPERUSER" && send_to == "DG") {
            // Revert back to DG
            const dgUser = await getUserDataV2({
              ROLE: "DG",
              ENTITY_ID: userInfo.ENTITY_ID,
            });
            if (dgUser.length) {
              const revertBackToDG = {
                userType: "DG",
                emailType: "ACCESS_REQUEST_REVERT_BACK_NOTIFICATION",
                subject:
                  "Request for Sensitive Data Returned with Additional Information ",
                recepientEmail: decryptEmail(dgUser[0].EMAIL),
                recepientName: dgUser[0].NAME,
                requestor: superUserObj.NAME,
              };
              await sendEmail(revertBackToDG);
            }
          } else if (
            approvalLevel == "SUPERUSER" &&
            send_to == "PRODUCT_ENGAGEMENT"
          ) {
            // Revert back to PE
            const peUser = await getUserDataV2({ ROLE: "PRIMARY_PE_USER" });
            const revertBackToPE = {
              userType: "PRODUCT_ENGAGEMENT",
              emailType: "ACCESS_REQUEST_REVERT_BACK_NOTIFICATION",
              subject:
                "Request for Sensitive Data Returned with Additional Information ",
              recepientEmail: decryptEmail(peUser[0].EMAIL),
              recepientName: peUser[0].NAME,
              requestor: decryptEmail(superUserObj.EMAIL),
            };
            await sendEmail(revertBackToPE);
          } else {
            // Revert to Superuser
            // const revertToSu = {
            //   userType: "SUPERUSER",
            //   emailType: "ACCESS_REQUEST_REVERT_NOTIFICATION",
            //   subject:
            //     "Request for Sensitive Data Returned for Additional Information",
            //   recepientEmail: decryptEmail(superUser[0].EMAIL),
            //   recepientName: superUser[0].NAME,
            //   requestor: user[0].NAME,
            // };
            // await sendEmail(revertToSu);
            const revertToSuEmails = superUsers.map(superUser => ({
              userType: "SUPERUSER",
              emailType: "ACCESS_REQUEST_REVERT_NOTIFICATION",
              subject: "Request for Sensitive Data Returned for Additional Information",
              recepientEmail: decryptEmail(superUser.EMAIL),
              recepientName: superUser.NAME,
              requestor: user[0].NAME,
            }));
            await Promise.all(revertToSuEmails.map(emailData => sendEmail(emailData)));
          }
        } catch (error) {
          log.error(`Something went wrong when sending email: ${error}`);
        }
      }
      else if(action === 'revoke'){
        updateStatus = 'REVOKED';
        const classificationLevels = ["open", "confidential", "sensitive", "secret"];
        const classificationRank = {
            open: 1,
            confidential: 2,
            sensitive: 3,
            secret: 4,
        };
        const requestId = uuidv4();
        for (const userAccess of userAccessData) {
          const domain = userAccess.domain;
          const accessLevel = userAccess.classification;
          const currentRank = classificationRank[accessLevel.toLowerCase()]; // Get the rank of current access level

          // Revoke (delete) access for the domain and all higher levels
          for (const classification of classificationLevels) {
              if (classificationRank[classification] > currentRank) {
                  if (accessLevel.toUpperCase() !== "OPEN" && accessLevel.toUpperCase() !== "CONFIDENTIAL") {
                    const userAccessId = uuidv4();
                    await createUserAccess(requestId, userAccessId, userId, domain, accessLevel,"REVOKE");
                    await createUserAccessApproval(uuidv4(), userAccessId, "REVOKED", null);
                  }
              }
          }
        }
        await createUserAccessRequest(requestId, userId)
        await updateUserAccessRequestStatus(requestId, 'COMPLETED');

        try {
          const user = await getUserDataV2({ ID: userId });
          const superUsers = await getUserDataV2({
            ROLE: ["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER"],
            ENTITY_ID: user[0].ENTITY_ID,
          });
          const dgUser = await getUserDataV2({
            ROLE: "DG",
            ENTITY_ID: user[0].ENTITY_ID,
          });
          const peUser = await getUserDataV2({ ROLE: "PRIMARY_PE_USER" });
          const userAccessRevokeNotification = {
            userType: "USER",
            emailType: "ACCESS_REVOKE_NOTIFICATION",
            subject: "Access to Sensitive Data",
            recepientEmail: decryptEmail(user[0].EMAIL),
            recepientName: user[0].NAME,
            userEmail: decryptEmail(user[0].EMAIL)
          };
          const dgAccessRevokeNotification = {
            userType: "DG",
            emailType: "ACCESS_REVOKE_NOTIFICATION",
            subject: "Access to Sensitive Data",
            recepientEmail: decryptEmail(dgUser[0].EMAIL),
            recepientName: dgUser[0].NAME,
            userEmail: decryptEmail(user[0].EMAIL),
            actorEmail: decryptEmail(userInfo.EMAIL)
          };
          // const superUserAccessRevokeNotification = {
          //   userType: "SUPERUSER",
          //   emailType: "ACCESS_REVOKE_NOTIFICATION",
          //   subject: "Access to Sensitive Data",
          //   recepientEmail: decryptEmail(superUser[0].EMAIL),
          //   recepientName: superUser[0].NAME,
          //   userEmail: decryptEmail(user[0].EMAIL),
          //   actorEmail: decryptEmail(userInfo.EMAIL)
          // };
          const superUserAccessRevokeNotifications = superUsers.map(superUser => ({
            userType: "SUPERUSER",
            emailType: "ACCESS_REVOKE_NOTIFICATION",
            subject: "Access to Sensitive Data",
            recepientEmail: decryptEmail(superUser.EMAIL),
            recepientName: superUser.NAME,
            userEmail: decryptEmail(user[0].EMAIL),
            actorEmail: decryptEmail(userInfo.EMAIL)
          }));          
          const peUserAccessRevokeNotification = {
            userType: "PRODUCT_ENGAGEMENT",
            emailType: "ACCESS_REVOKE_NOTIFICATION",
            subject: "Access to Sensitive Data",
            recepientEmail: decryptEmail(peUser[0].EMAIL),
            recepientName: peUser[0].NAME,
            userEmail: decryptEmail(user[0].EMAIL),
            actorEmail: decryptEmail(userInfo.EMAIL)
          };
          const notificationPromises = [
            sendEmail(userAccessRevokeNotification),
            sendEmail(dgAccessRevokeNotification)
          ];
          
          if (approvalLevel == 'PRODUCT_ENGAGEMENT') {
            superUserAccessRevokeNotifications.forEach(emailData => {
              notificationPromises.push(sendEmail(emailData));
            });
          } else {
            notificationPromises.push(sendEmail(peUserAccessRevokeNotification))
          }
          
          await Promise.all(notificationPromises);
        } catch (error) {
          log.error(`Something went wrong when sending notification: ${error}`);
        }
      }

      return { message: message.SUCCESS.APPROVAL_CYCLE_UPDATE[action.toUpperCase()] };
    } catch (error) {
      throw error
    }
  }

}

class InvitationController {
  constructor() {
    this.secretKey = process.env.APP_SECRET_KEY;
    this.uaePassService = new UAEPassService();
  }

  async validateInvitation(req) {
    const { invitationToken, accessToken } = req.body;
    const userInfo = await this.uaePassService.getUserInfo(accessToken);
    if (!userInfo) {
      throw new IFPError(400, "Invalid ACCESS Token");
    }

    const { valid, decoded } = this.verifyInvitationToken(invitationToken);
    if (!valid) {
      throw new IFPError(400, "Invalid invitation token");
    }

    const { mobile, idn } = userInfo;
    const { requestId, role } = decoded;

    if (role == "DG" || role == "USER") {
      // Validate EID for DG and User
      const hashedEid = hashEmiratesId(idn);
      const eidMapping = await getEIDInvitationMapping(requestId);

      if (eidMapping.length == 0) {
        throw new IFPError(403, "EID not found for given invitation.");
      }

      if (eidMapping[0].EID !== hashedEid) {
        throw new IFPError(403, "EID mismatch in invitation.");
      }
    } else {
      // Validate Mobile for Superusers
      const invitationDetails = await getInvitationData(requestId);

      const { PHONE: encrptedPhone } = invitationDetails;
      const decryptedPhone = decryptPhone(encrptedPhone);

      if (decryptedPhone != mobile) {
        throw new IFPError(403, "Mobile mismatch in invitation.");
      }
    }

    return { isValidUser: true };
  }

  async deleteInvitationRecord(req) {
    const { invitationId } = req.params;
    try {
      const response = await deleteInvitation(invitationId);
    } catch (error) {
      log.error(
        `Something went wrong during invitatio delete for invitation -> ${invitationId}: ${error}`
      );
      throw new IFPError(500, "Something went wrong.");
    }
  }

  verifyInvitationToken(token) {
    try {
      const decoded = jwt.verify(token, this.secretKey);
      return { valid: true, decoded };
    } catch (error) {
      return { valid: false, error };
    }
  }

  async updateSUInvitation(req) {
    try {
      let newData = {};
      const { invitationId } = req.params;
      let { userEmail, phone, designation, platformAccess } = req.body;

      if (!userEmail && !phone && !designation && !platformAccess) {
        throw new IFPError(400, message.ERROR.UPDATE_DATA_EMPTY);
      }

      if (userEmail) {
        newData.USER_EMAIL = encryptEmail(userEmail);
        newData.EMAIL_MASKED = maskEmail(userEmail);
      }

      if (phone) {
        newData.PHONE = encryptPhone(`${phone}`);
      }

      if (designation) {
        newData.INVITEE_DESIGNATION = designation;
      }

      if (platformAccess) {
        if (!["web", "all"].includes(platformAccess)) {
          throw new IFPError(400, message.ERROR.INVITATION.INVALID_PLATFORM_TYPE)
        }
        newData.PLATFORM_ACCESS = platformAccess;
      }

      await updateInvitation(invitationId, newData);
    } catch (error) {
      log.error(
        `Something went wrong during updating invitation -> ${invitationId}: ${error}`
      );
      throw new IFPError(500, "Something went wrong.");
    }
  }

  async updateUserInvitation(req) {
    try {
      let newData = {};
      const { invitationId } = req.params;
      let { userEmail, designation, platformAccess } = req.body;

      if (!userEmail && !designation) {
        throw new IFPError(400, message.ERROR.UPDATE_DATA_EMPTY);
      }

      if (userEmail) {
        newData.USER_EMAIL = encryptEmail(userEmail);
        newData.EMAIL_MASKED = maskEmail(userEmail);
      }

      if (designation) {
        newData.INVITEE_DESIGNATION = designation;
      }

      if (platformAccess) {
        if (!["web", "all"].includes(platformAccess)) {
          throw new IFPError(400, message.ERROR.INVITATION.INVALID_PLATFORM_TYPE)
        }
        newData.PLATFORM_ACCESS = platformAccess;
      }

      await updateInvitation(invitationId, newData);
    } catch (error) {
      log.error(
        `Something went wrong during updating invitation -> ${invitationId}: ${error}`
      );
      throw new IFPError(500, "Something went wrong.");
    }
  }
}

class EIDRequestMappingController {
  constructor() {}

  async getAllMappings(req) {
    try {
      const data = await getAllMappingsData();
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getMappingByRequestIds(req) {
    try {
      const requestIds = req.body.requestIds;
      const data = await getMappingByRequestIdsData(requestIds);
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getMappingByEmails(req) {
    try {
      const emails = req.body.emails;
      const rows = getMappingByEmailsData(emails);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  async bulkDeleteMappings(req) {
    try {
      const requestIds = req.body.requestIds;
      await bulkDeleteMappingsData(requestIds);
      return { message: "Mappings deleted successfully" };
    } catch (error) {
      throw error;
    }
  }
}

async function downloadUserGuideController(req, res) {
  let fileName;
  const { type } = req.query;
  const options = {
    root: path.join(__dirname, "user-guides"),
    dotfiles: "deny",
    headers: {
      "x-timestamp": Date.now(),
      "x-sent": true,
    },
  };

  if (type.toUpperCase() == "CONFIDENTIAL") {
    fileName = "user-onboarding-confidential-access-guide.pdf";
  } else {
    fileName = "user-onboarding-sensitive-access-guide.pdf";
  }

  res.sendFile(fileName, options);
}

async function listDeletedUsers(req) {
  const { entityId } = req.query;
  try {
    let data = await getDeletedUsersList(entityId);

    for (let d of data) {
      d.userRole = getRoleName(d.userRole);
      d.deletedByRole = getRoleName(d.deletedByRole);
      d.userEmail = decryptEmail(d.userEmail);
    }

    return data;
  } catch (error) {
    log.error(`Something went wrong listing deleted users: ${error}`);
    throw error;
  }
}


module.exports = {
  EntityController,
  DGController,
  ProductEngagementController,
  SuperuserController,
  UserController,
  ApprovalController,
  RoleController,
  InvitationController,
  EIDRequestMappingController,
  downloadUserGuideController,
  listDeletedUsers
}
