const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function listNotificationsDataQuery(userEmail,status,lang,pageNumber=1,itemsPerPage=10) {
    return new Promise((resolve, reject) => {
        try {
            const offset = (pageNumber - 1) * itemsPerPage;
            let contentNameColumn = lang == 'EN'?'CONTENT_NAME':`CONTENT_NAME_${lang}`
            let contentDescriptionColumn = lang == 'EN'?'CONTENT_DESCRIPTION':`CONTENT_DESCRIPTION_${lang}`

            let query;
            if (status){
                query = `
                SELECT 
                    APP_TYPE,
                    ${contentNameColumn} AS CONTENT_NAME,
                    ${contentDescriptionColumn} AS CONTENT_DESCRIPTION,
                    CONTENT_TYPE,
                    INSERT_DATE, 
                    NODE_ID,
                    NOTIFICATION_ID,
                    READ_DATE,
                    READ_STATUS,
                    USER_EMAIL
                FROM VW_IFP_NOTIFICATIONS
                WHERE USER_EMAIL = '${userEmail}' AND READ_STATUS = '${status}'
                ORDER BY INSERT_DATE DESC
                OFFSET ${offset} ROWS FETCH NEXT ${itemsPerPage} ROWS ONLY
                `
            }
            else{
                query = `
                SELECT 
                    APP_TYPE,
                    ${contentNameColumn} AS CONTENT_NAME,
                    ${contentDescriptionColumn} AS CONTENT_DESCRIPTION,
                    CONTENT_TYPE,
                    INSERT_DATE,
                    NODE_ID,
                    NOTIFICATION_ID,
                    READ_DATE,
                    READ_STATUS,
                    USER_EMAIL
                FROM VW_IFP_NOTIFICATIONS
                WHERE USER_EMAIL = '${userEmail}'
                ORDER BY INSERT_DATE DESC
                OFFSET ${offset} ROWS FETCH NEXT ${itemsPerPage} ROWS ONLY
                `
            }

            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.listNotificationsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

function getNotificationCountQuery(userEmail) {
    const query = `
    SELECT 
        COUNT(*) AS NOTIFICATION_COUNT,
        COUNT(CASE WHEN READ_STATUS = 'READ' THEN 1 END) AS READ_NOTIFICATION_COUNT,
        COUNT(CASE WHEN READ_STATUS = 'UNREAD' THEN 1 END) AS UNREAD_NOTIFICATION_COUNT
    FROM VW_IFP_NOTIFICATIONS
    WHERE USER_EMAIL = '${userEmail}'
    `
    return query;
}

async function getNotificationMappingsDataQuery(userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT NODE_ID, IS_EMAIL FROM IFP_USER_NOTIFICATION_MAP WHERE USER_EMAIL='${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getNotificationMappingsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function subscribeNotificationsDataQuery(nodeId,userEmail,contentType,appType,viewName,isEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `
            MERGE INTO IFP_USER_NOTIFICATION_MAP tgt\
            USING (\
                SELECT\
                    '${nodeId}' AS NODE_ID,\
                    '${userEmail}' AS USER_EMAIL,\
                    '${contentType}' AS CONTENT_TYPE,\
                    '${appType}' AS APP_TYPE,\
                    '${viewName}' AS SOURCE_NAME,\
                    '${isEmail}' AS IS_EMAIL\
                FROM dual\
            ) src\
            ON (tgt.NODE_ID = src.NODE_ID AND tgt.USER_EMAIL = src.USER_EMAIL)\
            WHEN MATCHED THEN\
                UPDATE SET\
                    tgt.CONTENT_TYPE = src.CONTENT_TYPE,\
                    tgt.APP_TYPE = src.APP_TYPE,\
                    tgt.SOURCE_NAME = src.SOURCE_NAME,\
                    tgt.IS_EMAIL = src.IS_EMAIL\
            WHEN NOT MATCHED THEN\
                INSERT (NODE_ID, USER_EMAIL, CONTENT_TYPE, APP_TYPE,SOURCE_NAME, IS_EMAIL)\
                VALUES (src.NODE_ID, src.USER_EMAIL, src.CONTENT_TYPE, src.APP_TYPE,src.SOURCE_NAME, src.IS_EMAIL)`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.subscribeNotificationsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function subscribeEmailNotificationsDataQuery(nodeId,userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_USER_NOTIFICATION_MAP SET IS_EMAIL=1 WHERE NODE_ID = '${nodeId}' AND USER_EMAIL = '${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.subscribeNotificationsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function unsubscribeNotificationsDataQuery(nodeId,userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM IFP_USER_NOTIFICATION_MAP WHERE NODE_ID = '${nodeId}' AND USER_EMAIL = '${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.unsubscribeNotificationsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function unsubscribeEmailNotificationsDataQuery(nodeId,userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_USER_NOTIFICATION_MAP SET IS_EMAIL=0 WHERE NODE_ID = '${nodeId}' AND USER_EMAIL = '${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.subscribeNotificationsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function readNotificationDataDataQuery(notificationId,userEmail,isShareApp=false) {
    try {
        let query;
        let binds = {
            notificationId:notificationId,
            userEmail:userEmail
        }
        if(isShareApp){
            query = `UPDATE IFP_SHARE_APPS_NOTIFICATIONS SET READ_STATUS = 'READ', READ_DATE = CURRENT_TIMESTAMP WHERE NOTIFICATION_ID=:notificationId AND USER_EMAIL=:userEmail`
        }
        else{
            query = `UPDATE IFP_NOTIFICATION_READ_STATUS SET READ_STATUS = 'READ', READ_DATE = CURRENT_TIMESTAMP WHERE NOTIFICATION_ID=:notificationId AND USER_EMAIL=:userEmail`
        }
        return {query:query,binds:binds}
    } catch (err) {
        log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.insertNotificationMappingsDataQuery with error ${err} `);
        throw err;
    }
}

async function getCompareNodesByIdDataQuery(compareId) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT NODE FROM IFP_COMPARE_NODES WHERE ID = '${compareId}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getCompareNodesByIdDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getIndicatorsMetaDataQuery(indicatorIds,lang) {
    return new Promise((resolve, reject) => {
        let joinedIndicatorIds = indicatorIds.join(',')
        try {
            let translatedKeys = {
                titleColumn : lang == 'en'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR',
                topicColumn : lang == 'en'?'TOPIC_NAME_ENGLISH':'TOPIC_NAME_ARABIC',
                themeColumn : lang == 'en'?'THEME_NAME_ENGLISH':'THEME_NAME_ARABIC',
                subThemeColumn : lang == 'en'?'SUB_THEME_NAME_ENGLISH':'SUB_THEME_NAME_ARABIC',
                productColumn : lang == 'en'?'PRODUCT_NAME_ENGLISH':'PRODUCT_NAME_ARABIC'
            }
            let binds ={
                
            }
            let query = `SELECT 
                INDICATOR_ID,
                ${translatedKeys.titleColumn} AS INDICATOR_NAME,
                ${translatedKeys.topicColumn} AS TOPIC_NAME,
                ${translatedKeys.themeColumn} AS THEME_NAME,
                ${translatedKeys.subThemeColumn} AS SUB_THEME_NAME,
                ${translatedKeys.productColumn} AS PRODUCT_NAME,
                SOURCE_TABLE FROM VW_INDICATOR_MAP WHERE INDICATOR_ID IN (${joinedIndicatorIds})`

            return resolve({ query: query, binds: binds });
            
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-notifications.services.getQuery.service.getIndicatorsMetaDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getMappingsDataQuery(userEmail,page,limit) {
    return new Promise((resolve, reject) => {
        try {
            const offset = (page - 1) * limit;

            let query = `SELECT t.*,COUNT(*) OVER() AS TOTAL FROM IFP_USER_NOTIFICATION_MAP t WHERE USER_EMAIL='${userEmail}' ORDER BY INSERT_DT OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getMappingsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getMappingsDataNoPageQuery(userEmail,page,limit) {
    return new Promise((resolve, reject) => {
        try {
            const offset = (page - 1) * limit;

            let query = `SELECT * FROM IFP_USER_NOTIFICATION_MAP WHERE USER_EMAIL='${userEmail}' ORDER BY INSERT_DT`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getMappingsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = { listNotificationsDataQuery, getNotificationMappingsDataQuery, subscribeNotificationsDataQuery, 
    subscribeEmailNotificationsDataQuery, unsubscribeNotificationsDataQuery, unsubscribeEmailNotificationsDataQuery, readNotificationDataDataQuery, getCompareNodesByIdDataQuery, getIndicatorsMetaDataQuery, getMappingsDataQuery, getMappingsDataNoPageQuery, getNotificationCountQuery };
