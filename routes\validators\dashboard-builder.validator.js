const { getDashboardData, getUserDashboardListData, getDashboardRequestorData } = require("../../microservice-dashboard-builder/services/executeQuery");
const { checkUserAppAssignment } = require("../../services/graph");
const { validateEmailContent } = require("../../services/helpers/helper");
const { IFPError } = require("../../utils/error");

const validateDashboard = (req, res, next) => {
    try{
        const regex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;
    if (!regex.test(req.params.id))
            throw new IFPError(400,`${req.params.id} is not a valid indicator ID`) 
        next()
    }
    catch(exp){
        throw exp
  }
};

  const validateDashboardList = (req,res,next)=>{
    try{

    const page = Number(req.query.page);
    const limit = Number(req.query.limit);

    if (!Number.isFinite(page) || page < 0)
            throw new IFPError(400,`${req.query.page} is not a valid page number`);

    if (!Number.isFinite(limit) || limit < 1)
            throw new IFPError(400,`${req.query.limit} is not a valid limit number`);

        next()
    }
    catch(exp){
        throw exp
  }
  }

  const validateCreateDashboard = (req,res,next)=>{
    try{
        if (!'data' in req.body)
            throw new IFPError(400,`Please provide dashboard data, missing 'data'`)

        if ('files' in req){
            if (!typeof(req.files ,'Object'))
                throw new IFPError(400,`Invalid file data`)

            const allowedMimeTypes = ['image/jpeg', 'image/png']; 
            Object.values(req.files).forEach(file=>{
                file = file[0]

        if (!allowedMimeTypes.includes(file.mimetype)) {
                    throw new IFPError(400,`File must be an image (JPEG/PNG) accepted types: [${allowedMimeTypes.join(',')}`);
        }
        req[file.fieldname] = {
                    buffer:file.buffer,
                    type: file.mimetype
                  }
            })
            
    }

        if (!(('thumbnailLight' in req) && ('thumbnailDark' in req)) )
            throw new IFPError(400,'Thumbnail is a required field')

        req.body = JSON.parse(req.body.data)

        const requiredParentKeys = [
            "name",
            "nodes"
        ];

        const requiredNodeKeys = [
            "id",
            "properties"
        ]

        const missingKeys = requiredParentKeys.filter(key => !(key in req.body));
        if (missingKeys.length > 0) {
            throw new IFPError(400,`Missing keys: ${missingKeys.join(', ')}`)
        }

        if (! req.body.name) {
            throw new IFPError(400,`Please provide a valid name`)
        }

        if (!typeof(req.body.nodes) == 'object')
            throw new IFPError(400,`'nodes' should be an object`)

        Object.entries(req.body.nodes).map(([contentType,nodes]) =>{
            nodes.forEach(node=>{
                const missingKeys = requiredNodeKeys.filter(key => !(key in node));
                if (missingKeys.length > 0) {
                    throw new IFPError(400,`Missing keys for node: ${missingKeys.join(', ')} under ${contentType}`)
        }
            })
        })

        next()
    }
    catch(exp){
        throw exp
  }
  }

  const validateEditDashboard = async (req,res,next)=>{
    try{
        const regex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;
    if (!regex.test(req.params.id))
            throw new IFPError(400,`${req.params.id} is not a valid indicator ID`) 

    let dashboard = {
      id: req.params.id,
            userEmail: req.user.preferred_username
        }

        let dashboardData = await getDashboardData(dashboard)
        if (dashboardData.length<1)
            throw new IFPError(404,`The dashboard with id ${dashboard.id} is not found`)

        if (!'data' in req.body)
            throw new IFPError(400,`Please provide dashboard data, missing 'data'`)

        if ('files' in req){
            if (!typeof(req.files ,'Object'))
                throw new IFPError(400,`Invalid file data`)

            const allowedMimeTypes = ['image/jpeg', 'image/png']; 
            Object.values(req.files).forEach(file=>{
                file = file[0]

        if (!allowedMimeTypes.includes(file.mimetype)) {
                    throw new IFPError(400,`File must be an image (JPEG/PNG) accepted types: [${allowedMimeTypes.join(',')}`);
        }
        req[file.fieldname] = {
                    buffer:file.buffer,
                    type: file.mimetype
                    }
            })
            
    }

        if (!(('thumbnailLight' in req) && ('thumbnailDark' in req)) )
            throw new IFPError(400,'Thumbnail is a required field')

        req.body = JSON.parse(req.body.data)

        const requiredParentKeys = [
            "name",
            "nodes"
        ];

        const requiredNodeKeys = [
            "id",
            "properties"
        ]

        const missingKeys = requiredParentKeys.filter(key => !(key in req.body));
        if (missingKeys.length > 0) {
            throw new IFPError(400,`Missing keys: ${missingKeys.join(', ')}`)
        }

        if (!typeof(req.body.nodes) == 'object')
            throw new IFPError(400,`'nodes' should be an object`)

        Object.entries(req.body.nodes).map(([contentType,nodes]) =>{
            nodes.forEach(node=>{
                const missingKeys = requiredNodeKeys.filter(key => !(key in node));
                if (missingKeys.length > 0) {
                    throw new IFPError(400,`Missing keys for node: ${missingKeys.join(', ')} under ${contentType}`)
        }
            })
        })

        next()
    }
    catch(exp){
        next(exp)
  }
  }


  const validateDeleteDashboard = async (req,res,next)=>{
    try{
        const regex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;
    if (!regex.test(req.params.id))
            throw new IFPError(400,`${req.params.id} is not a valid indicator ID`) 

    let dashboard = {
      id: req.params.id,
            userEmail: req.user.preferred_username
        }

        let dashboardData = await getDashboardData(dashboard)
        if (dashboardData.length<1)
            throw new IFPError(404,`The dashboard with id ${dashboard.id} is not found`)

        next()
    }
    catch(exp){
        next(exp)
  }
}

const validateShareDashboard = async (req,res,next)=>{
    try{
        const requiredKeys = [
            "dashboards",
            "shareList"
        ];
        const missingKeys = requiredKeys.filter(key => !(key in req.body));

        if (missingKeys.length > 0) {
                throw new IFPError(400,`Missing keys: ${missingKeys.join(', ')}`)
        }

        if (!Array.isArray(req.body.dashboards))
                throw new IFPError(400,`Field 'dashboards' should be an array`) 

        if (!Array.isArray(req.body.shareList))
                throw new IFPError(400,`Field 'shareList' should be an array`) 

        const shareEmailChecks = req.body.shareList.map(async email=>{
            const isEmailRegistered = await checkUserAppAssignment(email)
            if (!isEmailRegistered)
                throw new IFPError(400, `The email ${email} is not registered with the system`)
        })

        await Promise.all(shareEmailChecks);

        if (req.body.comment){
            validateEmailContent(req.body.comment)
        }

        let dashboardData = await getUserDashboardListData(req.user.preferred_username)
        req.body.dashboards.forEach(dashboard=>{
            userDashboard = dashboardData.find(d=>d.ID==dashboard)
            
        if (!userDashboard)
                throw new IFPError(400,`The dashboard '${dashboard}' is not found`) 

        })

        next()
    }
    catch(exp){
        next(exp)
  }
}

const validateShareListDashboard = async (req,res,next)=>{
    try{
        
        if (!['sent','received'].includes(req.params.type))
            throw new IFPError(400,`Type '${req.params.type}' is not a valid type`) 

        if (req.query.sort && !['asc','desc'].includes(req.query.sort))
            throw new IFPError(400,`Type '${req.query.sort}' is not a valid sort type`) 

        next()
    }
    catch(exp){
        next(exp)
  }
}

const validateDeleteShareDashboard = async (req, res, next) => {
  try {
  
      const type = req.params.type
      if (!['sent', 'received'].includes(type))
        throw new IFPError(400, `Please provide a valid type: [sent, received]`)

      const id = req.params.id
      if (!id)
        throw new IFPError(400, `Please provide a valid type id`)

      const userEmail = req.user.preferred_username
      const requestor = type == 'sent' ? 'sender' : 'recepient'
      shareData = await getDashboardRequestorData(id, userEmail, requestor)

    if (shareData.length < 1)
        throw new IFPError(403, `Either this share app is not available or you don't have necessary privilege to access it`)

      next()
  }
    catch (error) {
      next(error)
    }  
}

/**
 * Validate that payload is in the right format (Object[]).
 * This format is what is required for client to map data
 * to the table.
 */
const validateDashboardCardData = async (req, res, next) => {
  try {
    const { body } = req;
    if (!Array.isArray(body)) {
      throw new IFPError(400, "Bad Payload");
    }
    next();
  } catch (error) {
    next(error);
  }
};

module.exports = {
  validateDashboard,
  validateDashboardList,
  validateCreateDashboard,
  validateEditDashboard,
  validateDeleteDashboard,
  validateShareDashboard,
  validateShareListDashboard,
  validateDeleteShareDashboard,
  validateDashboardCardData,
};
