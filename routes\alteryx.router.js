const express = require("express");
const router = new express.Router();
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const {
  alteryxOauthParamsController,
} = require("../microservice-alteryx/alteryx.controller");

router.post("/oauth-params", async (req, res, next) => {
  try {
    const data = await alteryxOauthParamsController(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for alteryx router, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
