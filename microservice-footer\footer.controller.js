const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
let constants = require('../config/constants.json');
/**
 * function to get domains content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getFooter(req) {
  log.debug(`>>>>>Entered microservice.footer.controller.getFooter`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsHeaderUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_FOOTER_URL}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsHeaderUrl, req.user.groups);
    log.debug(`<<<<<Exited microservice.footer.controller.getFooter successfully `);
    const boolKeys = [
      "show_accessibility",
      "show_language",
      "show_notifications",
      "show_themes"
    ]

    const iconKeys = [
      "address_icon",
      "address_light_icon",
      "contact_number_icon",
      "contact_number_light_icon",
      "contact_email_icon",
      "contact_email_light_icon",
      "contact_us_icon",
      "contact_us_light_icon",
      "site_logo",
      "site_logo_light",
      "site_slogan_light",
      "site_slogan"
    ]
    data.forEach(element => {
      Object.keys(element).forEach(key => {
        if (boolKeys.includes(key)) {
          element[key] = element[key] == 'Yes' ? true : false;
        }
        if (iconKeys.includes(key)) {
          element[key] = `${process.env.CMS_BASEPATH_URL}${element[key]}`;
        }
        
      })
    })
    return data[0];
  } catch (err) {
    log.error(`<<<<<Exited header-microservice.footer.controller.getFooter on getting CMS data with error ${err}`);
    throw err;
  }
}

async function aboutUs(req) {
  log.debug(`>>>>>Entered microservice.footer.controller.aboutUs`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsAboutUsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_ABOUT_US_URL}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsAboutUsUrl, req.user.groups);
    let response = data[0]
    let url = response.header_video_thumbnail.split('sites')
    response.header_video_thumbnail = `${process.env.CMS_BASEPATH_URL}/sites${url[1]}`
    response.page_images.forEach(img=>{
      let url = img.url.split('sites')
      img.url = `${process.env.CMS_BASEPATH_URL}/sites${url[1]}`
    })
    log.debug(`<<<<<Exited microservice.footer.controller.aboutUs successfully `);
    return response
  } catch (err) {
    log.error(`<<<<<Exited header-microservice.footer.controller.aboutUs on getting CMS data with error ${err}`);
    throw err;
  }
}

async function products(req) {
  log.debug(`>>>>>Entered microservice.footer.controller.aboutUs`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsProductsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_PRODUCT_URL}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsProductsUrl, req.user.groups);
    log.debug(`<<<<<Exited microservice.footer.controller.aboutUs successfully `);
    let response = data[0]

    response.analytical_apps.items.forEach(item=>{
      item.image_dark_theme = `${process.env.CMS_BASEPATH_URL}/sites${item.image_dark_theme[0].url.split('sites')[1]}`
      item.image_light_theme = `${process.env.CMS_BASEPATH_URL}/sites${item.image_light_theme[0].url.split('sites')[1]}`
    })

    response.cards.forEach(card =>{
      card.image_dark_theme = `${process.env.CMS_BASEPATH_URL}/sites${card.image_dark_theme[0].url.split('sites')[1]}`
      card.image_light_theme = `${process.env.CMS_BASEPATH_URL}/sites${card.image_light_theme[0].url.split('sites')[1]}`
    })

    response.domain.items.forEach(item=>{
      item.page_menu_icon = `${process.env.CMS_BASEPATH_URL}/sites${item.page_menu_icon[0].url.split('sites')[1]}`
      item.page_menu_light_icon = `${process.env.CMS_BASEPATH_URL}/sites${item.page_menu_light_icon[0].url.split('sites')[1]}`
    })

    return response
  } catch (err) {
    log.error(`<<<<<Exited header-microservice.footer.controller.aboutUs on getting CMS data with error ${err}`);
    throw err;
  }
}

async function privacyPolicy(req) {
  log.debug(`>>>>>Entered microservice.footer.controller.aboutUs`);
  try {
    const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsPrivacyPolicyUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_PRIVACY_POLICY_URL}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const data = await getMetaFromCMS(req,cmsLoginUrl, cmsPrivacyPolicyUrl, req.user.groups);
    log.debug(`<<<<<Exited microservice.footer.controller.aboutUs successfully `);
    let response = data[0]
    return response
  } catch (err) {
    log.error(`<<<<<Exited header-microservice.footer.controller.aboutUs on getting CMS data with error ${err}`);
    throw err;
  }
}

module.exports = { getFooter, aboutUs, products, privacyPolicy };
