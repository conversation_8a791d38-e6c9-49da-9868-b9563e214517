const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  setInteractionDataQuery,
  getInteractionDataQuery
} = require('./getQuery.service');


/**
 * Function to get my apps
 * @param {*} userEmail - user email 
 */
async function setInteractionData(nodeId,userEmail) {
  return new Promise((resolve, reject) => {
    setInteractionDataQuery(nodeId,userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-interaction.executeQuery.service.setInteractionData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-interaction.executeQuery.service.setInteractionData with error ${err}`);
      reject(err);
    })
  })
}

async function getInteractionData(nodeId,userEmail) {
  return new Promise((resolve, reject) => {
    getInteractionDataQuery(nodeId,userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-interaction.executeQuery.service.getInteractionData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-interaction.executeQuery.service.getInteractionData with error ${err}`);
      reject(err);
    })
  })
}

async function getData(query) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-interaction.services.executeQuery.service.getData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-interaction.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-myapps.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = {
  setInteractionData,
  getInteractionData
};
