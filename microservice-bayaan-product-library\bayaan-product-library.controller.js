const Logger = require("scad-library").logger;
require("dotenv").config();
const { getMetaFromCMS } = require("../services/common-service");
const { setRedis, getRedis } = require("../services/redis.service");
const log = new Logger().getInstance();
const encodeUrl = require("encodeurl");
const axios = require("axios");
const constants = require("../config/constants.json");
const { IFPError } = require("../utils/error");
const {
  getDomainWiseofficialScreenerViews,
  getDomainOfficialScreenerCount,
} = require("./services/executeQuery.service");
let cms_mappings = require("../config/cms_mappings.json");
const {
  getScreenerCount,
  getIndicatorData,
  getScreenerData,
} = require("../microservice-domains/services/executeQuery.service");
const { getViewNameById } = require("./services/executeQuery.service");
const crypto = require("crypto");
const screenerData = require("./helper/constants.json");


async function productLibraryTreeView(req) {
  log.debug(
    `>>>>>Entered microservice-bayaan-product-library.controller.productLibraryTreeViewV2`
  );
  try {
    const lang =
      req.headers["accept-language"] === "en"
        ? ""
        : `/${req.headers["accept-language"]}`;
    let cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V2;

    let cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${cmsDomainsUrlPath}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

    const cmsResponse = await getMetaFromCMS(
      req,
      cmsLoginUrl,
      cmsDomainsUrl,
      req.user.groups
    );

    const cmsDomainDetailsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
    const cmsDomainData = await getMetaFromCMS(
      req,
      cmsLoginUrl,
      cmsDomainDetailsUrl,
      req.user.groups
    );

    log.debug(
      `<<<<<Exited microservice-bayaan-product-library.controller.getDomains successfully `
    );

    let cmsCacheKey = `cmsNavigation_${crypto
      .createHash("md5")
      .update(JSON.stringify(cmsResponse))
      .digest("hex")}`;
    const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
    if (cmsCacheResults) {
      log.info(
        `<<<<<Cache found for microservice-bayaan-product-library.controller.productLibraryTreeViewV2`
      );
      return JSON.parse(cmsCacheResults);
    }

    let domainName;
    let subdomainName;
    let subthemeName;
    let restructuredData = {};

    let AnalyticalApps = constants.classifications.analyticalApps;
    let Reports = constants.classifications.reports;
    let IFPIndicators = constants.classifications.innovativeStatistics;
    let OfficialStatistics = constants.classifications.officialStatistics;
    let nodeData = await getIndicatorData(req);
    if (nodeData) {
      restructuredData = await restructureDomainData(nodeData, cmsDomainData);
    }

    const domainIdCMSMap =
      cms_mappings.domainIDCMSMap[process.env.NODE_ENV] || {};
    const result = await Promise.all(
      Object.values(cmsResponse.data).map(async (classification) => {
        const classificationObj = {
          id: classification.id,
          name: classification.classification,
          key: classification.key,
          light_icon: `${process.env.CMS_BASEPATH_URL}${classification.light_icon}`,
          dark_icon: `${process.env.CMS_BASEPATH_URL}${classification.dark_icon}`,
          nodeCount: 0,
          showTree: true,
        };
        let nodeCount = 0;
        if ([AnalyticalApps, Reports].includes(classification.key)) {
          // Remove items with name "Census" and "Exicutive Insights" from topic_pages
          if (classification.domains && Array.isArray(classification.domains)) {
            const validIds = Object.values(domainIdCMSMap);
            classification.domains = classification.domains.filter((item) =>
              validIds.includes(Number(item.id))
            );
          }
          let domains = await Promise.all(
            Object.values(classification.domains).map(async (domain) => {
              let domainName = domain.name.toLowerCase().split(" ").join("-");
              let subdomains = [];
              // For Analytical Apps, we need to group nodes by app_type
              if (Array.isArray(domain.nodes)) {
                // Group nodes by app_type
                const appTypeGroups = domain.nodes.reduce((acc, node) => {
                // Use node.category.name as group name if available, else fallback to app_type or "unknown"
                let groupName = (node.category && node.category.name) || node.app_type || "unknown";
                if (!node.category){
                  groupName = node?.smart_publisher === true ? "Smart Publisher" : "Publications";
                }
                
                if (!acc[groupName]) acc[groupName] = [];
                acc[groupName].push(node);
                return acc;
                }, {});
                // Create subdomain objects for each app_type
                subdomains = Object.entries(appTypeGroups).map(([appType, nodes]) => ({
                  id: nodes[0]?.category?.id || nodes[0]?.id,
                  name: nodes[0]?.category?.name || appType,
                  dark_icon: `${process.env.CMS_BASEPATH_URL}${nodes[0]?.category?.dark_icon || ""}`,
                  light_icon: `${process.env.CMS_BASEPATH_URL}${nodes[0]?.category?.light_icon || ""}`,
                  //showTree: true,
                  nodeCount: nodes.length,
                  //nodes,
                }));
              }
              const domainObj = {
                id: domain.id,
                name: domain.name,
                nodeCount: domain.node_count,
                route: `/domain-exploration/${domainName}/${domain.id}`,
                light_icon: `${process.env.CMS_BASEPATH_URL}${domain.light_icon}`,
                dark_icon: `${process.env.CMS_BASEPATH_URL}${domain.dark_icon}`,
                showTree: true,
                subdomains: subdomains,
                //nodes: domain.nodes,
              };
              nodeCount += domain.node_count;
              return domainObj;
            })
          );
          classificationObj.nodeCount = nodeCount;
          classificationObj.showTree = false;
          classificationObj.domains = domains;
        } else if ([IFPIndicators].includes(classification.key)) {
          if (classification.domains) {
            let totalDomainNodeCount = 0;
            let domains = await Promise.all(
              Object.values(classification.domains).map(async (domain) => {
                let currentDomain;
                // if (apiVersion == 2) {
                //   currentDomain = domain;
                // } else {
                //   currentDomain = Object.values(cmsResponse.domains).find(element => element.id == domain.id);
                // }
                domain.name = domain.name.replace(/\s+/g, " ");
                let domainName = domain.name.toLowerCase().split(" ").join("-");
                let domainNodeCount = domain.node_count ? domain.node_count : 1;
                const domainObj = {
                  id: domain.id,
                  name: domain.name,
                  light_icon: `${process.env.CMS_BASEPATH_URL}${domain.light_icon}`,
                  dark_icon: `${process.env.CMS_BASEPATH_URL}${domain.dark_icon}`,
                  route: `/domain-exploration/${domainName}/${domain.id}`,
                  showTree: true,
                  nodeCount: domain.node_count,
                  subdomains: await Promise.all(
                    Object.values(domain.subdomains).map(async (subdomain) => {
                      subdomain.name = subdomain.name.replace(/\s+/g, " ");
                      let subdomainName = subdomain.name
                        .toLowerCase()
                        .split(" ")
                        .join("-");
                      const subDomainObj = {
                        id: subdomain.id,
                        name: subdomain.name,
                        route: `/domain-exploration/${domainName}/${subdomainName}/${subdomain.id}`,
                        showTree: true,
                        nodeCount: subdomain.node_count,
                        subthemes: await Promise.all(
                          Object.values(subdomain.subthemes).map(
                            async (subtheme) => {
                              subtheme.name = subtheme.name.replace(
                                /\s+/g,
                                " "
                              );
                              let subthemeName = subtheme.name
                                .toLowerCase()
                                .split(" ")
                                .join("-");
                              const subThemeObj = {
                                id: subtheme.id,
                                name: subtheme.name,
                                showTree: true,
                                route: `/domain-exploration/${domainName}/${subdomainName}/${subthemeName}/${subtheme.id}`,
                                nodeCount: subtheme.node_count,
                              };
                              // if (classification.key == constants.classifications.officialStatistics && ['3193','3260'].includes(subtheme.id))
                              //   subThemeObj.screener = true
                              // else
                              //   subThemeObj.screener = false

                              try {
                                subThemeObj.products = Object.values(
                                  subtheme.products
                                ).map((product) => {
                                  return {
                                    name: product.name,
                                    showTree: true,
                                    nodes: product.nodes,
                                  };
                                });
                              } catch (e) {}
                              if (
                                classification.key ==
                                constants.classifications.officialStatistics
                              ) {
                                subThemeObj.screener = subtheme.show_screener;
                                subThemeObj.screenerConfiguration =
                                  subtheme.screener_configuration;

                                if (subtheme.screener_configuration) {
                                  let dbCount = 0;
                                  let screenerConfiguration = {
                                    ...subtheme.screener_configuration,
                                  };

                                  if (
                                    Number(screenerConfiguration.screenerView)
                                  ) {
                                    let viewName = await getViewNameById(
                                      screenerConfiguration.screenerView
                                    );
                                    screenerConfiguration.screenerView =
                                      viewName;
                                  }
                                  if (
                                    Object.keys(
                                      constants.censusViewMap
                                    ).includes(
                                      screenerConfiguration.screenerView
                                    )
                                  )
                                    subThemeObj.products = [];
                                  dbCount = await getScreenerCount(
                                    screenerConfiguration
                                  );

                                  subThemeObj.count = dbCount
                                    ? Number(dbCount)
                                    : 0;
                                  subThemeObj.screenerConfiguration =
                                    screenerConfiguration;
                                  domainNodeCount += subThemeObj.count - 1;
                                }
                              }

                              return subThemeObj;
                            }
                          )
                        ),
                      };
                      if (
                        classification.key ==
                        constants.classifications.innovativeStatistics
                      ) {
                        subDomainObj.screener = subdomain.show_screener;
                        if (subdomain.screener_configuration) {
                          let dbCount = 0;
                          let screenerConfiguration = {
                            ...subdomain.screener_configuration,
                          };

                          if (Number(screenerConfiguration.screenerView)) {
                            let viewName = await getViewNameById(
                              screenerConfiguration.screenerView
                            );
                            screenerConfiguration.screenerView = viewName;
                          }

                          dbCount = await getScreenerCount(
                            screenerConfiguration
                          );

                          subDomainObj.count = dbCount ? Number(dbCount) : 0;
                          subDomainObj.screenerConfiguration =
                            screenerConfiguration;
                          domainNodeCount += subDomainObj.count - 1;
                        }
                      }
                      return subDomainObj;
                    })
                  ),
                };
                nodeCount += domain.node_count;
                totalDomainNodeCount += domainNodeCount;
                domainObj.nodeCount = domainNodeCount;
                return domainObj;
              })
            );
            classificationObj.showTree = [OfficialStatistics].includes(
              classification.key
            )
              ? true
              : false;
            classificationObj.nodeCount = totalDomainNodeCount;
            classificationObj.domains = domains.sort(
              (a, b) => b.nodeCount - a.nodeCount
            );
          }
        } else if ([OfficialStatistics].includes(classification.key)) {
          // Update domain count once domain id matches
          // Find the key from screenerViews.screenerView[OfficialStatistics]
          const screenerViewKeys = Object.keys(
            screenerData.screenerViews[OfficialStatistics] || {}
          );
          // Update node count in domain data where id === key
          if (Array.isArray(restructuredData)) {
            for (const domainData of restructuredData) {
              if (screenerViewKeys.includes(String(domainData.id))) {
                // You can update nodeCount as needed, e.g., increment by 1 or set to a specific value
                // fetching official screener views
                const officialScreenerViews =
                  await getDomainWiseofficialScreenerViews(
                    domainData.id,
                    OfficialStatistics
                  );
                let domainOfficialScreenerCount = [{ count: 0 }];
                // calculating official screener count from the database
                if (officialScreenerViews && officialScreenerViews.length > 0) {
                  domainOfficialScreenerCount =
                    await getDomainOfficialScreenerCount(
                      officialScreenerViews,
                      domainData.name
                    );
                }
                // Update nodeCount in domainData and also in restructuredData array
                const screenerCount =
                  Number(domainOfficialScreenerCount[0]?.count) || 0;
                domainData.nodeCount = (domainData.subdomains || []).reduce(
                  (sum, subdomain) => sum + (subdomain.count || 0),
                  0
                );
                classificationObj.nodeCount += screenerCount;
                domainData.nodeCount += screenerCount;
                // If restructuredData is an array of objects, update the matching object by id
                const idx = restructuredData.findIndex(
                  (d) => d.id === domainData.id
                );
                if (idx !== -1) {
                  restructuredData[idx].nodeCount = domainData.nodeCount;
                }
              }
            }
          }
          classificationObj.domains = restructuredData;
          classificationObj.nodeCount += nodeData.length;
        }

        return classificationObj;
      })
    );

    setRedis(cmsCacheKey, JSON.stringify(result), 8000, req.headers);
    return result;
  } catch (err) {
    log.error(
      `<<<<<Exited microservice-bayaan-product-library.controller.productLibraryTreeViewV2 on getting CMS data with error ${err}`
    );
    throw err;
  }
}

async function restructureDomainData(data, cmsDomainData) {
  const output = {};
  const domainIndicatorMap = {};
  const addedIndicators = new Set();
  const subthemes = [...new Set(data.map((obj) => obj.subtheme_id))];
  const screenerData = await getScreenerData(subthemes);

  const domainMapper = cms_mappings.domainIDCMSMap[process.env.NODE_ENV];

  const cmsDomainDataMap = cmsDomainData.reduce((map, item) => {
    map[item.id] = item;
    return map;
  }, {});

  data.forEach((item) => {
    const {
      domain_id: domainId,
      theme_id: themeId,
      subtheme_id: subthemeId,
      product_id: productId,
      indicator_id: indicatorId,
      domain,
      theme,
      subtheme,
      product,
      node_id: nodeId,
      title,
      data_source: dataSource,
    } = item;

    const domainName = domain.toLowerCase().replace(/\s+/g, "-");
    const themeName = theme.toLowerCase().replace(/\s+/g, "-");
    const subthemeName = subtheme.toLowerCase().replace(/\s+/g, "-");
    const cmsDomain = cmsDomainDataMap[domainMapper[parseInt(domainId)]];
    const env = process.env.NODE_ENV || "dev";

    if (!output[domainId]) {
      output[domainId] = {
        id: domainId,
        name: domain,
        route: `/domain-exploration/${domainName}/${domainId}`,
        showTree: true,
        subdomains: [],
        light_icon: `${process.env.CMS_BASEPATH_URL}${cmsDomain.light_icon}`,
        dark_icon: `${process.env.CMS_BASEPATH_URL}${cmsDomain.dark_icon}`,
      };
      domainIndicatorMap[domainId] = new Set();
    }

    const domainObj = output[domainId];
    let themeObj = domainObj.subdomains.find((t) => t.id === themeId);

    if (!themeObj) {
      themeObj = {
        id: themeId,
        name: theme,
        route: `/domain-exploration/${domainName}/${themeName}/${themeId}`,
        showTree: true,
        subthemes: [],
        nodeCount: 0, // Initialize count for themes
      };

      domainObj.subdomains.push(themeObj);
    }
    // Increment count each time entering the condition
    themeObj.nodeCount += 1;

    let subthemeObj = themeObj.subthemes.find((st) => st.id === subthemeId);
    const screener = screenerData.find(
      (item) => item.subtheme_id === subthemeId
    );

    if (!subthemeObj) {
      subthemeObj = {
        id: subthemeId,
        name: subtheme,
        route: `/domain-exploration/${domainName}/${themeName}/${subthemeName}/${subthemeId}`,
        showTree: screener ? true : false,
        screener: !!screener,
        screenerConfiguration: screener ? JSON.parse(screener?.config) : [],
        products: [],
      };
      themeObj.subthemes.push(subthemeObj);
    }

    let productObj = subthemeObj.products.find((p) => p.id === productId);

    if (!productObj && !screener) {
      productObj = {
        id: productId,
        name: product,
        nodes: [],
      };
      subthemeObj.products.push(productObj);
    }

    if (!addedIndicators.has(indicatorId) && !screener) {
      productObj.nodes.push({
        id: nodeId,
        note: indicatorId,
        title,
        data_source: dataSource,
        app_type: null,
        content_type: "scad_official_indicator",
        content_classification: {
          name: "Official Statistics",
          key: "official_statistics",
        },
        category: {
          id: Object.keys(cms_mappings.classificationIDCMSMap[env]).find(
            (k) =>
              cms_mappings.classificationIDCMSMap[env][k] ===
              "official_statistics"
          ),
          name: "Official Statistics",
        },
      });

      addedIndicators.add(indicatorId);
      domainIndicatorMap[domainId].add(indicatorId);
    }
  });

  return Object.values(output).map((domain) => ({
    ...domain,
    nodeCount: domainIndicatorMap[domain.id].size,
  }));
}

module.exports = {
  productLibraryTreeView,
};
