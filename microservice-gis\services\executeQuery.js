const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const {  getMapDistrictsColumnsDataQuery, getMapDistrictsQuery, getMapDistrictsV2Query, getMapDistrictsV3Query } = require('./getQuery.service');


async function getMapDistrictsColumnsData() {
  return new Promise((resolve, reject) => {
    
    getMapDistrictsColumnsDataQuery().then((results) => {
      log.debug(`>>>>> Enter microservice-gis.services.getGraphData.getMapDistrictsColumnsData`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsColumnsData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsColumnsData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-gis.executeQuery.service.getMapDistrictsColumnsData with error ${err}`);
      reject(err);
    })
  })
}

async function getMapDistrictsData() {
  return new Promise((resolve, reject) => {
    
    getMapDistrictsQuery().then((results) => {
      log.debug(`>>>>> Enter microservice-gis.services.getGraphData.getMapDistrictsQuery`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsQuery successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsQuery with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-gis.executeQuery.service.getMapDistrictsQuery with error ${err}`);
      reject(err);
    })
  })
}

async function getMapDistrictsV2Data(params,filters) {
  return new Promise((resolve, reject) => {
    
    getMapDistrictsV2Query(params,filters).then((results) => {
      log.debug(`>>>>> Enter microservice-gis.services.getGraphData.getMapDistrictsQuery`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsQuery successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsQuery with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-gis.executeQuery.service.getMapDistrictsQuery with error ${err}`);
      reject(err);
    })
  })
}

async function getMapDistrictsV3Data(params,filters,columns) {
  return new Promise((resolve, reject) => {
    
    getMapDistrictsV3Query(params,filters,columns).then((results) => {
      log.debug(`>>>>> Enter microservice-gis.services.getGraphData.getMapDistrictsV3Data`);

      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsV3Data successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-gis.services.getGraphData.getMapDistrictsV3Data with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-gis.executeQuery.service.getMapDistrictsV3Data with error ${err}`);
      reject(err);
    })
  })
}

module.exports = {
  getMapDistrictsColumnsData,
  getMapDistrictsData,
  getMapDistrictsV2Data,
  getMapDistrictsV3Data
}