const Logger = require('scad-library').logger;
require('dotenv').config();
const { getMetaFromCMS } = require('../services/common-service');
const log = new Logger().getInstance();
const constants = require('../config/constants.json');
const { getWhatNewData, getLatestData } = require('./services/executeQuery')

const { getViewNameById } = require('../services/executeQuery.service');

async function getWhatsNewBK(req) {
  log.debug(`>>>>>Entered microservice.whatsnew.controller.getWhatsNew`);
  return new Promise(async (resolve, reject) => {

      try {

          const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
          const language = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
          const cmsClassificationList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST}`;
          const cmsOfficialStatisticsList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_STATISTICS_INSIGHTS_URL}`;
          const cmsDomainsList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
          const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

          let whatsNewRight;
          let classificationList;
          let officialNodes;
          let domainsList;

          [classificationList,officialNodes,domainsList,whatsNewRight] = await Promise.all([
              getMetaFromCMS(req,cmsLoginUrl, cmsClassificationList, req.user.groups),
              getMetaFromCMS(req,cmsLoginUrl, cmsOfficialStatisticsList, req.user.groups),
              getMetaFromCMS(req,cmsLoginUrl, cmsDomainsList, req.user.groups),
              getWhatNewData('experimental_statistics')
          ])

          let nodeList = []
          let nodeIndicatorMap = {}

          officialNodes.forEach(node=>{
            if (node.type=='official_statistics' && node.content_classification_key == 'official_statistics'){
              nodeList.push(node.note)
              nodeIndicatorMap[node.note]=node
            }
          })

          try{
            const userDomainAccess = domainsList.map(d=>d.name.toUpperCase());
            whatsNewRight = whatsNewRight.filter(w=>userDomainAccess.includes(w.DOMAIN))
            const userAccessSet = new Set()
            whatsNewRight.forEach(w=>{
              userAccessSet.add(w.SOURCE)
            })
            const userAccess = Array.from(userAccessSet);
            
            const maxRecordsPerDomain = 3;

            const domainMap = {};
            whatsNewRight.forEach(record => {
              if (!domainMap[record.SOURCE]) {
                domainMap[record.SOURCE] = [];
              }
              domainMap[record.SOURCE].push(record);
            });

            const filteredRecords = [];

            const remainingDomains = [...userAccess];

            while (remainingDomains.length > 0) {
              remainingDomains.forEach((domain, index) => {
                const recordsForDomain = domainMap[domain] || [];
                const nextRecord = recordsForDomain.shift();

                if (nextRecord) {
                  filteredRecords.push(nextRecord);
                }

                if (recordsForDomain.length === 0) {
                  remainingDomains.splice(index, 1);
                }
              });
            }

            whatsNewRight = filteredRecords.slice(0,5)
          }
          catch(exp){
            whatsNewRight = []
            log.error(`<<<<< Exited microservice.whatsnew.controller.getWhatsNew with error ${exp} `)
          }

          let whatsNewLeft = []
          if (nodeList.length)
            whatsNewLeft = await getLatestData(nodeList)

          
          let officialClassification = Object.values(classificationList.classification).find(classification=>classification.key == 'official_statistics')
          let experimentalClassification = Object.values(classificationList.classification).find(classification=>classification.key == 'experimental_statistics')

          let officialStatistics = {
            label: officialClassification.name,
            title: officialClassification.name,
            key: `${officialClassification.key}`,
            sectionAlignment: "left",
            sectionOrder: 1,
            indicatorList: whatsNewLeft.map(wnNode=>{
              let indicatorId = wnNode['INDICATOR_ID']
              return {
                indicatorId: nodeIndicatorMap[indicatorId].id,
                title: nodeIndicatorMap[indicatorId].title,
                contentType: "scad_official_indicator",
                appType: null,
                type: "official_statistics",
                domain: nodeIndicatorMap[indicatorId].domain
              }
            })
          }

          let experimentalStatistics = {
            label: experimentalClassification.name,
            title: experimentalClassification.name,
            key: `${experimentalClassification.key}_screener`,
            sectionAlignment: "right",
            sectionOrder: 2,
            indicatorList: whatsNewRight.map(wnNode=>{
              return {
                indicatorId: wnNode.INDICATOR_ID,
                title: wnNode[`INDICATOR_NAME_${language}`],
                contentType: "innovative-insights",
                appType: null,
                type: "official_statistics",
                domain: "Industry & Business"
              }
            })
          }

          let response = {
              title: "What's new",
              warnings: [],
              "pageDescription": null,
              "pageType": "whatsnewpage",
              subsections: [
                officialStatistics,
                experimentalStatistics
              ],
              
            }
          if (!whatsNewRight.length){
            response.warnings.push("Either the user has no access to or there is no data available for Experimental statistics indicators")
          }
          if (!whatsNewLeft.length){
            response.warnings.push("Either the user has no access to or there is no data available for Official statistics indicators")
          }
          return resolve(response)

      } catch (err) {
          
          log.error(`<<<<< Exited microservice.whatsnew.controller.getWhatsNew with error ${err} `)
          reject(err);
      }
  });

}

async function getWhatsNew(req) {
    log.debug(`>>>>>Entered microservice.whatsnew.controller.getWhatsNew`);
    return new Promise(async (resolve, reject) => {

        try {

            const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
            const language = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
            const cmsWhatsNewOptimizedList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_WHATS_NEW_OPT_URL}`;
            const cmsDomainsList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST_V2}`;
            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

            let experimentalNodes;
            let domainsList;

            [whatsNewData,domainsList,experimentalNodes] = await Promise.all([
                getMetaFromCMS(req,cmsLoginUrl, cmsWhatsNewOptimizedList, req.user.groups),
                getMetaFromCMS(req,cmsLoginUrl, cmsDomainsList, req.user.groups),
                getWhatNewData('experimental_statistics')
            ])

            let  officialLayout = whatsNewData.find(wn=>wn.key=='official_statistics')
            let experimentalLayout = whatsNewData.find(wn=>wn.key=='experimental_statistics')

            let expSources = []
            expSources = await Promise.all(experimentalLayout.indicatorList.map(async exp=>{
              let viewName = exp.subDomainDetails.screener_configuration.screenerView
              if (Number(exp.subDomainDetails.screener_configuration.screenerView))
                viewName = await getViewNameById(exp.subDomainDetails.screener_configuration.screenerView)
              return viewName
            }))

            try{
              const domainKey = language == 'EN'?'DOMAIN':`DOMAIN_${language}`
              const userDomainAccess = domainsList.map(d=>d.name.toUpperCase());
              experimentalNodes = experimentalNodes.filter(w=>userDomainAccess.includes(w[domainKey]) && expSources.includes(w.SOURCE))
              const userAccessSet = new Set()
              experimentalNodes.forEach(w=>{
                userAccessSet.add(w.SOURCE)
              })
              const userAccess = Array.from(userAccessSet);
            
              const domainMap = {};
              experimentalNodes.forEach(record => {
                if (!domainMap[record.SOURCE]) {
                  domainMap[record.SOURCE] = [];
                }
                domainMap[record.SOURCE].push(record);
              });

              const filteredRecords = [];

              const remainingDomains = [...userAccess];

              while (remainingDomains.length > 0) {
                remainingDomains.forEach((domain, index) => {
                  const recordsForDomain = domainMap[domain] || [];
                  const nextRecord = recordsForDomain.shift();

                  if (nextRecord) {
                    filteredRecords.push(nextRecord);
                  }

                  if (recordsForDomain.length === 0) {
                    remainingDomains.splice(index, 1);
                  }
                });
              }

              experimentalNodes = filteredRecords.slice(0,5)
            }
            catch(exp){
              experimentalNodes = []
              log.error(`<<<<< Exited microservice.whatsnew.controller.getWhatsNew with error ${exp} `)
            }

            let officialStatistics = {
              label: officialLayout.title,
              title: officialLayout.title,
              key: officialLayout.key,
              sectionAlignment: "left",
              sectionOrder: 1,
              indicatorList: officialLayout.indicatorList.map(wnNode=>{
                return {
                  indicatorId: wnNode.id,
                  title: wnNode.title,
                  contentType: wnNode.contentType,
                  appType: null,
                  type: "official_statistics",
                  domain: wnNode.domainDetails.title
                }
              })
            }

            let experimentalStatistics = {
              label: experimentalLayout.title,
              title: experimentalLayout.title,
              key: `${experimentalLayout.key}_screener`,
              sectionAlignment: "right",
              sectionOrder: 2,
              indicatorList: experimentalNodes.map(wnNode=>{
                return {
                  indicatorId: wnNode.INDICATOR_ID,
                  title: wnNode[`INDICATOR_NAME_${language}`],
                  contentType: "innovative-insights",
                  appType: null,
                  type: "official_statistics",
                  domain: "Industry & Business"
                }
              })
            }

            let response = {
                title: "What's new",
                warnings: [],
                "pageDescription": null,
                "pageType": "whatsnewpage",
                subsections: [
                  officialStatistics,
                  experimentalStatistics
                ],
                
              }
            if (!experimentalNodes.length){
              response.warnings.push("Either the user has no access to or there is no data available for Experimental statistics indicators")
            }
            if (! officialLayout.indicatorList.length){
              response.warnings.push("Either the user has no access to or there is no data available for Official statistics indicators")
            }
            return resolve(response)

        } catch (err) {
            
            log.error(`<<<<< Exited microservice.whatsnew.controller.getWhatsNew with error ${err} `)
            reject(err);
        }
    });

}

module.exports = {
    getWhatsNew
};
