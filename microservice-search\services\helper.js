const { getExperimentalIndicatorsData } = require("./executeQuery.service")


async function fetchScreenerNodes(queryString,navigationResponse,classification,lang, apiVerison){
    return new Promise(async (resolve, reject) => {
        try{
            let screenerConfigurations = []
            let expClassifications  = navigationResponse.data.find(nv=>nv.key=='experimental_statistics')
            let officalClassifications  = navigationResponse.data.find(nv=>nv.key=='official_statistics')
            if (expClassifications){
                expClassifications.domains.forEach(domain => {
                    domain.subdomains.forEach(subdomain =>{
                        if(subdomain.show_screener && subdomain.screener_configuration){
                            screenerConfigurations.push(subdomain.screener_configuration.screenerView)
                        }
                    })
                })
            }
            if (officalClassifications) {
                officalClassifications.domains.forEach(domain => {
                    domain.subdomains.forEach(subDomain => {
                        subDomain.subthemes.forEach(subTheme => {
                            if (subTheme.show_screener && subTheme.screener_configuration.screenerView) {
                                screenerConfigurations.push(subTheme.screener_configuration.screenerView)
                            }
                        })
                    })
                })
            }
            if (screenerConfigurations.length){
                let experimentalData = await getExperimentalIndicatorsData(queryString,screenerConfigurations,lang)
                if (experimentalData.length){
                    classification.items = experimentalData.map(exp=>{
                        let contentClassification = pageCategory = 'Official Statistics';
                        let type = 'official-insights';
                        if (exp.INDICATOR_TYPE == 'EXPERIMENTAL') {
                            contentClassification = pageCategory = 'Experimental Statistics'
                            type = 'innovative-insights'
                        }
                        if (apiVerison == 2) {
                          return {
                            id: exp.INDICATOR_ID,
                            title: exp.INDICATOR_NAME,
                            contentClassification,
                            subTitle: exp.INDICATOR_NAME,
                            topic: exp.TOPIC_NAME,
                            type,
                            pageCategory,
                          };
                        }
                        return {
                          id: exp.INDICATOR_ID,
                          title: exp.INDICATOR_NAME,
                          content_classification: contentClassification,
                          subtitle: exp.INDICATOR_NAME,
                          domains: [exp.TOPIC_NAME],
                          category: pageCategory,
                          type,
                        };
                    })
                }   
            }
            return resolve(classification)
        }
        catch(exp){
            return reject(exp)
        }
    })
}

module.exports = {fetchScreenerNodes}