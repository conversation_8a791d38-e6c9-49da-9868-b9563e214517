const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json')
const messages = require('./messages')

async function sendInsightsEmail(req, purpose, insight = {}, email = "", business_name = "") {
    new Promise((resolve, reject) => {
        let reqBody = req.body;

        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });

        let businessFullname;
        let setBusinessName;
        let approverFullname;
        let setApproverName;
        let approverActName;

        switch (purpose) {
            case 'ADD':
                const nodeTitle = reqBody.nodeTitle
                const nodeLink = reqBody.nodeLink
                const nodeInsight = reqBody.insight
                businessFullname = req.user.preferred_username.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: req.user.preferred_username,
                    subject: `Thank you for your valuable insight!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.businessAddInsightsMessage(req.user.name, nodeTitle, nodeLink, nodeInsight)
                };

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    log.info(`Email sent successfully to user ${req.user.preferred_username} \n${info.response}`);
                    resolve();
                })

                break;

            case 'UPDATE':
                const nodeUpdateTitle = reqBody.nodeTitle
                const nodeUpdateLink = reqBody.nodeLink
                const nodeUpdateInsight = reqBody.insight
                const prevInsight = insight.prevInsight
                businessFullname = insight.email.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                let subject;
                let message;

                subject = 'Insight Updated!'
                message = messages.businessUpdateInsightsMessage(req.user.name, nodeUpdateTitle, nodeUpdateLink, nodeUpdateInsight, prevInsight)

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: insight.email,
                    subject: subject,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: message
                };

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    log.info(`Email sent successfully to user ${insight.email} \n${info.response}`);
                    resolve();
                })

                break;

            case 'DELETE':
                const nodeDelTitle = insight.NODE_TITLE
                const nodeDelLink = insight.NODE_LINK
                const nodeDelInsight = insight.INSIGHT


                businessFullname = insight.EMAIL.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }
                let delMessage;

                delMessage = messages.businessDeleteInsightsMessage(req.user.name, nodeDelTitle, nodeDelLink, nodeDelInsight)

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: insight.EMAIL,
                    subject: `Insight Deleted!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: delMessage
                };

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    log.info(`Email sent successfully to user ${insight.EMAIL} \n${info.response}`);
                    resolve();
                })
                break;

        }
    });
}


module.exports = { sendInsightsEmail }