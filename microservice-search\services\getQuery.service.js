
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getGlossaryDataQuery(lang, page, limit, filters, sortBy, searchTerm) {
    try {
        const binds = {};
        let bindCounter = 0;
        const offset = (page - 1) * limit;

        const whereClauses = [];

        if (searchTerm) {
            bindCounter++;
            binds[`term${bindCounter}`] = `%${searchTerm.toUpperCase()}%`;
            whereClauses.push(`(UPPER(TITLE_EN) LIKE {term${bindCounter}:String} OR UPPER(TITLE_AR) LIKE {term${bindCounter}:String})`);
        }

        Object.entries(filters).forEach(([key, value]) => {
            if (value.length > 0) {
                if (key === `TITLE_${lang}`) {
                    bindCounter++;
                    binds[`filter${bindCounter}`] = `${value[0].toUpperCase()}%`;
                    whereClauses.push(`UPPER(${key}) LIKE {filter${bindCounter}:String}`);
                } else if (Array.isArray(value)) {
                    const bindValues = value.map(val => val.toUpperCase());
                    bindCounter++;
                    binds[`filter${bindCounter}`] = bindValues;
                    whereClauses.push(`UPPER(${key}) IN ({filter${bindCounter}: Array(String)})`);
                } else {
                    bindCounter++;
                    binds[`filter${bindCounter}`] = value.toUpperCase();
                    whereClauses.push(`UPPER(${key}) = {filter${bindCounter}:String}`);
                }
            }
        });

        const sortByClauses = Object.entries(sortBy).map(([key, value]) => {
            return key === 'alphabetical' ? `upper(TITLE_${lang}) ${value.toUpperCase()}` : '';
        }).filter(clause => clause.length > 0);

        if (sortByClauses.length === 0) {
            sortByClauses.push(`upper(TITLE_${lang}) ASC`);
        }

        let query = `SELECT t.*, toUInt32(COUNT(*) OVER()) AS TOTAL FROM VW_IFP_GLOSSARY t ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''
            } ORDER BY ${sortByClauses.join(', ')} OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;

        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-glossary.services.getQuery.service.getGlossaryDataQuery with error ${err} `);
        throw err;
    }
}

async function getScreenerSearchQuery(searchTerm, screenerConfigurations, lang) {
    return new Promise((resolve, reject) => {
        try {
            let titleColumn = `INDICATOR_NAME_${lang}`;
            let domainColumn = `TOPIC_NAME_${lang === 'EN' ? 'ENGLISH' : 'ARABIC'}`;
            
            // Prepare the query with named parameters including types everywhere they are used
            let query = `
                SELECT
                    INDICATOR_ID,
                    ${titleColumn} AS INDICATOR_NAME,
                    ${domainColumn} AS TOPIC_NAME,
                    INDICATOR_TYPE,
                    ngramSearchCaseInsensitive(INDICATOR_NAME_EN, {searchTerm:String}) AS SIMILARITY
                FROM VW_INDICATOR_MAP
                JOIN IFP_SCREENER_VIEW_MAP 
                    ON VW_INDICATOR_MAP.SOURCE_TABLE = IFP_SCREENER_VIEW_MAP.VIEWNAME
                WHERE
                    SCREENER = 1
                    AND IFP_SCREENER_VIEW_MAP.ID IN ({screenerConfigurations:Array(UInt32)})
                    AND ngramSearchCaseInsensitive(INDICATOR_NAME_EN, {searchTerm:String}) > 0.9
                    AND UPPER(INDICATOR_NAME_EN) LIKE UPPER(concat('%', {searchTerm:String}, '%'))
                ORDER BY SIMILARITY DESC, INDICATOR_NAME_EN
                LIMIT 10;
            `;
            
            // Construct binds object with proper types
            let binds = {
                searchTerm: searchTerm,
                screenerConfigurations: screenerConfigurations.map(Number),
            };
            
            return resolve({ query: query, binds: binds });
        } catch (err) {
            console.error(`Error in getScreenerSearchQuery: ${err}`);
            reject([424, err]);
        }
    });
}

module.exports = { getGlossaryDataQuery, getScreenerSearchQuery };
