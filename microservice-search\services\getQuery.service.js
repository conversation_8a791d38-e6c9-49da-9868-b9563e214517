
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getGlossaryDataQuery(lang, page, limit, filters, sortBy, searchTerm) {
    try {
        const binds = {};
        let bindCounter = 0;
        const offset = (page - 1) * limit;

        const whereClauses = [];

        if (searchTerm) {
            bindCounter++;
            binds[`term${bindCounter}`] = `%${searchTerm.toUpperCase()}%`;
            whereClauses.push(`(UPPER(TITLE_EN) LIKE {term${bindCounter}:String} OR UPPER(TITLE_AR) LIKE {term${bindCounter}:String})`);
        }

        Object.entries(filters).forEach(([key, value]) => {
            if (value.length > 0) {
                if (key === `TITLE_${lang}`) {
                    bindCounter++;
                    binds[`filter${bindCounter}`] = `${value[0].toUpperCase()}%`;
                    whereClauses.push(`UPPER(${key}) LIKE {filter${bindCounter}:String}`);
                } else if (Array.isArray(value)) {
                    const bindValues = value.map(val => val.toUpperCase());
                    bindCounter++;
                    binds[`filter${bindCounter}`] = bindValues;
                    whereClauses.push(`UPPER(${key}) IN ({filter${bindCounter}: Array(String)})`);
                } else {
                    bindCounter++;
                    binds[`filter${bindCounter}`] = value.toUpperCase();
                    whereClauses.push(`UPPER(${key}) = {filter${bindCounter}:String}`);
                }
            }
        });

        const sortByClauses = Object.entries(sortBy).map(([key, value]) => {
            return key === 'alphabetical' ? `upper(TITLE_${lang}) ${value.toUpperCase()}` : '';
        }).filter(clause => clause.length > 0);

        if (sortByClauses.length === 0) {
            sortByClauses.push(`upper(TITLE_${lang}) ASC`);
        }

        let query = `SELECT t.*, toUInt32(COUNT(*) OVER()) AS TOTAL FROM VW_IFP_GLOSSARY t ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''
            } ORDER BY ${sortByClauses.join(', ')} OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;

        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-glossary.services.getQuery.service.getGlossaryDataQuery with error ${err} `);
        throw err;
    }
}

async function getScreenerSearchQuery(searchTerm, screenerConfigurations, lang) {
    return new Promise((resolve, reject) => {
        try {
            let titleColumn = `INDICATOR_NAME_${lang}`;
            let domainColumn = `TOPIC_NAME_${lang === 'EN' ? 'ENGLISH' : 'ARABIC'}`;
            
            // Prepare the query with named parameters including types everywhere they are used
            let query = `
                SELECT
                    INDICATOR_ID,
                    ${titleColumn} AS INDICATOR_NAME,
                    ${domainColumn} AS TOPIC_NAME,
                    INDICATOR_TYPE,
                    ngramSearchCaseInsensitive(INDICATOR_NAME_EN, {searchTerm:String}) AS SIMILARITY
                FROM VW_INDICATOR_MAP
                JOIN IFP_SCREENER_VIEW_MAP 
                    ON VW_INDICATOR_MAP.SOURCE_TABLE = IFP_SCREENER_VIEW_MAP.VIEWNAME
                WHERE
                    SCREENER = 1
                    AND IFP_SCREENER_VIEW_MAP.ID IN ({screenerConfigurations:Array(UInt32)})
                    AND ngramSearchCaseInsensitive(INDICATOR_NAME_EN, {searchTerm:String}) > 0.9
                    AND UPPER(INDICATOR_NAME_EN) LIKE UPPER(concat('%', {searchTerm:String}, '%'))
                ORDER BY SIMILARITY DESC, INDICATOR_NAME_EN
                LIMIT 10;
            `;
            
            // Construct binds object with proper types
            let binds = {
                searchTerm: searchTerm,
                screenerConfigurations: screenerConfigurations.map(Number), // Ensure IDs are numbers
            };
            
            return resolve({ query: query, binds: binds });
        } catch (err) {
            console.error(`Error in getScreenerSearchQuery: ${err}`);
            reject([424, err]);
        }
    });
}

async function officialNodeSearchQuery(req, searchQuery) { 
    const user_groups = req.user.groups;
    const lang = req.headers["accept-language"] === 'en' ? '' : '_ar';

    // Base SELECT columns
    const commonColumns = `
        vdoi.indicator_id, vdoi.note, vdoi.enableCompare,
        vdoi.component_subtitle${lang} AS subtitle,
        vdoi.theme_id, vdoi.showLegend, vdoi.subtheme_id,
        vdoi.language, vdoi.showPointLabels, vdoi.yAxisFormat,
        vdoi.component_title${lang} AS title,
        vdoi.domain_id, vdoi.tooltipValueFormat, vdoi.product_id,
        vdoi.xAxisLabel, vdoi.publication_date,
        vdoi.theme${lang} AS theme,
        vdoi.product${lang} AS product,
        vdoi.subtheme${lang} AS subtheme,
        vdoi.narrative,
        vdoi.domain${lang} AS domain,
        vdoi.xAxisFormat, vdoi.domain_details,
        vdoi.label, vdoi.node_id,
        vdoi.data_source, vdoi.yAxisLabel,
        vdoi.name, vdoi.updated, vdoi.tooltipTitleFormat
    `;

    // Start query
    let query = `
        SELECT DISTINCT ${commonColumns}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS AS vdoi
        WHERE vdoi.group_id IN {group_ids:Array(String)} AND vdoi.is_screener !={is_screener:String}
    `;

    // Handle search filter
    if (req.headers["accept-language"] === 'ar') {
        query += ` AND vdoi.component_title_ar LIKE {searchQuery:String}`;
    } else {
        query += ` AND LOWER(vdoi.component_title) LIKE LOWER({searchQuery:String})`;
    }

    const binds = {
        group_ids: user_groups,
        is_screener: 1,
        searchQuery: `%${searchQuery}%`,  // LIKE requires wildcard
    };

    return { query, binds };
}

// Alternative version with multi-term search and advanced relevance scoring
async function officialNodeSearchQueryAdvanced(req, searchQuery) {
    const userGroups = req.user.groups;
    const isArabic = req.headers["accept-language"] === 'ar';
    const langSuffix = isArabic ? '_ar' : '';
    
    if (!userGroups?.length || !searchQuery?.trim()) {
        return { query: '', binds: {} };
    }
    
    // Normalize and validate search query
    const normalizedQuery = searchQuery.trim().toLowerCase().replace(/-/g, ' ');
    const searchTerms = normalizedQuery.split(/\s+/).filter(term => term.length > 0);
    
    // Limit search terms to prevent performance issues
    const maxTerms = 25;
    const limitedTerms = searchTerms.slice(0, maxTerms);
    
    // Pre-compute column definitions
    const columns = {
        title: `vdoi.component_title${langSuffix}`,
        subtitle: `vdoi.component_subtitle${langSuffix}`,
        theme: `vdoi.theme${langSuffix}`,
        product: `vdoi.product${langSuffix}`,
        subtheme: `vdoi.subtheme${langSuffix}`,
        domain: `vdoi.domain${langSuffix}`
    };

    // Build search conditions more efficiently
    const searchConditions = [];
    const relevanceConditions = [];
    const binds = {
        group_ids: userGroups,
        is_screener: 1,
        full_query: normalizedQuery, // Add full query for exact matching
        full_query_original: searchQuery.trim() // Original case for exact matching
    };

    // Check if query is numeric (potential ID search)
    const isNumericQuery = /^\d+$/.test(normalizedQuery);
    
    for (let i = 0; i < limitedTerms.length; i++) {
        const term = limitedTerms[i];
        const termParam = `searchTerm${i}`;
        binds[termParam] = term;
        
        // Optimize search conditions based on term characteristics
        const termConditions = [];
        
        // Prioritize exact ID matches for numeric queries
        if (isNumericQuery && limitedTerms.length === 1) {
            // Only for single numeric terms
            termConditions.push(`vdoi.node_id = {${termParam}:UInt64}`);
            termConditions.push(`vdoi.indicator_id = {${termParam}:String}`);
        } else {
            termConditions.push(`toString(vdoi.node_id) LIKE {${termParam}_wildcard:String}`);
            termConditions.push(`toString(vdoi.indicator_id) LIKE {${termParam}_wildcard:String}`);
        }
        
        // Text field searches
        const textFields = ['title', 'subtitle', 'domain', 'theme', 'subtheme', 'product'];
        textFields.forEach(field => {
            termConditions.push(`positionCaseInsensitive(toString(${columns[field]}), {${termParam}:String}) > 0`);
        });
        
        // Note field search
        termConditions.push(`positionCaseInsensitive(toString(vdoi.note), {${termParam}:String}) > 0`);
        
        searchConditions.push(`(${termConditions.join(' OR ')})`);
        
        // Add wildcard patterns to binds
        binds[`${termParam}_wildcard`] = `%${term}%`;
        binds[`${termParam}_prefix`] = `${term}%`;
    }
    
    // Enhanced relevance scoring - prioritize exact matches
    const exactMatchBonus = `
        (CASE 
            WHEN lower(toString(${columns.title})) = {full_query:String} THEN 10000
            WHEN toString(${columns.title}) = {full_query_original:String} THEN 9500
            ELSE 0
        END)
    `.replace(/\s+/g, ' ').trim();
    
    // Individual term scoring
    const termScores = [];
    for (let i = 0; i < limitedTerms.length; i++) {
        const termParam = `searchTerm${i}`;
        
        const termScore = `
            (CASE 
                WHEN toString(vdoi.node_id) = {${termParam}:String} THEN 1000
                WHEN toString(vdoi.node_id) LIKE {${termParam}_prefix:String} THEN 900
                WHEN positionCaseInsensitive(toString(${columns.title}), {${termParam}:String}) = 1 THEN 800
                WHEN positionCaseInsensitive(toString(${columns.title}), {${termParam}:String}) > 0 THEN 700
                WHEN positionCaseInsensitive(toString(${columns.subtitle}), {${termParam}:String}) > 0 THEN 600
                WHEN positionCaseInsensitive(toString(${columns.domain}), {${termParam}:String}) > 0 THEN 500
                WHEN positionCaseInsensitive(toString(${columns.theme}), {${termParam}:String}) > 0 THEN 400
                WHEN positionCaseInsensitive(toString(${columns.subtheme}), {${termParam}:String}) > 0 THEN 300
                WHEN positionCaseInsensitive(toString(${columns.product}), {${termParam}:String}) > 0 THEN 200
                WHEN positionCaseInsensitive(toString(vdoi.note), {${termParam}:String}) > 0 THEN 100
                ELSE 0
            END)
        `.replace(/\s+/g, ' ').trim();
        
        termScores.push(termScore);
    }
    
    // Calculate average term score (not sum) to avoid bias toward longer queries
    const avgTermScore = termScores.length === 1 
        ? termScores[0]
        : `(${termScores.join(' + ')}) / ${termScores.length}`;
    
    // Total relevance: exact match bonus + average term score
    const totalRelevanceScore = `(${exactMatchBonus} + ${avgTermScore})`;
    
    // Combine search conditions
    const allSearchConditions = searchConditions.join(' AND ');
    
    // Optimized column selection
    const coreColumns = [
        'vdoi.indicator_id', 'vdoi.note', 'vdoi.enableCompare',
        'vdoi.theme_id', 'vdoi.showLegend', 'vdoi.subtheme_id',
        'vdoi.language', 'vdoi.showPointLabels', 'vdoi.yAxisFormat',
        'vdoi.domain_id', 'vdoi.tooltipValueFormat', 'vdoi.product_id',
        'vdoi.xAxisLabel', 'vdoi.publication_date', 'vdoi.narrative',
        'vdoi.xAxisFormat', 'vdoi.domain_details', 'vdoi.label',
        'vdoi.node_id', 'vdoi.data_source', 'vdoi.yAxisLabel',
        'vdoi.name', 'vdoi.updated', 'vdoi.tooltipTitleFormat'
    ].join(', ');
    
    const langColumns = [
        `${columns.title} AS title`,
        `${columns.subtitle} AS subtitle`, 
        `${columns.theme} AS theme`,
        `${columns.product} AS product`,
        `${columns.subtheme} AS subtheme`,
        `${columns.domain} AS domain`
    ].join(', ');

    // Optimized query with proper relevance scoring
    const query = `
        SELECT DISTINCT 
            ${coreColumns},
            ${langColumns},
            ${totalRelevanceScore} AS relevance_score
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS AS vdoi
        WHERE vdoi.group_id IN {group_ids:Array(String)}
            AND toInt32(vdoi.is_screener) != {is_screener:Int32}
            AND (${allSearchConditions})
        ORDER BY relevance_score DESC, ${columns.title}
        LIMIT {limit:UInt16}
    `.trim();

    // Add limit to binds for better query plan caching
    binds.limit = 1000;
    
    return { query, binds };
}

module.exports = {
    getGlossaryDataQuery,
    getScreenerSearchQuery,
    officialNodeSearchQuery,
    officialNodeSearchQueryAdvanced
};
