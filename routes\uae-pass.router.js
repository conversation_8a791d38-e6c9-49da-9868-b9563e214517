const express = require("express");
const Logger = require("scad-library").logger;

const router = new express.Router();
const log = new Logger().getInstance();

const {
  getUserInfoController,
  getAccessTokenController,
} = require("../microservice-uae-pass/uae-pass.controller");

router.get("/user-info", async (req, res, next) => {
  try {
    const data = await getUserInfoController(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for microservice-uae-pass getUserInfoController, ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/access-token", async (req, res, next) => {
  try{
    const data = await getAccessTokenController(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();  
  } catch (err) {
    log.error(
      `Error fetching data for microservice-uae-pass getAccessTokenController, ERROR: ${err}`
    );
    next(err);
  }
});

module.exports = router;
