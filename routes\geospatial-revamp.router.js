const express = require('express');
const router = new express.Router();
const geospatialRevampController = require('../microservice-geospatial-revamp/geospatial-revamp.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/regions', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getRegionsFilters(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/allFilters', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getAllFilters(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/summary', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getSummary(req);
    res.set('Content-Type', 'application/json');
    res.send(data); 
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});
 
router.post('/summaryPopulation', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getPopulationSummary(req);
    res.send(data);
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/summaryLaborForce', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getLaborForceSummary(req);
    res.send(data);
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/summaryRealEstate', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getRealEstateSummary(req);
    res.send(data);
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});



module.exports = router;
