const express = require('express');
const router = new express.Router();
const geospatialRevampController = require('../microservice-geospatial-revamp/geospatial-revamp.controller');
const { validateGeospatialPermissions } = require('./validators/geospatial-revamp.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

router.get('/domains', async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getGeospatialDomains(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/regions', validateGeospatialPermissions, async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getRegionsFilters(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/allFilters', validateGeospatialPermissions, async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getAllFilters(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/summary', validateGeospatialPermissions, async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getSummary(req);
    res.set('Content-Type', 'application/json');
    res.send(data); 
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});
 
router.post('/summaryPopulation', validateGeospatialPermissions, async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getPopulationSummary(req);
    res.send(data);
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/summaryLaborForce', validateGeospatialPermissions, async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getLaborForceSummary(req);
    res.send(data);
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/summaryRealEstate', validateGeospatialPermissions, async (req, res, next) => {
  try {
    const data = await geospatialRevampController.getRealEstateSummary(req);
    res.send(data);
    next(); 
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});



module.exports = router;
