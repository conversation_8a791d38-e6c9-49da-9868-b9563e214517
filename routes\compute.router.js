const express = require('express');
const router = new express.Router();
const computeController = require('../microservice-compute/compute.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { validateCompute } = require('./validators/compute.validator');

router.post('/',validateCompute, async (req, res, next) => {
    try {
        const data = await computeController.computeData(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for compute by id content-type, ERROR: ${err}`);
        next(err);
    }
});

module.exports = router;
