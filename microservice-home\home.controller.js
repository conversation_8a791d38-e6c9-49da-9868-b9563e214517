const Logger = require('scad-library').logger;
require('dotenv').config();
const { getMetaFromCMS } = require('../services/common-service');
const log = new Logger().getInstance();
const constants = require('../config/constants.json');

async function getHome(req) {
  log.debug(`>>>>>Entered microservice.whatsnew.controller.getHome`);
  try {
      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      const cmsHome = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_HOME_URL}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      
      let data = await getMetaFromCMS(req,cmsLoginUrl, cmsHome, req.user.groups);
      return data;
  } catch (err) {
      log.error(`<<<<< Exited microservice.whatsnew.controller.getWhatsNew with error ${err} `)
      throw err;
  }
}

module.exports = {
  getHome
}
