const AzureOpenAI = require("openai").AzureOpenAI;
const zodResponseFormat = require("openai/helpers/zod").zodResponseFormat;
const z = require("zod");
const Logger = require("scad-library").logger;
const axios = require("axios");
require("dotenv").config();
const log = new Logger().getInstance();
const pgModels = require("../../database/postgres/models");
const config = require("../ai-insight-report.constants.json")[
  process.env.NODE_ENV || "dev"
];

const client = new AzureOpenAI({
  apiKey: config.AZURE_OPENAI_API_KEY,
  endpoint: config.AZURE_OPENAI_BASE_URL,
  deploymentName: config.AZURE_OPENAI_MODEL_NAME,
  apiVersion: config.AZURE_OPENAI_API_VERSION,
});

// Marker schema
const marker = z.object({
  fillColor: z.string(),
  lineWidth: z.number(),
  lineColor: z.string(),
  symbol: z.string(),
  enabled: z.boolean(),
});

// CustomChart schema
const customChart = z.object({
  name: z.string(),
  data: z.array(z.number()), // Data for chart
  dashStyle: z.enum(["Solid", "Dash"]), // "Solid" or "Dash"
  type: z.enum(["line", "bar"]), // "line" or "bar"
  zIndex: z.number(),
  lineWidth: z.number(),
  color: z.string(),
  marker: marker,
  category: z.array(z.string()),
  yAxisLabel: z.string(),
  xAxisLabel: z.string(),
});

// ChartConfig schema
const chartConfig = z.object({
  charts: z.array(customChart), // This is mandatory
  isTimePeriod: z.boolean(),
  dashboardTitle: z.string(),
});

// RowObject schema
const rowObject = z.object({
  indicator: z.string(),
  previous_value: z.number(),
  latest_value: z.number(),
  percentage_change: z.number(),
  percentage_change_progress: z.boolean(), // This indicates whether the value shows a positive trend, only applicable for numbers.
  current_period: z.string(),
  previous_period: z.string(),
});

// Row schema
const Row = z.object({
  row: rowObject,
});

// DataModel schema
const dataModel = z.object({
  rows: z.array(rowObject), // Row objects in a table
  show_progress: z.boolean(), // Indicates whether to show the progress icon or not. Depends on the progress key in rows.
});

// Drivers schema
const drivers = z.object({
  indicator_name: z.string(), // This should be the official indicator name. Use the same indicator name as in the input.
  heading: z.string(),
  value: z.number(), // This is the percentage change compared to previous time period.
  unit: z.string(), // Unit of above value
  whether_positive_trend: z.boolean(), // This indicates whether the key value is positive or not.
  curr_period: z.string(), // latest period of data
  pre_period: z.string(), // second latest period of data
});

// KeyDrivers schema
const keyDrivers = z.object({
  ai_summary: z
    .string()
    .describe("This should be a summary of the key drivers"),
  drivers: z.array(drivers).describe("This should be 4 key drivers"),
});

// Impact schema
const impact = z.object({
  heading: z.string().describe("The heading of the essay in key text"), // The heading of the essay in key text.
  text: z
    .string()
    .describe("This should be a  detailed analysis for impact section "), // This is detailed analysis. Make this similar to how SME does an analysis for an executive report. Make this 2-3 paragraphs, like a 1000-word essay. This is a must.
  chart: chartConfig, // This is mandatory
});

// Insight schema
const insight = z.object({
  heading: z.string().describe("The heading of the essay in key text"), // The heading of the essay in key text.
  text: z
    .string()
    .describe("This should be a  detailed analysis for Key Insights"), // This is detailed analysis. Make this similar to how SME does an analysis for an executive report. Make this 2-3 paragraphs, like a 1000-word essay. This is a must.
  chart: chartConfig, // This is mandatory
});

// NegativeImpact schema
const negativeImpact = z.object({
  status_name: z.literal("Negative"),
  value: z.number(), // Value of negative percent of sentiment.
  color: z.literal("E33C1A"),
  key: z.literal("NEG"),
});

// NeutralImpact schema
const neutralImpact = z.object({
  status_name: z.literal("Neutral"),
  value: z.number(), // Value of neutral percent of sentiment.
  color: z.literal("F5AB34"),
  key: z.literal("NEU"),
});

// PositiveImpact schema
const positiveImpact = z.object({
  status_name: z.literal("Positive"),
  value: z.number(), // Value of positive percent of sentiment.
  color: z.literal("81B76A"),
  key: z.literal("POS"),
});

// ImpactStatus schema
const impactStatus = z.object({
  negative: negativeImpact,
  neutral: neutralImpact,
  positive: positiveImpact,
});

// Sentiment schema
const sentiment = z.object({
  title: z.string(),
  overall_impact: z.enum(["Neutral Impact","Positive Impact","Negative Impact"]),
  data_source: z.string(),
  meta_data: z.string(),
  key: z.enum(["NEU", "POS", "NEG"]), // NEU - Neutral, POS - Positive, NEG - Negative
  overall_impact_status: impactStatus, // This should add up to 100
  key_insights_desc: z
    .string()
    .describe("This should be a  detailed analysis for sentiment section "), // This is detailed analysis. Make this similar to how SME does an analysis for an executive report. Make this 2-3 paragraphs, like a 1000-word essay. This is a must.
  chart: chartConfig, // This is mandatory
});

// All schema
const all = z.object({
  key_drivers: keyDrivers,
  impact: impact,
  key_insights: z
    .array(insight)
    .describe(
      "Always Keep 4 distinct Insights. The key key_insights should have 4 elements."
    ),
  comparison: dataModel,
  sentiment: sentiment,
});

const insights = z.object({
  insights: z.array(insight).describe("This should be 4 insights"),
});

const INSTRUCTION_TEMPLATE = `
  From the provided list of indicators, correlate and analyze them thoroughly along with their inter-relationships to generate a comprehensive Quarterly Executive Report. The report must adhere to the provided structure and following guidelines:
  You will be provided the SV Data (Official Statistics), Bloomberg Data, Web Data, and Sentiment Data.
  
  1. **Impact Section:**
     - Provide a detailed, SME-level, executive analysis that is in-depth and insightful.
     - The analysis should be extensive, with a minimum of 400 words, clearly outlining the impact of the indicators on overall performance and strategic direction.
  
  2. **Key Drivers**
     - Use the original SV name (STAT_VALUE_NAME_EN) for the indicator title. For the heading, use STAT_VALUE_NAME_EN_MODIFIED.
     - Only use data from Official SVs here.
     - For the value key, infer the value from key PCT_CHANGE for each indicator in the provided SV data. The unit is percentage.
  
  3. **Insights:**
     - Include exactly 4 Key Drivers and exactly 4 Insights in the report.
     - Each Key Driver and Insight should be clearly identified and elaborated, ensuring that they are directly supported by the analysis of the indicators.
     - Always include charts.
  
  4. **Comparison Section with Data Model Alignment:**
     - Design a structured comparison table with at least **four columns** and **three rows** to ensure clarity.

  5. Properly Give each part of the schema.

  6. Data contains data for chart in array
  The user comments are : {{user_comments}}
  `;

async function regenerate(part, user_comments, reportData) {
  try {
    const INSTRUCTION = INSTRUCTION_TEMPLATE.replace(
      "{{user_comments}}",
      user_comments
    );
    let reportPart;
    let dataContent;
    const instructionData = pgModels.InstructionRegeneration
      ? await pgModels.InstructionRegeneration.findOne({
          where: {
            object_id: reportData.object_id,
          },
        })
      : null;
    switch (part) {
      case "comparison":
        reportPart = dataModel;
        dataContent = instructionData?.exp_content;
        break;
      case "key_drivers":
        reportPart = keyDrivers;
        dataContent = instructionData?.main_content;
        break;
      case "impact":
        reportPart = impact;
        dataContent = instructionData?.full_content;
        break;
      case "key_insights":
        reportPart = insights;
        dataContent = instructionData?.full_content;
        break;
      case "sentiment":
        reportPart = sentiment;
        dataContent = instructionData?.full_content;
        break;
      case "all":
        reportPart = all;
        dataContent = instructionData?.full_content;
        break;
      default:
        throw new Error("Invalid part");
    }

    const completion = await client.beta.chat.completions.parse({
      model: "gpt-4o-2024-08-06",
      messages: [
        { role: "system", content: INSTRUCTION },
        { role: "user", content: dataContent },
      ],
      response_format: zodResponseFormat(reportPart, "provided"),
    });

    const event = completion.choices[0].message.parsed;
    if (part == "key_insights") {
      return event.insights;
    } else if (part === "all") {
      // regenerate key_drivers and comparison sections with main_content and exp_content respectively
      const completionkeyDrivers = await client.beta.chat.completions.parse({
        model: "gpt-4o-2024-08-06",
        messages: [
          { role: "system", content: INSTRUCTION },
          { role: "user", content: instructionData?.main_content },
        ],
        response_format: zodResponseFormat(keyDrivers, "provided"),
      });

      const eventkeyDrivers = completionkeyDrivers.choices[0].message.parsed;

      const completionDataModel = await client.beta.chat.completions.parse({
        model: "gpt-4o-2024-08-06",
        messages: [
          { role: "system", content: INSTRUCTION },
          { role: "user", content: instructionData?.exp_content },
        ],
        response_format: zodResponseFormat(dataModel, "provided"),
      });

      const eventComparison = completionDataModel.choices[0].message.parsed;

      // replace key_drivers and comparison sections in the event object
      event.key_drivers = eventkeyDrivers;
      event.comparison = eventComparison;
      return event;
    } else {
      return event;
    }
  } catch (error) {
    log.error("Error in regeneration:", error);
    throw error; // Rethrow the error after logging it
  }
}
//console.log(JSON.stringify(await regenerate("key_insights", "Make sure to add more analysis")))

module.exports = { regenerate }; // Export the regenerate function for use in other modules.
