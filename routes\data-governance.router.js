const express = require('express');
const router = new express.Router();
const dataGovernanceController = require('../microservice-data-governance/data-governance.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const jsonToCsv = require('../microservice-data-governance/helper/jsonToCsv.helper');

router.get('/', async (req, res, next) => {
    try {
      const data = await dataGovernanceController.getDataGovernanceChart(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {  
      log.error(`Error fetching data for data governance chart, ERROR: ${err}`);
      next(err);
    }
  });


// new routes //
router.get('/data-science-input', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getDataSceinceInput(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for data-science-input, ERROR: ${err}`);
    next(err);
  }
});

router.get('/data-science-output', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getDataSceinceOutput(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for data-science-output, ERROR: ${err}`);
    next(err);
  }
});
router.post('/use-case', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getUseCaseTableData(req);
    
    // Check if export_type is csv
    if (req.body.export_type === 'csv' || req.query.export_type === 'csv') {
      // Convert JSON to CSV
      const csvData = jsonToCsv(data.tableData.data);
      const filename = data.tableData.title.replace(/\s+/g, '_').toLowerCase() + '.csv';
      
      // Set headers for file download
      res.set({
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=' + filename,
        'Content-Length': Buffer.byteLength(csvData)
      });
      res.send(csvData);
    } else {
      // Default JSON response
      res.set('Content-Type', 'application/json');
      res.send(data);
    }
    
    next();
  } catch (err) {  
    log.error(`Error fetching data for use-case, ERROR: ${err}`);
    next(err);
  }
});

router.get('/data-source', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getDataSourceTableData(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for data-source, ERROR: ${err}`);
    next(err);
  }
});

router.get('/data-science', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getDatascienceTableData(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for data-science, ERROR: ${err}`);
    next(err);
  }
});

router.get('/workflow-details', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getWorkflowDetailsTableData(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for workflow-details, ERROR: ${err}`);
    next(err);
  }
});


router.post('/bayaan-svs', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getBayaanSvsTableData(req);

    // Check if export_type is csv
    if (req.body.export_type === 'csv' || req.query.export_type === 'csv') {
      // Convert JSON to CSV
      const csvData = jsonToCsv(data.tableData.data);
      const filename = data.tableData.title.replace(/\s+/g, '_').toLowerCase() + '.csv';
      
      // Set headers for file download
      res.set({
        'Content-Type': 'text/csv',
        'Content-Disposition': 'attachment; filename=' + filename,
        'Content-Length': Buffer.byteLength(csvData)
      });
      res.send(csvData);
    } else {
      // Default JSON response
      res.set('Content-Type', 'application/json');
      res.send(data);
    }
    next();
  } catch (err) {  
    log.error(`Error fetching data for bayaan-svs, ERROR: ${err}`);
    next(err);
  }
});

router.post("/statistical-indicator", async (req, res, next) => {
  try {
      const data = await dataGovernanceController.getStatisticalIndicatorTableData(req);
      // Check if export_type is csv
      if (req.body.export_type === 'csv' || req.query.export_type === 'csv') {
        // Convert JSON to CSV
        const csvData = jsonToCsv(data.tableData.data);
        const filename = data.tableData.title.replace(/\s+/g, '_').toLowerCase() + '.csv';

        // Set headers for file download
        res.set({
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=' + filename,
          'Content-Length': Buffer.byteLength(csvData)
        });
        res.send(csvData);
      } else {
        // Default JSON response
        res.set('Content-Type', 'application/json');
        res.send(data);
      }
      next();
  } catch (err) {
      log.error(
      `Error fetching data statistical-indicator ERROR: ${err}`
      );
      next(err);
  }
});

router.get('/comparison', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getComparisonChartTableData(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for comparison, ERROR: ${err}`);
    next(err);
  }
});

router.get('/use-case-dropdown', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getUseCaseDropDownOptions(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for use-case-dropdown, ERROR: ${err}`);
    next(err);
  }
});

router.get('/data-source-dropdown', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getDataSoureDropDownOptions(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for data-source-dropdown, ERROR: ${err}`);
    next(err);
  }
});

router.get('/data-science-dropdown', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getDataScienceDropDownOptions(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for data-science-dropdown, ERROR: ${err}`);
    next(err);
  }
});

router.get('/workflow-details-dropdown', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getWorkflowDetailsDropDownOptions(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for workflow-details-dropdown, ERROR: ${err}`);
    next(err);
  }
});

router.get('/bayaan-svs-dropdown', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getBayaanSvsDropDownOptions(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for bayaan-svs-dropdown, ERROR: ${err}`);
    next(err);
  }
});

router.get('/statistical-indicator-dropdown', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getStatisticalIndicatorDropDownOptions(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for statistical-indicator-dropdown, ERROR: ${err}`);
    next(err);
  }
});

router.get('/sv-calender', async (req, res, next) => {
  try {
    const data = await dataGovernanceController.getCalenderData(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {  
    log.error(`Error fetching data for sv calender data, ERROR: ${err}`);
    next(err);
  }
});

router.post("/event-detail", async (req, res, next) => {
  try {
      const data = await dataGovernanceController.getSvEventDetail(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
  } catch (err) {
      log.error(
      `Error fetching data statistical-indicator ERROR: ${err}`
      );
      next(err);
  }
});


module.exports = router;


