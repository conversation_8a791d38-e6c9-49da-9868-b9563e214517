const crypto = require('crypto')
const Logger = require("scad-library").logger;
const { getMetaFromCMS } = require("../../services/common-service");
const constants = require("../../config/constants.json");
const { setRedis, getRedis } = require("../../services/redis.service");

/**
 * DashboardService class provides utility methods related to the dashboard functionality.
 */
class DashboardService {
	constructor() {
		this.log = new Logger().getInstance();
	}

	/**
	 * Retrieves the language code from the request headers. If not provided, defaults to 'en'.
	 * @param {Object} req - The request object containing headers.
	 * @returns {string} The language code extracted from the request headers.
	 */
	getLangCode(req) {
		return req.headers["accept-language"] || "en";
	}

	/**
	 * Constructs a CMS URL based on the language and content ID.
	 * @param {string} lang - The language code.
	 * @param {string} contentId - The content ID.
	 * @returns {string} The constructed CMS URL.
	 */
	constructCMSUrl(lang, contentId) {
		// Get the base URL for the CMS, using an empty string as default if not provided
		const cmsBaseUrl = process.env.CMS_BASEPATH || "";

		// Determine the language segment based on the language code
		const langSegment = lang === "en" ? "" : `/${lang}`;

		// Construct and return the CMS URL using the base URL, language segment, and content ID
		return `${cmsBaseUrl}${langSegment}${constants.cmsGroupUrl.CMS_ANALYTICAL_APPS_BYID}${contentId}`;
	}

	/**
	 * Fetches data from the CMS for the dashboard.
	 * @param {Object} req - The request object.
	 * @param {string} cmsUrl - The CMS URL.
	 * @returns {Promise<Object>} The meta data from the CMS for the dashboard.
	 */
	async fetchCMSData(req, cmsUrl) {
		const cmsBaseUrl = process.env.CMS_BASEPATH || "";
		const cmsLoginUrl = `${cmsBaseUrl}${constants.cmsUrl.CMS_LOGIN_URL}`;
		const userGroups = req.user.groups;

		let data = await getMetaFromCMS(req, cmsLoginUrl, cmsUrl, userGroups, "", req);

		if (Array.isArray(data) && typeof data.length !== undefined) {
			if (data.length === 1) {
				data = data[0];
			}
		}

		// Validate type
		if (data.tagKey !== "dashboard") {
			return {};
		}

		return data;
	}

	/**
	 * Checks the cache for the given cache key and returns the cached results if found.
	 * @param {string} cacheKey - The cache key.
	 * @param {Object} headers - The request headers.
	 * @returns {Promise<Object|null>} The cached results if found, otherwise null.
	 */
	async getCache(cacheKey, headers) {
		const cmsCacheResults = await getRedis(cacheKey, headers);
		if (cmsCacheResults) {
			this.log.info(
				`<<<<<Cache found for microservice-statistics-insights.getDashboardById`
			);
			return JSON.parse(cmsCacheResults);
		}
		return null;
	}

	/**
	 * Sets data in the cache with the provided cache key, response, and headers.
	 * @param {string} cacheKey - The cache key to store the response.
	 * @param {Object} response - The response object to be cached.
	 * @param {Object} headers - The request headers.
	 * @param {number} ttl - The time-to-live (TTL) for the cached data. Defaults to the CMS response TTL.
	 * @returns {Promise<null>} A promise that resolves with null.
	 */
	async setCache(
		cacheKey,
		response,
		headers,
		ttl = constants.redis.cmsResponseTTL
	) {
		// Store the response in the cache
		setRedis(cacheKey, JSON.stringify(response), ttl, headers);

		// Log cache setting information
		this.log.info(`<<<<<Cache set ${cacheKey}`);

		// Return null indicating successful cache setting
		return null;
	}

	/**
	 * Generates a cache key based on the content ID and CMS response.
	 * @param {string} contentId - The content ID.
	 * @param {Object} cmsResponse - The CMS response object.
	 * @param {Object} filters - The query filters.
	 * @returns {string} The generated cache key.
	 */
	generateCacheKey(contentId, cmsResponse, filters={}) {
		const combinedData = JSON.stringify(cmsResponse) + JSON.stringify(filters);
		return `cmsMetaStatisticsInsights_${contentId}_${crypto
			.createHash("md5")
			.update(combinedData)
			.digest("hex")}`;
	}
}

module.exports = DashboardService;
