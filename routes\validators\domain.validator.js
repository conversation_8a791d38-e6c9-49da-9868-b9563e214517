const { IFPError } = require("../../utils/error")

const validateDomainDetail = (req,res,next)=>{
    if (isNaN(req.params.id))
        throw new IFPError(400,'Please provide a valid domain id')

    if (Number(req.query.page)==0)
        req.query.page=1

    if (req.query.page){
        if (Number(req.query.page)){
            if (Number(req.query.page)<0)
                throw new IFPError(400,`${req.query.page} is not a valid page number`)
        }
        else
            throw new IFPError(400,`${req.query.page} is not a valid page number`)
    }

    if (req.query.limit){
        if (Number(req.query.limit)){
            if (Number(req.query.limit)<0){
                throw new IFPError(400,`${req.query.limit} is not a valid limit number`)
            }
        }
        else
            throw new IFPError(400,`${req.query.limit} is not a valid limit number`)
    }

    
    next()
}

module.exports = {
    validateDomainDetail
}