const scadLib = require('scad-library');
const util = scadLib.util;

const Logger = scadLib.logger;
const log = new Logger().getInstance();
/**
 * Function to process data for sunburst chart according to how the data structure provided by UI
 * @param {*} seriesData object
 * @param {*} visualization object
 * @param {*} treeData object
 */
const getTreeSeries = async (visualization, seriesData) => {
    //adding data array inside each series

    return new Promise((resolve, reject) => {
        try {
            visualization.seriesMeta.forEach(series => {
                series["data"] = [];
            });
            log.debug(`>>>>> Enter services.chart-services.tree-map-with-change-chart.getTreeSeries`);
            if (seriesData && seriesData.length > 0) {
                /*formatting data received from db by mapping id to values from seriesData 
                based on dimension object provided within json through CMS*/
                seriesData.forEach(element => {
                    if (element) {
                        element.OBS_DT = util.convertDate(`${element.OBS_DT}`);
                        visualization.seriesMeta.forEach(series => {
                            let count = [];
                            let dimensionLength = Object.keys(series.dimension).length;
                            Object.entries(series.dimension).forEach(([column, value]) => {
                                let elementValue = element[column] != null ? element[column].toUpperCase().trim() : "";
                                if (value.toUpperCase() === elementValue) {
                                    count.push(1);
                                }
                            });
                            if (count.length === dimensionLength) {
                                Object.entries(element).forEach(([key, value]) => {
                                    if (value === "" || value === null || key === 'INSERT_DT' || key === 'INSERT_USER_ID') {
                                        delete element[key];
                                    }
                                });
                                series.data.push(element);
                            }
                        })
                    } else {
                        log.error(`Received null value in db result`);
                    }
                })
            }            
            resolve(visualization);
        } catch (err) {
            
            log.error(`<<<<< Exit services.chart-services.tree-map-with-change-chart.getTreeSeries with error ${err}`);
            reject([422,err]);
        }

    })

}


module.exports = { getTreeSeries }
