const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const messages = require('./messages')

async function sendSLAEmail(data) {
    new Promise((resolve, reject) => {

        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });

        let externalFullname;
        let setExternalName;

        externalFullname = data.email.toLowerCase().split('@')[0];
        setExternalName = externalFullname.split('.');
        if (setExternalName.length > 1) {
            setExternalName = setExternalName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
        }

        var externalMailOptionsResponse = {
            from: process.env.SYSTEM_MAILID,
            to: data.email,
            subject: `Service Level Agreement Expiration`,
            attachments: [
                {
                    filename: 'IFP_logo.png',
                    path: __dirname + '/images/IFP_logo.png',
                    cid: 'ifp-logo'
                },
                {
                    filename: 'SCAD_logo.png',
                    path: __dirname + '/images/SCAD_logo.png',
                    cid: 'scad-logo'
                },
            ],
            html: messages.externalSLAExpirationMessage(data)
        };

        transporter.sendMail(externalMailOptionsResponse, function (error, info) {
            if (error) {
                reject(error);
            }

            log.info(`Email sent successfully to ${data.email} \n${info.response}`);
            resolve();
        })
    });
}

module.exports = {sendSLAEmail}