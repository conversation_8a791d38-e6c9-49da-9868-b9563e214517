const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const clkTables = require('../../config/constants.json').clickhouseTables;
const {
  getPopStatsDataQuery, getStatNodesDataQuery, getScreenerCountQuery, getSingleDimensionNodesQuery } = require('./getQuery.service');

/**
 * Function to get popular stats
 * @param {*} nodeIds - Node IDs 
 */
async function getPopStatsData(nodeIds) {
  try {
    const query = await getPopStatsDataQuery(nodeIds);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getPopStatsData with error ${err}`);
    throw err;
  }
}

async function getScreenerCount(screenerConfiguration) {
  try {
    const result = await getScreenerCountQuery(screenerConfiguration);
    const data = await getData(result.query, result.binds);
    return data[0]['COUNT'];
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getScreenerCount with error ${err}`);
    throw err;
  }
}

async function getSingleDimensionNodes(nodes) {
  try {
    const result = await getSingleDimensionNodesQuery(nodes);
    const data = await getClkData(result.query, result.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-domains.executeQuery.service.getSingleDimensionNodes with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    const containsClView = clkTables.some(view => query.includes(view));
    const executeFunction = containsClView ? clkdb.simpleExecute : db.simpleExecute;
    const data = await executeFunction(query, binds);
    log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getClkData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    const data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}


module.exports = {
  getPopStatsData,
  getScreenerCount,
  getSingleDimensionNodes
};
