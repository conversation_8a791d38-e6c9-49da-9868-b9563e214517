const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const clkTables = require('../../config/constants.json').clickhouseTables;
const {
	getPopStatsDataQuery,
	getScreenerCountQuery,
	getSingleDimensionNodesQuery,
	getSingleDimensionNodesQueryV3,
	getNodeBasicDeatailsQuery,
	getScreenerDeatailsQuery,
	getRankDataQuery,
	getDomainContentListQuery,
	getDomainContentCountQuery,
	getDomainContentProductListQuery,
	getDomainFilteredQuery,
	getDomainContentFiltersQuery,
	getDomainOfficialStatisticsCountQuery,
} = require("./getQuery.service");

/**
 * Function to get popular stats
 * @param {*} nodeIds - Node IDs 
 */
async function getPopStatsData(nodeIds) {
  try {
    const query = await getPopStatsDataQuery(nodeIds);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getPopStatsData with error ${err}`);
    throw err;
  }
}

async function getScreenerCount(screenerConfiguration) {
  try {
    const result = await getScreenerCountQuery(screenerConfiguration);
    const data = await getData(result.query, result.binds);
    return data[0]['COUNT'];
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getScreenerCount with error ${err}`);
    throw err;
  }
}

async function getSingleDimensionNodes(nodes) {
  try {
    const result = await getSingleDimensionNodesQuery(nodes);
    const data = await getData(result.query, result.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getSingleDimensionNodes with error ${err}`);
    throw err;
  }
}

async function getSingleDimensionNodesV3(indicators) {
  try {
    const result = await getSingleDimensionNodesQueryV3(indicators);
    const data = await getData(result.query, result.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-myapps.executeQuery.service.getSingleDimensionNodes with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    const containsClView = clkTables.some(view => query.includes(view));
    const executeFunction = containsClView ? clkdb.simpleExecute : db.simpleExecute;
    const data = await executeFunction(query, binds);
    log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getIndicatorData(req) {
  try {
    let {query, binds} = await getNodeBasicDeatailsQuery(req);
    let data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited services.executeQuery.service.revokeRefreshToken with error ${err}`);
    throw err;
  }
}

async function getScreenerData(subThemeIds){
  try{
    let {query, binds} = await getScreenerDeatailsQuery(subThemeIds);
    let data = await clkdb.simpleExecute(query, binds);
    return data;

  }catch(err){
    log.error(`<<<<< Exited services.executeQuery.service.revokeRefreshToken with error ${err}`);
    throw err;
  }
}

async function getRankData(id,key){
  try{
    let {query,binds} = await getRankDataQuery(id,key);
    let data = await clkdb.simpleExecute(query, binds);
    return data;

  }catch(err){
    log.error(`<<<<< Exited services.executeQuery.service.revokeRefreshToken with error ${err}`);
    throw err;
  }
}

/**
 * Retrieves a list of domain content based on the provided filters.
 *
 * @async
 * @function getDomainContentList
 * @param {string|null} [domainId=null] - The ID of the domain to filter content by. If null, no domain filter is applied.
 * @param {string|null} [subdomainId=null] - The ID of the subdomain to filter content by. If null, no subdomain filter is applied.
 * @param {Array<string>} [userGroups=[]] - An array of user group identifiers to filter accessible content.
 * @param {number} [limit=10] - The maximum number of content items to return.
 * @param {number} [offset=0] - The number of items to skip before starting to collect the result set.
 * @param {string} [lang='en'] - The language code for the content. Default is 'en'.
 * @returns {Promise<Array<Object>>} Resolves to an array of domain content objects matching the filters.
 * @throws Will throw an error if the query execution fails.
 *
 * @example
 * const contentList = await getDomainContentList('3', null, ['a11dfv-dfvfddf-dfv...'], 10, 0);
 */
async function getDomainContentList(
  domainId = null,
  subdomainId = null,
  subthemeId = null,
  productID = null,
  search = null,
  userGroups = [],
  limit = 10,
  offset = 0,
  lang = "en",
  data_security = null
) {
  try {
    const { query, binds } = await getDomainContentListQuery(
      domainId,
      subdomainId,
      subthemeId,
      productID,
      search,
      userGroups,
      limit,
      offset,
      lang,
      data_security
    );
    return await getData(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-domains.executeQuery.service.getDomainContentList with error ${err}`
    );
    throw err;
  }
}

/**
 * Retrieves the count of domain content based on the provided domain, subdomain, and user groups.
 *
 * @async
 * @function getDomainContentCount
 * @param {string|null} [domainId=null] - The unique identifier of the domain. If null, counts for all domains.
 * @param {string|null} [subdomainId=null] - The unique identifier of the subdomain. If null, counts for all subdomains.
 * @param {Array<string>} [userGroups=[]] - An array of user group identifiers to filter the content count.
 * @returns {Promise<number>} The count of domain content matching the criteria.
 * @throws {Error} Throws an error if the query execution fails.
 *
 * @example
 * // Get content count for a specific domain and user groups
 * const count = await getDomainContentCount('domain123', null, ['groupA', 'groupB']);
 */
async function getDomainContentCount(
	domainId = null,
	subdomainId = null,
  subthemeId = null,
  productID = null,
  search = null,
	userGroups = [],
  lang = "en",
  data_security = null
) {
	try {
		const { query, binds } = await getDomainContentCountQuery(
			domainId,
			subdomainId,
      subthemeId,
      productID,
      search,
			userGroups,
      lang,
      data_security
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-domains.executeQuery.service.getDomainContentCount with error ${err}`
		);
		throw err;
	}
}
/**
 * Retrieves a list of content products for a given domain and subdomain, filtered by user groups.
 *
 * This function constructs and executes a query to fetch content product data associated with the specified
 * domain and subdomain. The results can be further filtered based on the provided user groups.
 *
 * @async
 * @function getDomainContentProductList
 * @param {?number} [domainId=null] - The ID of the domain to filter content products by. If null, no domain filter is applied.
 * @param {?number} [subdomainId=null] - The ID of the subdomain to filter content products by. If null, no subdomain filter is applied.
 * @param {Array<string>} [userGroups=[]] - An array of user group identifiers to filter the content products.
 * @param {string} [lang='en'] - The language code for the content products. Default is 'en'.
 * @returns {Promise<Array<Object>>} Resolves to an array of content product objects matching the filters.
 * @throws {Error} Throws an error if the query execution fails.
 *
 * @example
 * const products = await getDomainContentProductList(1, 2, ['admin', 'editor']);
 */
async function getDomainContentProductList(
	domainId = null,
	subdomainId = null,
  subthemeId = null,
	userGroups = [],
  lang = "en",
) {
	try {
		const { query, binds } = await getDomainContentProductListQuery(
			domainId,
			subdomainId,
      subthemeId,
			userGroups,
			lang
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-domains.executeQuery.service.getDomainContentProductList with error ${err}`
		);
		throw err;
	}
}

async function getDomainContentFilters(
	domainId = null,
	userGroups = [],
	lang = "en"
) {
	try {
		const { query, binds } = await getDomainContentFiltersQuery(
			domainId,
			userGroups,
			lang
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-domains.executeQuery.service.getDomainContentFilters with error ${err}`
		);
		throw err;
	}
}

async function getOfficialDomainFilteredData(domainId,req){
  try{
    let {query,binds} = await getDomainFilteredQuery(domainId,req);
    const data = await clkdb.simpleExecute(query, binds);
    return data;

  }catch(err){
    log.error(`<<<<< Exited services.executeQuery.service.revokeRefreshToken with error ${err}`);
    throw err;
  }
}

async function getDomainOfficialStatisticsCount(
	domainId = null,
	userGroups = [],
) {
	try {
		const { query, binds } = await getDomainOfficialStatisticsCountQuery(
			domainId,
			userGroups,
		);
		return await getData(query, binds);
	} catch (err) {
		log.error(
			`<<<<< Exited microservice-domains.executeQuery.service.getDomainOfficialStatisticsCount with error ${err}`
		);
		throw err;
	}
}

module.exports = {
	getPopStatsData,
	getScreenerCount,
	getSingleDimensionNodes,
	getSingleDimensionNodesV3,
	getIndicatorData,
	getScreenerData,
	getRankData,
	getData,
	getDomainContentList,
	getDomainContentCount,
	getDomainContentProductList,
	getOfficialDomainFilteredData,
	getDomainContentFilters,
	getDomainOfficialStatisticsCount,
};
