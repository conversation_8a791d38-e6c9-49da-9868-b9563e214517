const Redis = require('ioredis');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('./config/constants.json')

let redisClient;
(async () => {
    let redisConf = {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT
    };

    if (process.env.REDIS_PASSWORD && (process.env.REDIS_PASSWORD !== '' || process.env.REDIS_PASSWORD !== null)) {
        redisConf.password = process.env.REDIS_PASSWORD;
    }

    // Create a new ioredis client instance
    redisClient = new Redis(redisConf);

    redisClient.on("error", (error) => log.error(`Error Redis: ${error}`));
    redisClient.on("connect", () => log.info(`Connected to Redis`));
    redisClient.on('ready', () => log.info(`Redis is ready`));
    redisClient.on('close', () => log.info('Redis connection closed'));

    // Connect to Redis (ioredis automatically connects upon instantiation)
    try {
        await redisClient.connect(); // Optional, ioredis will connect on instantiation.
        log.info("Redis connection established");
    } catch (error) {
        log.error(`Failed to connect to Redis: ${error.message}`);
    }
})();

module.exports = redisClient;
