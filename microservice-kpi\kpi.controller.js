const moment = require("moment");
const redisClient = require("../redis-con");
const { IFPError } = require("../utils/error");
const {
  getMetaFromCMS,
  getMetaFromCMSAdmin,
} = require("../services/common-service");
const constants = require("../config/constants.json");
const {
  getOverallDomainsData,
  getCommonStatisticsData,
  getExperimentalStatisticsData,
  getAnalyticalAppsData,
  getEntityList,
  getselfServiceToolData,
  getselfServiceToolUserData,
  getGeoSpatialData,
  getGeoSpatialUserData,
  getEntityDetails,
  getEnabledIndicators,
  getExperimentalIndicatorName,
} = require("./services/executeQuery.service");

const {
  getUsersData,
  getUsersByStatus,
  getTopUsersByUsage,
  getPlatformUsageByHour,
  getAverageUsageDuration,
  getUserWithGroup,
  getAllTypeUsersCount,
  getEntityUsers,
  getTotalUsers,
  getEntityDomainsList,
  getActiveUserSplitCount,
  getConsolidatedActiveUsers,
  generateGeospatialOverview,
  generateToolsOverview,
  generateUsersList,
  getActiveUsersList,
} = require("./services/user-service/executeQuery.service");
const {
  getTotalEntities,
  getEntitiesCountByMonth,
  getTopEntitiesByUsage,
  getEntityByClassification,
  getEntityActiveList,
  getDownloadIndicatorList,
  getEntitiesListWithUsersCount,
  getEntityByDomain,
  getEntityFullName,
} = require("./services/entity-service/executeQuery.service");

const updateKpiSession = async (req) => {
  const { type } = req.params;
  const { sessionId, sessionType, nodeId, time, data1, data2, downloadType } =
    req.body;

  if (!constants.redis.enabled) {
    throw new IFPError(400, `Redis not enabled.`);
  }

  if (type !== "start" && type !== "end") {
    throw new IFPError(400, "Invalid session type provided.");
  }

  if (type == "start") {
    if (!sessionId || !time || !sessionType) {
      throw new IFPError(400, `sessionId, sessionType, time are required.`);
    }
  } else {
    if (!sessionId || !time) {
      throw new IFPError(400, `sessionId, sessionType are required.`);
    }
  }

  const sessionKey = `KPI_${process.env.NODE_ENV}_${type}_${sessionId}`;
  const userEmail = req.user.preferred_username;
  try {
    // Store session data in Redis
    const sessionData = {
      sessionId,
      userEmail,
      sessionType,
      type,
      nodeId,
      time,
      data1,
      data2,
      downloadType,
    };
    await redisClient.set(
      sessionKey,
      JSON.stringify(sessionData),
      "EX",
      60 * 60 * 3, // 3 hours in seconds
      "NX"
    );
    return { message: `Session (ID: ${sessionId}) ${type}ed.` };
  } catch (err) {
    throw new IFPError(400, `Error queuing KPI session: ${err.message}`);
  }
};

async function getUserDataController(req, res) {
  const filter = req.body;
  try {
    const data = await getUsersData(filter);
    const total = data.reduce((acc, curr) => acc + Number(curr.count), 0);
    const result = {
      total,
      rateChange: 0,
      monthlySplit: data,
    };
    res.set("Content-Type", "application/json").send(result);
  } catch (err) {
    throw new IFPError(400, `getUserDataController: ${err.message}`);
  }
}

async function getEntitiesDataController(req, res) {
  const filter = req.body;
  try {
    const [total, monthlySplit] = await Promise.all([
      getTotalEntities(filter),
      getEntitiesCountByMonth(filter),
    ]);
    const result = {
      total,
      rateChange: 0,
      monthlySplit,
    };
    res.set("Content-Type", "application/json").send(result);
  } catch (err) {
    throw new IFPError(400, `getEntitiesDataController: ${err.message}`);
  }
}

async function getUsersByStatusController(req, res) {
  const filter = req.body;
  try {
    const data = await getUsersByStatus(filter);
    res.set("Content-Type", "application/json").send(data);
  } catch (err) {
    throw new IFPError(400, `getUsersByStatusController: ${err.message}`);
  }
}

async function getTopUsersByUsageController(req, res) {
  const filter = req.body;
  try {
    let data = await getTopUsersByUsage(filter);
    const entityDomains = data.map((x) => x.entity);
    const entityNames = await getEntityFullName(entityDomains);
    const total = data.reduce((acc, curr) => acc + Number(curr.duration), 0);
    await data.forEach((d) => {
      d.entity = entityNames.find(
        (x) => x.DOMAIN == d.entity.toLowerCase()
      ).NAME;
      const duration = moment.duration(d.duration, "seconds");
      const formatted = moment
        .utc(duration.asMilliseconds())
        .format("HH:mm:ss");
      d.duration = formatted;
    });
    res.set("Content-Type", "application/json").send(data);
  } catch (err) {
    throw new IFPError(400, `getTopUsersByUsageController: ${err.message}`);
  }
}

async function getTopEntitiesByUsageController(req, res) {
  const filter = req.body;
  try {
    const data = await getTopEntitiesByUsage(filter);

    // Percentage Share & Usage formatting
    const total = data.reduce((acc, curr) => acc + Number(curr.duration), 0);
    data.forEach((d) => {
      const percentageShare = +((Number(d.duration) / total) * 100).toFixed(2);
      d.usage_share = percentageShare;

      const duration = moment.duration(d.duration, "seconds");
      const formatted = moment
        .utc(duration.asMilliseconds())
        .format("HH:mm:ss");
      d.duration = formatted;
    });
    res.set("Content-Type", "application/json").send(data);
  } catch (err) {
    throw new IFPError(400, `getTopEntitiesByUsageController: ${err.message}`);
  }
}

async function getPlatformUsageByHourController(req, res) {
  const filter = req.body;
  try {
    const { avg: avgInSeconds } = await getAverageUsageDuration(filter);
    let avg, unit;
    if (avgInSeconds >= 86400) {
      avg = +(avgInSeconds / 86400.0).toFixed(2);
      unit = "days";
    } else if (avgInSeconds >= 3600) {
      avg = +(avgInSeconds / 3600.0).toFixed(2);
      unit = "hours";
    } else if (avgInSeconds >= 60) {
      avg = +(avgInSeconds / 60.0).toFixed(2);
      unit = "minutes";
    } else if (avgInSeconds < 60) {
      avg = +avgInSeconds.toFixed(2);
      unit = "seconds";
    }
    const hourlyUsage = await getPlatformUsageByHour(filter);
    const isEmptyHourlyUsage = hourlyUsage.every((d) => d.count == 0);
    const data = {
      avgUsage: avg ? { value: avg, unit } : {},
      hourlySplit: isEmptyHourlyUsage ? [] : hourlyUsage,
    };
    res.set("Content-Type", "application/json").send(data);
  } catch (err) {
    throw new IFPError(400, `getPlatformUsageByHourController: ${err.message}`);
  }
}

async function getOverallDomainsDataController(req, res) {
  const filter = req.body;
  try {
    const overallDomains = await getOverallDomainsData(filter);
    res.set("Content-Type", "application/json").send(overallDomains);
  } catch (err) {
    throw new IFPError(400, `getOverallDomainsDataController: ${err.message}`);
  }
}

async function getCommonStatisticsDataController(req, res) {
  const filter = req.body;
  try {
    filter.contentClassificationKey = "official_statistics";
    const commonStatisticsData = await getCommonStatisticsData(filter);
    res.set("Content-Type", "application/json").send(commonStatisticsData);
  } catch (err) {
    throw new IFPError(
      400,
      `getCommonStatisticsDataController: ${err.message}`
    );
  }
}

async function getExperimentalStatisticsDataController(req, res) {
  const filter = req.body;
  try {
    const experimentalStatisticsData = await getExperimentalStatisticsData(
      filter
    );
    res
      .set("Content-Type", "application/json")
      .send(experimentalStatisticsData);
  } catch (err) {
    throw new IFPError(
      400,
      `getExperimentalStatisticsDataController: ${err.message}`
    );
  }
}

async function getReportsDataController(req, res) {
  const filter = req.body;
  try {
    filter.contentClassificationKey = "reports";
    const reportsData = await getCommonStatisticsData(filter);
    res.set("Content-Type", "application/json").send(reportsData);
  } catch (err) {
    throw new IFPError(400, `getReportsDataController: ${err.message}`);
  }
}

async function getAnalyticalAppsDataController(req, res) {
  const filter = req.body;
  try {
    const analyticalData = await getAnalyticalAppsData(filter);
    res.set("Content-Type", "application/json").send(analyticalData);
  } catch (err) {
    throw new IFPError(400, `getAnalyticalAppsDataController: ${err.message}`);
  }
}

async function getEntityListController(req, res) {
  try {
    const entityList = await getEntityList();
    const scadIndex = entityList.findIndex(
      (item) => item.entity_domain === "SCAD"
    );
    if (scadIndex !== -1) {
      const [scadItem] = entityList.splice(scadIndex, 1);
      entityList.unshift(scadItem);
    }
    res.set("Content-Type", "application/json").send(entityList);
  } catch (err) {
    throw new IFPError(400, `getEntityListController: ${err.message}`);
  }
}

async function getSelfServiceToolsController(req, res) {
  const filter = req.body;
  try {
    const selfService = await getselfServiceToolData(filter);
    res.set("Content-Type", "application/json").send(selfService);
  } catch (err) {
    throw new IFPError(400, `getSelfServiceToolsController: ${err.message}`);
  }
}

async function getSelfServiceToolsUserController(req, res) {
  const filter = req.body;
  const { limit = 10, offset = 0, isPaginated = true } = req.query;
  // Validate inputs
  // Validate sessionType
  try {
    if (
      !filter.sessionType ||
      !filter.sessionType.startsWith("self_service__")
    ) {
      return res.status(400).json({ error: "Invalid sessionType." });
    }
    const selfService = await getselfServiceToolUserData(
      filter,
      parseInt(limit),
      parseInt(offset),
      isPaginated
    );
    res.set("Content-Type", "application/json").send(selfService);
  } catch (err) {
    throw new IFPError(
      400,
      `getSelfServiceToolsUserController: ${err.message}`
    );
  }
}

async function getGeoSpatialController(req, res) {
  const { limit = 10, offset = 0 } = req.query;
  const filter = req.body;
  try {
    // Validate the category field
    if (!["domain", "district"].includes(filter.category)) {
      return res.status(400).json({
        error: "Invalid category. Allowed values are 'domain' or 'district'.",
      });
    }
    const selfService = await getGeoSpatialData(
      filter,
      parseInt(limit),
      parseInt(offset)
    );
    res.set("Content-Type", "application/json").send(selfService);
  } catch (err) {
    throw new IFPError(400, `getGeoSpatialController: ${err.message}`);
  }
}

async function getGeoSpatialUserController(req, res) {
  const { limit = 10, offset = 0 } = req.query;
  const filter = req.body;
  try {
    // Input validation
    if (!filter.category) {
      return res.status(400).json({ error: "Category is required." });
    }

    const selfService = await getGeoSpatialUserData(
      filter,
      parseInt(limit),
      parseInt(offset)
    );
    res.set("Content-Type", "application/json").send(selfService);
  } catch (err) {
    throw new IFPError(400, `getGeoSpatialUserController: ${err.message}`);
  }
}

async function getUserWithGroupController(req, res) {
  const filter = req.body;
  try {
    const { limit = 10, offset = 0, isPaginated = true} = req.query;
    const entityList = await getEntityList();
    if (filter.entityId) {
      const splitEntityIds = filter.entityId.split(",");
      const domainMap = new Map(
        entityList.map((e) => [e.entity_id, e.entity_domain])
      );

      filter.entityNames = splitEntityIds
        .map((id) => domainMap.get(id))
        .filter((domain) => domain);
    }
    const userDetails = await getUserWithGroup(
      filter,
      parseInt(limit),
      parseInt(offset),
      isPaginated,
      filter.selectedColumns
    );
    
    if(!filter.selectedColumns?.length || filter.selectedColumns?.includes('entity_name')){
      userDetails.result.map(
        (x) =>
          (x.entity_name = entityList.find(
            (y) => y.domain == x.email.split("@")[1]
          )?.name)
      );
    }
    if(filter.selectedColumns?.length && !filter.selectedColumns.includes('email')){
      delete userDetails.result.map(x => delete x.email);
    }
    userDetails.result =  removeTotalCount(userDetails.result)
    res.set("Content-Type", "application/json").send(userDetails);
  } catch (err) {
    throw new IFPError(400, `getUserWithGroupController: ${err.message}`);
  }
}

async function getPrimarySecondaryUsers(req, res) {
  const { status, entity_id } = req.query;

  const ranking = constants.classificationRanking;

  try {
    const [entityDomain] = await getEntityDomainsList(entity_id);
    const activeUsersList = await getActiveUsersList(entityDomain.domain);
    const [entityDetails] = await getEntityDetails(entity_id, status);

    const users = entityDetails?.user || [];
    const activeMails = activeUsersList.map((user) => user.userEmail);

    const isStatusActive = status === "active";
    const isStatusInactive = status === "inactive";

    const filteredUsers = users
      .filter((user) => {
        if (isStatusActive) return activeMails.includes(user.email);
        if (isStatusInactive) return !activeMails.includes(user.email);
        return true; // if no status filtering
      })
      .map((user) => {
        const isActive = activeMails.includes(user.email);
        const highestPermission = user.permission.reduce((a, b) =>
          ranking[a] > ranking[b] ? a : b
        );

        return {
          ...user,
          status: isActive ? "Active" : "Inactive",
          permission: [highestPermission],
        };
      });

    res.set("Content-Type", "application/json").send({
      ...entityDetails,
      user: filteredUsers,
    });
  } catch (err) {
    throw new IFPError(400, `getPrimarySecondaryUsers: ${err.message}`);
  }
}

async function getTypeOfUsersCount(req, res) {
  const entity_id = req.query.entityId;
  try {
    const usersCount = await getAllTypeUsersCount(entity_id);
    const resp = {
      total: {
        entity: {
          entities: usersCount[1][0]?.total_entity,
          sensitive_entities: usersCount[1][0]?.sensitive_entity,
        },
        user: {
          users: usersCount[0][0]?.total_users,
          director_generals: usersCount[0][0]?.director_generals,
          secondary_super_users: usersCount[0][0]?.secondary_super_users,
          primary_super_users: usersCount[0][0]?.primary_super_users,
          external_users: usersCount[0][0].external_users ?? 0,
          internal_users: usersCount[0][0].internal_users ?? 0,
        },
      },
      entity_count: {
        total: usersCount[2][0]?.total_user_count,
        inActive:
          usersCount[2][0]?.total_user_count -
          parseInt(usersCount[3][0]?.active_count),
        active: parseInt(usersCount[3][0]?.active_count),
      },
    };
    res.set("Content-Type", "application/json").send(resp);
  } catch (err) {
    throw new IFPError(400, `getTypeOfUsersCount: ${err.message}`);
  }
}

async function getEntityDomains(req, res) {
  const entity_id = req.query.entityId;
  try {
    const classification = req.query.classification;
    const result = await getEntityByDomain(entity_id, classification);
    res.set("Content-Type", "application/json").send(result);
  } catch (err) {
    throw new IFPError(400, `getEntityDomains: ${err.message}`);
  }
}

async function getEntityClassificationOverview(req, res) {
  const entityId = req.query.entityId;
  try {
    const result = await getEntityByClassification(entityId);

    const classificationRanking = {
      OPEN: 1,
      CONFIDENTIAL: 2,
      SENSITIVE: 3,
      SECRET: 4,
    };

    result.sort((a, b) => {
      const rankA = classificationRanking[a.name.toUpperCase()] || Infinity;
      const rankB = classificationRanking[b.name.toUpperCase()] || Infinity;
      return rankA - rankB;
    });

    res.set("Content-Type", "application/json").send(result);
  } catch (err) {
    throw new IFPError(400, `getEntityClassificationOverview: ${err.message}`);
  }
}

async function getDomainProducts(req, res) {
  const domain = encodeURIComponent(req.query.domain);
  try {
    const classification = req.query.classification;
    const lang =
      req.headers["accept-language"] === "en"
        ? ""
        : `/${req.headers["accept-language"]}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const cmcNodesListEndpoint = `${process.env.CMS_BASEPATH}${lang}${
      constants.cmsUrl.CMS_PRODUCT_LIST
    }/${domain}/${encodeURIComponent(
      constants.cmsClassifications[classification]
    )}`;
    const productData = await getMetaFromCMS(
      req,
      cmsLoginUrl,
      cmcNodesListEndpoint,
      req.user.groups
    );
    res.set("Content-Type", "application/json").send(productData);
  } catch (err) {
    throw new IFPError(400, `getEntityClassificationOverview: ${err.message}`);
  }
}

async function getProductAndNodeFromCMS(req, res) {
  try {
    const dataClassification = req.body.classification;
    const domain = encodeURIComponent(req.body.domain);
    const product = encodeURIComponent(req.body.product);
    const lang =
      req.headers["accept-language"] === "en"
        ? ""
        : `/${req.headers["accept-language"]}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const cmcNodesListEndpoint = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_INDICATOR_LIST}?field_category_target_id=
			${product}&domain_name=${domain}&field_security_target_id=${constants.cmsClassifications[dataClassification]}`;
    let data = await getMetaFromCMSAdmin(
      req,
      cmsLoginUrl,
      cmcNodesListEndpoint,
      req.user.groups
    );

    res.set("Content-Type", "application/json").send(data);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-statistics-insights.getPopularOfficialStatistics with error ${err} `
    );
    throw new IFPError(400, `getUsersByStatusController: ${err.message}`);
  }
}

async function getEntityUsersByClassification(req, res) {
  const filter = req.body;
  const { limit = 10, offset = 0 } = req.query;
  try {
    const classificationData = await getEntityUsers(limit, offset, filter);

    if (classificationData?.length) {
      const classificationHierarchy = [
        "SECRET",
        "SENSITIVE",
        "CONFIDENTIAL",
        "OPEN",
      ];

      for (let user of classificationData) {
        if (user.CLASSIFICATIONS) {
          user.CLASSIFICATIONS = user.CLASSIFICATIONS.split(",");
          const highestClassification = classificationHierarchy.find(
            (classification) => user.CLASSIFICATIONS.includes(classification)
          );

          if (highestClassification) {
            const index = classificationHierarchy.indexOf(
              highestClassification
            );
            user.CLASSIFICATIONS = classificationHierarchy.filter(
              (x, classIndex) => classIndex >= index
            );
          }
        }
      }
    }

    const usersData = {
      userData: classificationData,
      total_count: classificationData[0]?.TOTAL_COUNT,
    };
    res.set("Content-Type", "application/json").send(usersData);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getEntityUsersByClassification with error: ${err.message}`
    );
  }
}

async function getActiveusersList(req, res) {
  const filter = req.body;
  try {
    const domainsList = await getEntityDomainsList(filter.entityId);
    filter.domain = domainsList.map((x) => x.domain);
    const userDetails = await getTotalUsers(filter);
    const activeUsersList = await getActiveUserSplitCount(filter);
    const consolidatedActiveusers = await getConsolidatedActiveUsers(filter);
    const montlyspit =
      activeUsersList?.map((element, index) => ({
        month: element.month,
        users: element.userEmails,
        total_users:
          userDetails[index]?.CUMULATIVE_COUNT ||
          userDetails[userDetails.length - 1]?.CUMULATIVE_COUNT,
        total_active_users: +element.usersCount || 0,
      })) || [];

    const result = {
      total: userDetails[userDetails.length - 1]?.CUMULATIVE_COUNT,
      active: +consolidatedActiveusers[0]?.usersCount,
      inActive:
        parseInt(userDetails[userDetails.length - 1]?.CUMULATIVE_COUNT) -
        parseInt(consolidatedActiveusers[0]?.usersCount),
      monthly_users_data: montlyspit,
    };
    res.set("Content-Type", "application/json").send(result);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getActiveusersList with error: ${err.message}`
    );
  }
}

async function getActiveEntities(req, res) {
  const filter = req.body;
  try {
    const domainsList = await getEntityDomainsList(filter.entityId);
    filter.domain = domainsList.map((x) => x.domain);

    let results = await getEntityActiveList(filter);

    if (results?.length) {
      const domainMap = Object.fromEntries(
        domainsList.map((e) => [e.domain, e.name])
      );

      results = results.map((item) => ({
        ...item,
        active_entities: item.active_domains.map((domain) => ({
          domain,
          name: domainMap[domain] || "Unknown",
        })),
      }));
    }

    res.set("Content-Type", "application/json").send(results);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getActiveEntitis with error: ${err.message}`
    );
  }
}

async function getGeospatialOverviewList(req, res) {
  const filter = req.body;
  try {
    const results = await generateGeospatialOverview(filter);
    const groupedEntities = results.reduce((acc, curr) => {
      const key = curr.entity;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(curr);
      return acc;
    }, {});

    res.set("Content-Type", "application/json").send(groupedEntities);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getGeospatialOverviewList with error: ${err.message}`
    );
  }
}

async function getToolsOverviewList(req, res) {
  const filter = req.body;
  try {
    const toolsOverview = await generateToolsOverview(filter);
    const groupedEntities = toolsOverview.reduce((acc, curr) => {
      const key = curr.entity;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(curr);
      return acc;
    }, {});
    res.set("Content-Type", "application/json").send(groupedEntities);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getToolsOverviewList with error: ${err.message}`
    );
  }
}

async function getDownloadIndicators(req, res) {
  const filter = req.body;
  try {
    const resp = await getDownloadIndicatorList(filter);
    res.set("Content-Type", "application/json").send(resp);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getDownloadIndicators with error: ${err.message}`
    );
  }
}

async function getEnabledIndicatorsList(req, res) {
  try {
    const filter = req.body;
    const results = await getEnabledIndicators(filter);

    if (!results.countList.length) {
      return res.json([]);
    }

    const innovativeInsights = [];
    const nonInnovativeInsights = [];

    results.countList.forEach((element) => {
      if (element.CONTENT_TYPE === "innovative-insights") {
        innovativeInsights.push(element.NODE_ID);
      } else {
        const node = results.nodesList.find((x) => x.nid === element.NODE_ID);
        element.name = node?.title;
        nonInnovativeInsights.push(element);
      }
    });

    if (innovativeInsights.length) {
      const innovatieveData = await getExperimentalIndicatorName(
        innovativeInsights
      );
      const innovatieveMap = new Map(
        innovatieveData.map((item) => [
          item.INDICATOR_ID,
          item.INDICATOR_NAME_EN,
        ])
      );

      results.countList.forEach((element) => {
        if (element.CONTENT_TYPE === "innovative-insights") {
          element.name = innovatieveMap.get(element.NODE_ID) || null;
        }
      });
    }

    results.countList = results.countList.filter((x) => x.name != null);
    res.json(results.countList);
  } catch (error) {
    console.error("Error in getEnabledIndicatorsList:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
}

async function getEntityandUsersList(req, res) {
  const { offset=0, limit=10, entityId, selectedColumns = '', isPaginated = true } = req.query;
  try {
    const result = await getEntitiesListWithUsersCount(offset, limit, entityId, selectedColumns, isPaginated);
    const resp = {
      totalCount: result?.[0]?.total_count ?? 0,
      data: removeTotalCount(result),
    };
    res.set("Content-Type", "application/json").send(resp);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getEntityandUsersList with error: ${err.message}`
    );
  }
}

async function getUsersListWithDetails(req, res) {
  const { offset=0, limit=10, entity_id, external, selectedColumns = '', isPaginated = true  } = req.query;
  const ranking = constants.classificationRanking;
  try {
    let result = await generateUsersList(offset, limit, entity_id, external, selectedColumns, isPaginated);
    if (result?.length) {
      result.forEach((element) => {
        if (!element.classifications) return;
        element.classifications = element.classifications
          .split(",")
          .reduce((a, b) => (ranking[a] > ranking[b] ? a : b));
      });
    }
    const resp = {
      totalCount: result?.[0]?.total_count ?? 0,
      data: removeTotalCount(result),
    };
    res.set("Content-Type", "application/json").send(resp);
  } catch (err) {
    throw new IFPError(
      400,
      `Exited microservice-statistics-insights.getUsersListWithDetails with error: ${err.message}`
    );
  }
}

function removeTotalCount(data) {
  if (data?.length <= 0) {
    return [];
  }
  const transformedData = data.map((item) => {
     item.total_count ? delete item.total_count : delete item.totalCount;
    return item;
  });

  return transformedData;
}

module.exports = {
  updateKpiSession,
  getUserDataController,
  getEntitiesDataController,
  getUsersByStatusController,
  getTopUsersByUsageController,
  getTopEntitiesByUsageController,
  getPlatformUsageByHourController,
  getOverallDomainsDataController,
  getCommonStatisticsDataController,
  getExperimentalStatisticsDataController,
  getReportsDataController,
  getAnalyticalAppsDataController,
  getEntityListController,
  getSelfServiceToolsController,
  getSelfServiceToolsUserController,
  getGeoSpatialController,
  getGeoSpatialUserController,
  getUserWithGroupController,
  getPrimarySecondaryUsers,
  getTypeOfUsersCount,
  getEntityClassificationOverview,
  getProductAndNodeFromCMS,
  getEntityUsersByClassification,
  getActiveusersList,
  getActiveEntities,
  getGeospatialOverviewList,
  getToolsOverviewList,
  getDownloadIndicators,
  getEnabledIndicatorsList,
  getEntityandUsersList,
  getUsersListWithDetails,
  getEntityDomains,
  getDomainProducts,
};
