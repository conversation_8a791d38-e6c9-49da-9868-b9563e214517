const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getGlossaryDataQuery(lang, page, limit, filters, sortBy, searchTerm) {
    try {
        const binds = {};
        let bindCounter = 0;
        const offset = (page - 1) * limit;

        const whereClauses = [];

        if (searchTerm) {
            bindCounter++;
            binds[`term${bindCounter}`] = `%${searchTerm.toUpperCase()}%`;
            whereClauses.push(`(UPPER(TITLE_EN) LIKE {term${bindCounter}:String} OR UPPER(TITLE_AR) LIKE {term${bindCounter}:String})`);
        }

        Object.entries(filters).forEach(([key, value]) => {
            if (value.length > 0) {
                if (key === `TITLE_${lang}`) {
                    bindCounter++;
                    binds[`filter${bindCounter}`] = `${value[0].toUpperCase()}%`;
                    whereClauses.push(`UPPER(${key}) LIKE {filter${bindCounter}:String}`);
                } else if (Array.isArray(value)) {
                    const bindValues = value.map(val => val.toUpperCase());
                    bindCounter++;
                    binds[`filter${bindCounter}`] = bindValues;
                    whereClauses.push(`UPPER(${key}) IN ({filter${bindCounter}: Array(String)})`);
                } else {
                    bindCounter++;
                    binds[`filter${bindCounter}`] = value.toUpperCase();
                    whereClauses.push(`UPPER(${key}) = {filter${bindCounter}:String}`);
                }
            }
        });

        const sortByClauses = Object.entries(sortBy).map(([key, value]) => {
            return key === 'alphabetical' ? `upper(TITLE_${lang}) ${value.toUpperCase()}` : '';
        }).filter(clause => clause.length > 0);

        if (sortByClauses.length === 0) {
            sortByClauses.push(`upper(TITLE_${lang}) ASC`);
        }

        let query = `SELECT t.*, toUInt32(COUNT(*) OVER()) AS TOTAL FROM VW_IFP_GLOSSARY t ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''
            } ORDER BY ${sortByClauses.join(', ')} OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;

        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-glossary.services.getQuery.service.getGlossaryDataQuery with error ${err} `);
        throw err;
    }
}


async function getDomainsDataQuery(lang) {
    try {
        let query = `SELECT DISTINCT TOPIC_${lang} AS TOPIC,THEME_${lang}  AS THEME,TYPE FROM VW_IFP_GLOSSARY ORDER BY TOPIC ASC, THEME ASC, TYPE ASC`
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-glossary.services.getQuery.service.getDomainsDataQuery with error ${err} `);
        throw err;
    }
}

async function getAlphabetsDataQuery(lang, searchTerm, filters) {
    try {
        const whereClauses = [];
        const binds = {};
        let bindVarIndex = 1;

        const addBindVariable = (value) => {
            const bindsVar = `bv${bindVarIndex++}`
            const varName = `{${bindsVar}:String}`;
            binds[bindsVar] = value;
            return `${varName}`;
        };

        if (searchTerm) {
            const varName = addBindVariable(`%${searchTerm.toUpperCase()}%`);
            whereClauses.push(`(upper(TITLE_EN) LIKE upper(${varName}) OR upper(TITLE_AR) LIKE upper(${varName}))`);
        }

        Object.entries(filters).forEach(([key, value]) => {
            if (value.length > 0 && key !== `TITLE_${lang}`) {
                if (Array.isArray(value)) {
                    const values = value.map(val => val.toUpperCase());
                    const bindsVar = `bv${bindVarIndex++}`;
                    binds[bindsVar] = values;
                    whereClauses.push(`upper(${key}) IN ({${bindsVar}:Array(String)})`);
                } else {
                    const varName = addBindVariable(value.toUpperCase());
                    whereClauses.push(`upper(${key}) = upper(${varName})`);
                }
            }
        });

        const langCondition = lang === 'EN'
            ? "match(upper(substr(TITLE_EN, 1, 1)), '^[A-Z]$')"
            : "match(upper(substringUTF8(TITLE_AR, 1, 1)), '^[أ-ي]$')";
        whereClauses.push(langCondition);

        const whereCondition = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
        const query = `
            SELECT DISTINCT CHARACTER, COUNT(*) AS COUNT
            FROM (
                SELECT upper(substringUTF8(TITLE_${lang}, 1, 1)) AS CHARACTER
                FROM VW_IFP_GLOSSARY
                ${whereCondition}
            ) GROUP BY CHARACTER
            ORDER BY CHARACTER
        `;

        return { query, binds };
    } catch (err) {
        log.error(`<<<<<< Exited microservice-glossary.services.getQuery.service.getAlphabetsDataQuery with error ${err} `);
        throw err;
    }
}

module.exports = { getGlossaryDataQuery, getDomainsDataQuery, getAlphabetsDataQuery };