const { Sequelize, DataTypes } = require("sequelize");

/**
 * Model to store publication images with publication ID and image URL.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const PublicationImages = sequelize.define(
    "PublicationImages",
    {
      id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      publicationId: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      imageUrl: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      tableName: "publication_images",
      timestamps: true,
    }
  );

  return PublicationImages;
}

module.exports = model;
