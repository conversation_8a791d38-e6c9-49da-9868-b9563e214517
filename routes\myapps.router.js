const express = require("express");
const router = new express.Router();
const myAppsController = require("../microservice-myapps/myapps.controller");
const {
  validateCreateShare,
  validateDeleteShareApp,
  validateSubmitApps,
  validateDraft<PERSON>pps,
  validateState,
  validateShareList,
  validateReadShareApp,
} = require("./validators/myapps.validator");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

router.get("/", async (req, res, next) => {
  try {
    const data = await myAppsController.listMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

// router.post("/add", async (req, res, next) => {
//   try {
//     const data = await myAppsController.addAppsToMyApps(req);
//     res.set("Content-Type", "application/json");
//     res.send(data);
//     next();
//   } catch (err) {
//     log.error(
//       `Error fetching data for myapps ERROR: ${err}`
//     );
//     next(err);
//   }
// });

// router.post("/remove", async (req, res, next) => {
//   try {
//     const data = await myAppsController.removeAppsFromMyApps(req);
//     res.set("Content-Type", "application/json");
//     res.send(data);
//     next();
//   } catch (err) {
//     log.error(
//       `Error fetching data for myapps ERROR: ${err}`
//     );
//     next(err);
//   }
// });

// router.post("/compare-remove", async (req, res, next) => {
//   try {
//     const data = await myAppsController.removeAppsFromMyApps(req);
//     res.set("Content-Type", "application/json");
//     res.send(data);
//     next();
//   } catch (err) {
//     log.error(
//       `Error fetching data for myapps ERROR: ${err}`
//     );
//     next(err);
//   }
// });

router.post("/draft",validateDraftApps, async (req, res, next) => {
  try {
    const data = await myAppsController.draftMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/submit", validateSubmitApps, async (req, res, next) => {
    try {
      const data = await myAppsController.submitMyApps(req);
      res.set("Content-Type", "application/json");
      res.send(data);
      next();
    } catch (err) {
      log.error(
        `Error fetching data for myapps ERROR: ${err}`
      );
      next(err);
    }
  }
);

// router.get("/domain-nodes/:id", async (req, res, next) => {
//   try {
//     const data = await myAppsController.domainNodesListMyApps(req);
//     res.set("Content-Type", "application/json");
//     res.send(data);
//     next();
//   } catch (err) {
//     log.error(
//       `Error fetching data for myapps ERROR: ${err}`
//     );
//     next(err);
//   }
// });

router.get("/apps-list", async (req, res, next) => {
  try {
    const data = await myAppsController.idListMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/draft-list", async (req, res, next) => {
  try {
    const data = await myAppsController.draftListMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/draft-nodes-list/:id", async (req, res, next) => {
  try {
    const data = await myAppsController.draftNodeListMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/draft/delete/:id", async (req, res, next) => {
  try {
    const data = await myAppsController.draftDeleteMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/state",validateState, async (req, res, next) => {
  try {
    const data = await myAppsController.dragStateSaveMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/state/reset", async (req, res, next) => {
  try {
    const data = await myAppsController.removeDragStateSaveMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/dashboards", async (req, res, next) => {
  try {
    const data = await myAppsController.listDashboards(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/dashboards/create", async (req, res, next) => {
  try {
    const data = await myAppsController.createDashboard(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/dashboards/detail/:id", async (req, res, next) => {
  try {
    const data = await myAppsController.detailDashboard(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/dashboards/edit/:id", async (req, res, next) => {
  try {
    const data = await myAppsController.editDashboard(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/dashboards/:id/nodes/remove", async (req, res, next) => {
  try {
    const data = await myAppsController.removeDashboardNodes(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/dashboards/delete/:id", async (req, res, next) => {
  try {
    const data = await myAppsController.deleteDashboard(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/share", validateCreateShare, async (req, res, next) => {
  try {
    const data = await myAppsController.shareMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/share/list/:type",validateShareList, async (req, res, next) => {
  try {
    const data = await myAppsController.getShareAppsList(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.post("/share/read/:id",validateReadShareApp, async (req, res, next) => {
  try {
    const data = await myAppsController.readShareApp(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

router.get("/share/view", async (req, res, next) => {
  try {
    const data = await myAppsController.getSharedMyApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});


router.delete("/share/delete/:type/:id",validateDeleteShareApp,async (req, res, next) => {
  try {
    const data = await myAppsController.deleteShareApps(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});


router.post("/share/request-access", async (req, res, next) => {
  try {
    const data = await myAppsController.requestAccessShareApp(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(
      `Error fetching data for myapps ERROR: ${err}`
    );
    next(err);
  }
});

module.exports = router;
