const db = require('../../services/database.service');
const jwt = require('jsonwebtoken');
const domainMatrixMap = require('../../microservice-users-v3/services/domainMatrixMap.json');
const { IFPError } = require('../../utils/error');
const { decode } = require('html-entities');
const { getActivePEUser, getActiveSuperuser, getActiveDGUser, getUserDataV3, getUserData, getInvitationData, checkExistingInvitations, getUserDataV2, getEIDInvitationMappingByEID, getEntityInvites } = require('../../microservice-users-v3/services/executeQuery');
const UAEPassService = require('../../services/uae-pass.service');
const { encryptEmail, decryptEmail, encryptPhone, decryptPhone } = require('../../services/encryption.service');
const { getRoleName, hashEmiratesId } = require('../../microservice-users-v3/helper');
const messages = require("../../microservice-users-v3/responseMessages.json")
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const validatePEUser = async (req, res, next) => {
    try{
        let email = req.user.preferred_username;
        const encryptedEmail = encryptEmail(email);
        const activePEUser = await getActivePEUser(encryptedEmail);
        if (activePEUser.length < 1)
            throw new IFPError(403,"The authenticated user is not part of product engagement")
        req.peUser = activePEUser[0];
        next();
    }
    catch(err){
        next(err)
    }
}

const validateSUUser = async (req, res, next) => {
    try{
        let email = req.user.preferred_username;
        let encryptedEmail = encryptEmail(email)
        const activeSUUser = await getActiveSuperuser(encryptedEmail);
        if (activeSUUser.length < 1)
            throw new IFPError(403,"The authenticated user is not a superuser")
        req.superuser = activeSUUser[0];
        next();
    }
    catch(err){
        next(err)
    }
}

const validateDGUser = async (req, res, next) => {
    try{
        let email = req.user.preferred_username;
        let encryptedEmail = encryptEmail(email)
        const activeDGUser = await getActiveDGUser(encryptedEmail);
        if (activeDGUser.length < 1)
            throw new IFPError(403,"The authenticated user is not a DG")
        req.dg = activeDGUser[0];
        next();
    }
    catch(err){
        next(err)
    }
}

const validatePEorSUUser = async (req, res, next) => {
    try {
        await validatePEUser(req, res, () => {});
        return next();
    } catch (peUserError) {
        try {
            await validateSUUser(req, res, () => {});
            return next();
        } catch (suUserError) {
            throw new IFPError(403,"The authenticated user is not a Product Engagement or Superuser");
        }
    }
};

const validateSuperUserInvite = async (req, res, next) => {
    try{
        const superusers = req.body.superusers;
        const entityId = req.body.entityId;
        
        if (!Array.isArray(superusers)) {
            throw new IFPError(400, "Superusers must be an array");
        }

        //validate already existing superusers in the entity or their invites
        const entityInvites = {}
        const existingSuperusers = {}
        entityInvites['primarySuperuser'] = await getEntityInvites('Primary Superuser','PENDING',entityId)
        entityInvites['secondarySuperuser'] = await getEntityInvites('Secondary Superuser','PENDING',entityId)
        existingSuperusers['primarySuperuser'] = await getUserDataV3({
            ENTITY_ID: entityId,
            ROLE: 'PRIMARY_SUPERUSER',
            STATUS: 'REGISTERED'
        })
        existingSuperusers['secondarySuperuser'] = await getUserDataV3({
            ENTITY_ID: entityId,
            ROLE: 'SECONDARY_SUPERUSER',
            STATUS: 'REGISTERED'
        })
        for (const user of superusers) {
            if (user['role'] == 'Primary Superuser' && entityInvites['primarySuperuser'].length) {
                throw new IFPError(400, messages.ERROR.PENDING_INVITE_EXISTS);
            }
            if (user['role'] == 'Secondary Superuser' && entityInvites['secondarySuperuser'].length) {
                throw new IFPError(400, messages.ERROR.PENDING_INVITE_EXISTS);
            }
            if (user['role'] == 'Primary Superuser' && existingSuperusers['primarySuperuser'].length) {
                throw new IFPError(400, messages.ERROR.PRIMARY_SUPERUSER_EXISTS);
            }
            if (user['role'] == 'Secondary Superuser' && existingSuperusers['secondarySuperuser'].length) {
                throw new IFPError(400, messages.ERROR.SECONDARY_SUPERUSER_EXISTS);
            }
        }

        // validate already invited or registered
        for (const user of superusers) {
          const encryptedEmail = encryptEmail(user.email);
          const encryptedPhone = encryptPhone(user.phone);
          const invitation = await checkExistingInvitations(
            encryptedEmail,
            encryptedPhone
          );
          if (invitation.length) {
            throw new IFPError(400, messages.ERROR.PENDING_INVITE_EXISTS);
          }

          // validate already registered
          const filters = {
            PHONE_NUMBER: encryptedPhone,
            EMAIL: encryptedEmail,
          };
          let userData = await getUserDataV3(filters, "OR");
          userData = userData.filter((data) => (
            data.STATUS === "REGISTERED" &&
            data.EXISTING_USER !== "EXISTING_UNLINKED"
          ));
          if (userData.length > 0) {
            throw new IFPError(400, messages.ERROR.USER_EXISTS);
          }
        }

        superusers.forEach((superuser, index) => {
            const requiredFields = ["email", "phone", "role"];
            const missingFields = [];

            requiredFields.forEach((field) => {
                if (!superuser.hasOwnProperty(field)) {
                    missingFields.push(field);
                }
            });

            if (missingFields.length > 0) {
                throw new IFPError(400, `Missing required fields for superuser at index ${index}`, {
                    missingFields: missingFields,
                });
            }
            superuser.phone = superuser.phone.toString();
            const phoneNumber = superuser.phone;
            const regex = /^9715\d{8}$/;

            if (!regex.test(phoneNumber)) {
                throw new IFPError(400, "Phone number should of the format 9715XXXXXXXX",{data:superuser});
            }

        });

        next();
    }
    catch(err){
        next(err);
    }
}

const validateUserInvite = async (req, res, next) => {
    try{
        const requiredRequestBody = ["type","recepients"];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            throw new IFPError(400, "Missing required fields in the request body.", { missingFields: missingFields });
        }

        if (!["USER","DG"].includes(req.body.type.toUpperCase())){
            throw new IFPError(400, "Type parameter only accepts only values `USER` & `DG`");
        }

        const recepients = req.body.recepients;

        if (!Array.isArray(recepients)) {
            throw new IFPError(400, "The field 'recepients' should be an array");
        }

        // validate already used EID, invited & already registered
        for (const user of recepients) {
          const hashedEID = hashEmiratesId(user.eid);
          const eidMapping = await getEIDInvitationMappingByEID(hashedEID);
          if (eidMapping.length !== 0) {
            let eidInvitation;
            try {
              // Check if corresponding invitation exists for eid mapping requestId
              eidInvitation = await getInvitationData(eidMapping[0].REQUEST_ID);
            } catch (error) {
              log.info(`Invitation for given requestId not found.`);
            }
            if (eidInvitation) {
              // Normal User
              if (
                eidInvitation.STATUS == "REGISTERED" ||
                eidInvitation.STATUS == "INVALID"
              ) {
                throw new IFPError(400, messages.ERROR.USER_EXISTS);
              } else {
                throw new IFPError(400, messages.ERROR.PENDING_INVITE_EXISTS);
              }
            } else {
              // Exisitng user
              const existingUser = await getUserDataV2({
                EMAIL: eidMapping[0].EMAIL,
                STATUS: "REGISTERED"
              });
              if (existingUser.length !== 0) {
                throw new IFPError(400, messages.ERROR.USER_EXISTS);
              }
            }
          }

          const encryptedEmail = encryptEmail(user.email);
          const invitation = await checkExistingInvitations(encryptedEmail);
          if (invitation.length) {
            throw new IFPError(400, messages.ERROR.PENDING_INVITE_EXISTS);
          }

          const userData = await getUserDataV2({ EMAIL: encryptedEmail, STATUS: "REGISTERED" });

          if (userData.length) {
            if (
              req.body.type.toUpperCase() == "DG" &&
              userData[0].EXISTING_USER !== "EXISTING_UNLINKED"
            ) {
              throw new IFPError(400, messages.ERROR.USER_EXISTS);
            } else if (
              req.body.type.toUpperCase() == "USER" &&
              userData[0].EXISTING_USER.includes("EXISTING")
            ) {
              throw new IFPError(
                400,
                messages.ERROR.INVITATION.INVITES_UNSUCCESSFUL_EXISTING_USER
              );
            }
          }
        }

        for (const recepient of recepients) {
            let requiredRecepientFields=[]
            if (req.body.type.toUpperCase() == "USER")
                requiredRecepientFields = ["email", "eid", "access"];
            else
                requiredRecepientFields = ["email", "eid", "designation"];

            let missingRecepientFields = [];

            requiredRecepientFields.forEach(field => {
                if (!recepient.hasOwnProperty(field)) {
                    missingRecepientFields.push(field);
                }
            });

            if (missingRecepientFields.length > 0) {
                throw new IFPError(400, "Missing required fields in the recepient object.", { missingFields: missingRecepientFields });
            }

            // const phoneNumber = recepient.phone;
            // const regex = /^9715\d{8}$/;

            // if (!regex.test(phoneNumber)) {
            //     throw new IFPError(400, "Phone number should of the format 9715XXXXXXXX",{data:recepient});
            // }
            if (req.body.type.toUpperCase() == "USER" && Array.isArray(recepient.access) && recepient.access.length < 1) {
                throw new IFPError(400, "The field 'access' should have atleast one item");
            }

            if (req.body.type.toUpperCase() == "DG")
                recepient.access = []

            const access = recepient.access;

            if (!Array.isArray(access)) {
                throw new IFPError(400, "The field 'access' should be an array");
            }

            access.forEach(accessItem => {
                const requiredAccessFields = ["domain", "classification"];
                let missingAccessFields = [];

                requiredAccessFields.forEach(field => {
                    if (!accessItem.hasOwnProperty(field)) {
                        missingAccessFields.push(field);
                    }
                });

                if (missingAccessFields.length > 0) {
                    throw new IFPError(400, "Missing required fields in the access object.", { missingFields: missingAccessFields });
                }
            });
            recepient.eid = recepient.eid.toString();
        };
        next();
    }
    catch(err){
        next(err);
    }
}

const validateDGInvite = async (req, res, next) => {
    try{
        const requiredRequestBody = ["email", "phone"];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            throw new IFPError(400, "Missing required fields in the request body.", { missingFields: missingFields });
        }

        if (isNaN(req.body.phone)) {
            throw new IFPError(400, "Phone number must be a number");
        }
        const phoneNumber = req.body.phone.toString()
        const regex = /^9715\d{8}$/;

        if (!regex.test(phoneNumber)) {
            throw new IFPError(400, "Phone number should of the format 9715XXXXXXXX",{data:req.body});
        }

        // Add additional validations for each field if needed

        next();
    }
    catch(err){
        next(err);
    }
}


const validateUAEPassToken = async (req, res, next) => { 
    try{
        if (!req.headers.hasOwnProperty('x-uaepass-token')) {
            throw new IFPError(400, "UAE Pass access token required");
        }
        
        if(!['dev','test','prod'].includes(process.env.NODE_ENV)){
        
            headers = Object.keys(req.headers)
            let uaepassHeaders = ['x-uaepass-name','x-uaepass-phone','x-uaepass-idn','x-uaepass-uuid']
            let missingHeaders = []
            uaepassHeaders.forEach(header=> {
                if (!headers.includes(header))
                    missingHeaders.push(header)
            })
            if (missingHeaders.length){
                throw new IFPError(400,`Missing UAE Pass headers: ${missingHeaders.join(', ')}`,{availableHeaders:uaepassHeaders})
            }
            req.user = {
                fullnameEN: req.headers['x-uaepass-name'],
                mobile: req.headers['x-uaepass-phone'],
                idn: req.headers['x-uaepass-idn'],
                uuid: req.headers['x-uaepass-uuid']
            }
            next();
        }
        else{
            let token = req.headers['x-uaepass-token'];
            if (!token) {
                throw new IFPError(400, "UAE Pass access token required");
            }
            const service = new UAEPassService()
            const userInfo = await service.getUserInfo(token);
            if (!userInfo) {
                throw new IFPError(401, "Invalid/Expired Access TOKEN");
            }
            log.info(`validateUAEPassToken=> The user ${userInfo.email} is a valid user`)
            req.user = userInfo;
            next();
        }
    }
    catch(err){
        next(err);
    }
}

const validateSuperUser = async (req, res, next) => {
    try{
        let email = req.user.preferred_username
        let encryptedEmail = encryptEmail(email)
        let binds = {
            email: encryptedEmail
        }
        let query = `SELECT ID,ENTITY FROM IFP_USER_MASTER WHERE EMAIL=:email AND ACTIVE=1 AND USER_TYPE='SUPERUSER' AND IS_PRIMARY=1`
        let data = await db.simpleExecute(query,binds)
        if (data.length < 1) {
            log.info(`validateSuperUser=> The user ${email} is not a valid superuser/primary superuser`)  
           throw new IFPError(403,"The authenticated user is not a superuser/primary superuser")
        }
        log.info(`validateSuperUser=> The user ${email} is a valid superuser`)
        const userData = data[0]
        req.user.entityUserId = userData.ID
        req.user.entity = userData.ENTITY
        next();
    }
    catch(err){
        next(err)
    }
};

const validateUserRegister = async (req, res, next) => {
    try{
        const requiredRequestBody = [];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            throw new IFPError(400, "Missing required fields in the request body.", { missingFields: missingFields });
        }

        // Add additional validations for each field if needed

        next();
    }
    catch(err){
        next(err);
    }
};

const validateSuperUserRegister = async (req, res, next) => {
    try{
        const requiredRequestBody = [];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            throw new IFPError(400, "Missing required fields in the request body.", { missingFields: missingFields });
        }
        next();
    }
    catch(err){
        next(err);
    }
};

const validateDGRegister = async (req, res, next) => {
    try{
        const requiredRequestBody = [];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        // if (isNaN(req.body.otp)) {
        //     throw new IFPError(400, "OTP must be a number");
        // }

        if (missingFields.length > 0) {
            throw new IFPError(400, "Missing required fields in the request body.", { missingFields: missingFields });
        }

        // Add additional validations for each field if needed

        next();
    }
    catch(err){
        next(err);
    }
};

const validateOtpRequest = async (req, res, next) => {
    try{
        const otp = req.body.otp;

        if (isNaN(otp)) {
            throw new IFPError(400, "OTP must be a number");
        }

        if (otp.toString().length !== 4) {
            throw new IFPError(400, "OTP must have 4 digits");
        }

        next();
    }
    catch(err){
        next(err);
    }
};

const validateShare = async (req, res, next) => {
    try{
        let recepients = req.body.recepients
        
        if (!recepients) 
           throw new IFPError(400,"Please provide the list of recepients")

        if (recepients.length<1) 
           throw new IFPError(400,"Please provide atleast one recepient")
        //Add validation for entity domain

        next();
    }
    catch(err){
        next(err)
    }
};



const validateAccessRequestsList = async (req, res, next) => {
    try{
        const type = req.params.type
        let binds = {
            email: req.user.preferred_username
        }

        if (req.query.page<1)
            req.query.page = 1

        if (req.query.limit<1)
            req.query.page = 10

        if (req.query.sortBy){
            let formattedSortBy = req.query.sortBy.toUpperCase()
            if (!(['NAME','REQUESTDATE'].includes(formattedSortBy)))
                throw new IFPError(400,"Only name and requestDate are accepted");

            if (formattedSortBy == 'REQUESTDATE')
                formattedSortBy = 'PLATFORM_REQUEST_DT'

            req.query.sortBy = formattedSortBy

            
            if (req.query.order){
                let formattedOrderBy =req.query.order.toUpperCase()
                if(!(['ASC','DESC'].includes(formattedOrderBy)))
                    throw new IFPError(400,"Only asc and desc are accepted");
                req.query.order = formattedOrderBy
            }
            else
                req.query.order = 'ASC'
        }
        
        if (type == 'entity'){
            let query = `SELECT ID,ENTITY FROM IFP_USER_MASTER WHERE EMAIL=:email AND USER_TYPE='SUPERUSER'`
            let data = await db.simpleExecute(query,binds)
            if (!data.length)
                throw new IFPError(403,"The authenticated user is not a superuser");
            const userData = data[0]
            req.user.entity = userData['ENTITY']
        }
        else if(type == 'ifp'){
            const IFPEngagementUsers = process.env.IFP_PE_USERS.split(',')
            if (!(IFPEngagementUsers.includes(req.user.preferred_username)))
                throw new IFPError(403,"The authenticated user is not part of product engagement");
        }
        else 
            throw new Error('Invalid access type');

        next()
    }
    catch(err){
        next(err);
    }


};

const validateSuperUserApprovalRequest = async (req, res, next) => {
    try{

        const requiredRequestBody = ["operation", "classifications", "email"];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            return res.status(400).json({
                message: "Missing required fields in the request body.",
                missingFields: missingFields
            });
        }

        if (!(['APPROVE','REJECT'].includes(req.body.operation))) {
            throw new IFPError(400,"Please provide a valid option for operation",{availableOptions: ['APPROVE','REJECT']});
        }

        const validClassifications = ['OPEN','CONFIDENTIAL','SENSITIVE','SECRET']
        let invalidClassifications = []
        req.body.classifications.forEach(classification=>{
            if (! validClassifications.includes(classification))
                invalidClassifications.push(classification)
        })
        if (invalidClassifications.length)
            throw new IFPError(400,`The field 'classifications' contains invalid values: ${invalidClassifications.join(', ')}`,{allowedClassifications:validClassifications})

        let binds = {
            entity: req.user.entity,
            requestorEmail: req.body.email
        }
        let query = `SELECT EMAIL FROM IFP_USER_ACCESS WHERE ENTITY=:entity AND EMAIL=:requestorEmail`
        let data = await db.simpleExecute(query,binds)
        if (!data.length)
            throw new IFPError(400,"Invalid user or you don't have permission to modify request status for this user");
        next();
    }
    catch(err){
        next(err);
    }


};

const validateIFPEngagementApprovalRequest = (req, res, next) => {
    try{

        const requiredRequestBody = ["operation", "accesses", "email"];
        let missingFields = [];

        requiredRequestBody.forEach(field => {
            if (!req.body.hasOwnProperty(field)) {
                missingFields.push(field);
            }
        });

        if (missingFields.length > 0) {
            throw new IFPError(400,"Missing required fields in the request body.",{missingFields: missingFields});
        }

        if (!Array.isArray(req.body.accesses))
           throw new IFPError(400,"The field `accesses` should be an array");

        if (!(['APPROVE','REJECT'].includes(req.body.operation))) {
            throw new IFPError(400,"Please provide a valid option for `operation`",{availableOptions: ['APPROVE','REJECT']});
        }

        const allowedDomains = Object.keys(domainMatrixMap)
        const allowedClassifications = ['OPEN','CONFIDENTIAL','SENSITIVE','SECRET']

        let invalidDomains = []
        let invalidClassifications = []
        req.body.accesses.forEach(domain=>{
            if (!(typeof(domain) == 'object'))
                throw new IFPError(400,'The item inside `accesses` should be an object')
            if (!('name' in domain) || !('classification' in domain))
                throw new IFPError(400,`Access should be an object with name and classification fields, got ${JSON.stringify(domain)}`)
            if (!(allowedDomains.includes(domain.name.toUpperCase())))
                invalidDomains.push(domain.name)   
            if (!(allowedClassifications.includes(domain.classification.toUpperCase())))
                invalidClassifications.push(domain.classification) 
        })

        if (invalidDomains.length)
           throw new IFPError(400,`Invalid domains ${invalidDomains.join(', ')} found in the request`,{availableOptions: allowedDomains});
        
        if (invalidClassifications.length)
           throw new IFPError(400,`Invalid classifications ${invalidClassifications.join(', ')} found in the request`,{availableOptions: allowedClassifications});

        let binds = {
            requestorEmail: req.body.email
        }
        let query = `SELECT EMAIL FROM IFP_USER_ACCESS WHERE EMAIL=:requestorEmail`
        db.simpleExecute(query,binds).then(data=>{
            if (!data.length)
                return res.status(400).json({
                    message: "Invalid user or you don't have permission to modify request status for this user",
                });
            next();
        }).catch(err=>{
            throw err;
        })

    }
    catch(err){
        throw err;
    }


};

const validateIFPEngagementUser = (req, res, next) => {
    try{
        let email = req.user.preferred_username
        const IFPEngagementUsers = process.env.IFP_PE_USERS.split(',')
        if (!(IFPEngagementUsers.includes(email)))
            throw new IFPError(403,"The authenticated user is not part of product engagement")
        next();
    }
    catch(err){
        throw err;
    }

};

const validateInvitationDelete = async (req, res, next) => {
    try {
      // Role Authorization
      let email = req.user.preferred_username;
      let encryptedEmail = encryptEmail(email);
      const user = await getUserData(encryptedEmail);
      if (!["PRIMARY_SUPERUSER","SECONDARY_SUPERUSER", "PRIMARY_PE_USER", "DG"].includes(user.ROLE)) {
        throw new IFPError(
          403,
          "You are not authorized to perform this action."
        );
      }

      // Entity Authorization
      const { invitationId } = req.params;
      const invitation = await getInvitationData(invitationId);
      if (!invitation) {
        throw new IFPError(
          404,
          `Invitation with given request id: ${invitationId} not found`
        );
      }
      if (
        user.ROLE != "PRIMARY_PE_USER" &&
        invitation.ENTITY_ID != user.ENTITY_ID
      ) {
        throw new IFPError(403, `Invitation belongs to another entity`);
      }
      next();
    } catch (err) {
      next(err);
    }
};

const validateInvitationUpdate = async (req, res, next) => {
    try {
      const { userEmail, phone } = req.body;
      const { invitationId } = req.params;

      const invitation = await getInvitationData(invitationId);

      if (invitation.STATUS !== "PENDING") {
        throw new IFPError(400, messages.ERROR.INVITATION_UPDATE_403);
      }

      const inviteeEmail = encryptEmail(userEmail);
      const inviteePhone = phone && phone !== "NA" ? encryptPhone(phone) : null;

      let inviteExists;
      if (invitation.INVITE_TYPE == "USER") {
        const existingInvitation = await checkExistingInvitations(inviteeEmail);
        if (existingInvitation.length) {
          inviteExists = true;
        }
      } else {
        const isEmailUpdated = inviteeEmail !== invitation.USER_EMAIL;
        const isPhoneUpdated = inviteePhone !== invitation.PHONE;

        if (isEmailUpdated || isPhoneUpdated) {
          const emailCheck = isEmailUpdated ? inviteeEmail : null;
          const phoneCheck = isPhoneUpdated ? inviteePhone : null;

          const existingInvitation = await checkExistingInvitations(
            emailCheck,
            phoneCheck
          );
          if (existingInvitation.length) {
            inviteExists = true;
          }
        }
      }

      if (inviteExists) {
        throw new IFPError(400, messages.ERROR.INVITATION.INVITE_EXISTS);
      }

      const existingUser = await getUserDataV3(
        {
          EMAIL: inviteeEmail,
          PHONE_NUMBER: inviteePhone,
        },
        "OR"
      );
      if (existingUser.length) {
        throw new IFPError(400, messages.ERROR.USER_EXISTS);
      }

      next();
    } catch (err) {
      next(err);
    }
};

const validateUserGuideDownload = async (req, res, next) => {
    try {
      const { type } = req.query;

      if (!type) {
        throw new IFPError(400, messages.ERROR.USER_GUIDE.TYPE_REQUIRED);
      }

      if (!["CONFIDENTIAL", "SENSITIVE"].includes(type.toUpperCase())) {
        throw new IFPError(400, messages.ERROR.USER_GUIDE.UNKNOWN_TYPE);
      }

      const encryptedEmail = encryptEmail(req.user.preferred_username);
      const userData = await getUserData(encryptedEmail);
      if (userData.ROLE == "USER") {
        throw new IFPError(400, messages.ERROR.COMMON.NOT_ALLOWED);
      }

      next();
    } catch (err) {
      next(err);
    }
}

const validateSUDeletedUsersListRequest = async (req, res, next) => {
    try {
      const { entityId } = req.query;

      if (!entityId) {
        const message = messages.ERROR.COMMON.QUERY_PARAM_REQUIRED.replace(
          "{query_param_name}",
          "entityId"
        );
        throw new IFPError(400, message);
      }
      next();
    } catch (error) {
      next(error);
    }
}
 

module.exports = {
    validatePEUser,
    validateSUUser,
    validateDGUser,
    validatePEorSUUser,
    validateSuperUserInvite,
    validateUserInvite,
    validateUserRegister,
    validateDGRegister,
    validateOtpRequest,
    validateDGInvite,
    validateSuperUserRegister,
    validateUAEPassToken,
    validateSuperUser,
    validateAccessRequestsList,
    validateShare,
    validateSuperUserApprovalRequest,
    validateIFPEngagementUser,
    validateIFPEngagementApprovalRequest,
    validateInvitationDelete,
    validateInvitationUpdate,
    validateUserGuideDownload,
    validateSUDeletedUsersListRequest,
}