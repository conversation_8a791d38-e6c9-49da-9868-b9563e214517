const axios = require('axios');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getHomePageData(homePageResponse, req) {
    return new Promise((resolve, reject) => {
        const host = req.headers.host;
        const lang = req.headers["accept-language"];
        if (homePageResponse.length > 0) {
            homePageResponse.forEach((home, index) => {
                let url = `http://${host}/api/content-type/pages/homepage/${home.id}`;
                log.info(`API call to ${url} from getHomePage.service.js`);
                axios.get(`${url}`, {
                    headers: {
                        Authorization: req.headers.authorization,
                        appType: 'insights-discovery',
                        "Accept-Language": lang
                    }
                }).then(response => {
                    resolve(response.data);
                }).catch(err => {
                    
                    return reject(err);
                })
            });

        }
    })
};

module.exports = { getHomePageData }