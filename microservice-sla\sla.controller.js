require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
let constants = require('../config/constants.json');
const axiosInstance = require('axios');
const http = require('http');
const https = require('https');
const axios = axiosInstance.create({
    httpAgent: new http.Agent({ keepAlive: true }),
    httpsAgent: new https.Agent({ keepAlive: true })
});

const { getUserGroupDetails, syncUserGroup } = require('../services/authorization.service');
const { featureGroupMap } = require("../config/constants.json");
const { getMaintenanceStatus } = require('./services/executeQuery.service');

const { getMetaFromCMS } = require('../services/common-service');

/**
 * Get features allowed for given user via the
 * SCAD EntraID groups assigned to them.
 * @param {Array<string>} userEntraIdGroupsIds 
 * @returns 
 */
function getUserFeatures(userEntraIdGroupsIds) {
    const featureObj = {
        genAi: false,
        dataPrepBasic: true,
        dataPrepAdv: true,
        dataExploration: true,
        autoML: true,
    };

    for (const groupId of userEntraIdGroupsIds) {
        if (featureGroupMap[groupId]) {
            featureObj[featureGroupMap[groupId]] = true;
        }
    }

    return featureObj;
}

/**
 * function to get whats-new content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getSLAStatus(req) {
    log.debug(`>>>>>Entered sla-microservice.sla.controller.getSLAStatus`);
    return new Promise(async (resolve, reject) => {
        try {

            const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;

            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

            const slaGroup = req.user.groups.find(g => g == process.env.SLA_GROUP)
            if (slaGroup) {
                log.info(`[SLASTATUS] - ${req.user.preferred_username}| The user belongs to SLA Group : ${slaGroup}`)
                return resolve({ "message": "SLA has expired", "error_code": "sla_expired", "status": false });
            }

            const allowedFeatures = getUserFeatures(req.user.groups)

            const cmsMaintananceUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_MAINTANANCE_URL}`;

            const maintenanceStatus = await getMetaFromCMS(req, cmsLoginUrl, cmsMaintananceUrl, req.user.groups);
            if (maintenanceStatus && maintenanceStatus.maintenance_mode == 1) {
                log.info(`[SLASTATUS] - ${req.user.preferred_username}| System under maintenance`)
                return resolve({
                    "message": "System Maintenance",
                    "status": false,
                    ...maintenanceStatus,
                    maintenance_mode: Boolean(+maintenanceStatus.maintenance_mode),
                    features: allowedFeatures
                });
            }

            let userCredentials = {}

            log.info(`[SLASTATUS] - ${req.user.preferred_username}|Commence CMS Sync`)
            const result = await syncUserGroup(req);

            log.info(`[SLASTATUS] - ${req.user.preferred_username}|Complete CMS Sync`)

            if (!(result && result.groups)) {
                log.info(`[SLASTATUS] - ${req.user.preferred_username}| No data received. CMS Sync Failed`)
                log.info(`[SLASTATUS] - ${req.user.preferred_username}| User does not belongs to any valid content group`)
                return resolve({ "message": "User does not belongs to any valid content group!!!", "error_code": "group_unassigned", "status": false });
            }
            else {
                if (result.groups.includes('SLA_EXPIRY')) {
                    log.info(`[SLASTATUS] - ${req.user.preferred_username}| SLA Group found in the groups. SLA has expired`)
                    return resolve({ "message": "SLA has expired", "error_code": "sla_expired", "status": false });
                }

                userCredentials = {
                    username: result.email,
                    password: process.env.USER_SYNC_DEFAULT_PASSWORD,
                    groups: result.groups
                }
                log.info(`User belonged to user groups: ${userCredentials.groups}`);
            }

            log.info(`[SLASTATUS] - ${req.user.preferred_username}| Logging into CMS to verify the access`)
            axios.post(`${cmsLoginUrl}`, {
                name: userCredentials.username, pass: userCredentials.password,
            }).then(async (postResponse) => {
                log.info(`[SLASTATUS] - ${req.user.preferred_username}| Login Success`)
                const cookie = postResponse ? (postResponse.headers['set-cookie'][0]).split(';')[0] : '';
                let preventLogout = false
                let noLogoutUsers;
                try {
                    noLogoutUsers = process.env.IFP_NO_LOGOUT_USERS.split(',')
                }
                catch {
                    noLogoutUsers = []
                }
                if (noLogoutUsers.includes(req.user.preferred_username))
                    preventLogout = true

                let result = { "message": "The user has access to platform", "status": true, "preventLogout": preventLogout }
                result = { ...result, features: allowedFeatures }
                resolve(result);

            }).catch((err) => {
                if (err.response.status == 400) {
                    log.info(`[SLASTATUS] - ${req.user.preferred_username}| Login Failed. User does not belongs to any valid content group`)
                    return resolve({ "message": "User does not belongs to any valid content group!!!", "error_code": "group_unassigned", "status": false });

                }
                else if (err.response.status == 503) {
                    log.info(`[SLASTATUS] - ${req.user.preferred_username}| System under maintenance`)
                    return reject({ "message": "System Maintenance", "status": false });
                }
                else {
                    log.info(`[SLASTATUS] - ${req.user.preferred_username}| Error while logging into CMS ${err}`)
                    return reject({ "message": "Unknown Error", "status": false });
                }

            });


        } catch (err) {

            log.error(`<<<<<Exited sla-microservice.sla.controller.getSLAStatus on getting CMS data with error ${err}`);
            if (err[1] == 'No valid groups') {
                log.error(`User [${req.user.preferred_username}] does not belongs to any valid content group!!!`);
                return resolve({ "message": `User [${req.user.preferred_username}] does not belongs to any valid content group!!!`, "error_code": "group_unassigned", "status": false });
            }
            // reject(err);
        }
    })
}
module.exports = { getSLAStatus };