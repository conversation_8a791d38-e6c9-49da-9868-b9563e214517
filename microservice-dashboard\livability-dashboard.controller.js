const Logger = require('scad-library').logger;
const DashboardService  = require("./services/dashboard.service");
const {
	getLivabilityData,
	getLivabilityCategory,
	getLivabilityComposition,
	getLivabilityMapMeta,
} = require("./services/executeQuery");

class LivabilityDashboardController {
	/**
	 * Constructor for the LivabilityDashboardController class.
	 */
	constructor() {
		// Initialize properties
		this.cmsData = {};
		this.lang = "en";
		this.contentId = null;
		this.filters = null;
		this.cmsUrl = "";
		this.log = new Logger().getInstance();
	}

	/**
	 * Method to fetch data for the livability dashboard.
	 * @param {Object} req - The request object containing parameters and query.
	 * @returns {Object} - The data for the livability dashboard.
	 */
	getData = async (req) => {
		try {
			// Instantiate DashboardService
			const dashboardService = new DashboardService();

			this.lang = dashboardService.getLangCode(req);
			this.contentId = req.params.id;
			this.filters = req.query;
			// Construct CMS URL based on language and content ID and Fetch CMS data
			this.cmsUrl = dashboardService.constructCMSUrl(this.lang, this.contentId);
			this.cmsData = await dashboardService.fetchCMSData(req, this.cmsUrl);

			// If no cms data or a livability node, return empty
			if (!(await this.validate(req))) {
				return {};
			}

			// Check for cached results
			const cacheKey = dashboardService.generateCacheKey(
				this.contentId,
				this.cmsData,
				this.filters
			);
			const cachedResults = await dashboardService.getCache(
				cacheKey,
				req.headers
			);
			if (cachedResults !== null) {
				return cachedResults;
			}

			// Extract parent category and category filters
			let parentCategory = this.filters.parent_category || null;
			let category = this.filters.category || null;

			// Get data from database
			let dashboardPrimaryCategory = await getLivabilityCategory(this.lang);
			if (parentCategory === null && dashboardPrimaryCategory.length > 0) {
				parentCategory = dashboardPrimaryCategory[0].category;
			}
			if (category === null && parentCategory) {
				category = parentCategory;
			}

			// Get Secondery filyer items, map data and card datas
			const [
				dashboardSecondaryLevels,
				dashboardMapData,
				dashboardMapMetaData,
				dashboardData,
				composition,
			] = await Promise.all([
				getLivabilityCategory(this.lang, parentCategory),
				getLivabilityData(this.lang, parentCategory, category, 1),
				getLivabilityMapMeta(this.lang, category),
				getLivabilityData(this.lang, parentCategory, category, null, true),
				getLivabilityComposition(this.lang, parentCategory, category),
			]);

			// Map title and subtitle
			let dashboardMapDataMod = this.updateMapData(
				dashboardMapData,
				dashboardMapMetaData
			);

			// Construct response object
			let response = {
				type: this.cmsData.type,
				title: this.cmsData.component_title,
				subTitle: this.cmsData.component_subtitle,
				tagName: this.cmsData.tagName,
				tagColorCode: this.cmsData.tagColorCode,
				domainId: this.cmsData.domain_id,
				domain: this.cmsData.domain,
				publicationDate: this.cmsData.publication_date,
				updated: this.cmsData.updated,
				dataSource: this.cmsData.data_source,
				dashboardPrimaryCategory: dashboardPrimaryCategory,
				dashboardSecondaryLevels: dashboardSecondaryLevels,
				dashboardMapData: dashboardMapDataMod,
				dashboardData: dashboardData,
				dashboardComposition: composition,
			};

			// Update cache
			dashboardService.setCache(cacheKey, response, req.headers);

			return response;
		} catch (error) {
			// Handle errors
			this.log.error(
				`<<<<< Exited LivabilityDashboardController.getData with error ${error} `
			);
			throw error; // Rethrow the error to the caller
		}
	};

	validate = async (req) => {
		try {
			// Check node available
			if (Object.keys(this.cmsData).length === 0) {
				return false;
			}

			// Check node is belongs to livability.
			if (this.cmsData.type_key !== "livability") {
				return false;
			}

			return true;
		} catch (error) {
			throw error; // Rethrow the error to the caller
		}
	};

	updateMapData = (dashboardMapData, dashboardMapMetaData) => {
		let dashboardMapDataMod = {
			title: dashboardMapMetaData.title,
			subTitle: dashboardMapMetaData.subTitle,
			isActive: dashboardMapMetaData.isActive,
			data: dashboardMapData,
		};
		return dashboardMapDataMod;
	};
}


module.exports = LivabilityDashboardController;
