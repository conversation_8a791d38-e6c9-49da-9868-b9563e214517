const oracledb = require('oracledb');
const dbConfig = require('../config/dbConfig.js');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const constants = require('../config/constants.json')
const { setRedis, getRedis } = require('./redis.service')



const {shouldCache,generateCacheKey} = require('./helpers/helper.js')

async function initialize() {
    try {
        const pool = await oracledb.createPool(dbConfig.hrPool);
        log.info(`DB Pool created successfully`)
        pool._logStats();
    } catch (err) {
        
        log.error(`<PERSON>rror creating DB Pool ${err}`);
        log.info(`Error creating DB Pool ${err}`);
    }
}

async function close() {
    await oracledb.getPool().close();
}

async function handlePoolTimeoutError() {
    log.info('Reinitializing the DB Pool...');
    close().then(() => {
      initialize();
    });
  }



function simpleExecute(statement, binds, opts = {}) {
    // binds = (binds && binds.length) > 0 ? binds : [];
    binds = binds || {};

    let poolRestart = false
    const log = new Logger().getInstance();
    return new Promise(async (resolve, reject) => {
        let conn;
        log.debug(`>>>>> Enter services.database.service.simpleExecute`);

        const cacheKey = generateCacheKey(statement,binds);

        const cachedResult = await getRedis(cacheKey);
        if (cachedResult) {
            log.info(`Cache found for the query.`);
            return resolve(JSON.parse(cachedResult));
        }

        log.info(`[Oracle] Query to be executed: ${statement} with binds: ${JSON.stringify(binds)}`);
        try {
            opts.outFormat = oracledb.OBJECT;
            opts.autoCommit = true;
            conn = await oracledb.getPool().getConnection();
            
            log.debug(`Oracle Client Version${oracledb.oracleClientVersionString}`);
            log.debug(`OracleDB npm version ${oracledb.versionString}`);
            log.debug(`Oracle Server Version ${conn.oracleServerVersionString}`);


            // const result = binds.length ? await conn.executeMany(statement, binds, opts) : await conn.execute(statement, [], opts);
            const result = await conn.execute(statement, binds, opts);

            if (shouldCache(statement)) {
                setRedis(cacheKey, JSON.stringify(result.rows), constants.redis.dataResponseTTL);
                }

            log.debug(`<<<<< Exit services.database.service.simpleExecute successfully`);
            return resolve(result.rows);

        } catch (err) {
            log.error(`<<<<< Exit services.database.service.simpleExecute with error ${err}`);
            
            log.info(`<<<<< Exit services.database.service.simpleExecute with error ${err}`);
            if (err.message.includes('NJS-040'))
                poolRestart = true
            reject(err);
        } finally {
            if (conn) { // conn assignment worked, need to close
                try {
                    log.debug(`Closing DB connection`);
                    await conn.close();
                    if (poolRestart)
                        await handlePoolTimeoutError(conn);
                } catch (err) {
                    
                    log.error(`Error Closing DB connection ${err}`);
                }
            }
        }
    });
}

function simpleExecuteNoCache(statement, binds, opts = {}) {
    binds = (binds && binds.length) > 0 ? binds : [];
    let poolRestart = false
    const log = new Logger().getInstance();
    return new Promise(async (resolve, reject) => {
        let conn;
        log.debug(`>>>>> Enter services.database.service.simpleExecute`);

        log.info(`Query to be executed: ${statement}`);
        try {
            opts.outFormat = oracledb.OBJECT;
            opts.autoCommit = true;
            conn = await oracledb.getPool().getConnection();
            
            log.debug(`Oracle Client Version${oracledb.oracleClientVersionString}`);
            log.debug(`OracleDB npm version ${oracledb.versionString}`);
            log.debug(`Oracle Server Version ${conn.oracleServerVersionString}`);

            let result;
            if ((binds && binds.length) > 0) {
                result = await conn.executeMany(statement, binds, opts);
            } else {
                result = await conn.execute(statement, binds, opts);
            }
            log.debug(`<<<<< Exit services.database.service.simpleExecute successfully`);
            
            return resolve(result);
        } catch (err) {
            
            log.error(`<<<<< Exit services.database.service.simpleExecute with error ${err}`);
            log.info(`<<<<< Exit services.database.service.simpleExecute with error ${err}`);
            if (err.message.includes('NJS-040'))
                poolRestart = true

            reject(err);
        } finally {
            if (conn) { // conn assignment worked, need to close
                try {
                    log.debug(`Closing DB connection`);
                    await conn.close();
                    if (poolRestart)
                        await handlePoolTimeoutError(conn);
                } catch (err) {
                    
                    log.error(`Error Closing DB connection ${err}`);
                }
            }
        }
    });
}

module.exports = {
    simpleExecute: simpleExecute,
    simpleExecuteNoCache: simpleExecuteNoCache,
    close: close,
    initialize: initialize
};
