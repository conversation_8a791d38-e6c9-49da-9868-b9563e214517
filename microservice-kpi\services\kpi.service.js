function ensureSeconds(timestamp) {
	// If the timestamp is in milliseconds (more than 10 digits), convert to seconds
	return timestamp.toString().length > 10
		? Math.floor(parseInt(timestamp, 10) / 1000)
		: parseInt(timestamp, 10);
}

/**
 * User friendly session name
 * @param {string} sessionType 
 */
function formatSessionType(sessionType) {
	let normalizedSessionType = sessionType;
  if (sessionType.includes("self_service")) {
    normalizedSessionType = sessionType.split("__")[1];
  }
  const MAP = {
    exploratory: "Exploratory",
    auto_ml: "Auto ML",
    basic_prep: "Basic Prep",
    advance_prep: "Advance Prep",
    dashboard: "Dashboard",
    my_bookmark: "My Bookmarks",
    Gen_AI: "Gen AI",
  };
  return MAP[normalizedSessionType] || normalizedSessionType;
}

module.exports = {
	ensureSeconds,
	formatSessionType,
};
