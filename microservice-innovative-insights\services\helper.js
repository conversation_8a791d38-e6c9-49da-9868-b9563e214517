function assignHierarchyValues(response, meta, domains) {
  if (!domains || !meta.TOPIC_NAME) {
    return response;
  }

  const { TOPIC_NAME, THEME_NAME } = meta;

  for (const fDomain of Object.values(domains)) {
    if (fDomain.parent_domain_name === TOPIC_NAME) {
      response.domain = TOPIC_NAME;
      response.domain_id = fDomain.id;
      response.domain_light_icon = `${process.env.CMS_BASEPATH_URL}${fDomain.page_menu_icon}`;
      response.domain_dark_icon = `${process.env.CMS_BASEPATH_URL}${fDomain.page_menu_light_icon}`;

      if (fDomain.subdomain) {
        for (const fSubDomain of Object.values(fDomain.subdomain)) {
          if (fSubDomain.subdomain_name === THEME_NAME) {
            response.subdomain = THEME_NAME;
            response.subdomain_id = fSubDomain.subdomain_node_id;
          }
        }
      }
      break; 
    }
  }

  return response;
}

module.exports = {
  assignHierarchyValues
};
