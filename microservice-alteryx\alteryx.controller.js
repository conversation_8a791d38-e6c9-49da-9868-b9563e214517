const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const AlteryxGalleryAPIOauthHelper = require("./helper/gallery-api-helper");

async function alteryxOauthParamsController(req) {
  const { httpMethod, host, endpointPath } = req.body;
  try {
    const oAuthHelper = new AlteryxGalleryAPIOauthHelper();
    const oAuthParams = oAuthHelper.generateAuthorizedParams(
      httpMethod,
      host,
      endpointPath
    );
    return oAuthParams;
  } catch (err) {
    log.error(
      `<<<<<Exited microservice-alteryx.alteryx.controller.alteryxOauthParamsController on getting params with: ${err}`
    );
    throw err;
  }
}

module.exports = { alteryxOauthParamsController };
