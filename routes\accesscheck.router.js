const express = require("express");
const router = new express.Router();
const accessCheckController = require("../microservice-accesscheck/accesscheck.controller");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

router.get("/", async (req, res, next) => {
  try {
    const data = await accessCheckController.getAccessCheck(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for accesscheck, ERROR: ${err}`);
    next(err);
  }
});

router.get("/access", async (req, res, next) => {
  try {
    const data = await accessCheckController.getAccessFromDb(req);
    res.set("Content-Type", "application/json");
    res.send(data);
    next();
  } catch (err) {
    log.error(`Error fetching data for getAccessFromDb, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;
