const db = require('./database.service');
const clkdb = require('./clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { 
  uploadCompareDataQuery,
  postMasterDataQuery,
  getDataExistQuery,
  getDataPublishQuery,
  getCompareDataQuery,
  deleteExistingDataQuery,
  deleteExistingDataMasterQuery,
  unpublishUploadedDataMasterQuery,
  getSLAExpiryQuery,
  setSLAEmailStatusQuery,
  getArcGISTokenQuery,
  insertArcGISTokenQuery,
  deleteArcGISTokenQuery,
  getInteractionDataQuery,
  getViewNameByIdQuery,
  getDynamicJsonByIdQuery,
  getUserDataQuery,
  setRefreshTokenQuery,
  getRefreshTokenQuery,
  revokeRefreshTokenQuery
} = require('./getQuery.service');

const constants = require('../config/constants.json');
const { IFPError } = require('../utils/error');

/**
 * Function to get user accepted terms and conditions flag from db values provided in userData
 * @param {*} userData - user details object 
 */
async function postUploadCompareData(values) {
  return new Promise((resolve, reject) => {
    uploadCompareDataQuery().then((query) => {
      db.simpleExecute(query, values)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.postUploadCompareData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.postUploadCompareData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      log.error(`<<<<< Exited services.executeQuery.postUploadCompareData with error ${err}`);
      reject(err);
    })
  })
}

async function postMasterData(inputData) {
  return new Promise((resolve, reject) => {
    postMasterDataQuery(inputData).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.postMasterData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery..postMasterData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      log.error(`<<<<< Exited services.executeQuery.postMasterData with error ${err}`);
      reject(err);
    })
  })
}

async function getUploadCompareData(obj) {
  return new Promise((resolve, reject) => {
    getCompareDataQuery(obj).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.getUploadCompareData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.getUploadCompareData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      log.error(`<<<<< Exited service.executeQuery.getUploadCompareData with error ${err}`);
      reject(err);
    })
  })
}

async function checkUploadedDataExists(obj) {
  return new Promise((resolve, reject) => {
    getDataExistQuery(obj).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.checkUploadedDataExists successfully`);
          if (data.length > 0) {
            resolve(true);
          } else {
            resolve(false);
          }
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.checkUploadedDataExists with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      log.error(`<<<<< Exited  services.executeQuery.checkUploadedDataExists with error ${err}`);
      reject(err);
    })
  })
}

async function checkUploadedDataPublished(obj) {
  return new Promise((resolve, reject) => {
    getDataPublishQuery(obj).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.checkUploadedDataExists successfully`);
          if (data.length > 0) {
            resolve(true);
          } else {
            resolve(false);
          }
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.checkUploadedDataExists with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      log.error(`<<<<< Exited  services.executeQuery.checkUploadedDataExists with error ${err}`);
      reject(err);
    })
  })
}

async function deleteExistingData(obj) {
  let query1 = await deleteExistingDataQuery(obj);
  let query2 = await deleteExistingDataMasterQuery(obj);
  let query = [query1, query2];
  let response = [];
  return new Promise((resolve, reject) => {
    query.forEach(q => {
      db.simpleExecute(q)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.deleteExistingData successfully`);
          if (data.rowsAffected > 0) {
            response.push(data.rowsAffected);
            if (query.length === response.length) {
              resolve(true);
            }
          } else {
            reject([423, `Unable to execute query ${q}`])
          }
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.deleteExistingData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    });
  })
}

async function unpublishMasterData(obj) {
  return new Promise((resolve, reject) => {
    unpublishUploadedDataMasterQuery(obj).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.unpublishMasterData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.unpublishMasterData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited service.executeQuery.unpublishMasterData with error ${err}`);
      reject(err);
    })
  })
}

async function getSLAExpiryData(obj) {
  return new Promise((resolve, reject) => {
    getSLAExpiryQuery(obj).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.getSLAExpiryData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.getSLAExpiryData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited service.executeQuery.getSLAExpiryData with error ${err}`);
      reject(err);
    })
  })
}

async function setSLAEmailStatus(email,status){
  return new Promise((resolve, reject) => {
    setSLAEmailStatusQuery(email,status).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.setSLAEmailStatus successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.setSLAEmailStatus with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited service.executeQuery.setSLAEmailStatus with error ${err}`);
      reject(err);
    })
  })
}

async function getArcGISTokenData(email,status){
  return new Promise((resolve, reject) => {
    getArcGISTokenQuery(email,status).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.getArcGISTokenData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.getArcGISTokenData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited service.executeQuery.getArcGISTokenData with error ${err}`);
      reject(err);
    })
  })
}

async function insertArcGISToken(email,status){
  return new Promise((resolve, reject) => {
    insertArcGISTokenQuery(email,status).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.insertArcGISToken successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.insertArcGISToken with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited service.executeQuery.insertArcGISToken with error ${err}`);
      reject(err);
    })
  })
}

async function deleteArcGISToken(){
  return new Promise((resolve, reject) => {
    deleteArcGISTokenQuery().then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.deleteArcGISToken successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.deleteArcGISToken with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited service.executeQuery.deleteArcGISToken with error ${err}`);
      reject(err);
    })
  })
}

async function getInteractionData(nodeId,userEmail) {
  return new Promise((resolve, reject) => {
    getInteractionDataQuery(nodeId,userEmail).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit services.executeQuery.service.getInteractionData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit services.executeQuery.service.getInteractionData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-interaction.executeQuery.service.getInteractionData with error ${err}`);
      reject(err);
    })
  })
}

async function getViewNameById(id){
  let {query,binds} = await getViewNameByIdQuery(id)
  let data = await clkdb.simpleExecute(query,binds)
  if (data.length)
    return data[0].VIEWNAME
  else
    throw new IFPError(400,`Invalid source. Not valid source available with this id: ${id}`)
}

async function getDynamicJsonById(id){
  let {query,binds} = await getDynamicJsonByIdQuery(id)
  let data = await clkdb.simpleExecute(query,binds)
  return data
}

async function getTableColumns(tableName) {
  try {
    let data;
    const clkTables = constants.clickhouseTables
    if (clkTables.includes(tableName)){
      let query = `SELECT name AS NAME FROM system.columns WHERE table='${tableName}' AND name NOT IN ('OBS_DT') AND database='${process.env.CLICKHOUSE_DATABASE}'`
      data = await clkdb.simpleExecute(query) 
    }
    else{
      const binds = {
        tableName: tableName,
        owner: process.env.DB_USER
      };

      let query = `SELECT COLUMN_NAME AS NAME
          FROM all_tab_columns
          WHERE table_name = :tableName AND column_name NOT IN ('OBS_DT') AND OWNER = :owner`;
     data = await db.simpleExecute(query,binds)
    }
    log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getTableColumns successfully`);
    return data
  } catch (err) {
    log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getTableColumns with error ${err} `);
    throw err;
  }
}

async function getUserData(uuid) {
  try {
    let { query, binds } = await getUserDataQuery(uuid);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0)
      throw new IFPError(404, `User with given id: '${uuid}'' not found`);
    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exited services.executeQuery.service.getUserData with error ${err}`
    );
    throw err;
  }
}
async function setRefreshToken(user, refreshToken) {
  try {

    let { query, binds } = await setRefreshTokenQuery(user, refreshToken);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited services.executeQuery.service.setRefreshToken with error ${err}`);
    throw err;
  }
}

async function getRefreshToken(user) {
  try {
    let { query, binds } = await getRefreshTokenQuery(user);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited services.executeQuery.service.getRefreshToken with error ${err}`);
    throw err;
  }
}

async function revokeRefreshToken(user) {
  try {
    let { query, binds } = await revokeRefreshTokenQuery(user);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited services.executeQuery.service.revokeRefreshToken with error ${err}`);
    throw err;
  }
}


module.exports = { 
  postUploadCompareData,
  postMasterData,
  getUploadCompareData,
  checkUploadedDataExists,
  checkUploadedDataPublished,
  deleteExistingData,
  unpublishMasterData,
  getSLAExpiryData,
  setSLAEmailStatus,
  getArcGISTokenData,
  insertArcGISToken,
  deleteArcGISToken,
  getInteractionData,
  getViewNameById,
  getTableColumns,
  getDynamicJsonById,
  getUserData,
  setRefreshToken,
  getRefreshToken,
  revokeRefreshToken
 }