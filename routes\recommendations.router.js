const express = require('express');
const router = new express.Router();
const recommendationsController = require('../microservice-recommendations/recommendations.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { validateRecommendations } = require('./validators/recommendations-validator');

router.post('/', validateRecommendations, async (req, res, next) => {
    try {
      const data = await recommendationsController.getRecommendations(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

module.exports = router;