const request = require('superagent');
const baseUrl = global.baseUrl;
const pjson = require('../package.json');


describe('(HealthCheck): SCAD BE Health Check', function () {
  
  it('should get healthcheck reposnse', async function () {
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    const baseUrl = process.env.BASE_SERVICE_URL || "http://localhost:3000"
    const health_check_url = baseUrl + '/api/healthcheck';
    console.log(health_check_url);
    const res = await request.get(health_check_url);
    expect(res.status).toBe(200);
    expect(res.text).toBe('Health Check');
  });

  it('should get correct microservice version', async function () {
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    const baseUrl = process.env.BASE_SERVICE_URL || "http://localhost:3000"
    const version_check_url = baseUrl + '/api/version';
    console.log(version_check_url);
    const res = await request.get(version_check_url);
    expect(res.status).toBe(200);
    expect(res.text).toBe(pjson.version);
  });
});