const crypto = require('crypto')
const { getDXPData, deltaShareConsumer } = require("./api.service");
const { dxpUrls } = require("../../config/constants.json");
const { IFPError } = require("../../utils/error");
const { getRedis, setRedis } = require('../../services/redis.service');

async function getAsset(req, assetId) {
  const url = `${dxpUrls.ASSET.DETAIL}/${assetId}`;
  try {
    const asset = await getDXPData(req, "GET", url);
    return asset;
  } catch (error) {
    throw error;
  }
}

async function getAssetTableData(assetId, assetCategory, tablePath) {
  try {
    // Get share details
    const response = await deltaShareConsumer.post(
      `/asset/${assetId}/table-data`,
      {
        asset_category: assetCategory,
        table_path: tablePath,
      }
    );
    const data = response.data;
    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Main function to fetch data for a given asset from the
 * Delta Share Service.
 * 
 * @param {*} req 
 * @param {Object} shareToken The Delta Share token for the product that
 * the asset is a part of.
 * @param {string} assetId The DXP asset ID.
 * @returns 
 */
async function getAssetTableDataWrapper(req, shareToken, assetId) {
  try {

    // Get cached data
    const cacheHash = crypto.createHash('md5').update(`${req.user.preferred_username}${assetId}`).digest("hex")
    const cacheKey = `node_be:dxp:table_data:${cacheHash}`
    const cachedData = await getRedis(cacheKey)
    if (cachedData) {
      return JSON.parse(cachedData)
    }

    // 1. Get table path.
    // See: https://github.com/delta-io/delta-sharing/blob/main/README.md#quick-start
    // 1.1 get share name
    const { shareName } = shareToken;
    if (!shareName) {
      // See: getOrCreateDeltaShareToken
      throw new IFPError(500, "Share name not found in token");
    }

    // 1.2 get table full name
    const asset = await getAsset(req, assetId);
    const assetCategory = asset.integrationMetadata.bayaan_category.value;
    let tableName = asset.edges[0].definition.resource_id.fullName;

    // 1.3 Generate Delta Share table path
    const tablePath = `${JSON.stringify(
      shareToken
    )}#${shareName}.${tableName.split(".").slice(1).join(".")}`;

    // 2. Fetch data via delta share service
    const tableData = await getAssetTableData(
      assetId,
      assetCategory,
      tablePath
    );

    await setRedis(cacheKey, JSON.stringify(tableData), 5000)

    return tableData;
  } catch(error) {
    throw new IFPError(500, "Failed to get asset table data");
  }
}

module.exports = {
  getAsset,
  getAssetTableData,
  getAssetTableDataWrapper,
};
