const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function uploadCompareDataQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO UPL_CMPR_DATA VALUES (TO_DATE(:1, 'yyyy-mm-dd'),:2,:3,TO_DATE(:4, 'yyyy-mm-dd'),:5,:6,TO_DATE(:7, 'yyyy-mm-dd'),:8)`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.uploadCompareDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function postMasterDataQuery(inputData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO UPL_CMPR_MASTER (LAST_UPLOAD_DATE, USERID, INSERT_DT, INSERT_USER_ID, CMS_NODE) VALUES (TO_DATE('${inputData.last_updated_date}', 'yyyy-mm-dd'),'${inputData.userId}',TO_DATE('${inputData.insertDt}','yyyy-mm-dd'),'${inputData.insertUserId}','${inputData.nodeId}')`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.postMasterDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getDataExistQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM UPL_CMPR_MASTER WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.getDataExistQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getDataPublishQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM UPL_CMPR_MASTER WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}' AND PUBLISHED = '1'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.getDataPublishQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getCompareDataQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM UPL_CMPR_DATA WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.getCompareDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function deleteExistingDataQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM UPL_CMPR_DATA WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.deleteExistingDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


async function deleteExistingDataMasterQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM UPL_CMPR_MASTER WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.deleteExistingDataMasterQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function unpublishUploadedDataMasterQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE UPL_CMPR_MASTER SET PUBLISHED = '0' WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.unpublishUploadedDataMasterQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getSLAExpiryQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_USER_ACCESS_SLA WHERE IS_MAILSENT=0`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.getSLAExpiryQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function setSLAEmailStatusQuery(email, status) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_USER_ACCESS_SLA SET IS_MAILSENT=${status} WHERE EMAIL='${email}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.setSLAEmailStatusQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getArcGISTokenQuery(token, expiry) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_ARCGIS_TOKEN ORDER BY EXPIRY DESC`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.getArcGISTokenQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function insertArcGISTokenQuery(token, expiry) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_ARCGIS_TOKEN (TOKEN, EXPIRY) VALUES('${token}','${expiry}')`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.insertArcGISTokenQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


async function deleteArcGISTokenQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM IFP_ARCGIS_TOKEN`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.deleteArcGISTokenQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInteractionDataQuery(userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_INTERACTION_MAP WHERE EMAIL = '${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited services.getQuery.service.getInteractionDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getViewNameByIdQuery(id){
    try{
        let binds = {
            id:id
        }
        let query = `SELECT VIEWNAME FROM IFP_SCREENER_VIEW_MAP WHERE ID = {id:Int}`
        return {query:query,binds:binds}
    } catch (err) {
        log.error(`<<<<<< Exited services.getQuery.service.getViewNameById with error ${err} `);
        throw err;
    }
}

async function getDynamicJsonByIdQuery(id){
    try{
        let binds = {

        }
        let query;
        if (Array.isArray(id)){
            query = `SELECT NODE_ID FROM VW_DYNAMIC_JSON_CONFIG WHERE INDICATOR_ID IN (${id.join(',')})`
        }       
        else{
            binds.id = id
            query = `SELECT NODE_ID FROM VW_DYNAMIC_JSON_CONFIG WHERE INDICATOR_ID = {id:Int}`
        }
        
        return {query:query,binds:binds}
    } catch (err) {
        log.error(`<<<<<< Exited services.getQuery.service.getDynamicJsonByIdQuery with error ${err} `);
        throw err;
    }
}


async function getUserDataQuery(uuid) {
    try {
      const binds = {
        id: uuid,
      };
      const query = `
        SELECT * FROM IFP_FLOW_USERS_V2 WHERE ID=:ID
      `;
  
      return { query: query, binds: binds };
    } catch (err) {
      log.error(
        `<<<<<< Exited services.getQuery.service.getUserDataQuery with error ${err} `
      );
      throw err;
    }
  }

  async function setRefreshTokenQuery(userId, refreshToken) {
    try {
        const query = `
            MERGE INTO IFP_REFRESH_TOKENS tgt
            USING (
            SELECT :userId AS ID, :refreshToken AS REFRESH_TOKEN FROM dual
            ) src
            ON (tgt.USER_ID = src.ID)
            WHEN MATCHED THEN
            UPDATE SET tgt.REFRESH_TOKEN = src.REFRESH_TOKEN
            WHEN NOT MATCHED THEN
            INSERT (USER_ID, REFRESH_TOKEN) 
            VALUES (src.ID, src.REFRESH_TOKEN)
        `;
  
      const binds = {
        userId: userId,
        refreshToken: refreshToken
      };
      return { query: query, binds: binds };
    }
    catch (err) {
      log.error(
        `<<<<<< Exited services.getQuery.service.setRefreshTokenQuery with error ${err} `
      );
      throw err;
    }
    }

async function getRefreshTokenQuery(userId) {
    try {
        const binds = {
            id: userId,
        };
        const query = `
        SELECT REFRESH_TOKEN FROM IFP_REFRESH_TOKENS WHERE ID=:ID
        `;

        return { query: query, binds: binds };
    } catch (err) {
        log.error(
            `<<<<<< Exited services.getQuery.service.getRefreshTokenQuery with error ${err} `
        );
        throw err;
    }
}

async function revokeRefreshTokenQuery(userId) {
    try {
        const binds = {
            id: userId,
        };
        const query = `
        DELETE FROM IFP_REFRESH_TOKENS WHERE ID=:ID
        `;

        return { query: query, binds: binds };
    } catch (err) {
        log.error(
            `<<<<<< Exited services.getQuery.service.revokeRefreshTokenQuery with error ${err} `
        );
        throw err;
    }
}


async function getWhatsNewNodeBasicDetailsQuery(req){
    const user_groups = req.user.groups;
    const binds = {
        group_ids: user_groups,
    };
    const lang = req.headers["accept-language"] === 'en' ? '' : '_ar';
    const commonColumns = `
        vdoi.indicator_id, vdoi.note, vdoi.enableCompare,
        vdoi.component_subtitle${lang} AS subtitle,
        vdoi.theme_id, vdoi.showLegend, vdoi.subtheme_id,
        vdoi.language, vdoi.showPointLabels, vdoi.yAxisFormat,
        vdoi.component_title${lang} AS title,
        vdoi.domain_id, vdoi.tooltipValueFormat, vdoi.product_id,
        vdoi.xAxisLabel, vdoi.publication_date,
        vdoi.theme${lang} AS theme,
        vdoi.product${lang} AS product,
        vdoi.subtheme${lang} AS subtheme,
        vdoi.narrative,
        vdoi.domain${lang} AS domain,
        vdoi.xAxisFormat, vdoi.domain_details,
        vdoi.label, vdoi.node_id,
        vdoi.data_source, vdoi.yAxisLabel,
        vdoi.name, vdoi.updated, vdoi.tooltipTitleFormat
    `;
    const applicationQuery = `
        SELECT ${commonColumns}
        FROM VW_DYNAMIC_OFFICIAL_INDICATORS AS vdoi
        WHERE vdoi.group_id IN {group_ids:Array(String)}
        AND (vdoi.screener_config IS NULL OR TRIM(vdoi.screener_config) = '')
        ORDER BY vdoi.updated DESC
        FETCH FIRST 6 ROWS ONLY
    `;
    
    return {query:applicationQuery,binds:binds};
}



module.exports = { 
    uploadCompareDataQuery,
    unpublishUploadedDataMasterQuery,
    postMasterDataQuery,
    getDataExistQuery,
    getDataPublishQuery,
    getCompareDataQuery,
    deleteExistingDataQuery,
    deleteExistingDataMasterQuery,
    getSLAExpiryQuery,
    setSLAEmailStatusQuery,
    getArcGISTokenQuery,
    insertArcGISTokenQuery,
    deleteArcGISTokenQuery,
    getInteractionDataQuery,
    getViewNameByIdQuery,
    getDynamicJsonByIdQuery,
    getUserDataQuery,
    setRefreshTokenQuery,
    getRefreshTokenQuery,
    revokeRefreshTokenQuery,
    getWhatsNewNodeBasicDetailsQuery,
 }