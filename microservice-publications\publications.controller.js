const Logger = require('scad-library').logger;
const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const constants = require('../config/constants.json');

const log = new Logger().getInstance();
/**
 * function to get whats-new content from CMS
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function getPublicationById(req) {
  log.debug('>>>>>Entered publications-microservice.publications.controller.getPublications');
  return new Promise(async (resolve, reject) => {
    try{
      let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      const cmsPublicationsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PUBLICATION_URL}/${req.params.id}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

      const cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsPublicationsUrl, req.user.groups)
      let response = cmsResponse[0]
      return resolve(response)
    }
    catch(err){
      reject(err)
    }
  });
}

module.exports = { getPublicationById };
