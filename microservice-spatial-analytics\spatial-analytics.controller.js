const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
let constants = require('../config/constants.json');

/**
 * function to get domains content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function getSpatialAnalytics(req) {
  log.debug(`>>>>>Entered header-microservice.spatial-analytics.controller.getSpatialAnalytics`);
  return new Promise(async (resolve, reject) => {
    try {
      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      const cmsSpatialAnalyticsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_SPATIAL_ANALYTICS_LIST}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      const data = await getMetaFromCMS(req,cmsLoginUrl, cmsSpatialAnalyticsUrl, req.user.groups);

      data.forEach(element =>{
        element.customLayers.forEach(layer => {
          layer.dark_icon =  `${process.env.CMS_BASEPATH_URL}${layer.dark_icon}`
          layer.light_icon =  `${process.env.CMS_BASEPATH_URL}${layer.light_icon}`
        })

  
        element.commonLayers.forEach(layer => {
          layer.dark_icon =  `${process.env.CMS_BASEPATH_URL}${layer.dark_icon}`
          layer.light_icon =  `${process.env.CMS_BASEPATH_URL}${layer.light_icon}`
        })

        element.modules.forEach(module =>{
          module.layers.forEach(layer => {
            layer.dark_icon =  `${process.env.CMS_BASEPATH_URL}${layer.dark_icon}`
            layer.light_icon =  `${process.env.CMS_BASEPATH_URL}${layer.light_icon}`

          })
          module.attachment_excel = `${process.env.CMS_BASEPATH_URL}${module.attachment_excel}`
          module.attachment_pdf = `${process.env.CMS_BASEPATH_URL}${module.attachment_pdf}`
        })
      })

      log.debug(`<<<<<Exited header-microservice.spatial-analytics.controller.getSpatialAnalytics successfully `);
      
      resolve(data);
    } catch (err) {
      
      log.error(`<<<<<Exited header-microservice.spatial-analytics.controller.getSpatialAnalytics on getting CMS data with error ${err}`);
      reject(err);
    }
  })
}

module.exports = { getSpatialAnalytics };
