const valuesDataFunctions = require('scad-library').valuesData;
const newValuesDataFunctions = require('scad-library').valuesDataAnalytical;
const { getValuesDataFromDB } = require('./getGraphData.service');
const Logger = require('scad-library').logger;

const log = new Logger().getInstance();

async function getValuesData(valuesData, data, scadEstimate) {
    return new Promise(async (resolve, reject) => {
        try {
            let scadData = [];
            let coiData = [];
            if (data.seriesMeta) {
                data.seriesMeta.forEach(series => {
                    if (series.id.includes('-forecast')) {
                        coiData.push(series.data);
                    }
                    else if (!series.id.includes('-forecast')) {
                        scadData.push(series.data);
                    }
                })
            } else {
                scadData = data;
            }
            let counter = [];
            for (let value of valuesData) {
                switch (value.id) {
                    case "total-population-value": {
                        const data = valuesDataFunctions.population(coiData);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "scad-projection": {
                        const data = valuesDataFunctions.scadEstimate(coiData, scadEstimate);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "current": {
                        const data = valuesDataFunctions.currentDate();
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "current-index": {
                        const data = valuesDataFunctions.todayIndex(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "current-percentage": {
                        const data = valuesDataFunctions.todayPercentage(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "quarter-index": {
                        const data = valuesDataFunctions.quarterIndex(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "quarter-percentage": {
                        const data = valuesDataFunctions.quarterPercentage(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "official-date": {
                        const data = valuesDataFunctions.officialDate(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "estimate": {
                        const data = valuesDataFunctions.estimatePercentage(scadData[0]);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case 'year-to-date-value': {
                        let dbData = await getValuesDataFromDB(value);
                        const data = valuesDataFunctions.yearToDateValue(value, dbData);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case "previous-quarter-index": {
                        const data = valuesDataFunctions.previousQuarterIndex(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        break;
                    }
                    case "previous-quarter-percentage": {
                        const data = valuesDataFunctions.previousQuarterPercentage(scadData[0], coiData[0]);
                        value = Object.assign(value, data);
                        break;
                    }
                    case 'year-to-date': {
                        let dbData = await getValuesDataFromDB(value);
                        const data = valuesDataFunctions.yearToDate(value, dbData);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    case 'percentage-change': {
                        let dbData = await getValuesDataFromDB(value);
                        let data = newValuesDataFunctions.percentageChange(value, dbData);
                        value = Object.assign(value, data);
                        counter.push(1);
                        break;
                    }
                    default: {
                        reject(`Values Function not available`);
                        break;
                    }
                }
                if (valuesData.length === counter.length) {
                    resolve();
                }
            }
        }
        catch (err) {
            
            reject([422, err]);
        }
    });
}

module.exports = { getValuesData }