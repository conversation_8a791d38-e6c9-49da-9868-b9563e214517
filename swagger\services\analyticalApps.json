{"paths": {"/content-type/analytical-apps/": {"get": {"tags": ["Analytical Apps"], "summary": "Lists Analytical Apps available to the user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "component_title": {"type": "string"}, "component_subtitle": {"type": "string"}}, "required": ["id", "type", "component_title", "component_subtitle"]}}}, "required": ["id", "items"]}}}}}}}}, "/content-type/analytical-apps/v2": {"get": {"tags": ["Analytical Apps"], "summary": "Lists V2 Analytical Apps available to the user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "component_title": {"type": "string"}, "component_subtitle": {"type": "string"}}, "required": ["id", "type", "component_title", "component_subtitle"]}}}, "required": ["id", "items"]}}}}}}}}, "/content-type/analytical-apps/{id}": {"post": {"tags": ["Analytical Apps"], "summary": "Lists Analytical Apps by id", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Analytical App"}], "requestBody": {"description": "Analytical App data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndicatorDrivers"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "component_title": {"type": "string"}, "component_subtitle": {"type": "string"}, "page_icon": {"type": "string"}, "page_light_icon": {"type": "string"}, "domains": {"type": "array", "items": {"type": "string"}}, "theme": {"type": "string"}, "subtheme": {"type": "string"}, "product": {"type": "string"}, "application_url": {"type": "string"}, "imgSrc": {"type": "string"}, "attachment": {"type": "string"}, "policy_guide": {"type": "string"}, "note": {"type": "string"}, "search_tags": {"type": "array", "items": {"type": "string"}}, "narrative": {"type": "string"}, "Indicator": {"type": "string"}, "indicator_list": {"type": "string"}, "indicatorValues_subtitle": {"type": "string"}, "indicatorValues_title": {"type": "string"}, "visualization_subtitle": {"type": "string"}, "visualization_title": {"type": "string"}, "default_visualisation": {"type": "string"}, "enableDynamicPanel": {"type": "boolean"}, "listofDyanmicPanelContent": {"type": "array", "items": {"type": "string"}}, "highlightsMeta": {"type": "string"}, "infogramUrl": {"type": "string"}, "confidenceIntervalMeta": {"type": "string"}, "enableConfidenceInterval": {"type": "boolean"}, "endpoint_label": {"type": "string"}, "endpoint_title": {"type": "string"}, "endpoint_url": {"type": "string"}, "default_layer": {"type": "string"}, "show_on_legend": {"type": "string"}, "defaultDistrictId": {"type": "string"}, "endpointType": {"type": "string"}, "nodeId": {"type": "string"}, "summaryCardId": {"type": "string"}, "endpoint_icon_id": {"type": "string"}, "cardDate": {"type": "string"}, "dashboardUrl": {"type": "string"}, "enablePointToggle": {"type": "boolean"}, "maxPointLimit": {"type": "string"}, "minLimitYAxis": {"type": "string"}, "tagName": {"type": "string"}, "tagColorCode": {"type": "string"}, "showInsights": {"type": "string"}, "height": {"type": "string"}, "host_url": {"type": "string"}, "embedded_code_version": {"type": "string"}, "site_root": {"type": "string"}, "external_name": {"type": "string"}, "tabs": {"type": "string"}, "toolbar": {"type": "string"}, "showAppBanner": {"type": "boolean"}, "related_items": {"type": "string"}, "related_insight_discoveries": {"type": "string"}, "related_scenario_drivers": {"type": "string"}, "domain_id": {"type": "integer"}, "domain": {"type": "string"}, "theme_id": {"type": "null"}, "subtheme_id": {"type": "null"}, "product_id": {"type": "null"}, "related_innovative_indicators": {"type": "array", "items": {"type": "string"}}, "data_source": {"type": "string"}, "publication_date": {"type": "string"}, "updated": {"type": "string"}, "external_name_dark": {"type": "string"}, "domain_details": {"type": "object", "properties": {"page_menu_icon": {"type": "string"}, "page_menu_light_icon": {"type": "string"}, "scad_description": {"type": "string"}, "security": {"type": "string"}, "uid": {"type": "string"}, "title": {"type": "string"}, "created": {"type": "string"}}}, "compare_data": {"type": "string"}, "indicatorDrivers": {"type": "null"}, "indicatorValues": {"type": "object", "properties": {"overviewValuesMeta": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "valueFormat": {"type": "string"}, "templateFormat": {"type": "string"}, "viewName": {"type": "string"}, "compareFilters": {"type": "array", "items": {"type": "string"}}, "hasDefault": {"type": "boolean"}, "dimension": {"type": "object", "properties": {"DASHBOARD_NAME": {"type": "string"}}}}}}}}, "unit": {"type": "string"}, "updatedDateFromDB": {"type": "boolean"}}, "required": ["id", "type", "component_title", "domains"]}}}}}}}}, "components": {"schemas": {"IndicatorDrivers": {"type": "object", "properties": {"indicatorDrivers": {"type": "object", "description": "Object containing drivers for indicators within the app", "additionalProperties": true}}, "required": ["indicatorDrivers"], "example": {"indicatorDrivers": {"driver1": "value1", "driver2": "value2"}}}}}}