{"paths": {"/content-type/official-insights/": {"post": {"tags": ["Official Screener Indicators"], "summary": "Retrieves list of Official Screener Indicators based on the provided filters", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "page", "in": "query", "description": "Page", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "Maximum number of items per page", "required": false, "type": "integer", "format": "int32"}], "requestBody": {"description": "Official Screener Filters. Depends on the type of Screener", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficialScreenerFilters"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"totalCount": {"type": "integer", "example": 100, "description": "The total count of items as a string representing a number."}, "page": {"type": "integer", "example": 1, "description": "The current page number."}, "limit": {"type": "integer", "example": 10, "description": "The number of items per page."}, "data": {"type": "array", "items": {"type": "object", "properties": {"indicatorId": {"type": "string", "description": "Unique identifier for the indicator."}, "title": {"type": "string", "description": "Title of the indicator."}, "compareFilters": {"type": "array", "items": {"type": "string"}, "description": "Filters used for comparison."}, "valueFormat": {"type": "string", "description": "Format of the value."}, "templateFormat": {"type": "string", "description": "Format of the template."}, "baseDate": {"type": ["string", "null"], "description": "Base date for the indicator, if applicable."}, "value": {"type": "number", "description": "Value of the indicator."}, "yearlyCompareValue": {"type": ["number", "null"], "description": "Yearly comparison value."}, "yearlyChangeValue": {"type": ["number", "null"], "description": "Yearly change value."}, "quarterlyCompareValue": {"type": ["number", "null"], "description": "Quarterly comparison value, if applicable."}, "quarterlyChangeValue": {"type": ["number", "null"], "description": "Quarterly change value, if applicable."}, "monthlyCompareValue": {"type": ["number", "null"], "description": "Monthly comparison value, if applicable."}, "monthlyChangeValue": {"type": ["number", "null"], "description": "Monthly change value, if applicable."}, "domain": {"type": "string", "description": "Domain of the indicator."}, "subdomain": {"type": "string", "description": "Subdomain of the indicator."}, "subtheme": {"type": "string", "description": "Subtheme related to the indicator."}, "product": {"type": "string", "description": "Product related to the indicator."}, "unit": {"type": "string", "description": "Unit of measurement for the indicator's value."}}, "required": ["indicatorId", "title", "compareFilters", "valueFormat", "templateFormat", "value", "domain", "subdomain", "subtheme", "product", "unit"]}}}, "required": ["totalCount", "page", "limit", "data"]}}}}}}}, "/content-type/official-insights/{id}": {"get": {"tags": ["Official Screener Indicators"], "summary": "Retrieve Detailed Official Screener Indicator by ID", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "The ID of the Official Screener Indicator"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the component."}, "component_title": {"type": "string", "description": "Title of the component."}, "component_subtitle": {"type": "string", "description": "Subtitle of the component."}, "domain": {"type": "string", "description": "Domain the component belongs to."}, "type": {"type": "string", "description": "Type of the component."}, "note": {"type": "string", "description": "Note related to the component."}, "maxPointLimit": {"type": ["string", "null"], "description": "Maximum point limit, if applicable."}, "minLimitYAxis": {"type": ["string", "null"], "description": "Minimum limit on the Y-axis, if applicable."}, "content_classification": {"type": "string", "description": "Classification of the content."}, "domain_id": {"type": "string", "description": "Identifier for the domain."}, "tagName": {"type": "string", "description": "Tag name associated with the component."}, "language": {"type": "string", "description": "Language of the component content."}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the tool."}, "disabled": {"type": ["boolean", "null"], "description": "Indicates if the tool is disabled."}, "label": {"type": "string", "description": "Label of the tool."}}, "required": ["id", "label"]}, "description": "Tools available for the indicator."}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the filter."}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the option."}, "label": {"type": "string", "description": "Label of the option."}, "unit": {"type": ["string", "null"], "description": "Unit of the option."}, "value": {"type": ["number", "null"], "description": "Value of the option."}, "isSelected": {"type": ["boolean", "null"], "description": "Indicates if the option is selected."}}, "required": ["id", "label"]}}}, "required": ["id", "options"]}, "description": "Filters available for the indicator."}, "indicatorDrivers": {"type": "array", "description": "Drivers for the indicator."}, "indicatorValues": {"type": "object", "description": "Values of the indicator."}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier for the visualization meta."}, "type": {"type": "string", "description": "Type of the visualization."}, "seriesMeta": {"type": "array", "items": {"type": "object"}, "description": "<PERSON><PERSON><PERSON> for the series in the visualization."}}, "required": ["id", "type", "seriesMeta"]}}, "visualizationDefault": {"type": "string", "description": "Default visualization."}}, "description": "Visualizations for the indicator."}, "domain_light_icon": {"type": "string", "description": "URL for the domain's light icon."}, "domain_dark_icon": {"type": "string", "description": "URL for the domain's dark icon."}, "subdomain": {"type": "string", "description": "Subdomain of the indicator."}, "viewName": {"type": "string", "description": "Name of the view."}, "content_classification_key": {"type": "string", "description": "Key for content classification."}, "overView": {"type": "object", "properties": {"compareFilters": {"type": "array", "items": {"type": "string"}}, "valueFormat": {"type": "string"}, "templateFormat": {"type": "string"}, "baseDate": {"type": ["string", "null"]}, "value": {"type": "number"}, "yearlyCompareValue": {"type": ["number", "null"]}, "yearlyChangeValue": {"type": ["number", "null"]}, "quarterlyCompareValue": {"type": ["number", "null"]}, "quarterlyChangeValue": {"type": ["number", "null"]}, "monthlyCompareValue": {"type": ["number", "null"]}, "monthlyChangeValue": {"type": ["number", "null"]}}, "description": "Overview of the indicator."}, "unit": {"type": "string", "description": "Unit of measurement."}, "data_source": {"type": "string", "description": "Source of the data."}, "publication_date": {"type": "string", "description": "Date of publication."}, "isMultiDimension": {"type": "boolean", "description": "Indicates if the indicator is multi-dimensional."}, "tableFields": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "Label for the table field."}, "path": {"type": "string", "description": "Path for the table field."}}, "required": ["label", "path"]}, "description": "Fields for the table representation."}}, "required": ["id", "component_title", "domain", "type", "content_classification", "domain_id", "tagName", "language", "indicatorTools", "indicatorFilters", "indicatorVisualizations", "subdomain", "viewName", "content_classification_key", "unit", "data_source", "publication_date", "tableFields"]}}}}}}}, "/content-type/official-insights/filters/{id}": {"get": {"tags": ["Official Screener Indicators"], "summary": "Retrieve Filter for Official Screener", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Official Indicator"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the component."}, "component_title": {"type": "string", "description": "Title of the component."}, "component_subtitle": {"type": "string", "description": "Subtitle of the component, if any."}, "domain": {"type": "string", "description": "Domain of the component."}, "type": {"type": "string", "description": "Type of the component."}, "note": {"type": "string", "description": "Additional notes regarding the component."}, "maxPointLimit": {"type": ["string", "null"], "description": "Maximum point limit for the visualization, if applicable."}, "minLimitYAxis": {"type": ["string", "null"], "description": "Minimum limit for the Y-axis in the visualization, if applicable."}, "content_classification": {"type": "string", "description": "Classification of the content."}, "domain_id": {"type": "string", "description": "Identifier for the domain."}, "tagName": {"type": "string", "description": "Tag name associated with the component."}, "language": {"type": "string", "description": "Language of the component."}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the tool."}, "disabled": {"type": "boolean", "description": "Indicates whether the tool is disabled."}, "label": {"type": "string", "description": "Label of the tool."}}, "required": ["id", "label"]}, "description": "List of tools associated with the indicator."}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the filter."}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the option."}, "label": {"type": "string", "description": "Label for the option."}, "unit": {"type": ["string", "null"], "description": "Unit of measurement for the option, if applicable."}, "value": {"type": ["number", "null"], "description": "Value associated with the option."}, "isSelected": {"type": "boolean", "description": "Indicates whether the option is selected."}}, "required": ["id", "label"]}}}, "required": ["id", "options"]}, "description": "Filters available for the indicator."}, "indicatorDrivers": {"type": "array", "description": "List of drivers for the indicator, if any."}, "indicatorValues": {"type": "object", "description": "Values associated with the indicator."}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the visualization metadata."}, "type": {"type": "string", "description": "Type of visualization."}, "seriesMeta": {"type": "array", "items": {"type": "object"}, "description": "<PERSON><PERSON><PERSON> for the series in the visualization."}}, "required": ["id", "type", "seriesMeta"]}}, "visualizationDefault": {"type": "string", "description": "Identifier for the default visualization."}}, "description": "Visualizations associated with the indicator."}, "domain_light_icon": {"type": "string", "description": "URL for the domain's light icon."}, "domain_dark_icon": {"type": "string", "description": "URL for the domain's dark icon."}, "subdomain": {"type": "string", "description": "Subdomain of the indicator."}, "subdomain_id": {"type": "string", "description": "Identifier for the subdomain."}, "viewName": {"type": "string", "description": "Name of the view for the component."}, "content_classification_key": {"type": "string", "description": "Key for the content classification."}, "overView": {"type": "object", "description": "Overview of the indicator values and changes."}, "data_source": {"type": "string", "description": "Source of the data."}, "unit": {"type": "string", "description": "Unit of measurement for the data."}, "publication_date": {"type": "string", "description": "Date of publication."}, "tableFields": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string", "description": "Label for the field."}, "path": {"type": "string", "description": "Path to the field's value."}}, "required": ["label", "path"]}, "description": "Fields to be displayed in a table format."}, "isMultiDimension": {"type": "boolean", "description": "Indicates if the indicator is multi-dimensional."}}, "required": ["id", "component_title", "domain", "type", "content_classification", "domain_id", "tagName", "language", "indicatorTools", "indicatorFilters", "indicatorVisualizations", "subdomain", "viewName", "content_classification_key", "data_source", "unit", "publication_date"]}}}}}}}}, "components": {"schemas": {"OfficialScreenerFilters": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"viewName": {"type": "string", "description": "The name of the view."}, "filters": {"type": "object", "properties": {"F_FREQUENCY": {"type": "array", "items": {"type": "string", "enum": ["ANNUALLY"]}, "description": "Filter for frequency."}, "F_STAR_RATING_EN": {"type": "array", "items": {"type": "string", "enum": ["NA"]}, "description": "Filter for star rating."}, "F_REVENUE_TYPE_EN": {"type": "array", "items": {"type": "string", "enum": ["NA"]}, "description": "Filter for revenue type."}, "F_REGION_EN": {"type": "array", "items": {"type": "string", "enum": ["NA"]}, "description": "Filter for region."}, "F_ITEM_EN": {"type": "array", "items": {"type": "string", "enum": ["NA"]}, "description": "Filter for item."}, "TOPIC_NAME_ENGLISH": {"type": "array", "items": {"type": "string", "enum": ["Industry & Business"]}, "description": "Filter for topic name in English."}, "THEME_NAME_ENGLISH": {"type": "array", "items": {"type": "string", "enum": ["Transport & services"]}, "description": "Filter for theme name in English."}, "SUB_THEME_NAME_ENGLISH": {"type": "array", "items": {"type": "string", "enum": ["Tourism"]}, "description": "Filter for sub-theme name in English."}}, "required": ["F_FREQUENCY", "F_STAR_RATING_EN", "F_REVENUE_TYPE_EN", "F_REGION_EN", "F_ITEM_EN", "TOPIC_NAME_ENGLISH", "THEME_NAME_ENGLISH", "SUB_THEME_NAME_ENGLISH"]}, "sortBy": {"type": "object", "properties": {"alphabetical": {"type": "string", "enum": ["asc", "desc"], "description": "Sort order for alphabetical sorting."}}, "required": ["alphabetical"]}}, "required": ["viewName", "filters", "sortBy"]}}}}