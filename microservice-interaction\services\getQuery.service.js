const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function setInteractionDataQuery(nodeId,userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `MERGE INTO IFP_INTERACTION_MAP tgt\
                USING (SELECT '${nodeId}' AS NODE_ID, '${userEmail}' AS EMAIL FROM DUAL) src\
                ON (tgt.NODE_ID = src.NODE_ID AND tgt.EMAIL = src.EMAIL)\
                WHEN MATCHED THEN\
                UPDATE SET tgt.STATUS = CASE WHEN tgt.STATUS = 0 THEN 1 ELSE 0 END\
                WHEN NOT MATCHED THEN\
                INSERT (NODE_ID, EMAIL, STATUS)\
                VALUES (src.NODE_ID, src.EMAIL, 1)`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-interaction.services.getQuery.service.setInteractionDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInteractionDataQuery(userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_INTERACTION_MAP WHERE EMAIL = '${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-interaction.services.getQuery.service.getInteractionDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = { 
    setInteractionDataQuery,
    getInteractionDataQuery
 };
