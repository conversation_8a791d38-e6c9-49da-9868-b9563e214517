const db = require('../../services/database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  getIndicatorTCStatusDataQuery,
  setIndicatorTCDataQuery
} = require('./getQuery.service');


/**
 * Function to get my apps
 * @param {*} userEmail - user email 
 */
async function setIndicatorTCData(nodeId,userEmail) {
  return new Promise((resolve, reject) => {
    setIndicatorTCDataQuery(nodeId,userEmail).then((results) => {
      getData(results.query,results.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-indicator-tc.executeQuery.service.setIndicatorTCData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-indicator-tc.executeQuery.service.setIndicatorTCData with error ${err}`);
      reject(err);
    })
  })
}

async function getIndicatorTCStatusData(nodeId,userEmail) {
  return new Promise((resolve, reject) => {
    getIndicatorTCStatusDataQuery(nodeId,userEmail).then((results) => {
      getData(results.query,results.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-indicator-tc.executeQuery.service.getIndicatorTCStatusData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-indicator-tc.executeQuery.service.getIndicatorTCStatusData with error ${err}`);
      reject(err);
    })
  })
}

async function getData(query,binds={}) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-interaction.services.executeQuery.service.getData`);
    db.simpleExecute(query,binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-interaction.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-myapps.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = {
  setIndicatorTCData,
  getIndicatorTCStatusData
};
