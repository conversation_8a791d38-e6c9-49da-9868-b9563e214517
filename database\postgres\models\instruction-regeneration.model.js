const { Sequelize, DataTypes } = require("sequelize");
/**
 * Existing model created for Bayaan UI used to store section wise regenerated data.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const InstructionRegeneration = sequelize.define(
    "InstructionRegeneration",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      object_id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
      },
      quarter: {
        type: DataTypes.INTEGER,
      },
      domain_id: {
        type: DataTypes.INTEGER,
      },
      full_content: {
        type: DataTypes.TEXT,
      },
      main_content: {
        type: DataTypes.TEXT,
      },
      exp_content: {
        type: DataTypes.TEXT,
      },
      report_name: {
        type: DataTypes.TEXT,
      },
    },
    {
      tableName: "instruction_regeneration",
      createdAt: "created_at",
      updatedAt: false,
    }
  );
  return InstructionRegeneration;
}
module.exports = model;
