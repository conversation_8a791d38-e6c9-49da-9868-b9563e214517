const OIDCBearerStrategy = require('passport-azure-ad').BearerStrategy; //BearerStrategy to authorize user
const passport = require('passport'); //Passport to authenticate user
require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const constants = require('../config/constants.json');
const crypto = require('crypto')
const { setRedis, getRedis, getRedisClient } = require('../services/redis.service')
const http = require('http');
const https = require('https');
const axiosInstance = require('axios');
const axios = axiosInstance.create({
    httpAgent: new http.Agent({ keepAlive: true }),
    httpsAgent: new https.Agent({ keepAlive: true })
});
const uuid = require('uuid');
const db = require('./database.service');

var qs = require('qs');
var JwtStrategy = require('passport-jwt').Strategy
var ExtractJwt = require('passport-jwt').ExtractJwt;
var BearerStrategy = require('passport-http-bearer');

const alteryxClientID = '48ed8bdc-7ae1-47a5-92c0-8b0df6bdd8ef';
const alteryxClientSecret = '132ed1c6-69de-4069-bf3d-3713e64cb181';
const groupMatrixInfo = require('./helpers/groupMatrix.json')
const { ApiKeyStrategy } = require('./helpers/strategy');
const { validateAPIKey } = require('./helpers/helper');
const { getUserData, setRefreshToken, revokeRefreshToken } = require('./executeQuery.service');
const { getUserInfo, getUserGroups } = require('./graph');
const jwt = require('jsonwebtoken');
const { platform } = require('os');
const { getActiveUserById } = require('../microservice-users-v3/services/executeQuery');
const { getBayaanGroupMatrix } = require('./group.service');
const { decryptEmail } = require('./encryption.service');

const Redlock  = require("redlock");
const redisClient = require('../redis-con');
const redlock = new Redlock(
    [redisClient], // Array of redis clients
    {
        driftFactor: 0.01, // time in ms to account for Redis processing
        retryCount: 5, // Retry up to 5 times before failing
        retryDelay: 1000, // Time between retries in ms
        retryJitter: 200, // Jitter to add randomness to retry delay
        automaticExtensionThreshold: 500, // Automatically extend lock if threshold is close
    }
);

var jwtMobileOptions = {
    secretOrKey :process.env.APP_SECRET_KEY,
    issuer : 'scad-mobile.scad.gov.ae',
    jwtFromRequest :function(req) {
        var token = null;
        if (req && req.headers) {
            token = req.headers['authorization'].split(' ')[1];
        }
        return token;
    },
    passReqToCallback: true
}

var jwtUAEPassOptions = {
    secretOrKey :process.env.APP_SECRET_KEY,
    issuer : process.env.PLATFORM_BASEPATH,
    jwtFromRequest :function(req) {
        var token = null;
        if (req && req.headers) {
            token = req.headers['authorization'].split(' ')[1];
        }
        return token;
    }
}

//config to connect with Azure AD
const options = {
    identityMetadata: process.env.TENANT_ID,
    clientID: process.env.CLIENT_ID,
    passReqToCallback: true,
    clientSecret: process.env.CLIENT_SECRET,
    responseType: 'code id_token',
    responseMode: 'form_post',
    loggingLevel: 'info'
};

//Utilizing passport Bearer Strategy to verify Bearer token sent in Authorization header
const bearerStrategy = new OIDCBearerStrategy(options,
    function (req, token, done) {
        if (req.headers['mock-user'])
            token.preferred_username = req.headers['mock-user']
        if (req.headers['mock-groups'])
            token.groups = JSON.parse(req.headers['mock-groups'])
        let user = token.preferred_username;
        getRedis(`${user}-groups`).then(redisGroups=>{
            if (redisGroups){
                try{
                    redisGroups = JSON.parse(redisGroups)
                    token.groups = redisGroups
                }
                catch(exp){
                    log.error('Error while reading custom groups from Redis')
                }
            }
            log.info(`Successfully Verified user ${user}`);
            done(null, token);
        })
       
    });

const jwtMobileStrategy = new JwtStrategy(jwtMobileOptions, function(req,token, done) {
        if (req.headers['mock-user'])
            token.preferred_username = req.headers['mock-user']

        if (token.preferred_username) {
            let user = {
                preferred_username:token.preferred_username,
                name: token.name,
                groups:token.groups
            }
            done(null, user);
        } else {
            done(null, false);
        }
})

const alteryxAuthStrategy = new BearerStrategy(
    (token, done) => {
        try{
            if (token){
                var splitToken = token.split(':')
                if (splitToken.length == 4 && splitToken[0] === alteryxClientID && splitToken[1] === alteryxClientSecret){    
                    try{
                        let groups = [];
                        const groupsMap = {
                            approver_access: ["815316be-989e-4f00-873e-8c04308ee971"]
                        }
                        const useGroups = groupsMap[splitToken[2]] || [];
                        groups.push(...useGroups);

                        let user = {
                            preferred_username:'<EMAIL>',
                            name: 'Official Status Check Alteryx',
                            groups: groups
                        }
                        return done(null, user);
                    }
                    catch(exp){
                        return done(null,false)
                    }
                }
                else
                    return done(null, false);
            } else {
                return done(null, false);
            }
        }
        catch(exp){
            log.error(`<<<<< Exited services.authorization.service.alteryxAuthStrategy with error ${exp}`);
            return done(null, false);
        }
    }
  )
  
const jwtUAEPassStrategy = new JwtStrategy(jwtUAEPassOptions, function(token, done) {
    if (token.preferred_username) {
        let user = {
            preferred_username:token.preferred_username,
            name: token.name,
            user_id:token.sub,
            groups:token.groups
        }
        done(null, user);
    } else {
        done(null, false);
    }
})

const apiStrategy = new ApiKeyStrategy(
    function(apiKey, done) {
        const binds = {key:apiKey}
        const query = `SELECT KEY FROM IFP_ADMIN_KEYS WHERE KEY=:key`
         db.simpleExecute(query,binds).then(data =>{
        
            if (!data.length) {
                return done(null, false, { message: 'Invalid API Key' });
            }
            return done(null, 'Validated');
        }
        ).catch(err=>{
            return done(null, false, { message: 'Something went wrong' });
        })
        
    }
)

async function getUserGroupDetails(groupObj) {

    try {
        let groupUser = {};
        let counter = [];
        if (groupObj == 'developer'){
            groupUser = {
                username: 'coi_admin',
                password: 'Welcome@123'
            };
            return groupUser;
        }
        if (groupObj) {
            log.info(`USER Groups ${groupObj}`);
            groupObj.forEach(_id => {
                let key = `AD_GROUP_ID_${_id}`
                let credential = process.env[key] ? process.env[key].split('/') : false;
                if (credential) {
                    groupUser = {
                        username: credential[0],
                        password: credential[1]
                    };
                }
                counter.push(1);
            });
            if (Object.keys(groupUser).length > 0 && groupObj.length === counter.length) {
                return (groupUser);
            } else {
                log.error(`User does not belong to any access groups`);
                return ([401, `User does not belong to any access groups`]);
            }
        }
    } catch (err) {
        
        log.error(`<<<<< Exited services.authorization.service.getUserGroupDetails with error ${err}`);
        return ([422, err]);
    }
}

async function syncUserGroup(req) {
    let lockKey;
    let lock;
    
    try {
        let syncId = uuid.v4();
        let groups = req.user.groups;
        let groupNames = new Set()
        let groupHash;
        let groupMatrixUser;

        groups.forEach(group=>{
            if (group in groupMatrixInfo){
                let dataClassifications = ['__OPEN','__CONFIDENTIAL','__SENSITIVE','__SECRET']
                dataClassifications.forEach(classification=>{
                    splitGroup = groupMatrixInfo[group].split(classification)
                    if (splitGroup.length==2){
                        domain = splitGroup[0]
                        access = splitGroup[1]
                        accessIndex = dataClassifications.indexOf(classification)
                        dataClassifications.slice(0, accessIndex + 1).forEach(d=>{
                            groupNames.add(`${domain}${d}`)
                        })
                    }
                })

                groupNames.add(groupMatrixInfo[group])
            }
        })
        groupNames = [...groupNames].sort()

        log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - ${req.user.preferred_username} - Groups Names Available for the user from the Active Directory: ${groupNames.join(',')}`)

        if(!groupNames.length){
            log.info(`[SYNCUSERGROUP] - SyncID:${syncId} ${req.user.preferred_username}| No Valid Groups`)
            return {'status':'invalid_groups','message':'No Valid Groups.','code':'intra_id_group_assign'};
        }
        
        const userInfo = {email:`matrix-${req.user.preferred_username}`, groups:groupNames.join('-')}
        groupHash = crypto.createHash('md5').update(JSON.stringify(groupNames)).digest("hex")

        let groupCacheKey = `syncGroupCMS_${groupHash}`
        groupMatrixUser = `matrix-${groupHash}@scad.gov.ae`

        let groupCacheResults = await getRedis(groupCacheKey,req.headers);
        if (groupCacheResults){
            log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - Cache found for ${groupNames} , skipping sync - services.authorization.service.getUserGroupDetails`);
            log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - Using group cache for ${groupMatrixUser}`);
            return JSON.parse(groupCacheResults);
        }
        
        lockKey = `sync_group_lock_${groupHash}`;
        // lock = await redlock.acquire([lockKey], 5000);
        const lock = await redlock.lock(lockKey, 10000); // Lock expires in 10 seconds

        groupCacheResults = await getRedis(groupCacheKey,req.headers);
        if (groupCacheResults){
            log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - Cache found for ${groupNames} , skipping sync - services.authorization.service.getUserGroupDetails`);
            log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - Using group cache for ${groupMatrixUser}`);
            await lock.unlock();
            return JSON.parse(groupCacheResults);
        }

        let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const cmsSyncURL = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_USER_SYNC_URL}`;
        const syncData = {
            email:groupMatrixUser, groups: groupNames,
        }
        const syncHeaders = {
            headers:{
                'X-API-KEY':process.env.USER_SYNC_API_KEY,
                'Accept-Language':"a"
            }
        }
        log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - ${groupMatrixUser} - Syncing the user to CMS`)
        const maxRetries = 2;
        const identifier = uuid.v4()
        for (let i = 0; i <= maxRetries; i++) {
            try{
                let postResponse = await axios.post(`${cmsSyncURL}`,syncData, syncHeaders );
                const resData = {
                    email: groupMatrixUser,
                    groups: `[${groupNames.join(' , ')}]`
                }
                log.info(`[SYNCUSERGROUP] - SyncID:${syncId} - ${groupMatrixUser} - User synced to CMS and groups are updated`)
                await setRedis(groupCacheKey, JSON.stringify(resData),constants.redis.cmsResponseTTL,req.headers);
                await lock.unlock();
                return resData
            }
            catch(err) {
                if (i==maxRetries) {
                    await lock.unlock();
                    return {"status":"sync_failed",'message':'Sync Failed from CMS'};
                }
                log.error(`Retrying CMS sync: Retry ${i} - Request ID ${identifier}`)
            };
        }

    } catch (err) {
        if (lock){
            await lock.unlock();
        }
        log.info(`[SYNCUSERGROUP] - ${groupMatrixUser} - Error while syncing the user to CMS ${err}`)
        log.error(`<<<<< Exited services.authorization.service.getUserGroupDetails with error ${err}`);
        throw err
    }
}



async function getArcGISToken() {
    log.debug('>>>>>Entered microservice-gis.getArcGISToken');
    log.info(`API call to ${process.env.GIS_AUTH_URL} from getArcGISToken`);
    try{
        let body = qs.stringify({
            client_id: process.env.GIS_CLIENT_ID,
            client_secret: process.env.GIS_CLIENT_SECRET,
            grant_type: "client_credentials",
            expiration: 1440,
            redirect_uri: process.env.GIS_REDIRECT_URL,
        })
        let response = await axios.post(`${process.env.GIS_AUTH_URL}`,body,{
                headers:
                    { 
                        'Content-Type' : 'application/x-www-form-urlencoded; charset=UTF-8'
                    }
            }
        )
        return response
    }
    catch(err){
        throw err
    }
  }

  async function createUAEPassPlatformAccessToken(userInfo){
    const userId = userInfo.uuid
    const userName = userInfo.fullnameEN
    let userData;
    try{
        userData= await getActiveUserById(userId)
    }catch(err){
        
    }
    if (!userData){
        return null
    }
    const decryptedEmail = decryptEmail(userData.EMAIL)
    const entraIdUserInfo = await getUserInfo(decryptedEmail)
    if (!entraIdUserInfo){
        log.error(`User with given email not found in EntraID: ${decryptedEmail}`)
        return null
    }

    const entraIdUserGroups = await getUserGroups(entraIdUserInfo.id)
    const groupIds = entraIdUserGroups.map(group=>group.id)
    const payload = {
        preferred_username: entraIdUserInfo.mail,
        role: userData.ROLE,
        designation: userData.DESIGNATION, 
        iss: process.env.PLATFORM_BASEPATH,
        groups: groupIds,
        name: userName, 
        sub: userId,
        platform: 'uae-pass',
        type:'access'
      };

    const token = jwt.sign(payload, process.env.APP_SECRET_KEY, { expiresIn: '2h' });
    return token
      
  }

  async function createUAEPassPlatformRefreshToken(userInfo){
    const userId = userInfo.uuid
    const userName = userInfo.fullnameEN
    let userData;
    try{
        userData= await getUserData(userId)
    }catch(err){
        
    }
    if (!userData){
        return null
    }
    const decryptedEmail = decryptEmail(userData.EMAIL)
    const entraIdUserInfo = await getUserInfo(decryptedEmail)
    if (!entraIdUserInfo){
        log.error(`User with given email not found in EntraID: ${decryptedEmail}`)
        return null
    }
    const payload = {
        preferred_username: entraIdUserInfo.mail,
        iss: process.env.PLATFORM_BASEPATH,
        name: userName, 
        sub: userId,
        platform: 'uae-pass',
        type:'refresh'
      };

    const token = jwt.sign(payload, process.env.APP_SECRET_KEY, { expiresIn: '1d' });
    await setRefreshToken(userId,token)
    return token
  }
  
    async function revokeUAEPassPlatformRefreshToken(userId){
        try{
            await revokeRefreshToken(userId)
        }
        catch(err){
            throw err
        }
    }

    function decodeJWT(refreshToken){
        try{
            const decoded = jwt.verify(refreshToken, process.env.APP_SECRET_KEY);
            return decoded
        }
        catch(err){
            throw err
        }
    }

    // Middleware to check token type and authenticate accordingly
	const authStrategySelector = (req, res, next) => {
        const apiKeyHeader = req.headers['x-api-key'];

        if (apiKeyHeader) {
            return passport.authenticate('xapikey', { session: false })(req, res, next);
        }

		const authHeader = req.headers['authorization'];

		if (!authHeader) {
			return res.status(401).send({ message: 'Authorization header missing' });
		}

		const token = authHeader.split(' ')[1];

		if (!token) {
			return res.status(401).send({ message: 'Token missing' });
		}

		try {
			const decodedToken = jwt.decode(token);

			// Check if the token is from MSAL (Microsoft EI)
			const isMsalToken = (
				// (decodedToken.iss && decodedToken.iss.includes('https://login.microsoftonline.com/')) ||
				(decodedToken && decodedToken.aud && decodedToken.aud === process.env.CLIENT_ID) 
				// (decodedToken.tid && decodedToken.tid === process.env.USER_TENANT_ID)
			);

			if (isMsalToken) {
				// Token is an MSAL OAuth Bearer token issued by Microsoft EI
				passport.authenticate('oauth-bearer', { session: false })(req, res, next);
			} else if(decodedToken && decodedToken.platform && decodedToken.platform === "uae-pass"){ 
				// Token is a JWT issued for UAE Pass login
				passport.authenticate('jwt-uaepass', { session: false })(req, res, next);
			} else if (decodedToken.issuer && decodedToken.issuer === "scad-mobile.scad.gov.ae") {
				// Token is a JWT issued for SCAD Mobile login
				passport.authenticate('jwt-mobile', { session: false })(req, res, next);
			} else {
				//Invalid token
				return res.status(400).send({ message: 'Invalid token' });
			}
		} catch (error) {
			return res.status(400).send({ message: 'Invalid token' });
		}
	};



module.exports = { 
    bearerStrategy, 
    jwtMobileStrategy,
    jwtUAEPassStrategy, 
    apiStrategy, 
    alteryxAuthStrategy,
    getUserGroupDetails,
    getArcGISToken, 
    syncUserGroup, 
    createUAEPassPlatformAccessToken,
    createUAEPassPlatformRefreshToken,
    revokeUAEPassPlatformRefreshToken,
    decodeJWT,
    authStrategySelector
};
