const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const constants = require("../../config/constants.json");

function getGovnernanceChartQuery() {
  try {
    let query = `SELECT
	TYPE_ID AS "id",
	COUNT AS "value",
	CONTENT AS "content",
	USECASE_TYPE_EN AS "name"
  FROM
	VW_DATA_GOV_OVERVIEW
	ORDER BY
  RANK`;
    return query;
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice.data-governance.services.getQuery.service.getGovnernanceChartQuery with error ${err} `
    );
    throw err;
  }
}

function getDataSceinceInputQuery(page, limit, filterName) {
  const offset = (page - 1) * limit;
  let query = `SELECT
    INDICATOR_ID,
    USECASE_NAME_EN,
    FREQUENCY_NAME_EN,
    formatDateTime(parseDateTimeBestEffort(MAX_OBS_DT), '%d-%m-%Y') AS MAX_OBS_DT,
    EXPECTED_DT,
    UPTODATE,
    BLOCKING_FORECAST,
    count() OVER () AS "total_count"
    FROM VW_T_DS_INDICATORS`;

  const binds = { offset, limit };
  if (filterName) {
    query += " WHERE USECASE_NAME_EN = {filterName:String}";
    binds.filterName = filterName;
  }
  query += " LIMIT {limit:Int32} OFFSET {offset:Int32}";

  return { query: query.trim(), binds };
}

function getDataSceinceOutputQuery(page, limit, filterName) {
  const offset = (page - 1) * limit;
  let query = `SELECT
    INDICATOR_ID,
    USECASE_NAME_EN,
    FREQUENCY_NAME_EN,
    formatDateTime(parseDateTimeBestEffort(MAX_OBS_DT), '%d-%m-%Y') AS MAX_OBS_DT,
    EXPECTED_DT,
    UPTODATE,
    BLOCKING_FORECAST,
    COUNT() OVER () AS "total_count"
    FROM VW_T_DS_INDICATORS`;

  const binds = { offset, limit };

  if (filterName) {
    query += " WHERE USECASE_NAME_EN = {filterName:String}";
    binds.filterName = filterName;
  }

  query += " LIMIT {limit:UInt32} OFFSET {offset:UInt32}";

  return { query: query.trim(), binds };
}

function generateUseCaseDropDownValuesQuery() {
  return `SELECT DISTINCT 
	USECASE_NAME_EN AS "name", 
	USECASE_ID AS "id" 
  FROM (SELECT main.*
FROM VW_BAYAAN_CALENDAR_STATUS AS main
JOIN (
    SELECT USECASE_NAME_EN, MAX(TARGET_DATE) AS max_target_date
    FROM VW_BAYAAN_CALENDAR_STATUS
    WHERE LEFT(TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')
    GROUP BY USECASE_NAME_EN
) AS sub
ON main.USECASE_NAME_EN = sub.USECASE_NAME_EN
AND main.TARGET_DATE = sub.max_target_date
WHERE main.CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
  AND LEFT(main.TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m'))
  WHERE CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'`;
}

function generateUseCaseDropDownTypeValuesQuery() {
  return `SELECT DISTINCT 
	USECASE_TYPE_EN AS "name" 
  FROM (SELECT main.*
FROM VW_BAYAAN_CALENDAR_STATUS AS main
JOIN (
    SELECT USECASE_NAME_EN, MAX(TARGET_DATE) AS max_target_date
    FROM VW_BAYAAN_CALENDAR_STATUS
    WHERE LEFT(TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')
    GROUP BY USECASE_NAME_EN
) AS sub
ON main.USECASE_NAME_EN = sub.USECASE_NAME_EN
AND main.TARGET_DATE = sub.max_target_date
WHERE main.CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
  AND LEFT(main.TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m'))
  WHERE CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'`;
}

function generateUseCaseStatusQuery() {
  return `SELECT
	DISTINCT STATUS AS "name"
  FROM (SELECT main.*
FROM VW_BAYAAN_CALENDAR_STATUS AS main
JOIN (
    SELECT USECASE_NAME_EN, MAX(TARGET_DATE) AS max_target_date
    FROM VW_BAYAAN_CALENDAR_STATUS
    WHERE LEFT(TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')
    GROUP BY USECASE_NAME_EN
) AS sub
ON main.USECASE_NAME_EN = sub.USECASE_NAME_EN
AND main.TARGET_DATE = sub.max_target_date
WHERE main.CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
  AND LEFT(main.TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m'))
  WHERE CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'`;
}

function generateUseCaseFrequencyQuery() {
  return `SELECT
	DISTINCT FREQUENCY_NAME_EN AS "name"
  FROM (SELECT main.*
FROM VW_BAYAAN_CALENDAR_STATUS AS main
JOIN (
    SELECT USECASE_NAME_EN, MAX(TARGET_DATE) AS max_target_date
    FROM VW_BAYAAN_CALENDAR_STATUS
    WHERE LEFT(TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')
    GROUP BY USECASE_NAME_EN
) AS sub
ON main.USECASE_NAME_EN = sub.USECASE_NAME_EN
AND main.TARGET_DATE = sub.max_target_date
WHERE main.CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
  AND LEFT(main.TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m'))
  WHERE CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'`;
}

function generateDataSourceDropDownValuesQuery() {
  return `SELECT DISTINCT 
	USECASE_NAME_EN AS name, 
	USECASE_ID AS id 
  FROM VW_T_SOURCE`;
}

function generateDataSceinceDropDownValuesQuery() {
  return `SELECT DISTINCT 
  USECASE_NAME_EN AS name,
	USECASE_ID AS id
  FROM
	VW_T_MODELS`;
}

function generateWorkflowDetailsDropDownValuesQuery() {
  return `SELECT DISTINCT 
  USECASE_NAME_EN AS "name"
  FROM
	VW_ALTERYX_WORKFLOW_STATUS`;
}

function generateBayaanSvStatusQuery() {
  return `SELECT
	DISTINCT visr.STATUS AS "name"
  FROM
	VW_IFP_SV_STATUS_REPORT visr`;
}

function generateStatisticalIndicatordropdownQuery() {
  return `SELECT
	DISTINCT TOPIC_NAME_ENGLISH,
	THEME_NAME_ENGLISH,
	SUB_THEME_NAME_ENGLISH,
	PRODUCT_NAME_ENGLISH
FROM
	VW_STATISTICAL_COMPARISON_REPORT 
	ORDER BY TOPIC_NAME_ENGLISH,
	THEME_NAME_ENGLISH,
	SUB_THEME_NAME_ENGLISH,
	PRODUCT_NAME_ENGLISH`;
}

function generateStatisticalIndicatorProductsQuery() {
  return `SELECT DISTINCT PRODUCT_NAME_ENGLISH AS "name"
  FROM VW_STATISTICAL_COMPARISON_REPORT`;
}

function generateStatisticalIndicatorstatusQuery() {
  return `SELECT DISTINCT STATUS AS "name"
FROM VW_STATISTICAL_COMPARISON_REPORT
WHERE STATUS IS NOT NULL
  AND STATUS != ''
`;
}

function genarateUseCaseDataCountQuery(type) {
  const binds = {};
  let query = `SELECT STATUS as "name", COUNT(STATUS)  as "value"
          FROM (SELECT main.*
FROM VW_BAYAAN_CALENDAR_STATUS AS main
JOIN (
    SELECT USECASE_NAME_EN, MAX(TARGET_DATE) AS max_target_date
    FROM VW_BAYAAN_CALENDAR_STATUS
    WHERE LEFT(TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')
    GROUP BY USECASE_NAME_EN
) AS sub
ON main.USECASE_NAME_EN = sub.USECASE_NAME_EN
AND main.TARGET_DATE = sub.max_target_date
WHERE main.CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
  AND LEFT(main.TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m'))
          WHERE CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
          `;
  if (type) {
    query += " AND USECASE_TYPE_EN = {type:String}";
    binds.type = type;
  }

  query += ` GROUP BY STATUS`;
  return { query: query.trim(), binds };
}

function generateDataSourceDataCountQuery() {
  return `SELECT IS_UPTODATE as field, COUNT(IS_UPTODATE)   as "count"
          FROM VW_T_SOURCE 
          GROUP BY IS_UPTODATE`;
}

function generateDataSceinceDataCountQuery() {
  return `SELECT IS_UPTODATE as field, COUNT(IS_UPTODATE)   as "count"
          FROM VW_T_MODELS 
          GROUP BY IS_UPTODATE`;
}

function generateWorkflowDetailsDataCountQuery() {
  return `SELECT JOB_SUCCES_STATUS as field, COUNT(JOB_SUCCES_STATUS)  as "count"
          FROM VW_ALTERYX_WORKFLOW_STATUS 
          GROUP BY JOB_SUCCES_STATUS`;
}

function generateBayaanSvDataCountQuery() {
  return `	SELECT
	visr.STATUS AS "name",
	COUNT(visr.STATUS) AS "value"
  FROM
	VW_IFP_SV_STATUS_REPORT visr
  GROUP BY visr.STATUS`;
}

// new query functions //
function generateUseCaseQuery(page, limit, filter) {
  let binds = {};
  let query;

  const dateColsQuery = filter.short ? `formatDateTime(parseDateTimeBestEffort(REFERENCE_DATE), '%m-%d-%Y') AS "reference date", ` :
  `formatDateTime(parseDateTimeOrNull(REFERENCE_DATE,'%Y%m%d'),'%m-%d-%Y') AS "reference date",
  formatDateTime(parseDateTimeOrNull(TARGET_DATE,'%Y%m%d'),'%d-%m-%Y') AS "target date",
  formatDateTime(parseDateTimeOrNull(REFERENCE_DT_NEXT,'%Y%m%d'),'%m-%d-%Y') AS "next reference date",
  formatDateTime(parseDateTimeOrNull(TARGET_DT_NEXT,'%Y%m%d'),'%d-%m-%Y') AS "next target date", `;

  query = `
  SELECT 
    USECASE_NAME_EN AS "name", 
    USECASE_TYPE_EN AS "type",
    STATUS AS "status",
    ${dateColsQuery}
    FREQUENCY_NAME_EN AS "frequency",
    count() OVER () AS "total_count"
  FROM (SELECT main.*
    FROM VW_BAYAAN_CALENDAR_STATUS AS main
    JOIN (
      SELECT USECASE_NAME_EN, MAX(TARGET_DATE) AS max_target_date
      FROM VW_BAYAAN_CALENDAR_STATUS
      WHERE LEFT(TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')
      GROUP BY USECASE_NAME_EN
    ) AS sub
  ON main.USECASE_NAME_EN = sub.USECASE_NAME_EN
  AND main.TARGET_DATE = sub.max_target_date
  WHERE main.CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT'
  AND LEFT(main.TARGET_DATE, 6) <= formatDateTime(today(), '%Y%m')) 
  WHERE CALENDAR_TYPE = 'BAYAAN STATISTICAL PRODUCT' 
  `;
  
  const fieldMappings = {
    USECASE_NAME_EN: { field: "USECASE_NAME_EN", bindKey: "filterName" },
    USECASE_TYPE_EN: { field: "USECASE_TYPE_EN", bindKey: "type" },
    STATUS: { field: "STATUS", bindKey: "status" },
    FREQUENCY_NAME_EN: { field: "FREQUENCY_NAME_EN", bindKey: "frequency" },
  };

  const conditions = [];

  if (filter.search) {
    conditions.push(
      `UPPER(USECASE_NAME_EN) LIKE UPPER({search:String})`
    );
    binds.search = `%${filter.search}%`;
  }

  for (const [key, { field, bindKey }] of Object.entries(fieldMappings)) {
    if (filter[key] && bindKey !== 'search') {
    conditions.push(`${field} = {${bindKey}:String}`);
    binds[bindKey] = filter[key];
    }
  }

  if (conditions.length > 0) {
    query += " AND " + conditions.join(" AND ");
  }

  if (filter.sortColumn) {
    const tableColumnsMap = {
      'reference date': 'REFERENCE_DATE',
      'target date': 'TARGET_DATE',
      'next reference date': 'REFERENCE_DT_NEXT',
      'next target date': 'TARGET_DT_NEXT',
      'name': 'USECASE_NAME_EN',
      'type': 'USECASE_TYPE_EN',
      'status': 'STATUS',
      'frequency': 'FREQUENCY_NAME_EN',
      'total count': 'total_count'

    }
    query += ` ORDER BY ${tableColumnsMap[filter.sortColumn]} ${filter.sort ? "ASC" : "DESC"}`;
  }
  
  // Add pagination if page and limit are provided
  if (page && limit) {
    const offset = (page - 1) * limit;
    query += ` LIMIT {limit:Int32} OFFSET {offset:Int32}`;
    binds.limit = limit;
    binds.offset = offset;
  }

  return { query: query.trim(), binds: binds };
}

function generateDataSourceQuery(page, limit, filterName) {
  const offset = (page - 1) * limit;
  let query = `
    SELECT 
      SOURCE_NAME_EN, 
      formatDateTime(parseDateTimeBestEffort(MAX_OBS_DT), '%d-%m-%Y') AS MAX_OBS_DT, 
      FREQUENCY_NAME_EN, 
      OWNER_NAME_EN, 
      IS_UPTODATE, 
      count() OVER () AS "total_count" 
    FROM VW_T_SOURCE
  `;

  const conditions = [];
  if (filterName) {
    conditions.push(`SOURCE_NAME_EN = {filterName:String}`);
  }

  if (conditions.length > 0) {
    query += " WHERE " + conditions.join(" AND ");
  }

  query += ` LIMIT {limit:Int32} OFFSET {offset:Int32}`;

  return { query: query.trim(), binds: { offset, limit, filterName } };
}

function generateDataScienceQuery(page, limit, filterName) {
  const offset = (page - 1) * limit;
  let query = `
    SELECT 
      MODEL_ID, 
      USECASE_NAME_EN, 
      FREQUENCY_NAME_EN, 
      formatDateTime(parseDateTimeBestEffort(MAX_OBS_DT), '%d-%m-%Y') AS MAX_OBS_DT, 
      REFERENCE_DT, 
      IS_UPTODATE, 
      count() OVER () AS "total_count" 
    FROM VW_T_MODELS
  `;

  const conditions = [];
  if (filterName) {
    conditions.push(`USECASE_NAME_EN = {filterName:String}`);
  }

  if (conditions.length > 0) {
    query += " WHERE " + conditions.join(" AND ");
  }

  query += ` LIMIT {limit:Int32} OFFSET {offset:Int32}`;

  return { query: query.trim(), binds: { offset, limit, filterName } };
}

function generateWorkflowDetailsQuery(page, limit, filterName) {
  const offset = (page - 1) * limit;
  let query = `
    SELECT 
      WORKFLOW_NAME, 
      USECASE_NAME_EN, 
      SCHEDULE_FREQUENCY, 
      SCHEDULE_LAST_RUN_TIME, 
      SCHEDULE_NEXT_RUN_TIME, 
      JOB_SUCCES_STATUS, 
      count() OVER () AS "total_count" 
    FROM VW_ALTERYX_WORKFLOW_STATUS
  `;

  const conditions = [];
  if (filterName) {
    conditions.push(`USECASE_NAME_EN = {filterName:String}`);
  }

  if (conditions.length > 0) {
    query += " WHERE " + conditions.join(" AND ");
  }

  query += ` LIMIT {limit:Int32} OFFSET {offset:Int32}`;

  return { query: query.trim(), binds: { offset, limit, filterName } };
}

function generateBayaanSvsQuery(page, limit, filter) {
  let binds = {};
  let query;
  const tableColumns = [
    {key: 'REFERENCE_DATE', label: 'REFERENCE DATE'},
    {key: 'TARGET_DATE', label: 'TARGET DATE'},
    {key: 'PROD_INSERT_DT', label: 'RELEASE DATE'}
  ];
  if (!filter.short) {
    query = `
     SELECT
	VISR.STAT_VALUE_ID AS "SV ID",
	VISR.STAT_VALUE_NAME_EN AS "STAT VALUE NAME",
	VISR.STATUS AS "STATUS",
	formatDateTime(parseDateTimeBestEffortOrNull(VISR.REFERENCE_DATE),
	'%d-%m-%Y') AS "REFERENCE DATE",
		formatDateTime(parseDateTimeBestEffortOrNull(VISR.TARGET_DATE),
	'%d-%m-%Y') AS "TARGET DATE",
		formatDateTime(parseDateTimeBestEffortOrNull(VISR.PROD_INSERT_DT),
	'%d-%m-%Y') AS "RELEASE DATE",
	VISR.SV_DB_STATUS AS "STAGING DB STATUS",
	VISR.LANDING_STATUS AS "LANDING STATUS",
	VISR.SOURCE_NAME_EN AS "SOURCE",
  VISR.FREQUENCY,
	count() OVER () AS "total_count"
FROM
	VW_IFP_SV_STATUS_REPORT VISR
  `;
  } else {
    query = `
   SELECT
	 VSS.SOURCE_NAME_EN AS "source_name",
	  VSS.STATUS AS "status",
	  Count(VSS.SOURCE_NAME_EN) AS "SV_count",
	  count() OVER () AS "total_count"
    FROM
	  VW_IFP_SV_STATUS_REPORT VSS`;
  }

  const conditions = [];
  if (filter.name) {
    conditions.push(`VISR.STAT_VALUE_NAME_EN = {sv_name:String}`);
    binds.sv_name = filter.name;
  }

  if (filter.status) {
    conditions.push(
      filter.short ? ` status={status:String}` : `VISR.STATUS = {status:String}`
    );
    binds.status = filter.status;
  }

  if (filter.search) {
    conditions.push(
      `UPPER(STAT_VALUE_NAME_EN) LIKE UPPER({search:String}) OR toString(STAT_VALUE_ID) LIKE ({search:String})`
    );
    binds.search = `%${filter.search}%`;
  }

  if (conditions.length > 0) {
    query += " WHERE " + conditions.join(" AND ");
  }

  if (filter.short) {
    query += ` GROUP BY VSS.SOURCE_NAME_EN, VSS.STATUS`;
  }

  if (filter.sortColumn) {
    const dateColIndex = tableColumns.findIndex(col => col.label === filter.sortColumn);
    const dateCol = dateColIndex >=0 ? tableColumns[dateColIndex].key : filter.sortColumn;
    query += ` ORDER BY ${dateCol} ${filter.sort ? "ASC" : "DESC"}`;
  }

  // Add pagination if limit and offset are provided
  if (page && limit) {
    const offset = (page - 1) * limit;
    query += ` LIMIT {limit:Int32} OFFSET {offset:Int32}`;
    binds.limit = limit;
    binds.offset = offset;
  }

  return { query: query.trim(), binds: binds };
}

function generateStatisticalIndicatorQuery(page, limit, filters) {
  let binds = {};
  const filterKeys = {
    domain: "TOPIC_NAME_ENGLISH",
    theme: "THEME_NAME_ENGLISH",
    subTheme: "SUB_THEME_NAME_ENGLISH",
    product: "PRODUCT_NAME_ENGLISH",
    status: "STATUS",
    updated_date: "formatDateTime(parseDateTimeBestEffort(INSERT_DATE), '%d-%m-%Y')",
    search: "STAT_VALUE_NAME_EN",
  };

  const tableColumns = [
    { key: 'STAT_VALUE_ID', label: 'SV ID' },
    { key: 'STAT_VALUE_NAME_EN', label: 'STAT VALUE NAME' },
    { key: 'MAX_OBS_DT', label: 'REFERENCE DATE' },
    { key: 'TOPIC_NAME_ENGLISH', label: 'TOPIC' },
    { key: 'SUB_THEME_NAME_ENGLISH', label: 'SUB THEME' },
    { key: 'PRODUCT_NAME_ENGLISH', label: 'PRODUCT' },
    { key: 'INSERT_DATE', label: 'RELEASE DATE' },
  ];

  let searchValue = filters?.search || null;
  let sortValue = filters?.sortColumn ? { sort: filters.sort, sortColumn: filters.sortColumn } : null;

  // Clean up filters
  const cleanedFilters = Object.entries(filters || {}).reduce((acc, [key, value]) => {
    if (value && key !== 'search' && key !== 'sort' && key !== 'sortColumn') {
      acc[key] = value;
    }
    return acc;
  }, {});

  let query = `
    SELECT 
      STAT_VALUE_ID AS "SV ID", 
      STAT_VALUE_NAME_EN AS "STAT VALUE NAME", 
      formatDateTime(parseDateTimeOrNull(MAX_OBS_DT,'%Y%m%d'),'%m-%d-%Y') AS "REFERENCE DATE",
      TOPIC_NAME_ENGLISH AS "TOPIC", 
      THEME_NAME_ENGLISH AS "THEME", 
      SUB_THEME_NAME_ENGLISH AS "SUB THEME", 
      PRODUCT_NAME_ENGLISH AS "PRODUCT", 
      formatDateTime(parseDateTimeBestEffort(INSERT_DATE), '%d-%m-%Y') AS "RELEASE DATE", 
      STATUS,
      vscr.FREQUENCY_EN as "FREQUENCY",
      count() OVER () AS "total_count" 
    FROM VW_STATISTICAL_COMPARISON_REPORT vscr
  `;

  const conditions = [];

  for (const [key, value] of Object.entries(cleanedFilters)) {
    const column = filterKeys[key];
    if (column) {
      conditions.push(`${column} = {${key}:String}`);
      binds[key] = value;
    }
  }

  if (conditions.length > 0) {
    query += ` WHERE ${conditions.join(" AND ")}`;
  }

  if (searchValue) {
    const searchCondition = `
      ${conditions.length > 0 ? "AND" : "WHERE"} 
      (UPPER(STAT_VALUE_NAME_EN) LIKE UPPER({search:String}) 
      OR toString(STAT_VALUE_ID) LIKE ({search:String}))
    `;
    query += searchCondition;
    binds.search = `%${searchValue}%`;
  }

  if (sortValue?.sortColumn) {
    const column = tableColumns.find(col => col.label === sortValue.sortColumn)?.key || sortValue.sortColumn;
    query += ` ORDER BY ${column} ${sortValue.sort ? "ASC" : "DESC"}`;
  }

  // Add pagination if page and limit are provided
  if (page && limit) {
    const offset = (page - 1) * limit;
    query += ` LIMIT {limit:Int32} OFFSET {offset:Int32}`;
    binds.limit = limit;
    binds.offset = offset;
  }

  return { query: query.trim(), binds };
}


function getSCADproductionCountQuery() {
  return `
    SELECT 
      count(STAT_VALUE_ID) AS SCAD_PRODUCTION 
    FROM VW_STATISTICAL_COMPARISON_REPORT 
    WHERE TYPE = 'OFFICIAL'
  `;
}

function getBayaanProductionQuery() {
  return `
    SELECT 
      count(STAT_VALUE_ID) AS BAYAAN_PRODUCTION 
    FROM VW_STATISTICAL_COMPARISON_REPORT 
    WHERE TYPE = 'OFFICIAL' 
      AND PLATFORM_STATUS = 'AVAILABLE'
  `;
}

function getNotApprovedQuery() {
  return `
    SELECT 
      count(STAT_VALUE_ID) AS NOT_APPROVED 
    FROM VW_STATISTICAL_COMPARISON_REPORT 
    WHERE TYPE = 'OFFICIAL' 
      AND PLATFORM_STATUS != 'AVAILABLE'
  `;
}

function getProductionCountQuery() {
  return `
    SELECT 
      TOPIC_NAME_ENGLISH, 
      count(*) AS VALUE 
    FROM VW_STATISTICAL_COMPARISON_REPORT 
    WHERE TYPE = 'OFFICIAL' 
      AND PLATFORM_STATUS = 'AVAILABLE' 
    GROUP BY TOPIC_NAME_ENGLISH
  `;
}

function getNotApprovedCountQuery() {
  return `
    SELECT 
      NOT_APPROVED_STATUS, 
      count(*) AS VALUE 
    FROM VW_STATISTICAL_COMPARISON_REPORT 
    WHERE TYPE = 'OFFICIAL' 
      AND PLATFORM_STATUS != 'AVAILABLE' 
    GROUP BY NOT_APPROVED_STATUS
  `;
}

function notApprovedDetailQuery() {
  return `SELECT 
    RESPONSIBLE_EN, 
    countIf( NOT_APPROVED_STATUS == 'TICKET NOT APPROVED') AS ticket_approval,
    countIf( NOT_APPROVED_STATUS == 'BUSINESS EXCEPTION') AS business_exception,
    countIf( NOT_APPROVED_STATUS == 'VALUE CHECK') AS value_check,
    countIf(NOT_APPROVED_STATUS == 'TICKET NOT APPROVED') 
    + countIf(NOT_APPROVED_STATUS == 'BUSINESS EXCEPTION') 
    + countIf(NOT_APPROVED_STATUS == 'VALUE CHECK') AS "indicator_count"
    FROM VW_STATISTICAL_COMPARISON_REPORT
    WHERE TYPE = 'OFFICIAL' AND PLATFORM_STATUS !='AVAILABLE'
    GROUP BY RESPONSIBLE_EN
    ORDER BY indicator_count DESC`;
}

function generateSvCalenderData(startDate, endDate) {
  let binds = {};
  let query = `SELECT 
    DISTINCT toDate(parseDateTimeBestEffort(toString(TARGET_DATE))) AS "date",
    groupArray(USECASE_NAME_EN)  AS "indicator",
    groupArray(STATUS) AS "status"
    FROM VW_BAYAAN_CALENDAR_STATUS`;

  if (startDate && endDate) {
    query += ` WHERE toDate(parseDateTimeBestEffort(toString(TARGET_DATE))) 
      BETWEEN toDate({startDate:String}) AND toDate({endDate:String})`;
    binds.startDate = startDate;
    binds.endDate = endDate;
  }

  query += ` GROUP BY TARGET_DATE
    ORDER BY TARGET_DATE`;

  return { query, binds };
}

function generateEventDetail(limit, offset, filter) {
  let binds = {};
  let query = `SELECT
    USECASE_NAME_EN AS "name",
    STATUS AS "status",
    count() OVER () AS "total_count"
    FROM
    VW_BAYAAN_CALENDAR_STATUS`;

  if (filter.date) {
    query += ` WHERE toDate(parseDateTimeBestEffort(toString(TARGET_DATE)))={date:String}`;
    binds.date = filter.date;
  }

  if (filter.status && filter.status != "") {
    query += ` AND UPPER(STATUS) == UPPER({status:String})`;
    binds.status = filter.status;
  }

  if (filter.sortColumn) {
    query += ` ORDER BY ${filter.sortColumn} ${filter.sort ? "ASC" : "DESC"}`;
  }

  query += ` LIMIT ${limit} OFFSET ${offset}`;

  return { query, binds };
}

module.exports = {
  getGovnernanceChartQuery,
  getDataSceinceInputQuery,
  getDataSceinceOutputQuery,
  generateUseCaseQuery,
  generateDataSourceQuery,
  generateDataScienceQuery,
  generateWorkflowDetailsQuery,
  generateBayaanSvsQuery,
  generateStatisticalIndicatorQuery,
  genarateUseCaseDataCountQuery,
  generateDataSourceDataCountQuery,
  generateDataSceinceDataCountQuery,
  generateWorkflowDetailsDataCountQuery,
  generateBayaanSvDataCountQuery,
  getSCADproductionCountQuery,
  getBayaanProductionQuery,
  getNotApprovedQuery,
  getProductionCountQuery,
  getNotApprovedCountQuery,
  notApprovedDetailQuery,
  generateUseCaseDropDownValuesQuery,
  generateDataSourceDropDownValuesQuery,
  generateDataSceinceDropDownValuesQuery,
  generateWorkflowDetailsDropDownValuesQuery,
  generateUseCaseDropDownTypeValuesQuery,
  generateUseCaseStatusQuery,
  generateBayaanSvStatusQuery,
  generateStatisticalIndicatordropdownQuery,
  generateSvCalenderData,
  generateEventDetail,
  generateUseCaseFrequencyQuery,
  generateStatisticalIndicatorProductsQuery,
  generateStatisticalIndicatorstatusQuery,
};
