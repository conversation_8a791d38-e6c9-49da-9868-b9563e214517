const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function listInsightDataQuery(nodeId) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_INSIGHTS WHERE NODE_ID = ${nodeId} AND STATUS != 'REJECTED'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.addInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function addInsightDataQuery(insightData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_INSIGHTS ( EMAIL, USER_NAME, NODE_ID, NODE_TITLE, NODE_LINK, INSIGHT, STATUS, ADD_DT ) VALUES ( '${insightData.email}','${insightData.user}',${insightData.nodeId}, '${insightData.nodeTitle}', '${insightData.nodeLink}', '${insightData.insight}', 'PENDING', TO_DATE('${insightData.date}','DD/MM/YYYY') )`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.addInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInsightDataQuery(id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_INSIGHTS WHERE ID = ${id}`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getInsightsDataQuery(ids) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_INSIGHTS WHERE ID IN (${ids.toString()})`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateInsightDataQuery(insightData,approver=false) {
    return new Promise((resolve, reject) => {
        try {
            let query
            if (approver)
                query = `UPDATE IFP_INSIGHTS SET NODE_TITLE='${insightData.nodeTitle}', NODE_LINK='${insightData.nodeLink}', INSIGHT='${insightData.insight.replace(/'/g, "''")}', STATUS='${insightData.status}', ADD_DT=TO_DATE('${insightData.date}','DD/MM/YYYY'), EMAILSENT=1 WHERE ID='${insightData.id}'`
            else
                query = `UPDATE IFP_INSIGHTS SET NODE_TITLE='${insightData.nodeTitle}', NODE_LINK='${insightData.nodeLink}', INSIGHT='${insightData.insight.replace(/'/g, "''")}', STATUS='${insightData.status}', ADD_DT=TO_DATE('${insightData.date}','DD/MM/YYYY'), EMAILSENT=0 WHERE ID='${insightData.id}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.updateInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateInsightStatusQuery(userEmail,setting,value) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_USER_SETTINGS ( EMAIL, SETTING, VALUE ) VALUES ( '${userEmail}', '${setting}', '${value}' )`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.updateInsightStatusQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function approveInsightDataQuery(ids,date) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_INSIGHTS SET STATUS = 'APPROVED', ADD_DT=TO_DATE('${date}','DD/MM/YYYY'), EMAILSENT = 1 WHERE ID IN (${ids.toString()}) `
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.approveInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function rejectInsightDataQuery(ids,date){
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_INSIGHTS SET STATUS = 'REJECTED', ADD_DT=TO_DATE('${date}','DD/MM/YYYY'), EMAILSENT = 0 WHERE ID IN (${ids.toString()}) `
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.rejectInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function requestEditInsightDataQuery(ids,date){
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_INSIGHTS SET STATUS = 'PENDING', ADD_DT=TO_DATE('${date}','DD/MM/YYYY'), EMAILSENT = 0 WHERE ID IN (${ids.toString()}) `
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.requestEditInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function deleteInsightDataQuery(id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM IFP_INSIGHTS WHERE ID = ${id} `
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.deleteInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getSubmitRequestInsightsDataQuery(email, insightId) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_INSIGHTS WHERE EMAIL = '${email}' AND EMAILSENT = 0 AND STATUS = 'PENDING' AND ID IN (${insightId.toString()})`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getSubmitRequestInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateSubmitRequestInsightsDataQuery(email, insightId) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_INSIGHTS SET EMAILSENT = 1 WHERE EMAIL = '${email}' AND EMAILSENT = 0 AND STATUS = 'PENDING' AND ID IN (${insightId.toString()})`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getSubmitRequestInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = { listInsightDataQuery, addInsightDataQuery, getInsightDataQuery, updateInsightDataQuery, updateInsightStatusQuery, approveInsightDataQuery, rejectInsightDataQuery, deleteInsightDataQuery, getSubmitRequestInsightsDataQuery,updateSubmitRequestInsightsDataQuery,getInsightsDataQuery,requestEditInsightDataQuery };
