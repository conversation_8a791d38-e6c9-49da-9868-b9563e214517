const { IFPError } = require("../../utils/error");
const model = require("../../database/models");
const { Op } = require('sequelize');

/**
 * Updates the status and ticket ID of an AI Insight Report.
 *
 * @async
 * @param {string} report_id - The unique identifier of the AI Insight Report to update.
 * @param {string} TicketID - The ticket ID to associate with the report.
 * @param {string} statusValue - The new status value to set for the report.
 * @returns {Promise<Object>} The updated AI Insight Report instance.
 * @throws {IFPError} If the status value or report data is invalid.
 */
async function updateReportStatus(report_id, TicketID, statusValue) {
  try {
    const conditions = [];
    if (report_id && report_id !== "") conditions.push({ id: report_id });
    if (TicketID && TicketID !== "") conditions.push({ ticket_id: String(TicketID) });
    
    if(conditions.length === 0) {
      throw new IFPError(400, "No report_id or TicketID provided");
    }

    const [reportStatusData, reportData] = await Promise.all([
      model.DropdownOption.findOne({
        include: [
          {
            model: model.DropdownCategory,
            as: "category",
            attributes: ["name"],
            where: {
              name: "ai_insight_report_status",
            },
          },
        ],
        where: {
          value: statusValue,
        },
      }),
      model.AiInsightReport.findOne({
        where: {
          [Op.or]: conditions,
        },
      }),
    ]);

    if (!reportStatusData || !reportData) {
      throw new IFPError(400, `Invalid report status ${statusValue} or report data ${report_id}`);
    }

    reportData.report_status_id = reportStatusData.id;
    reportData.ticket_id = String(TicketID);
    await reportData.save();

    return reportData;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

module.exports = {
  updateReportStatus,
};