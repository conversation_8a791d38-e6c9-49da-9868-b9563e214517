const { default: axios } = require("axios");
const FormData = require("form-data");
const Logger = require("scad-library").logger;
const { uaePass } = require("../config/constants.json");

const log = new Logger().getInstance();

class UAEPassService {
  constructor() {
    this.redirectUrl = process.env.PLATFORM_BASEPATH + uaePass.REDIRECT_PATH;
    this.accessTokenUrl =
      process.env.UAE_PASS_HOST +
      uaePass.ACCESS_TOKEN_PATH +
      `?grant_type=authorization_code&redirect_uri=${this.redirectUrl}`;
    this.authCredential = btoa(
      `${process.env.UAE_PASS_CLIENT_ID}:${process.env.UAE_PASS_CLIENT_SECRET}`
    );
  }

  /**
   * Fetches fresh token from UAE Pass
   * @param {string} accessCode Access code recieved from UAE Pass after succssful login.
   */
  async getAccessToken(accessCode) {
    const data = new FormData();
    data.append("code", accessCode);

    const headers = {
      Authorization: `Basic ${this.authCredential}`,
      ...data.getHeaders(),
    };

    try {
      const tokenReponse = await axios.post(this.accessTokenUrl, data, {
        method: "POST",
        headers,
      });
      const { access_token } = tokenReponse.data;
      return access_token;
    } catch (err) {
      log.error(
        `Error fetching accessToken from microservice-uae-pass.services.getAccessToken ${err}`
      );
      return null;
    }
  }

  /**
   * Fetchs user info from UAE Pass.
   * @param {string} accessToken access token generated from the uae pass /token endpoint
   */
  async getUserInfo(accessToken) {
    const userInfoUrl = process.env.UAE_PASS_HOST + uaePass.USER_INFO_PATH;

    const headers = {
      Authorization: `Bearer ${accessToken}`,
    };
    try {
      const response = await axios.get(userInfoUrl, { headers });
      return response.data;
    } catch (err) {
      log.error(
        `Error fetching userInfo from microservice-uae-pass.services.getUserInfo ${err}`
      );
      return null;
    }
  }
}

module.exports = UAEPassService;
