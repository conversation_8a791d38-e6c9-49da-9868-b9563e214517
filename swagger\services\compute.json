{"paths": {"/content-type/compute/": {"post": {"tags": [" Compute"], "summary": "Compute specified operation on the values of different dimensions of an indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Compute Data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ComputeData"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string"}, "component_title": {"type": "string"}, "component_subtitle": {"type": "string"}, "domain": {"type": "string"}, "type": {"type": "string"}, "note": {"type": "string"}, "maxPointLimit": {"type": "string"}, "minLimitYAxis": {"type": "string"}, "content_classification": {"type": "string"}, "domain_id": {"type": "string"}, "tagName": {"type": "string"}, "language": {"type": "string"}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "disabled": {"type": "boolean"}, "label": {"type": "string"}}, "required": ["id", "label"]}}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "unit": {"type": ["string", "null"]}, "value": {"type": ["integer", "null"]}, "isSelected": {"type": ["boolean", "null"]}}, "required": ["id", "label"]}}}, "required": ["id", "options"]}}, "indicatorDrivers": {"type": "array"}, "indicatorValues": {"type": "object"}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object"}}, "visualizationDefault": {"type": "string"}}, "required": ["visualizationsMeta", "visualizationDefault"]}, "domain_light_icon": {"type": "string", "format": "uri"}, "domain_dark_icon": {"type": "string", "format": "uri"}, "subdomain": {"type": "string"}, "subdomain_id": {"type": "string"}, "viewName": {"type": "string"}, "content_classification_key": {"type": "string"}, "overView": {"type": "object", "properties": {"compareFilters": {"type": "array", "items": {"type": "string"}}, "valueFormat": {"type": "string"}, "templateFormat": {"type": "string"}, "baseDate": {"type": "string", "format": "date"}, "value": {"type": "integer"}, "yearlyCompareValue": {"type": "integer"}, "yearlyChangeValue": {"type": "number"}, "quarterlyCompareValue": {"type": "integer"}, "quarterlyChangeValue": {"type": "number"}, "monthlyCompareValue": {"type": "integer"}, "monthlyChangeValue": {"type": "number"}}, "required": ["compareFilters", "valueFormat", "templateFormat", "baseDate", "value"]}, "data_source": {"type": "string"}, "unit": {"type": "string"}, "publication_date": {"type": "string", "format": "date"}, "tableFields": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "path": {"type": "string"}}, "required": ["label", "path"]}}, "isMultiDimension": {"type": "boolean"}}, "required": ["id", "component_title", "domain", "type", "content_classification", "domain_id", "tagName", "language", "indicatorTools", "indicatorFilters", "indicatorVisualizations", "viewName", "content_classification_key", "<PERSON><PERSON><PERSON><PERSON>", "data_source", "unit", "publication_date", "tableFields"], "additionalProperties": false}}}}}}}}, "components": {"schemas": {"ComputeData": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier of the Indicator."}, "dimensions": {"type": "object", "properties": {"TYPE_NAME_EN": {"type": "array", "items": {"type": "string"}, "description": "Array of Dimension values to be compared"}}, "required": ["TYPE_NAME_EN"], "description": "Object containing dimension details such as hotel types."}, "operation": {"type": "string", "description": "Operation to be performed, indicated by a '-' symbol."}}, "required": ["id", "dimensions", "operation"], "description": "A request body structure for an operation involving hotel types, specifying the ID, the dimensions of interest, and the operation to be performed."}}}}