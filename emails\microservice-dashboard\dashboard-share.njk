{% extends 'base-email-notification-template.njk' %}
{% block content %}
  <tr>
    <td bgcolor="#ffffff">
      <table width="540" style="font-size: 15px" align="center" border="0" cellpadding="0" cellspacing="0">
        <tbody>
          <tr>
            <td style=" font-family: arial, sans-serif; font-size: 16px; color: #4a5662; line-height: 22px; mso-line-height-rule:
                exactly; text-align: left; padding-bottom: 24px; ">
              Dear
              {{ recepient }},
            </td>
          </tr>
          <tr>
            <td style=" font-family: arial, sans-serif; font-size: 16px; color: #4a5662; line-height: 22px; mso-line-height-rule:
                exactly; text-align: left; padding-bottom: 24px; ">
              Please click the
              {% if shareData | length > 1 %}
                links
              {% else %}
                link
              {% endif %}
              below to access the received dashboards, or visit the Bayaan Platform to view the dashboard in your received list.
            </td>
          </tr>
          {% if shareComment %}
            <tr>
              <td style=" font-family: arial, sans-serif; font-size: 16px; color: #4a5662; line-height: 22px; mso-line-height-rule:
                  exactly; text-align: left; padding-bottom: 24px; ">
                Comments:
                {{ shareComment }}
              </td>
            </tr>
          {% endif %}
          <tr>
            <td style=" font-family: arial, sans-serif; font-size: 16px; color: #4a5662; line-height: 22px; mso-line-height-rule:
                exactly; text-align: left; padding-bottom: 24px; ">
              Dashboards:
              <ul>
                {% for dashboard in shareData %}
                  <li>
                    <a href="{{ dashboard.link }}" target="_blank" title="View App" style="text-decoration:none;color: #1E2937;">
                      <span style="text-decoration: underline; color: #1E2937;">{{ dashboard.name }}</span>
                    </a>
                  </li>
                {% endfor %}
              </ul>
            </td>
          </tr>
        </tbody>
      </table>
    </td>
  </tr>
{% endblock content %}