# See http://help.github.com/ignore-files/ for more about ignoring files.
# compiled output
/dist
/build
/tmp
/out-tsc
*.env
/coverage
/junit.xml
/logs
/cert
/.nyc_output
/npmrc_credentials.sh
# Only exists if <PERSON><PERSON> was run
/bazel-out

# dependencies
/node_modules

# profiling files
chrome-profiler-events*.json
speed-measure-plugin*.json

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db
junit.xml
.env
npm_install.sh
dev_docker_build.sh
dev_env.sh
healthCheck.xml
npmrc_credentials.sh
.scannerwork
