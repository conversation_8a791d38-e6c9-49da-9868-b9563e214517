const express = require('express');
const router = new express.Router();
const analyticalAppController = require('../microservice-analytical-apps/analytical-apps.controller');
const analyticalAppControllerV2 = require('../microservice-analytical-appsv2/analytical-apps.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { validateAnalyticalAppById, validateGeospatialPermissions } = require('./validators/analytical-app.validator');

router.get('/', async (req, res, next) => {
    try {
        const data = await new analyticalAppController().getAnalyticalApps(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for analytical-apps content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/v2', async (req, res, next) => {
    try {
        const data = await new analyticalAppController().getAnalyticalAppsV3(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for analytical-apps content-type, ERROR: ${err}`);
        next(err);
    }
});

router.post('/:id',validateAnalyticalAppById, async (req, res, next) => {
    try {
        const data = await new analyticalAppControllerV2().getAnalyticalAppById(req)
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for analytical-apps by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/mobile-dashboards', async (req, res, next) => {
    try {
        const data = await new analyticalAppControllerV2().getMobileDashboards(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching data for mobile-dashboards content-type, ERROR: ${err}`);
        next(err);
    }
});


// Icons for Geo Revamp 
router.get('/geo-revamp-icons', validateGeospatialPermissions, async (req, res, next) => {
    try {
        const data = await new analyticalAppControllerV2().getGeoRevampIcons(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching data for mobile-dashboards content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/power-bi-dashboards', async (req, res, next) => {
    try {
        const data = await new analyticalAppControllerV2().getPowerBIDashboards(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching data for power-bi-dashboards content-type, ERROR: ${err}`);
        next(err);
    }
});

module.exports = router;
