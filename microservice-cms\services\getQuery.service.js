const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getDomainsQuery(domainName = '') {
    try {
        let query = `SELECT * FROM IFP_DOMAINS`;
        if(domainName.length > 0){
            query = `${query} WHERE NAME = '${domainName}'`
        }
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-cms.services.getQuery.service.getDomains with error ${err} `);
        throw err;
    }
}
async function getPeriodicitiesQuery(){
    try {
        let query = `SELECT * FROM IFP_PUB_PERIODICITY`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-cms.services.getQuery.service.getPeriodicitiesQuery with error ${err} `);
        throw err;
    }
}

async function getDataClassificationsQuery(){
    try {
        let query = `SELECT * FROM IFP_DATA_CLASSIFICATIONS`;
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-cms.services.getQuery.service.getPeriodicitiesQuery with error ${err} `);
        throw err;
    }
}
module.exports = { getDomainsQuery, getPeriodicitiesQuery, getDataClassificationsQuery };
