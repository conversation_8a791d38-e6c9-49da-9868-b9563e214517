const express = require('express');
const router = new express.Router();
const notificationsController = require('../microservice-notifications/notifications.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { validateNotificationList, validateNotificationSubscribeEmail, validateNotificationSubscribe, validateNotificationUnSubscribe, validateNotificationUnSubscribeEmail, validateNotificationRead } = require('./validators/notifications.validator');

router.get('/', validateNotificationList, async (req, res, next) => {
    try {
      const data = await notificationsController.listNotifications(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/mappings', async (req, res, next) => {
    try {
      const data = await notificationsController.getNotificationsMappings(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/subscribe', validateNotificationSubscribe, async (req, res, next) => {
    try {
      const data = await notificationsController.subscribeNotifications(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/subscribe/email', validateNotificationSubscribeEmail, async (req, res, next) => {
    try {
      const data = await notificationsController.subscribeEmailNotifications(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/unsubscribe', validateNotificationUnSubscribe, async (req, res, next) => {
    try {
      const data = await notificationsController.unsubscribeNotifications(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/unsubscribe/email', validateNotificationUnSubscribeEmail, async (req, res, next) => {
    try {
      const data = await notificationsController.unsubscribeEmailNotifications(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/read', validateNotificationRead, async (req, res, next) => {
    try {
      const data = await notificationsController.readNotification(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/map-list', async (req, res, next) => {
    try {
      const data = await notificationsController.mapList(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
