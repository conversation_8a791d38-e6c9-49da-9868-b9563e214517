const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const oracledb = require('oracledb');

const Logger = require('scad-library').logger;
const constants = require('../../config/constants.json')
const log = new Logger().getInstance();
const {  getPortfolioDynamicQuery, getPortfolioDynamicQueryWithDimension,getIndicatorMetaDataQuery } = require('./getQuery.service');
const clkTables = constants.clickhouseTables

async function getOfficialIndicatorData(indicatorId, viewName,language) {
  let translatedKeys = {
    titleColumn:language == 'EN'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR',
    dataSourceColumn:language == 'EN'?'DATA_SOURCE':'DATA_SOURCE_AR',
    topicColumn:language == 'EN'?'TOPIC_NAME_ENGLISH':'TOPIC_NAME_ARABIC',
    themeColumn:language == 'EN'?'THEME_NAME_ENGLISH':'THEME_NAME_ARABIC',
    subThemeColumn:language == 'EN'?'SUB_THEME_NAME_ENGLISH':'SUB_THEME_NAME_ARABIC',
    productColumn:language == 'EN'?'PRODUCT_NAME_ENGLISH':'PRODUCT_NAME_ARABIC',
    unitColumn:language == 'EN'?'UNIT':'UNIT_AR'
  }
  
  if (viewName == 'VW_STATISTICAL_INDICATORS') {
    query = `SELECT
              t.VALUE,
              MAX(t.VALUE) OVER () AS MAX_VALUE,
              MIN(t.VALUE) OVER () AS MIN_VALUE,
              toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
              toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
              toString(toDate(toString(t.OBS_DT),	'yyyyMMdd')) AS OBS_DT,
              substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR ,
              TOPIC_NAME_ENGLISH AS TOPIC,
              THEME_NAME_ENGLISH AS THEME,
              SUB_THEME_NAME_ENGLISH AS SUBTHEME,
              PRODUCT_NAME_ENGLISH AS PRODUCT
            FROM
            ${viewName} t
            JOIN STATISTICAL_IND_HIERARCHY s 
                ON
              t.INDICATOR_ID = s.STAT_VALUE_ID
            WHERE
              t.INDICATOR_ID = toString(${indicatorId})
            ORDER BY OBS_DT`
  }else{
    query = `SELECT 
      t.VALUE,
      t.${translatedKeys.unitColumn} AS UNIT,
      t.${translatedKeys.titleColumn} AS INDICATOR_NAME,
      MAX(t.VALUE) OVER () AS MAX_VALUE,
      MIN(t.VALUE) OVER () AS MIN_VALUE,
      toString(toDate(toString(MAX(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MAX_OBS_DT,
      toString(toDate(toString(MIN(t.OBS_DT) OVER ()), 'yyyyMMdd')) AS MIN_OBS_DT,
      toString(toDate(toString(t.OBS_DT),	'yyyyMMdd')) AS OBS_DT,
      substring(toString(toDate(toString(t.OBS_DT), 'yyyyMMdd')), 1, 4) AS YEAR ,
      t.OBS_DT_LATEST,
      t.VALUE_LATEST,
      t.MONTHLY_CHANGE_VALUE,
      t.MONTHLY_COMPARE_VALUE,
      t.QUARTERLY_CHANGE_VALUE,
      t.QUARTERLY_COMPARE_VALUE,
      t.YEARLY_CHANGE_VALUE,
      t.YEARLY_COMPARE_VALUE,
      t.MONTHLY,
      t.QUARTERLY,
      t.YEARLY,
      t.DATA_SOURCE,
      t.${translatedKeys.topicColumn} AS TOPIC,
      t.${translatedKeys.themeColumn} AS THEME,
      t.${translatedKeys.subThemeColumn} AS SUBTHEME,
      t.${translatedKeys.productColumn} AS PRODUCT
    FROM VW_STAT_IND_SCREENER t WHERE INDICATOR_ID=toString(${indicatorId}) ORDER BY OBS_DT`
  }

  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-official-insights.services.getGraphData.getOfficialIndicatorData`);    
    const containsClView = clkTables.some(view => query.includes(view));
    if (containsClView)
      clkdb.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-official-insights.services.getGraphData.getOfficialIndicatorData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-official-insights.services.getGraphData.getOfficialIndicatorData with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
    else
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-official-insights.services.getGraphData.getOfficialIndicatorData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-official-insights.services.getGraphData.getOfficialIndicatorData with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
  });
}

async function getOfficialFilterData(filterIndicator,language) {

  query = `SELECT * FROM VW_STAT_IND_FILTER_ATTRIBUTES WHERE INDICATOR='${filterIndicator}' AND LANGUAGE='${language}' ORDER BY FILTER_SORT,RANK`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-official-insights.services.getGraphData.getOfficialFilterData`);
    clkdb.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-official-insights.services.getGraphData.getOfficialFilterData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-official-insights.services.getGraphData.getOfficialFilterData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getOfficialIndicatorsData(viewName,filters,filterBy={},sortBy={},offset,limit,language) {

  let translatedKeys = {
    titleColumn:language == 'EN'?'INDICATOR_NAME_EN':'INDICATOR_NAME_AR',
    dataSourceColumn:language == 'EN'?'DATA_SOURCE':'DATA_SOURCE_AR',
    topicColumn:language == 'EN'?'TOPIC_NAME_ENGLISH':'TOPIC_NAME_ARABIC',
    themeColumn:language == 'EN'?'THEME_NAME_ENGLISH':'THEME_NAME_ARABIC',
    subThemeColumn:language == 'EN'?'SUB_THEME_NAME_ENGLISH':'SUB_THEME_NAME_ARABIC',
    productColumn:language == 'EN'?'PRODUCT_NAME_ENGLISH':'PRODUCT_NAME_ARABIC',
    unitColumn:language == 'EN'?'UNIT':'UNIT_AR'
  }

  let combinedFilters = {...filters,...filterBy}
  const whereClauses = [];
    for (const filterKey in combinedFilters) {
      if (combinedFilters[filterKey].length > 0) {
        if (Array.isArray(combinedFilters[filterKey])) {
          const values = combinedFilters[filterKey].map(value => `UPPER('${value.replace(/'/g, "''")}')`).join(', ');
          whereClauses.push(`UPPER(${filterKey}) IN (${values})`);
        } else {
          whereClauses.push(`UPPER(${filterKey}) = UPPER('${combinedFilters[filterKey].replace(/'/g, "''")}')`);
        }
      }
    }
  
  const sortByClauses = [];
  for (const sortKey in sortBy){
    if (sortKey == 'alphabetical')
      sortByClauses.push(`t.INDICATOR_NAME ${sortBy[sortKey]}`)
    if (sortKey == 'byValue')
      sortByClauses.push(`t.VALUE_LATEST ${sortBy[sortKey]}`)
    else if (sortKey == 'byChange')
      sortByClauses.push(`t.YEARLY_CHANGE_VALUE ${sortBy[sortKey]}`)
  }

  if (Object.keys(sortBy).length == 0)
    sortByClauses.push(`t.SORT_ORDER ASC`)

  query = `SELECT \
            COUNT(*) OVER () AS TOTAL, t.*
          FROM (
            SELECT 
            DISTINCT t.INDICATOR_ID, 
            t.SORT_ORDER,
            t.${translatedKeys.titleColumn} AS INDICATOR_NAME,
            t.OBS_DT_LATEST,
            t.VALUE_LATEST,
            t.MONTHLY_CHANGE_VALUE,
            t.MONTHLY_COMPARE_VALUE,
            t.QUARTERLY_CHANGE_VALUE,
            t.QUARTERLY_COMPARE_VALUE,
            t.YEARLY_CHANGE_VALUE,
            t.YEARLY_COMPARE_VALUE,
            t.MONTHLY,
            t.QUARTERLY,
            t.YEARLY,
            t.${translatedKeys.unitColumn} AS UNIT,
            t.${translatedKeys.topicColumn} AS TOPIC,
            t.${translatedKeys.themeColumn} AS THEME,
            t.${translatedKeys.subThemeColumn} AS SUBTHEME,
            t.${translatedKeys.productColumn} AS PRODUCT
            FROM ${viewName} t
            ${whereClauses.length > 0 ? 'WHERE ' + whereClauses.join(' AND ') : ''}
          ) t
          ORDER BY ${sortByClauses.join(',')}
          OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-official-insights.services.getGraphData.getInnovativeFilterData`);
    clkdb.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-official-insights.services.getGraphData.getInnovativeFilterData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-official-insights.services.getGraphData.getInnovativeFilterData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getGeneratedVizConfigurationData(nodeId) {

  query = `SELECT * FROM VW_DYNAMIC_JSON_CONFIG WHERE INDICATOR_ID =${nodeId}`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-official-insights.services.getGraphData.getGeneratedVizConfigurationData`);
    clkdb.simpleExecute(query,[],{ fetchInfo: { CONFIGURATION: { type: oracledb.STRING },META_DATA: { type: oracledb.STRING } } })
      .then((data) => {
        log.debug(`<<<<< Exit microservice-official-insights.services.getGraphData.getGeneratedVizConfigurationData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-official-insights.services.getGraphData.getGeneratedVizConfigurationData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}


async function getGraphData(visualization, isFilterPanelEnabled,filterPanel=[]) {
  let query = "";

  if (visualization.isMultiDimension) {
    query = await getPortfolioDynamicQueryWithDimension(visualization, isFilterPanelEnabled,filterPanel);
  } else {
    query = await getPortfolioDynamicQuery(visualization);
  }
  
  return new Promise((resolve, reject) => {
    if (query == "") return resolve(visualization);
    getSeriesData(query).then((data) => {
      resolve(data);
    }).catch((err) => {
      
      reject(err);
    })
  })
}

async function getSeriesData(query) {

  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-official-insights.services.getGraphData.getSeriesData`);
    clkdb.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-official-insights.services.getGraphData.getSeriesData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-official-insights.services.getGraphData.getSeriesData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getIndicatorMetaData(indicatorId,lang) {
  return new Promise((resolve, reject) => {
    getIndicatorMetaDataQuery(indicatorId,lang).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery`);
      if (results.query.includes('VW_INDICATOR_MAP')){
        clkdb.simpleExecute(results.query, results.binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
      }else{
        db.simpleExecute(results.query, results.binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
      }
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getIndicatorMetaDataQuery with error ${err}`);
      reject(err);
    })
  })
}

module.exports = {
  getOfficialIndicatorData,
  getOfficialFilterData,
  getOfficialIndicatorsData,
  getGeneratedVizConfigurationData,
  getGraphData,
  getIndicatorMetaData
}