const { IFPError } = require("../../utils/error");

const validateIndicatorCompareNodes = (nodes) => {
    if (nodes.length<2) {
      throw new IFPError(400,'Please provide atleast two nodes');
    }
    nodes.forEach(node=>{
        if (!node.hasOwnProperty('indicatorId'))
            throw new IFPError(400,'Invalid request format: Node has no indicatorId!');
        if (!Number(node.indicatorId))
          throw new IFPError(400,`${node.indicatorId} is not a valid indicator id`);
    })
    return true;
  };

  const validateIndicatorCompareDetail = (req, res, next) => {
    if (!req.params.id) {
      throw new IFPError(400,'Please provide a valid id');
    }

    next()
  };

  const validateIndicatorCompareAddToMyApps = (req,res,next) => {
    if (!req.body.nodes)
      throw new IFPError(400,'Please provide nodes data')
    
    if (req.body.nodes.length<2) {
      throw new IFPError(400,'Please provide atleast two nodes');
    }
    req.body.nodes.forEach(node=>{
        if (!node.hasOwnProperty('indicatorId'))
            throw new IFPError(400,'Invalid request format: Node has no indicatorId!');
        if (!Number(node.indicatorId))
          throw new IFPError(400,`${node.indicatorId} is not a valid indicator id`);
    })

    if (!req.body.title)
      throw new IFPError(400,'Please provide a title')

    next()
  };
  

module.exports = {
    validateIndicatorCompareNodes,
    validateIndicatorCompareDetail,
    validateIndicatorCompareAddToMyApps
}