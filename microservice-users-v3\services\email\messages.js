function baseTemplate(childTemplate){
    return `
    <html>
    <body bgcolor="#ffffff" style="margin: 0px; padding: 0px; -webkit-text-size-adjust: none; -ms-text-size-adjust: none;" yahoo="fix">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;">
      <tr>
         <td align="center" valign="top" bgcolor="#ffffff">
            <div align="center">
            <table width="680" border="0" cellspacing="0" cellpadding="0" class="container">
              <tbody>
                <tr>
                    <td align="left" valign="top" bgcolor="#dedede">
                        <!-- header start -->
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td align="left" valign="top" bgcolor="#dedede" style="padding-top:15px; padding-right: 40px; padding-bottom: 15px; padding-left: 40px;">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td align="left" valign="middle" bgcolor="#dedede" style="font-family: 'Noto Sans', 'Open Sans', sans-serif; font-size:12px;color: #1E2937; font-weight:700;line-height:16px;mso-line-height-rule:exactly;text-align:left;padding-right: 10px;">
                                                <a href="https://ifp.scad.gov.ae" title="Bayaan" target="_blank" style="text-decoration: none;"><img src="cid:ifp-logo" width="170" alt="Bayaan Platform" title="Bayaan Platform" border="0" style="font-family: 'Noto Sans', 'Open Sans', sans-serif; font-size: 12px; color: #1E2937; display: block;"></a></td>
                                            <td align="right" valign="top" bgcolor="#dedede" style="text-align: right;">
                                                <a href="https://scad.gov.ae/" title="Statistics Centre" target="_blank" style="text-decoration: none;"><img src="cid:scad-logo" width="102" height="44" alt="Statistics Centre" title="Statistics Centre" border="0" style="font-family: 'Noto Sans', 'Open Sans', sans-serif; font-size: 12px; color: #1E2937; display: block; float: right;"></a>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                        <!--  header end -->
                        
                        <!-- body start -->
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td align="left" valign="top" style="background-color: #F4F7F6; padding-top:40px; padding-right: 40px; padding-bottom: 40px; padding-left: 40px;">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                        ${childTemplate}                                        
                                    </table>
                                </td>
                            </tr>
                        </table>
                        <!-- body end -->
    
                        <!-- footer start -->
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td align="left" valign="top" style="background-color: #dedede;">
                                    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="table-layout: fixed;">
                                        <tr>
                                            <td align="center" width="33.33%" valign="top" style="padding-top:15px; padding-right: 10px; padding-bottom: 10px;background-color: #dedede;">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td align="center" valign="top" width="20" style="background-color: #dedede;"><img src="cid:link-icon" width="20" height="20" alt="arrow" border="0" style="font-family: 'Noto Sans', 'Open Sans', sans-serif; font-size: 12px; color: #1E2937;"></td>
                                                    </tr>
                                                    <tr>
                                                        <td align="center" valign="top" style="background-color: #dedede;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:12px;color: #1E2937;line-height:16px;mso-line-height-rule:exactly; padding-top: 5px;"><a href="https://scad.gov.ae" target="_blank" title="scad.gov.ae" style="text-decoration:none;color: #1E2937;"><span style="text-decoration: none; color: #1E2937;">scad.gov.ae</span></a></td>
                                                    </tr>
                                                </table>
                                            </td>
    
                                            <td align="center" width="33.33%" valign="top" style="padding-top:15px; padding-right: 10px; padding-bottom: 10px;background-color: #dedede;">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td align="center" valign="top" width="20" style="background-color: #dedede;"><img src="cid:phone-icon" width="20" height="20" alt="phone" border="0" style="font-family: 'Noto Sans', 'Open Sans', sans-serif; color: #1E2937;"></td>
                                                    </tr>
                                                    <tr>
                                                        <td align="center" valign="top" style="background-color: #dedede; font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:12px;color: #1E2937;line-height:16px;mso-line-height-rule:exactly; padding-top: 5px;"><a href="tel:800 555" target="_blank" title="800 555" style="text-decoration:none;color: #1E2937;"><span style="text-decoration: none; color: #1E2937;">800 555</span></a></td>
                                                    </tr>
                                                </table>
                                            </td>
    
                                            <td align="center" width="33.33%" valign="top" style="padding-top:15px; padding-right: 10px; padding-bottom: 10px;background-color: #dedede;">
                                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                                    <tr>
                                                        <td align="center" valign="top" width="20" style="background-color: #dedede;"><img src="cid:mail-icon" width="20" height="20" alt="mail" border="0" style="font-family: 'Noto Sans', 'Open Sans', sans-serif; color: #1E2937;"></td>
                                                    </tr>
                                                    <tr>
                                                        <td align="center" valign="top" style="background-color: #dedede;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:12px;color: #1E2937;line-height:16px;mso-line-height-rule:exactly; padding-top: 5px;"><a href="mailto:<EMAIL>" target="_blank" title="<EMAIL>" style="text-decoration:none;color: #1E2937;"><span style="text-decoration: none; color: #1E2937;"><EMAIL></span></a></td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align="left" valign="top" bgcolor="#dedede" style="display: block;padding-top: 10px; padding-bottom: 20px;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size: 12px;line-height: 16px; color: #1E2937;text-align: center;">Copyright © 2024 Statistics Center Abu Dhabi. All Rights Reserved.</td>
                            </tr>
                        </table>
                        <!-- footer end -->
                      </td>
                </tr>
              </tbody>
            </table>
          </div>
        </td>
       </tr>
    </table>
    <!--Inserted to Prevent Gmail Font Resizing//-->
    <div style="display:none; white-space:nowrap; font:15px 'Noto Sans'; color: #1E2937;"> - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - </div>
    <!--//Inserted to Prevent Gmail Font Resizing-->
    </body>
    </html>`
}


function entityInviteMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have received an invite from SCAD to register with the Bayaan Platform as the entity focal point. Click the following link to register.</td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;"><a href="${data.registrationLink}" target="_blank">Register Here</a></td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">The link expires in 2 days.</td>
        </tr>`
    return baseTemplate(message)
}

function entityRegisterOTPMessage(data){
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear User,
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">Your OTP for registration is ${data.otp}. This OTP is valid for 10 minutes</td>
        </tr>
        `
    return baseTemplate(message)
}

function entityRegisterCompleteMessage(data){
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have completed the registration for Bayaan Platform as Superuser. You will soon receive an invite to join the SCAD active directory from Microsoft.</td>
        </tr>
        `
    return baseTemplate(message)
}

function dgRegisterCompleteMessage(data){
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have been onboarded into Bayaan Platform as Director General. You will soon receive an invite to join the SCAD active directory from Microsoft.</td>
        </tr>
        `
    return baseTemplate(message)
}

function dgInviteMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have received an invite from SCAD to register with the Bayaan Platform. Click the following link to register.</td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;"><a href="${data.registrationLink}" target="_blank">Register Here</a></td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">The link expires in 2 days.</td>
        </tr>`
    return baseTemplate(message)
}

function userInviteMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have received an invite from SCAD to register with the Bayaan Platform. Click the following link to register.</td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;"><a href="${data.registrationLink}" target="_blank">Register Here</a></td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">The link expires in 2 days.</td>
        </tr>`
    return baseTemplate(message)
}

function userRequestSuccessMessage(data) {
    let message =  `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">Your request to access Bayaan Platform has been sent to your organization. You'll receive an email once your organization process your request</td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">For more information contact ${data.superuserName} (${data.superuserEmail})</td>
        </tr>`

    return baseTemplate(message)
}

function userRequestNotifySuperuser(data){
    let message =  `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">${data.requestorName} (${data.requestorEmail}) has requested access to Bayaan Platform.</td>
        </tr>`

    return baseTemplate(message)
}

function userNotifyBySuperUserRequestStatusMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">Your access request to Bayaan Platfrom has been ${data.status.toLowerCase()} by your organisation. ${data.status=='APPROVED'?"You'll receive an email once the Bayaan product engagement process your request":""} </td>
        </tr>
        ${data.status == 'REJECTED' && data.comments?`
        <tr>
        <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">Comments: ${data.comments}</td>
        </tr>`:''}`
                       
    return baseTemplate(message)
}

function peNotifyBySuperUserRequestStatusMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">${data.requestorName} has requested access to Bayaan Platfrom. The request has been approved by ${data.requestorEntity}.</td>
        </tr>
        `
                       
    return baseTemplate(message)
}

function superUserProcessMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have ${data.status.toLowerCase()} ${data.requestorName}'s request for access to Bayaan Platform. ${data.status=='APPROVED'?'You will be notified once the Bayaan Product Engagement process the request':''}</td>
        </tr>`
    return baseTemplate(message)
}

function productEngagementProcessMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">You have ${data.status.toLowerCase()} ${data.requestorName}'s request for access to Bayaan Platform. ${data.status=='APPROVED'?'A ticket has been created in the SANADKOM for approval process':''}</td>
        </tr>`
    return baseTemplate(message)
}

function userNotifyByPERequestStatusMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">Your request has been ${data.status.toLowerCase()} by Bayaan Product Engagement. ${data.status=='APPROVED'?'You will be notified of any further updates.':''}</td>
        </tr>`
    return baseTemplate(message)
}

function userSendShareLinkMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">Please follow the link below to complete your registration for Bayaan:</td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;"><a href="${data.link}">Registration Link</a></td>
        </tr>
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;"><ul>
            <li>Click on the link above.</li>
            <li>Fill in the required information on the registration page.</li>
            <li>Choose your required access</li>
            <li>Submit your registration.</li>
        </ul></td>
        </tr>
        
        `
                       
    return baseTemplate(message)
}


function superUserNotifyByPERequestStatusMessage(data) {
    let message = `
        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly; padding-bottom: 15px;">
                Dear ${data.recepientName},
            </td>
        </tr>

        <tr>
            <td align="left" valign="top" style="background-color: #F4F7F6;font-family: 'Noto Sans', 'Open Sans', sans-serif;font-size:16px;color: #1E2937;line-height:22px;mso-line-height-rule:exactly;padding-bottom: 10px;">${data.requestorName}'s access request has been ${data.status.toLowerCase()} by Bayaan Product Engagement. ${data.status=='APPROVED'?'A ticket has been created in the SANADKOM for approval. You will be notified of any further updates.':''}</td>
        </tr>`
    return baseTemplate(message)
}


// New Templates
function newInviteLinkTemplate(data) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Invite</title>
        <style>
        body {
            font-family: "Poppins", sans-serif;
            color: #404453;
            margin: 0%;
        }

        .leave-request-email-temp {
            background: white;
            max-width: 600px;
            margin: auto;
            border-spacing: 0;
            border-radius: 5px;
            overflow: hidden;
            font-size: 17px;
            border: 2px solid #dddddd;
        }

        .main_table {
            margin: auto;
            border-spacing: 0;
            overflow: hidden;
            font-size: 17px;
            background-size: cover;
            background-position: center center;
            border-radius: 6px 6px 0px 0px;

            background-color: #024fa3;
        }

        .table-body {
            margin: auto;
            border-spacing: 0;
            overflow: hidden;
            font-size: 17px;
            background-color: #fff;
        }

        .Maintable {
            border-left: 1px solid #dddddd;
            border-right: 1px solid #dddddd;
            border-bottom: 1px solid #dddddd;
            border-top: none;
            padding-bottom: 30px;
            font-family: arial, sans-serif;
            border-collapse: collapse;
        }

        .Maintable tr td {
            padding: 12px 20px;
        }

        .Maintable td,
        th {
            border-bottom: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            color: #4a5662;
            font-size: 14px;
        }

        .MaintableHead thead tr th p {
            display: flex;
            margin: 0;
            align-items: center;
            font-weight: 300;
        }

        .apply_date {
            color: #2b2b2b;
            font-weight: bold;
            margin: 0 10px;
        }

        .footer {
            background-color: #f6f6f6;
            width: 100%;
        }

        .country {
            font-size: 15px;
            font-weight: 300;
            color: #4a5662;
            margin: 0;
        }

        .footer-content {
            color: #4a5662;
            font-weight: 300;
        }

        .btn-blue {
            border-radius: 7px;
            background: #448ef6;
            color: #ffffff;
            list-style-type: none;
            padding: 10px 22px;
            font-size: 15px;
            letter-spacing: 1px;
            text-decoration: none;
            font-weight: 100;
            padding: 10px 12px;
        }

        .btn-blue a {
            text-decoration: none;
            color: #fff;
        }

        .btn-border-blue {
            list-style-type: none;
            border-radius: 7px;
            border: 1px solid #448ef6;
            color: #448ef6;
            padding: 10px 12px;
            font-size: 15px;
            letter-spacing: 1px;
            text-decoration: none;
            font-weight: 100;
            color: #000;
        }

        .btn-border-blue a {
            text-decoration: none;
            color: #448ef6;
        }

        .height-10 {
            height: 10px;
        }
        </style>
    </head>

    <body bgcolor="#F0F4F6">
        <!-- ====== Module : Leave Request ====== -->
        <table
        class="leave-request-email-temp"
        width="600"
        height="100%"
        cellspacing="0"
        cellpadding="0"
        class="tableContent"
        align="center"
        style="border: 1px solid #dddddd"
        >
        <tbody>
            <tr>
            <td bgcolor="#ffffff">
                <table
                class="main_table"
                width="600"
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                >
                <tbody>
                    <tr>
                    <td bgcolor="#ffffff">
                        <table
                        width="600"
                        align="center"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        >
                        <tbody>
                            <!-- bg color -->
                            <!-- space -->

                            <!-- name -->
                            <tr>
                            <td
                                class="res-center"
                                style="
                                text-align: center;
                                color: white;
                                font-family: 'Poppins', sans-serif;
                                font-size: 32px;
                                letter-spacing: 2px;
                                word-break: break-word;
                                line-height: 50px;
                                padding-left: 2px;
                                "
                            >
                                <img
                                src="cid:invite-banner"
                                alt="img"
                                width="100%"
                                />
                            </td>
                            </tr>
                            <!-- paragraph end -->

                            <!-- subtitle -->
                            <!--<tr>
                                                <td class="res-center" style="text-align: left; color: white; font-family: 'Poppins', sans-serif; font-size: 25px; word-break: break-word; font-weight: 600;letter-spacing: 1px;">-->
                            <!-- Your leave request has <br> been recieved-->
                            <!--</td>
                                            </tr>-->
                            <!-- subtitle end -->
                        </tbody>
                        </table>
                    </td>
                    </tr>
                </tbody>
                </table>

                <table
                width="600"
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                >
                <tbody>
                    <tr>
                    <td bgcolor="#ffffff">
                        <table
                        width="540"
                        style="font-size: 15px"
                        align="center"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        >
                        <tbody>
                            <tr>
                            <td
                                style="
                                font-family: arial, sans-serif;
                                font-size: 16px;
                                color: #4a5662;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: left;
                                padding-bottom: 8px;
                                "
                            >
                                Dear ${data.recepientName},
                            </td>
                            </tr>
                            <tr>
                            <td
                                style="
                                font-family: arial, sans-serif;
                                font-size: 16px;
                                color: #4a5662;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: left;
                                padding-bottom: 24px;
                                "
                            >
                            You have received an invite from SCAD to register with the Bayaan Platform. Click the following link to register.
                            </td>
                            </tr>
                            <!-- button -->
                            <tr>
                            <td
                                style="
                                -ms-text-size-adjust: none;
                                border-collapse: collapse;
                                -ms-text-size-adjust: none;
                                -webkit-text-size-adjust: none;
                                padding-bottom: 24px;
                                "
                                align="center"
                            >
                                <table
                                style="
                                    mso-table-rspace: 0pt;
                                    mso-table-lspace: 0pt;
                                    font-size: 100%;
                                    mso-table-rspace: 0pt;
                                    border-collapse: collapse;
                                    mso-table-lspace: 0pt;
                                    mso-table-rspace: 0pt;
                                "
                                >
                                <tbody>
                                    <tr>
                                    
                                    <td
                                        style="
                                        border-collapse: collapse;
                                        -webkit-text-size-adjust: none;
                                        -ms-text-size-adjust: none;
                                        border-collapse: collapse;
                                        -ms-text-size-adjust: none;
                                        -webkit-text-size-adjust: none;

                                        text-align: center;
                                        background-color: #1f73e7;
                                        padding: 8px;
                                        "
                                        bgcolor="#1F73E7"
                                    >
                                        <table
                                        style="
                                            mso-table-lspace: 0pt;
                                            font-size: 100%;
                                            border-collapse: collapse;
                                            mso-table-lspace: 0pt;
                                            mso-table-rspace: 0pt;
                                            mso-table-lspace: 0pt;
                                            font-size: 100%;
                                            mso-table-rspace: 0pt;
                                            border-collapse: collapse;
                                            mso-table-lspace: 0pt;
                                            mso-table-rspace: 0pt;
                                            width: 100%;
                                        "
                                        >
                                        <tbody>
                                            <tr>
                                            <td
                                                valign="center"
                                                style="
                                                border-collapse: collapse;
                                                -webkit-text-size-adjust: none;
                                                -ms-text-size-adjust: none;
                                                -webkit-text-size-adjust: none;
                                                -ms-text-size-adjust: none;
                                                border-collapse: collapse;
                                                "
                                                align="center"
                                            >
                                                <a
                                                target="_blank"
                                                style="
                                                    text-decoration: none;
                                                    color: #000001;
                                                    text-decoration: none;
                                                    -webkit-text-size-adjust: none;
                                                    -ms-text-size-adjust: none;
                                                    text-decoration: none;
                                                    -ms-text-size-adjust: none;
                                                    -webkit-text-size-adjust: none;
                                                    -ms-text-size-adjust: none;
                                                    color: #000001;
                                                    padding: 8px;
                                                    text-decoration: none;
                                                "
                                                rel="noopener noreferrer"
                                                href="#"
                                                >
                                                <span
                                                    style="
                                                    font-family: poppins,
                                                        'Open Sans', sans-serif;
                                                    font-size: 14px;
                                                    color: #fff;
                                                    line-height: 30px;
                                                    font-weight: 700;
                                                    mso-line-height-rule: exactly;
                                                    text-align: center;
                                                    text-decoration: none;
                                                    "
                                                    >Register Here
                                                </span>
                                                </a>
                                            </td>
                                            <td
                                                valign="center"
                                                style="
                                                border-collapse: collapse;
                                                -webkit-text-size-adjust: none;
                                                -ms-text-size-adjust: none;
                                                -webkit-text-size-adjust: none;
                                                -ms-text-size-adjust: none;
                                                border-collapse: collapse;
                                                "
                                                align="center"
                                            >
                                                <a
                                                target="_blank"
                                                style="
                                                    text-decoration: none;
                                                    color: #000001;
                                                    text-decoration: none;
                                                    -webkit-text-size-adjust: none;
                                                    -ms-text-size-adjust: none;
                                                    text-decoration: none;
                                                    -ms-text-size-adjust: none;
                                                    -webkit-text-size-adjust: none;
                                                    -ms-text-size-adjust: none;
                                                    color: #000001;
                                                    text-decoration: none;
                                                "
                                                rel="noopener noreferrer"
                                                href="${data.registrationLink}"
                                                >
                                                <img
                                                    width="21"
                                                    style="
                                                    border: 0;
                                                    display: block;
                                                    border: 0px;
                                                    height: 21px;
                                                    width: 21px;
                                                    padding: 4px;
                                                    "
                                                    src="https://stratus.campaign-image.com/images/1699512598770_4_zc_v11_8_303285000012234290.png"
                                                    height="21"
                                                    class=""
                                                    alt="images/arrow.png"
                                                    align="middle"
                                                />
                                                </a>
                                            </td>
                                            </tr>
                                        </tbody>
                                        </table>
                                    </td>
                                    </tr>
                                </tbody>
                                </table>
                            </td>
                            </tr>
                            <!-- buttonend -->

                            <tr>
                            <td
                                style="
                                font-family: arial, sans-serif;
                                font-size: 16px;
                                color: #4a5662;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: left;
                                padding-bottom: 16px;
                                "
                            >
                            <span style="font-weight: bold;">Note:</span> The link will expire in 2 days.
                            </td>
                            </tr>
                        </tbody>
                        </table>
                    </td>
                    </tr>
                    <tr>
                    <td>
                        <table width="100%">
                        <!-- best regards -->
                        <tr>
                            <td>
                            <table width="540" align="center">
                                <tr>
                                <td
                                    style="
                                    font-family: arial, sans-serif;
                                    font-size: 16px;
                                    color: #4a5662;
                                    line-height: 22px;
                                    mso-line-height-rule: exactly;
                                    text-align: left;
                                    padding-bottom: 4px;
                                    "
                                >
                                    Best Regards,
                                </td>
                                </tr>
                                <tr>
                                <td
                                    style="
                                    font-family: arial, sans-serif;
                                    font-size: 16px;
                                    color: #1e2937;
                                    font-weight: 600;
                                    line-height: 22px;
                                    mso-line-height-rule: exactly;
                                    text-align: left;
                                    "
                                >
                                    Bayaan Team
                                </td>
                                </tr>
                            </table>
                            </td>
                        </tr>
                        <!-- best regards end -->
                        <tr>
                            <td height="25" style="font-size: 0px">&nbsp;</td>
                        </tr>
                        <!-- footer -->
                        <tr>
                            <td
                            bgcolor="#F3F4F6"
                            style="
                                padding: 20px;
                                font-family: poppins, 'Open Sans', sans-serif;
                                font-size: 12px;
                                color: #364151;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: center;
                            "
                            align="center"
                            v-align="middle"
                            >
                            Copyright © 2024 Statistics Center Abu Dhabi. All
                            Rights Reserved
                            </td>
                        </tr>
                        <!-- footer end -->
                        </table>
                    </td>
                    </tr>
                </tbody>
                </table>
            </td>
            </tr>
        </tbody>
        </table>

        <!-- ====== Module : Leave Request ====== -->
    </body>
    </html>

    `
}

function newOTPTemplate(data) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Invite</title>
        <style>
        body {
            font-family: "Poppins", sans-serif;
            color: #404453;
            margin: 0%;
        }

        .leave-request-email-temp {
            background: white;
            max-width: 600px;
            margin: auto;
            border-spacing: 0;
            border-radius: 5px;
            overflow: hidden;
            font-size: 17px;
            border: 2px solid #dddddd;
        }

        .main_table {
            margin: auto;
            border-spacing: 0;
            overflow: hidden;
            font-size: 17px;
            background-size: cover;
            background-position: center center;
            border-radius: 6px 6px 0px 0px;

            background-color: #024fa3;
        }

        .table-body {
            margin: auto;
            border-spacing: 0;
            overflow: hidden;
            font-size: 17px;
            background-color: #fff;
        }

        .Maintable {
            border-left: 1px solid #dddddd;
            border-right: 1px solid #dddddd;
            border-bottom: 1px solid #dddddd;
            border-top: none;
            padding-bottom: 30px;
            font-family: arial, sans-serif;
            border-collapse: collapse;
        }

        .Maintable tr td {
            padding: 12px 20px;
        }

        .Maintable td,
        th {
            border-bottom: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            color: #4a5662;
            font-size: 14px;
        }

        .MaintableHead thead tr th p {
            display: flex;
            margin: 0;
            align-items: center;
            font-weight: 300;
        }

        .apply_date {
            color: #2b2b2b;
            font-weight: bold;
            margin: 0 10px;
        }

        .footer {
            background-color: #f6f6f6;
            width: 100%;
        }

        .country {
            font-size: 15px;
            font-weight: 300;
            color: #4a5662;
            margin: 0;
        }

        .footer-content {
            color: #4a5662;
            font-weight: 300;
        }

        .btn-blue {
            border-radius: 7px;
            background: #448ef6;
            color: #ffffff;
            list-style-type: none;
            padding: 10px 22px;
            font-size: 15px;
            letter-spacing: 1px;
            text-decoration: none;
            font-weight: 100;
            padding: 10px 12px;
        }

        .btn-blue a {
            text-decoration: none;
            color: #fff;
        }

        .btn-border-blue {
            list-style-type: none;
            border-radius: 7px;
            border: 1px solid #448ef6;
            color: #448ef6;
            padding: 10px 12px;
            font-size: 15px;
            letter-spacing: 1px;
            text-decoration: none;
            font-weight: 100;
            color: #000;
        }

        .btn-border-blue a {
            text-decoration: none;
            color: #448ef6;
        }

        .height-10 {
            height: 10px;
        }
        </style>
    </head>

    <body bgcolor="#F0F4F6">
        <!-- ====== Module : Leave Request ====== -->
        <table
        class="leave-request-email-temp"
        width="600"
        height="100%"
        cellspacing="0"
        cellpadding="0"
        class="tableContent"
        align="center"
        style="border: 1px solid #dddddd"
        >
        <tbody>
            <tr>
            <td bgcolor="#ffffff">
                <table
                class="main_table"
                width="600"
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                >
                <tbody>
                    <tr>
                    <td bgcolor="#ffffff">
                        <table
                        width="600"
                        align="center"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        >
                        <tbody>
                            <!-- bg color -->
                            <!-- space -->

                            <!-- name -->
                            <tr>
                            <td
                                class="res-center"
                                style="
                                text-align: center;
                                color: white;
                                font-family: 'Poppins', sans-serif;
                                font-size: 32px;
                                letter-spacing: 2px;
                                word-break: break-word;
                                line-height: 50px;
                                padding-left: 2px;
                                "
                            >
                                <img
                                src="cid:generate-otp-banner"
                                alt="img"
                                width="100%"
                                />
                            </td>
                            </tr>
                            <!-- paragraph end -->

                            <!-- subtitle -->
                            <!--<tr>
                                                <td class="res-center" style="text-align: left; color: white; font-family: 'Poppins', sans-serif; font-size: 25px; word-break: break-word; font-weight: 600;letter-spacing: 1px;">-->
                            <!-- Your leave request has <br> been recieved-->
                            <!--</td>
                                            </tr>-->
                            <!-- subtitle end -->
                        </tbody>
                        </table>
                    </td>
                    </tr>
                </tbody>
                </table>

                <table
                width="600"
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                >
                <tbody>
                    <tr>
                    <td bgcolor="#ffffff">
                        <table
                        width="540"
                        style="font-size: 15px"
                        align="center"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        >
                        <tbody>
                            <tr>
                            <td
                                style="
                                font-family: arial, sans-serif;
                                font-size: 16px;
                                color: #4a5662;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: left;
                                padding-bottom: 8px;
                                "
                            >
                                Dear User,
                            </td>
                            </tr>
                            <tr>
                            <td
                                style="
                                font-family: arial, sans-serif;
                                font-size: 16px;
                                color: #4a5662;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: left;
                                padding-bottom: 24px;
                                "
                            >
                            As part of our ongoing commitment to ensuring the security of your account, we require your attention for a brief account verification process.
                            </td>
                            </tr>
                                            <!-- bg-sec -->
                    <tr>
                    <td>
                        <table width="540" align="center">
                        <tbody>
                            <tr>
                            <td
                                bgcolor="#F3F4F6"
                                style="padding: 20px"
                            >
                                <table
                                width="100%"
                                align="center"
                                border="0"
                                cellpadding="0"
                                cellspacing="0"
                                >
                                <tbody>
                                    <tr>
                                    <td
                                        style="
                                        font-family: arial, sans-serif;
                                        font-size: 16px;
                                        color: #4a5662;
                                        line-height: 22px;
                                        mso-line-height-rule: exactly;
                                        text-align: left;
                                        padding-bottom: 8px;
                                        "
                                    >Verification Code:
                                    </td>
                                    </tr>
                                    <tr>
                                    <td
                                        style="
                                        font-family: arial, sans-serif;
                                        font-size: 16px;
                                        color: #4a5662;
                                        line-height: 22px;
                                        mso-line-height-rule: exactly;
                                        text-align: left;
                                        letter-spacing: 10px;
                                        "
                                    ><span style="font-weight: bold;">${data.otp}</span>
                                    </td>
                                    </tr>
                                </tbody>
                                </table>
                            </td>
                            </tr>
                        </tbody>
                        </table>
                    </td>
                    </tr>
                    <!-- bg-sec end -->
                            <tr>
                            <td
                                style="
                                font-family: arial, sans-serif;
                                font-size: 16px;
                                color: #4a5662;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: left;
                                padding-top: 24px;
                                padding-bottom: 24px;
                                "
                            >To proceed with the verification, kindly enter the provided One Time Password (OTP) when prompted during the verification process. Please note that this OTP is valid for <span style="font-weight: bold;">10 minutes</span> and should be used promptly to ensure a smooth verification experience.
                            </td>
                            </tr>
                        </tbody>
                        </table>
                    </td>
                    </tr>
                    <tr>
                    <td>
                        <table width="100%">
                        <!-- best regards -->
                        <tr>
                            <td>
                            <table width="540" align="center">
                                <tr>
                                <td
                                    style="
                                    font-family: arial, sans-serif;
                                    font-size: 16px;
                                    color: #4a5662;
                                    line-height: 22px;
                                    mso-line-height-rule: exactly;
                                    text-align: left;
                                    padding-bottom: 4px;
                                    "
                                >
                                    Best Regards,
                                </td>
                                </tr>
                                <tr>
                                <td
                                    style="
                                    font-family: arial, sans-serif;
                                    font-size: 16px;
                                    color: #1e2937;
                                    font-weight: 600;
                                    line-height: 22px;
                                    mso-line-height-rule: exactly;
                                    text-align: left;
                                    "
                                >
                                    Bayaan Team
                                </td>
                                </tr>
                            </table>
                            </td>
                        </tr>
                        <!-- best regards end -->
                        <tr>
                            <td height="25" style="font-size: 0px">&nbsp;</td>
                        </tr>
                        <!-- footer -->
                        <tr>
                            <td
                            bgcolor="#F3F4F6"
                            style="
                                padding: 20px;
                                font-family: poppins, 'Open Sans', sans-serif;
                                font-size: 12px;
                                color: #364151;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: center;
                            "
                            align="center"
                            v-align="middle"
                            >
                            Copyright © 2024 Statistics Center Abu Dhabi. All
                            Rights Reserved
                            </td>
                        </tr>
                        <!-- footer end -->
                        </table>
                    </td>
                    </tr>
                </tbody>
                </table>
            </td>
            </tr>
        </tbody>
        </table>

        <!-- ====== Module : Leave Request ====== -->
    </body>
    </html>

    `
}

function newBaseTemplate(children, imageBanner) {
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Invite</title>
        <style>
        body {
            font-family: "Poppins", sans-serif;
            color: #404453;
            margin: 0%;
        }

        .leave-request-email-temp {
            background: white;
            max-width: 600px;
            margin: auto;
            border-spacing: 0;
            border-radius: 5px;
            overflow: hidden;
            font-size: 17px;
            border: 2px solid #dddddd;
        }

        .main_table {
            margin: auto;
            border-spacing: 0;
            overflow: hidden;
            font-size: 17px;
            background-size: cover;
            background-position: center center;
            border-radius: 6px 6px 0px 0px;

            background-color: #024fa3;
        }

        .table-body {
            margin: auto;
            border-spacing: 0;
            overflow: hidden;
            font-size: 17px;
            background-color: #fff;
        }

        .Maintable {
            border-left: 1px solid #dddddd;
            border-right: 1px solid #dddddd;
            border-bottom: 1px solid #dddddd;
            border-top: none;
            padding-bottom: 30px;
            font-family: arial, sans-serif;
            border-collapse: collapse;
        }

        .Maintable tr td {
            padding: 12px 20px;
        }

        .Maintable td,
        th {
            border-bottom: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            color: #4a5662;
            font-size: 14px;
        }

        .MaintableHead thead tr th p {
            display: flex;
            margin: 0;
            align-items: center;
            font-weight: 300;
        }

        .apply_date {
            color: #2b2b2b;
            font-weight: bold;
            margin: 0 10px;
        }

        .footer {
            background-color: #f6f6f6;
            width: 100%;
        }

        .country {
            font-size: 15px;
            font-weight: 300;
            color: #4a5662;
            margin: 0;
        }

        .footer-content {
            color: #4a5662;
            font-weight: 300;
        }

        .btn-blue {
            border-radius: 7px;
            background: #448ef6;
            color: #ffffff;
            list-style-type: none;
            padding: 10px 22px;
            font-size: 15px;
            letter-spacing: 1px;
            text-decoration: none;
            font-weight: 100;
            padding: 10px 12px;
        }

        .btn-blue a {
            text-decoration: none;
            color: #fff;
        }

        .btn-border-blue {
            list-style-type: none;
            border-radius: 7px;
            border: 1px solid #448ef6;
            color: #448ef6;
            padding: 10px 12px;
            font-size: 15px;
            letter-spacing: 1px;
            text-decoration: none;
            font-weight: 100;
            color: #000;
        }

        .btn-border-blue a {
            text-decoration: none;
            color: #448ef6;
        }

        .height-10 {
            height: 10px;
        }
        </style>
    </head>

    <body bgcolor="#F0F4F6">
        <!-- ====== Module : Leave Request ====== -->
        <table
        class="leave-request-email-temp"
        width="600"
        height="100%"
        cellspacing="0"
        cellpadding="0"
        class="tableContent"
        align="center"
        style="border: 1px solid #dddddd"
        >
        <tbody>
            <tr>
            <td bgcolor="#ffffff">
                <table
                class="main_table"
                width="600"
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                >
                <tbody>
                    <tr>
                    <td bgcolor="#ffffff">
                        <table
                        width="600"
                        align="center"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        >
                        <tbody>
                            <!-- bg color -->
                            <!-- space -->

                            <!-- name -->
                            <tr>
                            <td
                                class="res-center"
                                style="
                                text-align: center;
                                color: white;
                                font-family: 'Poppins', sans-serif;
                                font-size: 32px;
                                letter-spacing: 2px;
                                word-break: break-word;
                                line-height: 50px;
                                padding-left: 2px;
                                "
                            >
                                <img
                                src="cid:${imageBanner}"
                                alt="img"
                                width="100%"
                                />
                            </td>
                            </tr>
                            <!-- paragraph end -->

                            <!-- subtitle -->
                            <!--<tr>
                                                <td class="res-center" style="text-align: left; color: white; font-family: 'Poppins', sans-serif; font-size: 25px; word-break: break-word; font-weight: 600;letter-spacing: 1px;">-->
                            <!-- Your leave request has <br> been recieved-->
                            <!--</td>
                                            </tr>-->
                            <!-- subtitle end -->
                        </tbody>
                        </table>
                    </td>
                    </tr>
                </tbody>
                </table>

                <table
                width="600"
                align="center"
                border="0"
                cellpadding="0"
                cellspacing="0"
                >
                <tbody>
                    <tr>
                    <td bgcolor="#ffffff">
                        <table
                        width="540"
                        style="font-size: 15px"
                        align="center"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        >
                        <tbody>
                            ${children}
                        </tbody>
                        </table>
                    </td>
                    </tr>
                    <tr>
                    <td>
                        <table width="100%">
                        <!-- best regards -->
                        <tr>
                            <td>
                            <table width="540" align="center">
                                <tr>
                                <td
                                    style="
                                    font-family: arial, sans-serif;
                                    font-size: 16px;
                                    color: #4a5662;
                                    line-height: 22px;
                                    mso-line-height-rule: exactly;
                                    text-align: left;
                                    padding-bottom: 4px;
                                    "
                                >
                                    Best Regards,
                                </td>
                                </tr>
                                <tr>
                                <td
                                    style="
                                    font-family: arial, sans-serif;
                                    font-size: 16px;
                                    color: #1e2937;
                                    font-weight: 600;
                                    line-height: 22px;
                                    mso-line-height-rule: exactly;
                                    text-align: left;
                                    "
                                >
                                    Bayaan Team
                                </td>
                                </tr>
                            </table>
                            </td>
                        </tr>
                        <!-- best regards end -->
                        <tr>
                            <td height="25" style="font-size: 0px">&nbsp;</td>
                        </tr>
                        <!-- footer -->
                        <tr>
                            <td
                            bgcolor="#F3F4F6"
                            style="
                                padding: 20px;
                                font-family: poppins, 'Open Sans', sans-serif;
                                font-size: 12px;
                                color: #364151;
                                line-height: 22px;
                                mso-line-height-rule: exactly;
                                text-align: center;
                            "
                            align="center"
                            v-align="middle"
                            >
                            Copyright © 2024 Statistics Center Abu Dhabi. All
                            Rights Reserved
                            </td>
                        </tr>
                        <!-- footer end -->
                        </table>
                    </td>
                    </tr>
                </tbody>
                </table>
            </td>
            </tr>
        </tbody>
        </table>

        <!-- ====== Module : Leave Request ====== -->
    </body>
    </html>

    `
}

function newInviteLinkTemplateV2(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear ${data.recepientName},
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        You have received an invite from SCAD to register
        with the Bayaan Platform. Click the following link
        to register.
        </td>
    </tr>
    <tr>
        <td
        style="
            -ms-text-size-adjust: none;
            border-collapse: collapse;
            -ms-text-size-adjust: none;
            -webkit-text-size-adjust: none;
            padding-bottom: 24px;
        "
        align="center"
        >
        <table
            style="
            mso-table-rspace: 0pt;
            mso-table-lspace: 0pt;
            font-size: 100%;
            mso-table-rspace: 0pt;
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            "
        >
            <tbody>
            <tr>
                <td
                style="
                    border-collapse: collapse;
                    -webkit-text-size-adjust: none;
                    -ms-text-size-adjust: none;
                    border-collapse: collapse;
                    -ms-text-size-adjust: none;
                    -webkit-text-size-adjust: none;

                    text-align: center;
                    background-color: #1f73e7;
                    padding: 8px;
                "
                bgcolor="#1F73E7"
                >
                <table
                    style="
                    mso-table-lspace: 0pt;
                    font-size: 100%;
                    border-collapse: collapse;
                    mso-table-lspace: 0pt;
                    mso-table-rspace: 0pt;
                    mso-table-lspace: 0pt;
                    font-size: 100%;
                    mso-table-rspace: 0pt;
                    border-collapse: collapse;
                    mso-table-lspace: 0pt;
                    mso-table-rspace: 0pt;
                    width: 100%;
                    "
                >
                    <tbody>
                    <tr>
                        <td
                        valign="center"
                        style="
                            border-collapse: collapse;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            border-collapse: collapse;
                        "
                        align="center"
                        >
                        <a
                            target="_blank"
                            style="
                            text-decoration: none;
                            color: #000001;
                            text-decoration: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            text-decoration: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            color: #000001;
                            padding: 8px;
                            text-decoration: none;
                            "
                            rel="noopener noreferrer"
                            href="${data.registrationLink}"
                        >
                            <span
                            style="
                                font-family: poppins,
                                'Open Sans', sans-serif;
                                font-size: 14px;
                                color: #fff;
                                line-height: 30px;
                                font-weight: 700;
                                mso-line-height-rule: exactly;
                                text-align: center;
                                text-decoration: none;
                            "
                            >Register Here
                            </span>
                        </a>
                        </td>
                        <td
                        valign="center"
                        style="
                            border-collapse: collapse;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            border-collapse: collapse;
                        "
                        align="center"
                        >
                        <a
                            target="_blank"
                            style="
                            text-decoration: none;
                            color: #000001;
                            text-decoration: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            text-decoration: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            color: #000001;
                            text-decoration: none;
                            "
                            rel="noopener noreferrer"
                            href="${data.registrationLink}"
                        >
                            <img
                            width="21"
                            style="
                                border: 0;
                                display: block;
                                border: 0px;
                                height: 21px;
                                width: 21px;
                                padding: 4px;
                            "
                            src="cid:register-proceed-icon"
                            height="21"
                            class=""
                            alt="images/arrow.png"
                            align="middle"
                            />
                        </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
                </td>
            </tr>
            </tbody>
        </table>
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 16px;
        "
        >
        <span style="font-weight: bold">Note:</span> The
        link will expire in 2 days.
        </td>
    </tr>
    `
    return newBaseTemplate(body, "invite-banner")
}

function newOTPTemplateV2(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear User,
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        As part of our ongoing commitment to ensuring the
        security of your account, we require your attention
        for a brief account verification process.
        </td>
    </tr>
    <!-- bg-sec -->
    <tr>
        <td>
        <table width="540" align="center">
            <tbody>
            <tr>
                <td bgcolor="#F3F4F6" style="padding: 20px">
                <table
                    width="100%"
                    align="center"
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                >
                    <tbody>
                    <tr>
                        <td
                        style="
                            font-family: arial, sans-serif;
                            font-size: 16px;
                            color: #4a5662;
                            line-height: 22px;
                            mso-line-height-rule: exactly;
                            text-align: left;
                            padding-bottom: 8px;
                        "
                        >
                        Verification Code:
                        </td>
                    </tr>
                    <tr>
                        <td
                        style="
                            font-family: arial, sans-serif;
                            font-size: 16px;
                            color: #4a5662;
                            line-height: 22px;
                            mso-line-height-rule: exactly;
                            text-align: left;
                            letter-spacing: 10px;
                        "
                        >
                        <span style="font-weight: bold"
                            >${data.otp}</span
                        >
                        </td>
                    </tr>
                    </tbody>
                </table>
                </td>
            </tr>
            </tbody>
        </table>
        </td>
    </tr>
    <!-- bg-sec end -->
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-top: 24px;
            padding-bottom: 24px;
        "
        >
        To proceed with the verification, kindly enter the
        provided One Time Password (OTP) when prompted
        during the verification process. Please note that
        this OTP is valid for
        <span style="font-weight: bold">10 minutes</span>
        and should be used promptly to ensure a smooth
        verification experience.
        </td>
    </tr>
    `;
    return newBaseTemplate(body, "generate-otp-banner")
}

function successfulRegistrationTemplate(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear User,
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        You have completed the registration for Bayaan
        platform as Superuser. You will soon receive an
        invite to join the SCAD active directory from
        Microsoft.
        </td>
    </tr>
    `;
    return newBaseTemplate(body, "registration-success-banner")
}

function entityRegisterCompleteMessageV2(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear ${data.recepientName},
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        You have completed the registration for Bayaan
        platform as Superuser. You will soon receive an
        invite to join the SCAD active directory from
        Microsoft.
        </td>
    </tr>
    `;
    return newBaseTemplate(body, "registration-success-banner")
}

function dgRegisterCompleteMessageV2(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear ${data.recepientName},
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        You have been onboarded into Bayaan Platform as Director General.
        You will soon receive an invite to join the SCAD active directory from Microsoft.
        </td>
    </tr>
    `;
    return newBaseTemplate(body, "registration-success-banner")
}

function existingUserPlatformInvite(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear ${data.recepientName},
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        You have been invited to Bayaan platform as a ${data.userType}.
        Click the following link to login:
        </td>
    </tr>
    <tr>
        <td
        style="
            -ms-text-size-adjust: none;
            border-collapse: collapse;
            -ms-text-size-adjust: none;
            -webkit-text-size-adjust: none;
            padding-bottom: 24px;
        "
        align="center"
        >
        <table
            style="
            mso-table-rspace: 0pt;
            mso-table-lspace: 0pt;
            font-size: 100%;
            mso-table-rspace: 0pt;
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            "
        >
            <tbody>
            <tr>
                <td
                style="
                    border-collapse: collapse;
                    -webkit-text-size-adjust: none;
                    -ms-text-size-adjust: none;
                    border-collapse: collapse;
                    -ms-text-size-adjust: none;
                    -webkit-text-size-adjust: none;

                    text-align: center;
                    background-color: #1f73e7;
                    padding: 8px;
                "
                bgcolor="#1F73E7"
                >
                <table
                    style="
                    mso-table-lspace: 0pt;
                    font-size: 100%;
                    border-collapse: collapse;
                    mso-table-lspace: 0pt;
                    mso-table-rspace: 0pt;
                    mso-table-lspace: 0pt;
                    font-size: 100%;
                    mso-table-rspace: 0pt;
                    border-collapse: collapse;
                    mso-table-lspace: 0pt;
                    mso-table-rspace: 0pt;
                    width: 100%;
                    "
                >
                    <tbody>
                    <tr>
                        <td
                        valign="center"
                        style="
                            border-collapse: collapse;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            border-collapse: collapse;
                        "
                        align="center"
                        >
                        <a
                            target="_blank"
                            style="
                            text-decoration: none;
                            color: #000001;
                            text-decoration: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            text-decoration: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            color: #000001;
                            padding: 8px;
                            text-decoration: none;
                            "
                            rel="noopener noreferrer"
                            href="${data.bayaanLoginLink}"
                        >
                            <span
                            style="
                                font-family: poppins,
                                'Open Sans', sans-serif;
                                font-size: 14px;
                                color: #fff;
                                line-height: 30px;
                                font-weight: 700;
                                mso-line-height-rule: exactly;
                                text-align: center;
                                text-decoration: none;
                            "
                            >Login
                            </span>
                        </a>
                        </td>
                        <td
                        valign="center"
                        style="
                            border-collapse: collapse;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            border-collapse: collapse;
                        "
                        align="center"
                        >
                        <a
                            target="_blank"
                            style="
                            text-decoration: none;
                            color: #000001;
                            text-decoration: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            text-decoration: none;
                            -ms-text-size-adjust: none;
                            -webkit-text-size-adjust: none;
                            -ms-text-size-adjust: none;
                            color: #000001;
                            text-decoration: none;
                            "
                            rel="noopener noreferrer"
                            href="${data.bayaanLoginLink}"
                        >
                            <img
                            width="21"
                            style="
                                border: 0;
                                display: block;
                                border: 0px;
                                height: 21px;
                                width: 21px;
                                padding: 4px;
                            "
                            src="cid:register-proceed-icon"
                            height="21"
                            class=""
                            alt="images/arrow.png"
                            align="middle"
                            />
                        </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
                </td>
            </tr>
            </tbody>
        </table>
        </td>
    </tr>
    `
    return newBaseTemplate(body, "invite-banner")
}

/**
 * Notify users once they have a new access request.
 * @param {{
 * accessLevel: string
 * recepientName: string,
 * platformLink: string
 * }} data
 * @returns 
 */
function newAccessRequestNotification(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear ${data.recepientName},
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        You have received a new ${data.accessLevel} access request.
        To review the details of this request, <a href="${data.platformLink}" target="_blank">please click here</a>.
        </td>
    </tr>
    `;
    return newBaseTemplate(body, "email-new-message-banner")
}

/**
 * Approval / Rejection notification template.
 * @param {{
 * recepientName: string,
 * recepientRole: string,
 * accessLevel: string,
 * requestorEmail: string,
 * action: string,
 * actor: string}} data 
 * @returns 
 */
function accessRequestStatusUpdateNotification(data) {
    const body = `
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 8px;
        "
        >
        Dear ${data.recepientName},
        </td>
    </tr>
    <tr>
        <td
        style="
            font-family: arial, sans-serif;
            font-size: 16px;
            color: #4a5662;
            line-height: 22px;
            mso-line-height-rule: exactly;
            text-align: left;
            padding-bottom: 24px;
        "
        >
        Your request for ${data.accessLevel} access ${data.recepientRole !== 'USER' ? `for the user: ${data.requestorEmail}`: ''} has been ${data.action} by the ${data.actor} team.
        </td>
    </tr>
    `;
    return newBaseTemplate(body, "email-new-message-banner")
}

/**
 * Superuser Revoke.
 * @param {{
* recepientName: string,
* recepientRole: string,
* revokeeEmail: string,
* actor: string,
* domainData: Array}} data 
* @returns 
*/
function userRevokeNotification(data) {
   const body = `
   <tr>
       <td
       style="
           font-family: arial, sans-serif;
           font-size: 16px;
           color: #4a5662;
           line-height: 22px;
           mso-line-height-rule: exactly;
           text-align: left;
           padding-bottom: 8px;
       "
       >
       Dear ${data.recepientName},
       </td>
   </tr>
   <tr>
       <td
       style="
           font-family: arial, sans-serif;
           font-size: 16px;
           color: #4a5662;
           line-height: 22px;
           mso-line-height-rule: exactly;
           text-align: left;
           padding-bottom: 24px;
       "
       >
        ${data.recepientRole == 'USER' ? 'Your access' : `Access of the user: ${data.revokeeEmail}`} for the following domains has been revoked by the ${data.actor}:
        <ul>${data.domainData.map(d => `<li>${d}</li>`).join('')}</ul>
       </td>
   </tr>
   `;
   return newBaseTemplate(body, "email-new-message-banner")
}

/**
 * User delete notification template.
 * @param {{
* recepientName: string,
* recepientRole: string,
* userEmail: string,
* actorEmail: string}} data 
* @returns 
*/
function userDeleteNotification(data) {
   const body = `
   <tr>
       <td
       style="
           font-family: arial, sans-serif;
           font-size: 16px;
           color: #4a5662;
           line-height: 22px;
           mso-line-height-rule: exactly;
           text-align: left;
           padding-bottom: 8px;
       "
       >
       Dear ${data.recepientName},
       </td>
   </tr>
   <tr>
       <td
       style="
           font-family: arial, sans-serif;
           font-size: 16px;
           color: #4a5662;
           line-height: 22px;
           mso-line-height-rule: exactly;
           text-align: left;
           padding-bottom: 24px;
       "
       >
       ${data.recepientRole == 'USER' ? 'Your Bayaan account' : `The following user: ${data.userEmail}`} has been deleted
       by the super user: ${data.actorEmail}.
       </td>
   </tr>
   `;
   return newBaseTemplate(body, "email-new-message-banner")
} 

module.exports = { 

    entityInviteMessage,
    entityRegisterOTPMessage,
    entityRegisterCompleteMessage,
    userInviteMessage,
    dgRegisterCompleteMessage,
    dgInviteMessage,
    userRequestSuccessMessage, userRequestNotifySuperuser, userSendShareLinkMessage,
    superUserProcessMessage, userNotifyBySuperUserRequestStatusMessage, 
    peNotifyBySuperUserRequestStatusMessage, productEngagementProcessMessage,
    userNotifyByPERequestStatusMessage, superUserNotifyByPERequestStatusMessage,
    newInviteLinkTemplate, newOTPTemplate, newBaseTemplate, newOTPTemplateV2,
    newInviteLinkTemplateV2, successfulRegistrationTemplate, entityRegisterCompleteMessageV2,
    dgRegisterCompleteMessageV2, existingUserPlatformInvite, newAccessRequestNotification,
    accessRequestStatusUpdateNotification, userRevokeNotification, userDeleteNotification, 
}