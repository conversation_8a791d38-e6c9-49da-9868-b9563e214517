const express = require('express');
const router = new express.Router();
const innovativeInsightsController = require('../microservice-innovative-insights/innovative-insights.controller');
const { validateInnovativeInsights, validateInnovativeInsightsList } = require('./validators/innovative-insights.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.post('/', validateInnovativeInsightsList, async (req, res, next) => {
    try {
        const data = await innovativeInsightsController.getInnovativeIndicators(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/:id',validateInnovativeInsights,  async (req, res, next) => {
    try {
        const data = await innovativeInsightsController.getInnovativeInsightsById(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.post('/compare', async (req, res, next) => {
    try {
        const data = await innovativeInsightsController.compareInnovativeInsights(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/filters/:id', async (req, res, next) => {
    try {
        const data = await innovativeInsightsController.getInnovativeFilters(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for statistics-insights content-type, ERROR: ${err}`);
        next(err);
    }
});


module.exports = router;
