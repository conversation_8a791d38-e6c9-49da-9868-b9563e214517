require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const moment = require('moment')
const { getMetaFromCMS } = require('../services/common-service');
let constants = require('../config/constants.json');

const { checkSurveyAttend, attendSurveyQueries, checkUserTCAccept } = require('./services/executeQuery.service');
const { IFPError } = require('../utils/error');

/**
 * function to get survey content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */
async function checkSurveyStatus(req) {
    log.debug(`>>>>>Entered survey-microservice.survey.controller.checkSurveyStatus`);
    return new Promise(async (resolve, reject) => {
        try {
            let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
            let cmsSurveyUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_SURVEY_URL}`;
            let data = [];
            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
            try {
                data = await getMetaFromCMS(req,cmsLoginUrl, cmsSurveyUrl, req.user.groups);
            }
            catch {
                return resolve({
                    "message": 'No surveys to attend!!!',
                    "surveyStatus": false,
                    "reason":"CMS content not available"
                });
            }

            if (data.length<1) {
                return resolve({
                    "message": 'No surveys to attend!!!',
                    "surveyStatus": false,
                    "reason":"Survey content not available"
                });

            }



            let survey = {
                "id": data[0]['nid'],
                "title_en": data[0]['title'],
                "title_ar": data[0]['field_survey_title_ar'],
                "desc_en": data[0]['field_survey_subtitle'],
                "desc_ar": data[0]['field_survey_subtitle_ar'],
                "launch_date": data[0]['revision_timestamp'],
                "queries": []
            }
            data[0]['field_survey_questions_export'].forEach(query => {
                survey['queries'].push({
                    'query_en': query['survey_questions_en'],
                    'query_ar': query['survey_questions_ar']
                })
            })

            let tcData = await checkUserTCAccept(req.user.preferred_username);
            if (tcData.length) {

                let surveyAttendData = await checkSurveyAttend(req.user.preferred_username, survey['id'], survey['launch_date'].replace(/-/g, "/"))

                if (surveyAttendData.length) {

                    return resolve({
                        "message": 'No surveys to attend!!!',
                        "surveyStatus": false,
                        "reason":"User already attended this survey"
                    });
                    
                }
                else {
                    tcData = tcData[0]
                    const tcAcceptDate = moment(tcData['TC_ACCEPT_DT'], 'DD/MM/YYYY')

                    let diffDate = moment().diff(tcAcceptDate, 'days')
                    if (diffDate < 5){
                        return resolve({
                            "message": 'No surveys to attend!!!',
                            "surveyStatus": false,
                            "reason":"New user"
                        });
                    }
                    else{
                        return resolve({
                            "message": 'Surveys to attend!!!',
                            "surveyStatus": true,
                            "surveyDetails": survey
                        });
                    }
                }

                
            }
            else{
                return resolve({
                    "message": 'No surveys to attend!!!',
                    "surveyStatus": false,
                    "reason":"User has not accepted terms and conditions"
                });
            }
            
        } catch (err) {
            log.error(`<<<<<Exited survey-microservice.survey.controller.checkSurveyStatus on getting CMS data with error ${err}`);
            return reject(err);
        }
    })
}

async function attendSurvey(req) {
    log.debug(`>>>>>Entered survey-microservice.survey.controller.attendSurvey`);
    try {
        const survey = req.body
        const userId = req.user.preferred_username;
        const user_name = req.user.name

        let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        let cmsSurveyUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsUrl.CMS_SURVEY_URL}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        let surveyData = await getMetaFromCMS(req,cmsLoginUrl, cmsSurveyUrl, req.user.groups);
        let surveyIds = surveyData.map(s=>s.nid)
        if (!surveyIds.includes(survey.id))
            throw new IFPError(400,"Invalid Survey ID")


        const data = await attendSurveyQueries(userId, user_name, survey);
        if (data) {
            log.debug(`<<<<<Exited survey-microservice.survey.controller.attendSurvey successfully `);
            return {
                "message": 'Survey attended successfully',
                "status": true
            };
        } else {
            return {
                "message": 'Survey failed',
                "status": false,
            };
        }
    } catch (err) {
        log.error(`<<<<<Exited survey-microservice.survey.controller.attendSurvey on getting CMS data with error ${err}`);
        throw err;
    }
}


module.exports = { checkSurveyStatus, attendSurvey };
