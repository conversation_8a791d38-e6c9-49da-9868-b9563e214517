const Logger = require("scad-library").logger;
require("dotenv").config();
const log = new Logger().getInstance();
const constants = require("../config/constants.json");
const { IFPError } = require("../utils/error");
const _ = require("lodash");

const {
  getDataGovernanceHomeChart,
  getDataSceinceInputData,
  getDataSceinceOutputData,
  getUseCaseData,
  getDataSourceData,
  getDataScienceData,
  getWorkflowDetailsData,
  getBayaanSvsData,
  getStatisticalIndicatorData,
  getComparisonChartData,
  getUseCaseDropDownData,
  getDataSourceDropDownData,
  getDataScienceDropDownData,
  getWorkflowDetailsDropDownData,
  getBayaanSvsDropDownData,
  getStatisticalIndicatorDropDownData,
  getSvCalenderData,
  getEventDetail,
} = require("./services/executeQuery");

async function getDataGovernanceChart(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDataGovernanceChart`
  );
  try {
    let governanceChartData = await getDataGovernanceHomeChart();
    const resp = {
      products: governanceChartData.filter((x) => x.id == 1),
      indicators: governanceChartData.filter((x) => x.id == 2),
    };
    return resp;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

function removeTotalCount(data) {
  if (data?.length <= 0) {
    return [];
  }
  const transformedData = data.map((item) => {
    delete item.total_count;
    return item;
  });

  return transformedData;
}

async function getDataSceinceInput(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDataSceinceInput`
  );
  const page = req.query.page ? req.query.page : 1;
  const limit = req.query.limit ? req.query.limit : 10;
  const filterName = decodeURI(req.query.filter);

  try {
    let governanceCategoryData = await getDataSceinceInputData(
      page,
      limit,
      filterName
    );

    return {
      title: "INPUT INDICATOR DETAILS",
      totalCount: governanceCategoryData?.length
        ? governanceCategoryData[0].total_count
        : 0,
      data: removeTotalCount(governanceCategoryData),
    };
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getDataSceinceOutput(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDataSceinceOutput`
  );
  const page = req.query.page ? req.query.page : 1;
  const limit = req.query.limit ? req.query.limit : 10;
  const filterName = decodeURI(req.query.filter);

  try {
    let governanceCategoryData = await getDataSceinceOutputData(
      page,
      limit,
      filterName
    );

    return {
      title: "OUTPUT INDICATOR DETAILS",
      totalCount: governanceCategoryData?.length
        ? governanceCategoryData[0].total_count
        : 0,
      data: removeTotalCount(governanceCategoryData),
    };
  } catch (err) {
    log.error(err);
    throw err;
  }
}

// new functions //
async function getUseCaseTableData(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getUseCaseTableData`
  );
  const page = req.query.page;
  const limit = req.query.limit;
  const filter = req.body;

  try {
    const data = await getUseCaseData(page, limit, filter);
    const result = {
      tableData: {
        title: "STATISTICAL PRODUCT",
        totalCount: data[0]?.length ? data[0][0].total_count : 0,
        data: removeTotalCount(data[0]),
      },
      tableValues: data[1],
    };
    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getDataSourceTableData(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDataSourceTableData`
  );
  const page = req.query.page ? req.query.page : 1;
  const limit = req.query.limit ? req.query.limit : 10;
  const filterName = decodeURI(req.query.filter);

  try {
    let data = await getDataSourceData(page, limit, filterName);
    const result = {
      tableData: {
        title: "DATA SOURCE",
        totalCount: data[0]?.length ? data[0][0].total_count : 0,
        data: removeTotalCount(data[0]),
      },
      tableValues: data[1],
    };

    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getDatascienceTableData(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDatascienceTableData`
  );
  const page = req.query.page ? req.query.page : 1;
  const limit = req.query.limit ? req.query.limit : 10;
  const filterName = decodeURI(req.query.filter);

  try {
    let data = await getDataScienceData(page, limit, filterName);
    const result = {
      tableData: {
        title: "MODEL DETAILS",
        totalCount: data[0]?.length ? data[0][0].total_count : 0,
        data: removeTotalCount(data[0]),
      },
      tableValues: data[1],
    };
    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getWorkflowDetailsTableData(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getWorkflowDetailsTableData`
  );
  const page = req.query.page ? req.query.page : 1;
  const limit = req.query.limit ? req.query.limit : 10;
  const filterName = decodeURI(req.query.filter);

  try {
    let data = await getWorkflowDetailsData(page, limit, filterName);
    const result = {
      tableData: {
        title: "WORKFLOW DETAILS",
        totalCount: data[0]?.length ? data[0][0].total_count : 0,
        data: removeTotalCount(data[0]),
      },
      tableValues: data[1],
    };
    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getBayaanSvsTableData(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getBayaanSvsTableData`
  );
  const page = req.query.page;
  const limit = req.query.limit;
  const filter = req.body;

  try {
    let data = await getBayaanSvsData(page, limit, filter);
    const result = {
      tableData: {
        title: "BAYAAN SV DETAILS",
        totalCount: data[0]?.length ? data[0][0].total_count : 0,
        data: removeTotalCount(data[0]),
      },
      tableValues: data[1],
    };
    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getStatisticalIndicatorTableData(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getStatisticalIndicatorTableData`
  );
  const page = req.query.page;
  const limit = req.query.limit;
  const filter = req.body;
  try {
    let data = await getStatisticalIndicatorData(page, limit, filter);
    const result = {
      tableData: {
        title: "STATISTICAL INDICATORS",
        totalCount: data?.length ? data[0].total_count : 0,
        data: removeTotalCount(data),
      },
    };
    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getComparisonChartTableData() {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getComparisonChartTableData`
  );

  try {
    const data = await getComparisonChartData();
    const comparisonData = {
      scad_production: data[0][0].SCAD_PRODUCTION,
      bayaan_production: data[1][0].BAYAAN_PRODUCTION,
      not_approved: data[2][0].NOT_APPROVED,
      bayaan_domain: data[3],
      not_approved_list: data[4],
      not_approved_table: data[5],
    };
    return comparisonData;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getUseCaseDropDownOptions(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getUseCaseDropDownOptions`
  );

  try {
    let data = await getUseCaseDropDownData();
    const result = [
      {
        name: "Statistical Product",
        options: data[0],
        path: "USECASE_NAME_EN",
      },
      {
        name: "Type",
        options: data[1],
        path: "USECASE_TYPE_EN",
      },
      {
        name: "Status",
        options: data[2],
        path: "STATUS",
      },
      {
        name: "Frequency",
        options: data[3],
        path: "FREQUENCY_NAME_EN",
      },
    ];
    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getDataSoureDropDownOptions(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDataSoureDropDownOptions`
  );

  try {
    let data = await getDataSourceDropDownData();
    return data;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getDataScienceDropDownOptions(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getDataScienceDropDownOptions`
  );

  try {
    let data = await getDataScienceDropDownData();
    return data;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getWorkflowDetailsDropDownOptions(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getWorkflowDetailsDropDownOptions`
  );

  try {
    let data = await getWorkflowDetailsDropDownData();
    return data;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getBayaanSvsDropDownOptions(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getBayaanSvsDropDownOptions`
  );
  let data = await getBayaanSvsDropDownData();
  const result = [
    {
      name: "Stat Value Name or SV ID",
      options: [],
      path: "search",
    },
    {
      name: "Status",
      options: data,
      path: "status",
    }
  ];
  return result;
}

async function getStatisticalIndicatorDropDownOptions(req) {
  log.debug(
    ">>>>> Entered microservice.data-governance.controller.getStatisticalIndicatorDropDownOptions"
  );

  try {
    const data = await getStatisticalIndicatorDropDownData();
    const tree = [];

    const findOrCreateChild = (arr, name) => {
      let node = arr.find((child) => child.name === name);
      if (!node) {
        node = { name, children: [] };
        arr.push(node);
      }
      return node;
    };

    data[0].forEach(
      ({
        TOPIC_NAME_ENGLISH,
        THEME_NAME_ENGLISH,
        SUB_THEME_NAME_ENGLISH,
        PRODUCT_NAME_ENGLISH,
      }) => {
        const topicNode = findOrCreateChild(tree, TOPIC_NAME_ENGLISH);
        const themeNode = findOrCreateChild(
          topicNode.children,
          THEME_NAME_ENGLISH
        );
        const subThemeNode = findOrCreateChild(
          themeNode.children,
          SUB_THEME_NAME_ENGLISH
        );
        // Avoid duplicate product entries
        if (
          !subThemeNode.children.find((p) => p.name === PRODUCT_NAME_ENGLISH)
        ) {
          subThemeNode.children.push({ name: PRODUCT_NAME_ENGLISH });
        }
      }
    );

    const themes = tree.flatMap(item => item.children || []);
    const subThems = themes.flatMap(item => item.children || []);

    const result = {
      domainHirarchy: tree,
      domains: tree,
      themes: themes,
      subthemes: subThems,
      products: data[1],
      status: data[2],
    };

    return result;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getCalenderData(req) {
  log.debug(
    ">>>>>Entered microservice.data-governance.controller.getCalenderData"
  );

  const { startDate, endDate } = req.query;

  try {
    const data = await getSvCalenderData(startDate, endDate);
    if (!data.length) return [];

    const resp = data.map(({ date, indicator, status }) => {
      // Group status counts
      const groupedStatus = Object.entries(
        status.reduce((acc, s) => ((acc[s] = (acc[s] || 0) + 1), acc), {})
      ).map(([name, count]) => ({ name, count }));

      return {
        date,
        name:
          indicator.length > 1
            ? `${indicator[0]} + more ${indicator.length - 1}`
            : indicator[0],
        status: groupedStatus,
        totalCount: groupedStatus.reduce((sum, { count }) => sum + count, 0),
      };
    });

    return resp;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

async function getSvEventDetail(req) {
  log.debug(
    `>>>>>Entered microservice.data-governance.controller.getSvEvenetDetail`
  );

  const offset = req.query.offset ? req.query.offset : 0;
  const limit = req.query.limit ? req.query.limit : 10;
  const filter = req.body;
  try {
    let data = await getEventDetail(limit, offset, filter);
    return {
      totalCount: data[0]?.total_count ?? 0,
      data: removeTotalCount(data),
    };
  } catch (err) {
    log.error(err);
    throw err;
  }
}

module.exports = {
  getDataGovernanceChart,
  getDataSceinceInput,
  getDataSceinceOutput,
  getUseCaseTableData,
  getDataSourceTableData,
  getDatascienceTableData,
  getWorkflowDetailsTableData,
  getBayaanSvsTableData,
  getStatisticalIndicatorTableData,
  getComparisonChartTableData,
  getUseCaseDropDownOptions,
  getDataSoureDropDownOptions,
  getDataScienceDropDownOptions,
  getWorkflowDetailsDropDownOptions,
  getBayaanSvsDropDownOptions,
  getStatisticalIndicatorDropDownOptions,
  getCalenderData,
  getSvEventDetail,
};
