const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json');


async function getWhatNewDataQuery(type) {
  return new Promise((resolve, reject) => {
      try {

          let binds ={

          }
          let whatsNewType = {
            [constants.classifications.officialStatistics]:"MVW_WN_STAT",
            [constants.classifications.innovativeStatistics]:"MVW_WN_EXP"
          }
          let query = `SELECT * FROM ${whatsNewType[type]} ORDER BY OBS_DT DESC`

          return resolve({ query: query, binds: binds });
          
      } catch (err) {
          
          log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeFilterDataQuery with error ${err} `);
          reject([424, err]);
      }
  });
}

async function getLatestDataQuery(nodes){
  return new Promise((resolve, reject) => {
    try {
        let nodeChunks = [];
        const chunkSize = 1000;
        for (let i = 0; i < nodes.length; i += chunkSize) {
            const chunk = nodes.slice(i, i + chunkSize);
            nodeChunks.push(chunk.map(node => `'${node}'`).join(','));
        }

        nodeChunks = nodeChunks.map(nodeChunk => ` INDICATOR_ID IN (${nodeChunk})`).join(' OR')

        let query = `SELECT INDICATOR_ID
        FROM VW_STATISTICAL_INDICATORS
        WHERE ${nodeChunks}
        GROUP BY INDICATOR_ID
        ORDER BY MAX(OBS_DT) DESC
        LIMIT 6;`

        return resolve({ query: query, binds: {} });
        
    } catch (err) {
        
        log.error(`<<<<<< Exited microservice-innovative-insights.services.getQuery.service.getInnovativeFilterDataQuery with error ${err} `);
        reject([424, err]);
    }
});
}
module.exports = { 
  getWhatNewDataQuery, getLatestDataQuery }