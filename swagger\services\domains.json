{"paths": {"/content-type/domains/": {"get": {"tags": ["Domains"], "summary": "Retrieve list of Domains available to the user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the domain."}, "name": {"type": "string", "description": "The name of the domain."}, "icon": {"type": "string", "description": "URL to the dark version of the icon for the domain."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the domain."}}}}}}}}}}, "/content-type/domains/navigation/": {"get": {"tags": ["Domains"], "summary": "Retrieves navigation content consisting of Classifications, Domains and other Hierarchies", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the classification."}, "name": {"type": "string", "description": "The name of the classification."}, "key": {"type": "string", "description": "The key of the classification."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the classification."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the classification."}, "nodeCount": {"type": "integer", "description": "The number of nodes under the classification."}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree for the classification."}, "domains": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the domain."}, "name": {"type": "string", "description": "The name of the domain."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the domain."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the domain."}, "route": {"type": "string", "description": "The route for the domain."}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree for the domain."}, "nodeCount": {"type": "integer", "description": "The number of nodes under the domain."}, "subdomains": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the subdomain."}, "name": {"type": "string", "description": "The name of the subdomain."}, "route": {"type": "string", "description": "The route for the subdomain."}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree for the subdomain."}, "nodeCount": {"type": "integer", "description": "The number of nodes under the subdomain."}, "subthemes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the subtheme."}, "name": {"type": "string", "description": "The name of the subtheme."}, "route": {"type": "string", "description": "The route for the subtheme."}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree for the subtheme."}, "products": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the product."}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree for the product."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the node."}, "title": {"type": "string", "description": "The title of the node."}, "content_type": {"type": "string", "description": "The content type of the node."}, "category": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the category."}, "name": {"type": "string", "description": "The name of the category."}, "key": {"type": "string", "description": "The key of the category."}}}}}}}}}}}}}}}}}}}}}}}}}}}, "/content-type/domains/detail/{id}": {"get": {"tags": ["Domains"], "summary": "Retrieves detailed information of a domain", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "The ID of the Domain"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"domain": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the domain."}, "name": {"type": "string", "description": "The name of the domain."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the domain."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the domain."}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the classification."}, "key": {"type": "string", "description": "The key of the classification."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the classification."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the classification."}, "count": {"type": "integer", "description": "The count of data items."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the node."}, "subthemes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the subtheme."}, "products": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the product."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the node."}, "title": {"type": "string", "description": "The title of the node."}, "content_type": {"type": "string", "description": "The content type of the node."}, "category": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the category."}, "name": {"type": "string", "description": "The name of the category."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the category."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the category."}}}, "app_type": {"type": ["string", "null"], "description": "The type of the app."}, "domain": {"type": "string", "description": "The domain of the node."}, "subdomain": {"type": "string", "description": "The subdomain of the node."}, "subtheme": {"type": "string", "description": "The subtheme of the node."}, "product": {"type": "string", "description": "The product of the node."}, "sort_order": {"type": "integer", "description": "The sort order of the node."}}, "required": ["id", "title", "content_type", "category", "domain", "subdomain", "subtheme", "product", "sort_order"]}}}, "required": ["name", "nodes"]}}, "screener": {"type": "boolean", "description": "Indicates if the subtheme has a screener."}, "screenerConfiguration": {"type": "object", "properties": {"screenerView": {"type": "string", "description": "The view of the screener."}, "screenerIndicator": {"type": "string", "description": "The indicator of the screener."}, "screenerFilterBy": {"type": "object", "properties": {"TOPIC_NAME_ENGLISH": {"type": "array", "items": {"type": "string"}}, "THEME_NAME_ENGLISH": {"type": "array", "items": {"type": "string"}}, "SUB_THEME_NAME_ENGLISH": {"type": "array", "items": {"type": "string"}}}}}}, "count": {"type": ["string", "integer"], "description": "The count of data items."}}, "required": ["name", "products", "screener", "count"]}}, "count": {"type": ["string", "integer"], "description": "The count of data items."}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree."}, "screener": {"type": "boolean", "description": "Indicates if the node has a screener."}}, "required": ["name", "subthemes", "count", "screener", "showTree"]}}, "showTree": {"type": "boolean", "description": "Indicates whether to show the tree."}}, "required": ["name", "key", "light_icon", "dark_icon", "count", "nodes", "showTree"]}}}, "required": ["domain", "data"]}}}}}}}, "/content-type/domains/subdomain/detail/{id}": {"get": {"tags": ["Domains"], "summary": "", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/domains/subdomain/subtheme/detail/{id}": {"get": {"tags": ["Domains"], "summary": "", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/domains/classifications/{id}": {"get": {"tags": ["Domains"], "summary": "Retrieves list of classifications available under a domain", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "The ID of the Domain"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"classification": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the classification."}, "key": {"type": "string", "description": "The key of the classification."}, "name": {"type": "string", "description": "The name of the classification."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the classification."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the classification."}, "count": {"type": ["string", "integer"], "description": "The count of data items."}}, "required": ["id", "key", "name", "light_icon", "dark_icon", "count"]}}, "domain": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the domain."}, "name": {"type": "string", "description": "The name of the domain."}, "dark_icon": {"type": "string", "description": "URL to the dark version of the icon for the domain."}, "light_icon": {"type": "string", "description": "URL to the light version of the icon for the domain."}}, "required": ["id", "name", "dark_icon", "light_icon"]}}, "required": ["classification", "domain"]}}}}}}}, "/content-type/domains/filters/{id}": {"get": {"tags": ["Domains"], "summary": "Retrieves list of filters that can be used for domain detail", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "The ID of the Domain"}, {"name": "classification", "in": "query", "required": true, "schema": {"type": "integer", "enum": [508, 509, 510, 511]}, "description": "The ID of the Classification"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the category."}, "name": {"type": "string", "description": "The name of the category."}, "showScreener": {"type": "boolean", "description": "Indicates whether the screener is shown for this category."}, "subthemes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the subtheme."}, "name": {"type": "string", "description": "The name of the subtheme."}, "showScreener": {"type": "boolean", "description": "Indicates whether the screener is shown for this subtheme."}, "screenerConfiguration": {"type": "object", "properties": {"screenerView": {"type": "string", "description": "The view of the screener."}, "screenerIndicator": {"type": "string", "description": "The indicator for the screener."}, "screenerFilterBy": {"type": "object", "properties": {"TOPIC_NAME_ENGLISH": {"type": "array", "items": {"type": "string"}, "description": "Filter by topic name in English."}, "THEME_NAME_ENGLISH": {"type": "array", "items": {"type": "string"}, "description": "Filter by theme name in English."}, "SUB_THEME_NAME_ENGLISH": {"type": "array", "items": {"type": "string"}, "description": "Filter by subtheme name in English."}}, "description": "Configuration for filtering the screener."}}, "description": "Configuration for the screener."}}, "required": ["id", "name", "showScreener"], "additionalProperties": false}}}, "required": ["id", "name", "showScreener", "subthemes"], "additionalProperties": false}}}}}}}}, "/content-type/domains/detailv2/{id}": {"get": {"tags": ["Domains"], "summary": "Retrieves detailed information of a domain", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "description": "The ID of the domain to retrieve details for.", "type": "integer"}, {"name": "classification", "in": "query", "required": true, "description": "Filters the results based on a specific classification ID.", "type": "integer"}, {"name": "subtheme", "in": "query", "required": false, "description": "Filters the results by a specific subtheme ID.", "type": "integer"}, {"name": "subdomain", "in": "query", "required": false, "description": "Filters the results based on a specific subdomain ID.", "type": "integer"}, {"name": "page", "in": "query", "required": false, "description": "The page number of the results to return.", "type": "integer"}, {"name": "limit", "in": "query", "required": false, "description": "The number of results to return per page.", "type": "integer"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "content_type": {"type": "string"}, "note": {"type": ["string", "null"]}, "category": {"oneOf": [{"type": "array", "minItems": 0}, {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "dark_icon": {"type": "string"}, "light_icon": {"type": "string"}}, "required": ["id", "name", "dark_icon", "light_icon"]}]}}, "required": ["id", "title", "content_type"]}}, "total_count": {"type": "integer"}, "products": {"type": "array", "items": {"type": "object", "properties": {"pid": {"type": "string"}, "count": {"type": "string"}, "title": {"type": "string"}}, "required": ["pid", "count", "title"]}}}, "required": ["results", "total_count", "products"]}}}}}}}}, "components": {"schemas": {}}}