{"paths": {"/content-type/publications/{id}": {"get": {"tags": ["Publications"], "summary": "Retrieve detailed information of a publication", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}, "description": "The ID of the Publication"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the report."}, "title": {"type": "string", "description": "Title of the report."}, "subtitle": {"type": "string", "description": "Subtitle of the report."}, "domains": {"type": "array", "items": {"type": "string"}, "description": "List of domains related to the report."}, "publication_date": {"type": "string", "description": "Publication date of the report in DD/MM/YYYY format.", "pattern": "^\\d{2}/\\d{2}/\\d{4}$"}, "type": {"type": "string", "description": "Type of the report (e.g., Monthly)."}, "search_tags": {"type": "array", "items": {"type": "string"}, "description": "List of search tags associated with the report."}, "domain": {"type": "string", "description": "Primary domain of the report."}, "content_classification": {"type": "string", "description": "Classification of the report content."}, "content_classification_key": {"type": "string", "description": "Key for the content classification."}, "imgSrc": {"type": "string", "format": "uri", "description": "URL to the report's image."}, "excel_attachment": {"type": "string", "format": "uri", "description": "URL to the Excel attachment for the report."}, "publication_attachment": {"type": "string", "format": "uri", "description": "URL to the PDF attachment for the report."}, "attachment": {"type": "string", "description": "URL to an additional attachment, if any."}, "country_flag": {"type": "string", "description": "URL to the country flag image, if applicable."}, "page_icon": {"type": "string", "description": "URL to the page icon."}, "page_light_icon": {"type": "string", "description": "URL to the page's light theme icon."}, "field_page_light_icon": {"type": "string", "description": "Alternative URL to the page's light theme icon."}, "page_menu_icon": {"type": "string", "description": "URL to the page menu icon."}, "page_menu_light_icon": {"type": "string", "description": "URL to the page menu's light theme icon."}, "field_page_menu_light_icon": {"type": "string", "description": "Alternative URL to the page menu's light theme icon."}}, "required": ["id", "title", "publication_date", "type", "domain", "content_classification", "content_classification_key", "imgSrc", "excel_attachment", "publication_attachment"], "description": "Schema for a response detailing a report, including metadata and attachments."}}}}}}}}}