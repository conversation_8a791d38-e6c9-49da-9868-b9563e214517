{"paths": {"/content-type/statistics-insights/": {"get": {"tags": ["Statistics Insights"], "summary": "List all the Statistics Insights available to the user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/statistics-insights/{id}": {"get": {"tags": ["Statistics Insights"], "summary": "Retrieve Detailed Statistics Insight by ID", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Statistics Insight"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string"}, "component_title": {"type": "string"}, "component_subtitle": {"type": "string"}, "domain": {"type": "string"}, "type": {"type": "string"}, "note": {"type": "string"}, "narrative": {"type": "string"}, "attachment": {"type": "string"}, "Indicator": {"type": "string"}, "policy_guide": {"type": "string"}, "enableCompare": {"type": "boolean"}, "compare_data": {"type": "string"}, "insights": {"type": "string"}, "enablePointToggle": {"type": "boolean"}, "showInsights": {"type": "string"}, "content_classification": {"type": "string"}, "content_classification_key": {"type": "string"}, "domain_id": {"type": "integer"}, "tagName": {"type": "string"}, "data_source": {"type": "string"}, "publication_date": {"type": "string"}, "updated": {"type": "string"}, "domain_details": {"type": "object", "properties": {"page_menu_icon": {"type": "string"}, "page_menu_light_icon": {"type": "string"}, "scad_description": {"type": "string"}, "security": {"type": "string"}, "uid": {"type": "string"}, "title": {"type": "string"}, "created": {"type": "string"}}, "required": ["scad_description", "security", "uid", "title", "created"]}, "language": {"type": "string"}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "disabled": {"type": "boolean"}, "label": {"type": "string"}}, "required": ["id", "label"]}}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "value": {"type": ["integer", "null"]}, "unit": {"type": ["string", "null"]}, "isSelected": {"type": "boolean"}}, "required": ["id", "label"]}}}, "required": ["id", "options"]}}, "indicatorDrivers": {"type": "null"}, "indicatorValues": {"type": "object", "properties": {"valuesMeta": {"type": "array", "items": {"type": "object"}}}}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object"}}, "visualizationDefault": {"type": "string"}}}, "isUploadCompareData": {"type": "boolean"}, "tableFields": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "path": {"type": "string"}}, "required": ["label", "path"]}}, "updatedDateFromDB": {"type": "boolean"}}, "required": ["id", "component_title", "domain", "type", "content_classification", "content_classification_key", "domain_id", "tagName", "data_source", "updated", "domain_details", "language", "indicatorTools", "indicatorFilters", "indicatorValues", "indicatorVisualizations", "isUploadCompareData", "tableFields", "updatedDateFromDB"]}}}}}}}, "/content-type/statistics-insights/filter": {"post": {"tags": ["Statistics Insights"], "summary": "Fetch data for Insights Discovery based on the provided filters", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Filter body for Insights Discovery", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightsDiscoveryFilter"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"dbColumn": {"type": "string"}, "dbIndicatorId": {"type": "string"}, "viewName": {"type": "string"}, "filterBy": {"type": "object", "properties": {"YEAR": {"type": "array", "items": {"type": "string"}}, "PROPERTY_TYPE": {"type": "string"}, "DISTRICT_NAME": {"type": "string"}, "NO_OF_ROOMS": {"type": "array", "items": {"type": "string"}}}, "required": ["YEAR", "PROPERTY_TYPE", "DISTRICT_NAME", "NO_OF_ROOMS"]}, "data": {"type": "array", "items": {"type": "object", "properties": {"DISTRICT_NAME": {"type": "string"}, "DISTRICT_NAME_AR": {"type": "string"}, "PROPERTY_TYPE_AR": {"type": "string"}, "NO_OF_ROOMS_AR": {"type": "string"}, "PROPERTY_TYPE": {"type": "string"}, "VALUE": {"type": "number"}, "INDICATOR_ID": {"type": "string"}, "NO_OF_ROOMS": {"type": "string"}, "YEAR": {"type": "string"}, "OBS_DT": {"type": "string", "format": "date"}}, "required": ["DISTRICT_NAME", "DISTRICT_NAME_AR", "PROPERTY_TYPE_AR", "NO_OF_ROOMS_AR", "PROPERTY_TYPE", "VALUE", "INDICATOR_ID", "NO_OF_ROOMS", "YEAR", "OBS_DT"]}}}, "required": ["dbColumn", "dbIndicatorId", "viewName", "filterBy", "data"]}}}}}}}}, "/content-type/statistics-insights/forecasts/list": {"get": {"tags": ["Statistics Insights"], "summary": "Lists available forecasts indicators for the user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"domain": {"type": "string"}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "page_icon": {"type": "string"}, "page_light_icon": {"type": "string"}, "type": {"type": "string"}, "domain": {"type": "string"}, "theme": {"type": "string"}, "subtheme": {"type": "string"}, "product": {"type": "string"}, "content_classification": {"type": "string"}, "content_classification_key": {"type": "string"}, "note": {"type": "string"}, "changed": {"type": "string", "format": "date-time"}}, "required": ["id", "title", "subtitle", "type", "domain", "content_classification", "content_classification_key", "changed"]}}}, "required": ["domain", "nodes"]}}}}}}}}, "/content-type/statistics-insights/overview": {"post": {"tags": ["Statistics Insights"], "summary": "Fetch Overview of Official Indicators by Node IDs", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Overview data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OverviewNodes"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/statistics-insights/visit/{id}": {"post": {"tags": ["Statistics Insights"], "summary": "Track Visit to an Official Indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Official Indicator"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "oneOf": [{"type": "object", "properties": {"message": {"type": "string", "enum": ["Visit tracked successfully"]}, "status": {"type": "string", "enum": ["success"]}}, "required": ["message", "status"], "additionalProperties": false}, {"type": "object", "properties": {"message": {"type": "string", "enum": ["Invalid ID"]}, "status": {"type": "string", "enum": ["failed"]}}, "required": ["message", "status"], "additionalProperties": false}], "description": "Response schema for tracking visits to an official indicator based on the given ID."}}}}}}}, "/content-type/statistics-insights/popular/list": {"get": {"tags": ["Statistics Insights"], "summary": "List Popular Official Indicators", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "subtitle": {"type": "string"}, "page_icon": {"type": "string"}, "page_light_icon": {"type": "string"}, "type": {"type": "string", "enum": ["official_statistics"]}, "domain": {"type": "string"}, "theme": {"type": "string"}, "subtheme": {"type": "string"}, "product": {"type": "string"}, "content_classification": {"type": "string", "enum": ["Official Statistics"]}, "content_classification_key": {"type": "string", "enum": ["official_statistics"]}, "note": {"type": "string"}, "changed": {"type": "string", "format": "date-time"}}, "required": ["id", "title", "type", "domain", "theme", "content_classification", "content_classification_key", "changed"]}, "description": "Response schema for listing popular official indicators."}}}}}}}}, "components": {"schemas": {"OverviewNodes": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "description": "Array of unique identifiers for which to fetch data."}, "type": {"type": "string", "enum": ["official_statistics", "experimental_statistics_screener"], "description": "Type of statistics to fetch. Can be either 'official_statistics' or 'experimental_statistics_screener' based on the ids."}}, "required": ["ids", "type"], "description": "Request payload for fetching specific statistical data based on provided IDs and the type of statistics."}}}}