const controller = require('../infographic-reports.controller');
const service = require('../../services/common-service');
const axios = require('axios');
describe('controller', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    test('should get json response from infographic reports controller - success', async () => {
        try {
            const mockGetResponse = {
                  "data":[{  "title": "Infographic Reports"}]
            };
            const mockPostResponse = {
                headers: {
                    'set-cookie': ['default;default']
                }
            };
            const mockExpectedResponse = [{
                "id": "infographic-reports",
                "items": mockGetResponse.data
            }]
            jest.spyOn(axios, 'get').mockResolvedValue(mockGetResponse);
            jest.spyOn(axios, 'post').mockResolvedValue(mockPostResponse);
            let groupId = ['6dde8c2c-1eff-4581-aa4b-84b2443cae32'];
            jest.spyOn(service,'cleanupCMSData').mockResolvedValue(mockGetResponse);
            result = await controller.getInfographicReports(groupId);
            //expect(JSON.stringify(result)).toEqual(JSON.stringify(mockExpectedResponse));
        } catch (err) {
            //expect(err).toEqual([401,new Error('Mock Error')]);
        }
    })
    test('should get error response from infographic reports controller - failure', async () => {
        try {
            jest.spyOn(axios, 'get').mockRejectedValue(new Error('Mock Error'));
            jest.spyOn(axios, 'post').mockRejectedValue(new Error('Mock Error'));
            let groupId = ['6dde8c2c-1eff-4581-aa4b-84b2443cae32'];
            await controller.getInfographicReports(groupId);
        } catch (err) {
            //expect(err).toEqual([401,new Error('Mock Error')]);
        }

    })
})