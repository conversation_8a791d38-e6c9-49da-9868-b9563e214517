const { getDXPData } = require("./api.service");
const { dxpUrls } = require("../../config/constants.json")

async function getProductDetail(req, productId) {
  const baseUrl = dxpUrls.DATA_PRODUCT_DETAIL;
  const url = `${baseUrl}/${productId}`
  try {
    const product = await getDXPData(req, "GET", url);
    if (product.id == "8NLYpWHhTsuKmlw536pvpA") {
      product.integrationMetadata = {
        data_map: {
          json_config_sf: "p2p5OrUXTOeVCtBuTNdfWw",
          json_config: "2LMYNhtqSqqawtqC5z8yoQ",
          data_table: "9z4ZvQFDTieweYGKsQn92w",
          indicator_map: "9q65eCnlScy9ThqK0UXohQ",
          overview: "hpASEk5GSEC1136zUW6R7A",
        },
        data_share_id: "zFAjtkXGTWWYRpxyrjdBqw",
      };
    }
    return product;
  } catch (error) {
    throw error;
    // return handleAxiosError(error, 'getProductDetail')
  }
}

async function listOrganizations(req) {
  const url = dxpUrls.LIST_ORGANIZATIONS
  try {
    const response = await getDXPData(req, "GET", url);
    return response;
  } catch (error) {
    throw error
  }
}

async function listProducts(req) {
  const url = dxpUrls.DATA_PRODUCT_DETAIL
  try {
    const response = await getDXPData(req, "GET", url);
    return response.data;
  } catch (error) {
    throw error
  }
}

async function listSubscribedProducts(req, userId = "FocfvtVURUmSMWQUFPlCjQ") {
  const url = `${dxpUrls.USER_SUBSCRIPTIONS}/${userId}`
  try {
    const response = await getDXPData(req, "GET", url);
    return response;
  } catch (error) {
    throw error
  }
}

module.exports = {
  getProductDetail,
  listOrganizations,
  listProducts,
  listSubscribedProducts,
};
