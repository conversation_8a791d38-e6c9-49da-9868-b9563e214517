const controller = require('../domains.controller');
const axios = require('axios');
describe('controller', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    test('should get json response from Domains controller - success', async () => {
        const mockGetResponse = {
            "data": [{
                "id": "Domains", "title": "Domains"
            }]
        };
        const expectedResponse = [{
            "id": "Domains", "title": "Domains", "isSelected": false
        }];
        const mockPostResponse = {
            headers: {
                'set-cookie': ['default;default']
            }
        };

        jest.spyOn(axios, 'get').mockResolvedValue(mockGetResponse);
        jest.spyOn(axios, 'post').mockResolvedValue(mockPostResponse);
        let groupId = 0;
        //result = await controller.getDomains(groupId);
        //expect(result).toEqual(expectedResponse);
    })
    test('should get error response from Domains controller - failure', async () => {
        try {
            jest.spyOn(axios, 'get').mockRejectedValue(new Error('Mock Error'));
            jest.spyOn(axios, 'post').mockRejectedValue(new Error('Mock Error'));
            let groupId = 0;
           // await controller.getDomains(groupId);
        } catch (err) {
          //  expect(err).toEqual([401,new Error('Mock Error')]);
        }

    })
})