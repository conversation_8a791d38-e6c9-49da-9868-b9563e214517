const Logger = require("scad-library").logger;
require("dotenv").config();
const constants = require("../config/constants.json");
const FormData = require("form-data");
const axiosInstance = require("axios");
const http = require("http");
const https = require("https");
const { getBayaanGroups } = require("../services/group.service");
const { IFPError } = require("../utils/error");
const { SME_DOMAIN_GROUPS, DEFAULT_GROUPS } = require("./cms.constants.json")
const { updateReportStatus } = require('../microservice-ai-insight-report/services/ai-insight-report.service')

const axios = axiosInstance.create({
  httpAgent: new http.Agent({ keepAlive: true }),
  httpsAgent: new https.Agent({ keepAlive: true }),
});

const log = new Logger().getInstance();

async function createPublications(req) {
  log.debug(
    ">>>>>Entered publications-microservice.publications.controller.createPublications"
  );

  try {
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    const cmsPublicationsUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_PUBLICATIONS_CREATE_URL}`;
    const cmsCredentials = {
      username: "<EMAIL>",
      password: "5Iu88Q5JP07hLP8",
    };

    let accessGroups = [];
    let domainGroups = [];
    let ticketNumber = req.ticketNumber;

    if (req.cleanedBody.publicationType == "consolidated") {
      domainGroups = await getBayaanGroups('classification', { classification: 'CONFIDENTIAL' });
      accessGroups.push(...domainGroups.map(g => g.NAME));
    } else {
      const cleanedDomain = req.cleanedBody.domain.replace(" & ", "").replace(" ", "");
      domainGroups = await getBayaanGroups('domainClassification', { domain: cleanedDomain, classification: 'CONFIDENTIAL' });
    }
    accessGroups.push(...domainGroups.map(g => g.NAME));
    if (!domainGroups.length > 0) {
      throw new IFPError(
        400,
        `Group not found for domain ${req.cleanedBody.domain}`
      );
    }
    accessGroups = [ ...domainGroups.map(g => g.NAME), ...DEFAULT_GROUPS ];
    const requestBody = {
      ...req.cleanedBody,
      content_classification: "reports",
      groups: JSON.stringify(accessGroups),
    };

    let data = new FormData();

    Object.entries(requestBody).forEach(([key, value]) => {
      data.append(key, value);
    });
    
    Object.entries(req.files).forEach(([fieldname, files]) => {
      files.forEach((file) => {
        data.append(`${fieldname}[]`, file.stream, { filename: file.name });
      });
    });
    
    let postResponse = await axios.post(`${cmsLoginUrl}`, {
      name: cmsCredentials.username,
      pass: cmsCredentials.password,
    });
    const cookie = postResponse
    ? postResponse.headers["set-cookie"][0].split(";")[0]
    : "";
    let response = await axios.post(`${cmsPublicationsUrl}`, data, {
      headers: {
        Cookie: cookie,
        "Content-Length": data.getLengthSync(),
        ...data.getHeaders(),
      },
    });

    await updateReportStatus(
      "",
      ticketNumber,
      "approved"
    ); // update report status

    return response.data;
  } catch (err) {
    log.error(`Error creating publications ${err}`);
    throw err;
  }
}

module.exports = { createPublications };
