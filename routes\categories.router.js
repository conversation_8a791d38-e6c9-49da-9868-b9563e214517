const express = require('express');
const router = new express.Router();
const categoriesController = require('../microservice-categories/categories.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
    try {
      const data = await categoriesController.getCategories(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for categories content-type, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
