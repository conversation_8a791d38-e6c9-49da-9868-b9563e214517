{"paths": {"/content-type/innovative-insights/": {"post": {"tags": ["Experimental Screener Indicators"], "summary": "Retrieves list of Experimental Indicators based on the provided filters", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "page", "in": "query", "description": "Page", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "Maximum number of items per page", "required": false, "type": "integer", "format": "int32"}], "requestBody": {"description": "Experimental Filter Data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExperimentalFilterList"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"viewName": {"type": "string", "description": "Name of the view from which to fetch data."}, "filters": {"type": "object", "properties": {"VALUE_TYPE": {"type": "array", "items": {"type": "string", "enum": ["CONTRACT VALUE"], "description": "Filter for the type of values to retrieve."}}, "REGION": {"type": "array", "items": {"type": "string", "enum": ["ALL"], "description": "Filter for the region of interest."}}, "COMPANY_TYPE": {"type": "array", "items": {"type": "string", "enum": ["ALL"], "description": "Filter for the type of companies to include."}}, "INDUSTRY": {"type": "array", "items": {"type": "string", "enum": ["ALL"], "description": "Filter for the industry sector of interest."}}}, "required": ["VALUE_TYPE", "REGION", "COMPANY_TYPE", "INDUSTRY"], "additionalProperties": false, "description": "Filters to apply to the data retrieval."}, "sortBy": {"type": "object", "properties": {"alphabetical": {"type": "string", "enum": ["asc", "desc"], "description": "Determines the sort order based on alphabetical criteria."}}, "required": ["alphabetical"], "additionalProperties": false, "description": "Specifies the sorting criteria and order."}}, "required": ["viewName", "filters", "sortBy"], "additionalProperties": false, "description": "Request body schema for API that fetches data based on specified view name, filters, and sorting criteria."}}}}}}}, "/content-type/innovative-insights/{id}": {"get": {"tags": ["Experimental Screener Indicators"], "summary": "Retrieve Detailed Experimental Indicator by ID", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Experimental Indicator"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string"}, "component_title": {"type": "string"}, "component_subtitle": {"type": "string"}, "domain": {"type": "string"}, "type": {"type": "string"}, "note": {"type": "string"}, "maxPointLimit": {"type": "string"}, "minLimitYAxis": {"type": "string"}, "content_classification": {"type": "string"}, "domain_id": {"type": "string"}, "tagName": {"type": "string"}, "language": {"type": "string"}, "indicatorTools": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "disabled": {"type": "boolean"}, "label": {"type": "string"}}, "required": ["id", "label"]}}, "indicatorFilters": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "options": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "unit": {"type": ["string", "null"]}, "value": {"type": ["integer", "null"]}, "isSelected": {"type": ["boolean", "null"]}}, "required": ["id", "label"]}}}, "required": ["id", "options"]}}, "indicatorDrivers": {"type": "array"}, "indicatorValues": {"type": "object"}, "indicatorVisualizations": {"type": "object", "properties": {"visualizationsMeta": {"type": "array", "items": {"type": "object"}}, "visualizationDefault": {"type": "string"}}, "required": ["visualizationsMeta", "visualizationDefault"]}, "domain_light_icon": {"type": "string", "format": "uri"}, "domain_dark_icon": {"type": "string", "format": "uri"}, "subdomain": {"type": "string"}, "subdomain_id": {"type": "string"}, "viewName": {"type": "string"}, "content_classification_key": {"type": "string"}, "overView": {"type": "object", "properties": {"compareFilters": {"type": "array", "items": {"type": "string"}}, "valueFormat": {"type": "string"}, "templateFormat": {"type": "string"}, "baseDate": {"type": "string", "format": "date"}, "value": {"type": "integer"}, "yearlyCompareValue": {"type": "integer"}, "yearlyChangeValue": {"type": "number"}, "quarterlyCompareValue": {"type": "integer"}, "quarterlyChangeValue": {"type": "number"}, "monthlyCompareValue": {"type": "integer"}, "monthlyChangeValue": {"type": "number"}}, "required": ["compareFilters", "valueFormat", "templateFormat", "baseDate", "value"]}, "data_source": {"type": "string"}, "unit": {"type": "string"}, "publication_date": {"type": "string", "format": "date"}, "tableFields": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "path": {"type": "string"}}, "required": ["label", "path"]}}, "isMultiDimension": {"type": "boolean"}}, "required": ["id", "component_title", "domain", "type", "content_classification", "domain_id", "tagName", "language", "indicatorTools", "indicatorFilters", "indicatorVisualizations", "viewName", "content_classification_key", "<PERSON><PERSON><PERSON><PERSON>", "data_source", "unit", "publication_date", "tableFields"], "additionalProperties": false}}}}}}}, "/content-type/innovative-insights/compare": {"post": {"tags": ["Experimental Screener Indicators"], "summary": "", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/innovative-insights/filters/{id}": {"get": {"tags": ["Experimental Screener Indicators"], "summary": "Retrieve filters for Experimental Screener", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "example": "IFP_IND_VISA", "description": "The ID of Screener"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The display name of the filter category."}, "key": {"type": "string", "description": "The key used for parameterization corresponding to the filter category."}, "default": {"type": "object", "properties": {"name": {"type": "string", "description": "The display name of the default option."}, "value": {"type": "string", "description": "The value of the default option used for parameterization."}}, "required": ["name", "value"], "description": "The default selection for the filter category."}, "items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The display name of the selectable option."}, "value": {"type": "string", "description": "The value of the selectable option used for parameterization."}}, "required": ["name", "value"], "description": "A selectable option within the filter category."}, "description": "The list of all selectable options within the filter category."}}, "required": ["name", "key", "default", "items"], "description": "A filter category with selectable options."}, "description": "Schema for API response detailing filter options for various categories."}}}}}}}}, "components": {"schemas": {"ExperimentalFilterList": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"viewName": {"type": "string", "description": "Name of the view from which to fetch data."}, "filters": {"type": "object", "properties": {"VALUE_TYPE": {"type": "array", "items": {"type": "string", "enum": ["CONTRACT VALUE"], "description": "Filter for the type of values to retrieve."}}, "REGION": {"type": "array", "items": {"type": "string", "enum": ["ALL"], "description": "Filter for the region of interest."}}, "COMPANY_TYPE": {"type": "array", "items": {"type": "string", "enum": ["ALL"], "description": "Filter for the type of companies to include."}}, "INDUSTRY": {"type": "array", "items": {"type": "string", "enum": ["ALL"], "description": "Filter for the industry sector of interest."}}}, "required": ["VALUE_TYPE", "REGION", "COMPANY_TYPE", "INDUSTRY"], "additionalProperties": false, "description": "Filters to apply to the data retrieval."}, "sortBy": {"type": "object", "properties": {"alphabetical": {"type": "string", "enum": ["asc", "desc"], "description": "Determines the sort order based on alphabetical criteria."}}, "required": ["alphabetical"], "additionalProperties": false, "description": "Specifies the sorting criteria and order."}}, "required": ["viewName", "filters", "sortBy"], "additionalProperties": false, "description": "Request body schema for API that fetches data based on specified view name, filters, and sorting criteria."}}}}