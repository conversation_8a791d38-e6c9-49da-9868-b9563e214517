const express = require('express');
const router = new express.Router();

const { acceptTermsAndConditions, getTermsAndConditions, updateLangPreferrence,getTermsAndConditionsContent } = require('../microservice-terms-and-conditions/terms-and-conditions.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { validateTCAccept } = require('./validators/terms-and-conditions.validator');

router.get('/', async (req, res, next) => {
  try {
    const data = await getTermsAndConditionsContent(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error accepting terms-and-conditions content-type, ERROR: ${err}`);
    next(err);
  }
});

router.post('/accept', validateTCAccept, async (req, res, next) => {
    try {
      const data = await acceptTermsAndConditions(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error accepting terms-and-conditions content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/check/:userId/:organization/:tcVersion', async (req, res, next) => {
    try {
      const data = await getTermsAndConditions(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error checking terms-and-conditions content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/update-lang-preferrence', async (req, res, next) => {
    try {
      const data = await updateLangPreferrence(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error updating language preferrence content-type, ERROR: ${err}`);
      next(err);
    }
  });

module.exports = router;
