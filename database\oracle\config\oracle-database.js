const { Sequelize } = require('sequelize');

const oracleConnection = new Sequelize({
  dialect: 'oracle',
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.HOST,
  port: process.env.ORACLE_PORT || 1521,
  dialectOptions: {
    connectString: process.env.DB_CONNECTIONSTRING,
    // If you need to set Oracle specific options
    // options: {}
  },
  define: {
    // Global model options
    freezeTableName: true,
    timestamps: false
  },
  // Disable sequelize logging in production
  logging: process.env.NODE_ENV === 'dev' ? console.log : false,
});

module.exports = oracleConnection;