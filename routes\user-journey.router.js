const express = require('express');
const router = new express.Router();
const userJourneyController = require('../microservice-user-journey/user-journey.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/status', async (req, res, next) => {
  try {
    const data = await userJourneyController.getUserJourney(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

router.post('/attend', async (req, res, next) => {
  try {
    const data = await userJourneyController.attendUserJourney(req);
    res.set('Content-Type', 'application/json');
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
    next(err);
  }
});

module.exports = router;