const db = require('../../services/database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { uploadCompareDataQuery, postMasterDataQuery, getDataExistQuery, deleteExistingDataQuery, deleteExistingDataMasterQuery } = require('./getQuery.service');

/**
 * Function to get user accepted terms and conditions flag from db values provided in userData
 * @param {*} userData - user details object 
 */
async function postUploadCompareData(values) {
  return new Promise((resolve, reject) => {
    uploadCompareDataQuery().then((query) => {
      db.simpleExecute(query, values)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.getAcceptedUserData with error ${err}`);
      reject(err);
    })
  })
}

async function postMasterData(inputData) {
  return new Promise((resolve, reject) => {
    postMasterDataQuery(inputData).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.getAcceptedUserData with error ${err}`);
      reject(err);
    })
  })
}

async function checkDataExists(obj) {
  return new Promise((resolve, reject) => {
    getDataExistQuery(obj).then((query) => {
      db.simpleExecute(query)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData successfully`);
          if (data.length > 0) {
            resolve(true);
          } else {
            resolve(false);
          }
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.getAcceptedUserData with error ${err}`);
      reject(err);
    })
  })
}

async function deleteExistingData(obj) {
  let query1 = await deleteExistingDataQuery(obj);
  let query2 = await deleteExistingDataMasterQuery(obj);
  let query = [query1, query2];
  let response = [];
  return new Promise((resolve, reject) => {
    query.forEach(q => {
      db.simpleExecute(q)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData successfully`);
          if (data.rowsAffected > 0) {
            response.push(data.rowsAffected);
            if (query.length === response.length) {
              resolve(true);
            }
          } else {
            reject([423, `Unable to execute query ${q}`])
          }
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData with error ${err}`);
          log.error(`Error Executing Query:- ${query}`);
          reject([423, err]);
        })
    });
  })
}

module.exports = { postUploadCompareData, postMasterData, checkDataExists, deleteExistingData }