const { resolve } = require('path');
const db = require('../../services/database.service');
const Logger = require('scad-library').logger;
const moment = require('moment')
const log = new Logger().getInstance();

const { checkSurveyAttendQuery, attendIndividualSurveyQuery,checkUserTCAcceptQuery } = require('./getQuery.service');

/**
 * Function to check user has attended survey or not
 * @param {*} userId - user email
 * @param {*} surveyId - survey id 
 * @param {*} launch_date - survey launch date 
 */
async function checkSurveyAttend(userId, surveyId, launch_date) {
  return new Promise((resolve, reject) => {
    checkSurveyAttendQuery(userId, surveyId, launch_date).then((query) => {
      getData(query).then(async (data) => {
        resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
      reject(err);
    })
  })
}

async function checkUserTCAccept(userId) {
  return new Promise((resolve, reject) => {
    checkUserTCAcceptQuery(userId).then((query) => {
      getData(query).then(async (data) => {
        resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to check user has attended survey or not
 * @param {*} userId - user email
 * @param {*} surveyId - survey id 
 * @param {*} launch_date - survey launch date 
 */
async function attendSurveyQueries(userId,user_name,survey) {
  return new Promise((resolve, reject) => {
    let survey_queries = survey.queries
    let survey_overall_rating = survey.overall_rating
    let survey_feedback = survey.feedback
    let survey_uid =  `${survey.id}${moment(survey.launch_date, 'DD/MM/YYYY').format("X")}`
    survey_queries.forEach(survey_query => {
      attendIndividualSurveyQuery(userId, survey, 'RATING', survey_query.query_en, survey_query.query_ar, survey_query.response,survey_query.question_feedback,survey_uid,user_name).then((query) => {
        getData(query).then(async (data) => {
          log.debug("Recorded rating response");
        }).catch((err) => {
          
          log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
          reject(err);
        })
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
        reject(err);
      })
    })

    attendIndividualSurveyQuery(userId, survey, 'OVERALL_RATING', 'OVERALL_RATING', 'OVERALL_RATING', survey_overall_rating,'',survey_uid,user_name).then((query) => {
      getData(query).then(async (data) => {
        log.debug("Recorded overall rating response");
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
      reject(err);
    })

    attendIndividualSurveyQuery(userId, survey, 'FEEDBACK', 'FEEDBACK', 'FEEDBACK', survey_feedback,'',survey_uid,user_name).then((query) => {
      getData(query).then(async (data) => {
        log.debug("Recorded feedback response");
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-survey.executeQuery.service.checkSurveyAttend with error ${err}`);
      reject(err);
    })

    resolve(true)
  });
}




async function getData(query) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-survey.services.executeQuery.service.getData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-survey.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-survey.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = { checkSurveyAttend, attendSurveyQueries, checkUserTCAccept, getData };
