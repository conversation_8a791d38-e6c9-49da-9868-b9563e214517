{"id": "GDP01", "type": "gdp-planning-simulator", "component_title": "محاكاة تخطيط الناتج المحلي الإجمالي", "component_subtitle": "محاكي تخطيط الناتج المحلي الإجمالي السنوي. يستفيد هذا التطبيق التحليلي من تحليل الانحدار لتقدير النمو المستهدف في محركات الاقتصاد بناءً على النمو المستهدف للناتج المحلي الإجمالي. تمثل القيم الموجودة في السلسلة النسبة المئوية للتغيير عن العام السابق.", "application_url": "", "attachment": "", "note": "", "domains": ["Economy"], "search_tags": ["Constant Prices"], "narrative": "", "policy_guide": "", "indicator_list": "", "indicatorValues_subtitle": "", "indicatorValues_title": "", "visualization_subtitle": "محاكي تخطيط الناتج المحلي الإجمالي السنوي. يستخدم هذا التطبيق التحليلي تحليل الانحدار لتقدير النمو المستهدف في محركات الاقتصاد بناءً على النمو المستهدف للناتج المحلي الإجمالي.", "visualization_title": "كيفية التحليل: محاكي تخطيط الناتج المحلي الإجمالي السنوي (ثابت)", "enableDynamicPanel": "", "highlightsMeta": "", "infogramUrl": "", "listofDyanmicPanelContent": [], "language": "ar", "indicatorTools": [{"id": "export-png", "disabled": true, "label": "تصدير PNG"}, {"id": "export-csv", "label": "تصدير CSV"}, {"id": "download-pdf", "label": "تحميل PDF"}], "indicatorValues": {"valuesMeta": [{"id": "year-to-date-value", "title": "الإنفاق الحكومي", "gdpGrossTarget": "default", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-gdp-spend.svg", "values": [{"value": 0, "type": "ABSOLUTE"}, {"value": null, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "أبوظبي المشاريع الممنوحة", "gdpGrossTarget": "default", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-land.svg", "values": [{"value": 23.89, "type": "ABSOLUTE"}, {"value": null, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "PMI", "gdpGrossTarget": "default", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-employee-compensation.svg", "values": [{"value": 0, "type": "ABSOLUTE"}, {"value": null, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "الإنفاق الحكومي", "gdpGrossTarget": "3", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-gdp-spend.svg", "values": [{"value": 4.45, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "أبوظبي المشاريع الممنوحة", "gdpGrossTarget": "3", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-land.svg", "values": [{"value": 7.39, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "PMI", "gdpGrossTarget": "3", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-employee-compensation.svg", "values": [{"value": 4.52, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "الإنفاق الحكومي", "gdpGrossTarget": "5", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-gdp-spend.svg", "values": [{"value": 5.85, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "أبوظبي المشاريع الممنوحة", "gdpGrossTarget": "5", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-land.svg", "values": [{"value": 9.4, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "PMI", "gdpGrossTarget": "5", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-employee-compensation.svg", "values": [{"value": 7.09, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "الإنفاق الحكومي", "gdpGrossTarget": "7", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-gdp-spend.svg", "values": [{"value": 7.46, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "أبوظبي المشاريع الممنوحة", "gdpGrossTarget": "7", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-land.svg", "values": [{"value": 12, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}, {"id": "year-to-date-value", "title": "PMI", "gdpGrossTarget": "7", "viewName": "VW_CONSUMER_DOMESTIC_SPEND", "iconId": "assets/icons/icon-employee-compensation.svg", "values": [{"value": 10.63, "type": "ABSOLUTE"}, {"value": 25, "type": "PERCENTAGE_CHANGE"}]}]}, "indicatorVisualizations": {"visualizationsMeta": [{"id": "line-chart-population-indicator", "type": "line-chart", "viewName": "VW_PP_PROJECTION_FEB2021", "isScadProjection": "true", "accuracyMetrics": {"title": "*معدل الدقة: {ACCURACY_VALUE} اعتبارًا من {OBS_DT}", "viewName": "DS_ACCURACY_METRICS", "dbColumn": "NODE_ID", "dateFormat": "MMMM YYYY", "data": {"NODE_ID": 368, "ACCURACY_VALUE": 97.45, "OBS_DT": "June 2021", "UNIT": "Percent", "INSERT_DT": "20210920", "INSERT_USER_ID": "ETL_USER", "VALUE_TYPE": "PERCENTAGE_CHANGE"}}, "seriesMeta": [{"id": "government-expenditure", "label": "الإنفاق الحكومي", "color": "#3E8FFF", "type": "solid", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": 2.86, "OBS_DT": "2020-01-01"}, {"VALUE": -10.92, "OBS_DT": "2021-01-01"}, {"VALUE": -2.01, "OBS_DT": "2022-01-01"}], "yMax": 2.86, "yMin": -10.92, "xMin": "2020-01-01", "xMax": "2022-01-01"}, {"id": "abudhabi-awarded-projects", "label": "أبوظبي المشاريع الممنوحة", "color": "#2FCCEC", "type": "solid", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": 26.9, "OBS_DT": "2020-01-01"}, {"VALUE": -49.86, "OBS_DT": "2021-01-01"}, {"VALUE": 99.9, "OBS_DT": "2022-01-01"}], "yMax": 99.9, "yMin": -49.86, "xMin": "2020-01-01", "xMax": "2022-01-01"}, {"id": "pmi", "label": "PMI", "color": "#983EFF", "type": "solid", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -5.43, "OBS_DT": "2020-01-01"}, {"VALUE": 3.37, "OBS_DT": "2021-01-01"}, {"VALUE": -6.51, "OBS_DT": "2022-01-01"}], "yMax": 3.37, "yMin": -6.51, "xMin": "2020-01-01", "xMax": "2022-01-01"}, {"id": "government-expenditure-forecast", "gdpGrossTarget": "default", "label": "الإنفاق الحكومي", "color": "#3E8FFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -2.01, "OBS_DT": "2022-01-01"}, {"VALUE": 0, "OBS_DT": "2023-01-01"}], "yMax": 0, "yMin": -2.01, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "government-expenditure-forecast", "gdpGrossTarget": "3", "label": "الإنفاق الحكومي", "color": "#3E8FFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -2.01, "OBS_DT": "2022-01-01"}, {"VALUE": 4.45, "OBS_DT": "2023-01-01"}], "yMax": 4.45, "yMin": -2.01, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "government-expenditure-forecast", "gdpGrossTarget": "5", "label": "الإنفاق الحكومي", "color": "#3E8FFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -2.01, "OBS_DT": "2022-01-01"}, {"VALUE": 5.85, "OBS_DT": "2023-01-01"}], "yMax": 5.85, "yMin": -2.01, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "government-expenditure-forecast", "gdpGrossTarget": "7", "label": "الإنفاق الحكومي", "color": "#3E8FFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -2.01, "OBS_DT": "2022-01-01"}, {"VALUE": 7.46, "OBS_DT": "2023-01-01"}], "yMax": 7.46, "yMin": -2.01, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "abudhabi-awarded-projects-forecast", "gdpGrossTarget": "default", "label": "أبوظبي المشاريع الممنوحة", "color": "#2FCCEC", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": 99.9, "OBS_DT": "2022-01-01"}, {"VALUE": 23.89, "OBS_DT": "2023-01-01"}], "yMax": 99.9, "yMin": 23.89, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "abudhabi-awarded-projects-forecast", "gdpGrossTarget": "3", "label": "أبوظبي المشاريع الممنوحة", "color": "#2FCCEC", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": 99.9, "OBS_DT": "2022-01-01"}, {"VALUE": 7.39, "OBS_DT": "2023-01-01"}], "yMax": 99.9, "yMin": 7.39, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "abudhabi-awarded-projects-forecast", "gdpGrossTarget": "5", "label": "أبوظبي المشاريع الممنوحة", "color": "#2FCCEC", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": 99.9, "OBS_DT": "2022-01-01"}, {"VALUE": 9.4, "OBS_DT": "2023-01-01"}], "yMax": 99.9, "yMin": 9.4, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "abudhabi-awarded-projects-forecast", "gdpGrossTarget": "7", "label": "أبوظبي المشاريع الممنوحة", "color": "#2FCCEC", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": 99.9, "OBS_DT": "2022-01-01"}, {"VALUE": 12, "OBS_DT": "2023-01-01"}], "yMax": 99.9, "yMin": 12, "xMin": "2021-01-01", "xMax": "2023-01-01"}, {"id": "pmi-forecast", "gdpGrossTarget": "default", "label": "PMI", "color": "#983EFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -6.51, "OBS_DT": "2022-01-01"}, {"VALUE": 0, "OBS_DT": "2023-01-01"}], "yMax": 0, "yMin": -6.51, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "pmi-forecast", "gdpGrossTarget": "3", "label": "PMI", "color": "#983EFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -6.51, "OBS_DT": "2022-01-01"}, {"VALUE": 4.52, "OBS_DT": "2023-01-01"}], "yMax": 4.52, "yMin": -6.51, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "pmi-forecast", "gdpGrossTarget": "5", "label": "PMI", "color": "#983EFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -6.51, "OBS_DT": "2022-01-01"}, {"VALUE": 7.09, "OBS_DT": "2023-01-01"}], "yMax": 7.09, "yMin": -6.51, "xMin": "2022-01-01", "xMax": "2023-01-01"}, {"id": "pmi-forecast", "gdpGrossTarget": "7", "label": "PMI", "color": "#983EFF", "type": "forecast", "xAccessor": {"type": "date", "path": "OBS_DT", "specifier": "%Y-%m-%d"}, "yAccessor": {"type": "value", "path": "VALUE"}, "data": [{"VALUE": -6.51, "OBS_DT": "2022-01-01"}, {"VALUE": 10.63, "OBS_DT": "2023-01-01"}], "yMax": 10.63, "yMin": -6.51, "xMin": "2022-01-01", "xMax": "2023-01-01"}], "markersMeta": [{"id": "population-projection_real-vs-forecast", "color": "#ffffff", "type": "line-with-label", "labelText": "Forecast", "axis": "x", "accessor": {"type": "date", "path": "DATE", "specifier": "%Y-%m-%d"}, "data": {"DATE": "2022-01-01"}}], "xAxisLabel": null, "yAxisLabel": null, "showLegend": false, "yAxisExtraStepMin": 0.2, "yAxisExtraStepMax": 0.05, "xAxisFormat": "date_y", "yAxisFormat": "d3-number", "showPointLabels": true, "tooltipTitleFormat": "date_y", "tooltipValueFormat": "number_1.0-0"}], "visualizationDefault": "line-chart-population-indicator", "visualizationGraphType": "population-projection"}, "indicatorDomains": ["population"], "indicatorType": "analytical-apps", "country_flag": "", "isFavorite": "false"}