const db = require("../../services/database.service");
const clkdb = require("../../services/clk-database.service");
const {
	getLivabilityDataQuery,
	getLivabilityCategoryQuery,
	getLivabilityCompositionQuery,
	getLivabilityCompositionDescriptionQuery,
	getLivabilityMapMetaQuery,
} = require("./getQuery.service");
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const clkTables = require("../../config/constants.json").clickhouseTables;

/**
 * Fetches dashboard data based on the provided parameters.
 * @param {string} lang - The language for which data is fetched.
 * @param {string} parentCategory - The parent category for filtering data.
 * @param {string} category - The category for filtering data.
 * @param {number} ismap - Indicator whether to fetch map data.
 * @returns {Promise<Object>} - A Promise resolving to the fetched data.
 * @throws {Error} - Throws an error if fetching data fails.
 */
async function getLivabilityData(lang, parentCategory, category, ismap, grouped=false) {
    try {
        // Fetch data query for the given language, category, and ismap
        const results = await getLivabilityDataQuery(lang, parentCategory, category, ismap);

		 // Check if the query contains table in Clickhouse
        const containsClView = clkTables.some((view) => results.query.includes(view));

        let data;
		// Execute the database query using the retrieved query and bindings
        if (containsClView) {
            data = await clkdb.simpleExecute(results.query, results.binds);
        } else {
            data = await db.simpleExecute(results.query, results.binds);
        }

		if (!grouped) {
			let responseData = data.map((item) => ({
				title: item.ITEM,
				value: item.VALUE,
				count: item.COUNT,
				region: item.REGION,
				parentCategory: item.PARENT_CATEGORY,
				category: item.CATEGORY,
				isActive: item.IS_ACTIVE,
			}));
			return responseData;
		}

		// Loop through the data array
		const responseData = data.reduce((acc, item) => {
			// Check if the GROUP_NAME exists as a key in the responseData object
			if (!acc[item.GROUP_NAME]) {
				// If the key doesn't exist, initialize it as an empty array
				acc[item.GROUP_NAME] = [];
			}
			// Push the current item to the array corresponding to its GROUP_NAME
			acc[item.GROUP_NAME].push({
				title: item.ITEM,
				value: item.VALUE,
				count: item.COUNT,
				region: item.REGION,
				parentCategory: item.PARENT_CATEGORY,
				category: item.CATEGORY,
				isActive: item.IS_ACTIVE,
			});
			return acc;
		}, {});

		const finalResponseData = Object.entries(responseData).map(
			([key, value]) => ({
				title: key,
				data: value,
			})
		);
        return finalResponseData;
    } catch (err) {
        log.error(`<<<<< Error in microservice-dashboard.services.getLivabilityData: ${err}`);
        throw err;
    }
}

/**
 * Fetches dashboard categories based on the provided language and parent label.
 * @param {string} lang - The language for which categories are fetched.
 * @param {string} parentLabel - The parent label for filtering categories.
 * @returns {Promise<Object>} - A Promise resolving to the fetched categories.
 * @throws {Error} - Throws an error if fetching categories fails.
 */
async function getLivabilityCategory(lang, parentLabel = null) {
	try {
		// Fetch data query for the given language
		const results = await getLivabilityCategoryQuery(lang, parentLabel);

		const containsClView = clkTables.some((view) =>
			results.query.includes(view)
		);

		let data;
		if (containsClView) {
			data = await clkdb.simpleExecute(results.query, results.binds);
		} else {
			data = await db.simpleExecute(results.query, results.binds);
		}

		let responseData = data.map((item) => ({
			category: item.CATEGORY,
			value: item.VALUE,
			count: item.COUNT,
			isActive: item.IS_ACTIVE,
		}));
		return responseData;
	} catch (err) {
		log.error(
			`<<<<< Error in microservice-dashboard.services.getLivabilityCategory: ${err}`
		);
		throw err;
	}
}

/**
 * Fetches Livability Composition based on the provided language and parent label.
 * @param {string} lang - The language for which data is fetched.
 * @param {string} parentCategory - The parent category for filtering data.
 * @param {string} category - The category for filtering data.
 * @returns {Promise<Object>} - A Promise resolving to the fetched categories.
 * @throws {Error} - Throws an error if fetching categories fails.
 */
async function getLivabilityComposition(lang, parentCategory, category) {
	try {
		// Fetch data query for the given language
		const results = await getLivabilityCompositionQuery(
			lang,
			parentCategory,
			category
		);

		const containsClView = clkTables.some((view) =>
			results.query.includes(view)
		);

		const [data, descriptionData] = await Promise.all([
			containsClView
				? clkdb.simpleExecute(results.query, results.binds)
				: db.simpleExecute(results.query, results.binds),
			getLivabilityCompositionDescription(lang, category),
		]);

		let responseData = data.map((item) => ({
			category: item.CATEGORY,
			description: item.COMPOSITION_DESCRIPTION,
			value: item.VALUE,
			count: item.COUNT,
			inLiveabilityIndex: item.IN_LIVEABILITY_INDEX,
			activePercent: item.ACTIVE_PERCENT,
			isActive: item.IS_ACTIVE,
		}));

		responseData = {
			description: descriptionData.description,
			isActive: descriptionData.isActive,
			data: responseData,
		};

		return responseData;
	} catch (err) {
		log.error(
			`<<<<< Error in microservice-dashboard.services.getLivabilityComposition: ${err}`
		);
		throw err;
	}
}

/**
 * Fetches Livability Composition description based on the provided language and category.
 * @param {string} lang - The language for which data is fetched.
 * @param {string} category - The category for filtering data.
 * @returns {Promise<Object>} - A Promise resolving to the fetched categories.
 * @throws {Error} - Throws an error if fetching categories fails.
 */
async function getLivabilityCompositionDescription(lang, category) {
	try {
		// Fetch data query for the given language
		const results = await getLivabilityCompositionDescriptionQuery(
			lang,
			category
		);

		const containsClView = clkTables.some((view) =>
			results.query.includes(view)
		);

		let data;
		if (containsClView) {
			data = await clkdb.simpleExecute(results.query, results.binds);
		} else {
			data = await db.simpleExecute(results.query, results.binds);
		}

		if (data.length == 1) {
			data = data[0];
			let responseData = {
				description: data.COMPOSITION_DESCRIPTION,
				isActive: data.IS_ACTIVE,
			};
			return responseData;
		}
		return data;
	} catch (err) {
		log.error(
			`<<<<< Error in microservice-dashboard.services.getLivabilityCompositionDescription: ${err}`
		);
		throw err;
	}
}
/**
 * Fetches Livability Composition description based on the provided language and category.
 * @param {string} lang - The language for which data is fetched.
 * @param {string} category - The category for filtering data.
 * @returns {Promise<Object>} - A Promise resolving to the fetched categories.
 * @throws {Error} - Throws an error if fetching categories fails.
 */
async function getLivabilityMapMeta(lang, category) {
	try {
		// Fetch data query for the given language
		const results = await getLivabilityMapMetaQuery(
			lang,
			category
		);

		const containsClView = clkTables.some((view) =>
			results.query.includes(view)
		);

		let data;
		if (containsClView) {
			data = await clkdb.simpleExecute(results.query, results.binds);
		} else {
			data = await db.simpleExecute(results.query, results.binds);
		}

		if (data.length == 1) {
			data = data[0];
			let responseData = {
				title: data.TITLE,
				subTitle: data.SUBTITLE,
				isActive: data.IS_ACTIVE,
			};
			return responseData;
		}
		return data;
	} catch (err) {
		log.error(
			`<<<<< Error in microservice-dashboard.services.getLivabilityMapMeta: ${err}`
		);
		throw err;
	}
}

module.exports = {
	getLivabilityData,
	getLivabilityCategory,
	getLivabilityComposition,
	getLivabilityCompositionDescription,
	getLivabilityMapMeta,
};