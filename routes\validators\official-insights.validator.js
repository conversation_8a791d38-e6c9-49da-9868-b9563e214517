const { getViewNameById, getTableColumns } = require("../../services/executeQuery.service");
const { IFPError } = require("../../utils/error");
const constants = require("../../config/constants.json")

const validateOfficialInsights = (req, res, next) => {
    if (req.params.id)
        if (isNaN(req.params.id))
            throw new IFPError(400,`${req.params.id} is not a valid indicator ID`)
    next()
  };

  const validateOfficialInsightsList = async (req, res, next) => {
    try{

        if (req.query.page)
            if (Number(req.query.page)){
                if (Number(req.query.page)<0)
                    throw new IFPError(400,`${req.query.page} is not a valid page number`)
            }
            else
                throw new IFPError(400,`${req.query.page} is not a valid page number`)

        if (req.query.limit){
            if (Number(req.query.limit)){
                if (Number(req.query.limit)<1){
                    throw new IFPError(400,`${req.query.limit} is not a valid limit number`)
                }
            }
            else
                throw new IFPError(400,`${req.query.limit} is not a valid limit number`)
        }
        
        let viewName = req.body.viewName
        if (viewName){
            if (Number(viewName)){
                viewName = await getViewNameById(viewName)
                if(!viewName)
                    throw new IFPError(400,'Invalid screener id')
            }   
        }
        else{
            throw new IFPError(400,'Invalid screener view')
        }

        let columns = await getTableColumns(viewName)
        columns = columns.map(c=>c.NAME)

        if (columns.length<1)
            throw new IFPError(400,'Invalid screener view')

        if (!Object.keys(constants.censusViewMap).includes(viewName))
        
            if(!columns.includes('SORT_ORDER'))
                throw new IFPError(400,'Invalid screener view')

        let filters = req.body.filters
        if (Object.keys(filters).length){
            let validateFilters =  Object.keys(filters).every(filter=>columns.includes(filter))
            if (!validateFilters)
                throw new IFPError(400,'Invalid filters')
        }

        if(req.body.sortBy){
            const allowedSortKeys = ['alphabetical', 'byValue'];
            const keys = Object.keys(req.body.sortBy);

            for (const key of keys) {
                if (!allowedSortKeys.includes(key)) {
                throw new IFPError(400,`Invalid key found: ${key}. Only 'alphabetical' and 'byValue' is allowed.`);
                }
            }
        }

        next()
    }
    catch(exp){
        next(exp)
    }
  };


module.exports = {
    validateOfficialInsights,
    validateOfficialInsightsList
}