'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.renameColumn('ai_insight_report', 'createdAt', 'created_at',);
    await queryInterface.renameColumn('ai_insight_report', 'updatedAt', 'updated_at',);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.renameColumn('ai_insight_report', 'created_at', 'createdAt');
    await queryInterface.renameColumn('ai_insight_report', 'updated_at', 'updatedAt');
  }
};
