const db = require('./database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { sendSLAEmail } = require('./sendEmail.service')
const { getSLAExpiryData,setSLAEmailStatus } = require('./executeQuery.service')
const moment = require('moment')

async function slaStatusCheck() {
    let userRecs = await getSLAExpiryData()
    let dateToday = moment()
    userRecs.forEach( rec => {
        var end_date = moment(rec.END_DATE, 'DD-MMM-YY')
        let diffDate = end_date.diff(dateToday, 'days')
        if (diffDate<=32 && diffDate>=1){
            var data = {}
            data['email'] = rec.EMAIL
            data['name'] = rec.NAME
            data['rem_days'] = diffDate
            data['date'] = moment(end_date).format('MMMM DD, YYYY');
            sendSLAEmail(data)
            setSLAEmailStatus(rec.EMAIL,1)
        }

    })
}

module.exports = {slaStatusCheck}