const { Sequelize, DataTypes, Model } = require("sequelize");
const ApprovalRequest = require("./approval-request.model");
const ApprovalRequestComment = require("./approval-request-comment.model");

/**
 * Model for tracking the history of status changes and assignee changes 
 * for approval requests in the Bayaan approval system.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const ApprovalRequestHistory = sequelize.define(
    "ApprovalRequestHistory",
    {
      id: {
        type: DataTypes.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      request_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      previous_status: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      new_status: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },
      previous_assignee: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      new_assignee: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      changed_by: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      changed_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      comment_id: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      tableName: "bayaan_approval_request_history",
      timestamps: false,
    }
  );
  
  ApprovalRequestHistory.belongsTo(ApprovalRequest(sequelize, DataTypes), {
    foreignKey: 'request_id',
    targetKey: 'id',
    as: 'request',
  });
  
  ApprovalRequestHistory.belongsTo(ApprovalRequestComment(sequelize, DataTypes), {
    foreignKey: 'comment_id',
    targetKey: 'id',
    as: 'comment',
  });
  
  return ApprovalRequestHistory;
}

module.exports = model;