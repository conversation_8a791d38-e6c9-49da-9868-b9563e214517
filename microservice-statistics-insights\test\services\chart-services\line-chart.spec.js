const lineChart = require("../../../services/chart-services/line-chart");

describe('Service', () => {

    afterEach(() => {
        jest.restoreAllMocks();
    });

    test('should get json response from processLineChartData scad without dimension - success', async () => {
        const mockData = [
            {
                "INDICATOR_ID": "33555",
                "INDICATOR_TITLE": "Age Dependency Ratio",
                "INDICATOR_UNIT": "Per cent",
                "OBS_DT": "2019-01-01",
                "VALUE": 25.326944351196296,
                "AREA_KEY": null,
                "SECTOR": null,
                "GENDER": null,
                "AGE_GROUP_NAME": null,
                "MARITAL_STATUS_NAME": null,
                "CITIZENSHIP": null,
                "EDUCATION": null,
                "INDUSTRY": null,
                "COUNTRY": null,
                "IPI_PPI_ACTIVITY": null,
                "ECO_ACT_2DIG": null,
                "GROUPS_OF_COMODITIES_SERVIS": null,
                "COICOP_CODE": null,
                "AIRCRAFT_AIRPORT_NAME": null,
                "OCCUPATION_NAME": null,
                "ECONOMY_SECTOR_NAME": null,
                "CHANGE_WORK_REASON": null,
                "INSERT_USER_ID": "ETL_USER",
                "INSERT_DT": "20210221"
            },
            {
                "INDICATOR_ID": "33555",
                "INDICATOR_TITLE": "Age Dependency Ratio",
                "INDICATOR_UNIT": null,
                "OBS_DT": "2019-02-01",
                "VALUE": 24.326944351196296,
                "AREA_KEY": null,
                "SECTOR": null,
                "GENDER": null,
                "AGE_GROUP_NAME": null,
                "MARITAL_STATUS_NAME": null,
                "CITIZENSHIP": null,
                "EDUCATION": null,
                "INDUSTRY": null,
                "COUNTRY": null,
                "IPI_PPI_ACTIVITY": null,
                "ECO_ACT_2DIG": null,
                "GROUPS_OF_COMODITIES_SERVIS": null,
                "COICOP_CODE": null,
                "AIRCRAFT_AIRPORT_NAME": null,
                "OCCUPATION_NAME": null,
                "ECONOMY_SECTOR_NAME": null,
                "CHANGE_WORK_REASON": null,
                "INSERT_USER_ID": "ETL_USER",
                "INSERT_DT": "20210221"
            }];

        const mockVisualization = {
            "id": "line-chart-age-dependency-ratio",
            "type": "line-chart",
            "dimension": false,
            "dbColumn": "INDICATOR_ID",
            "seriesMeta": [{
                "id": "age-dependency-ratio",
                "label": null,
                "color": "#3267FF",
                "type": "solid",
                "dbIndicatorId": "33555",
                "xAccessor": {
                    "type": "date",
                    "path": "OBS_DT",
                    "specifier": "%Y-%m-%d"
                },
                "yAccessor": {
                    "type": "value",
                    "path": "VALUE"
                },
                "data": []
            }],
            "markersMeta": [],
            "xAxisLabel": null,
            "yAxisLabel": null,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-percentage",
            "pointLabelsFormat": "percentage_1.1-1",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "d3-percentage_1.1-1"
        };

        const expectedResponse = {
            "id": "line-chart-age-dependency-ratio",
            "type": "line-chart",
            "dimension": false,
            "dbColumn": "INDICATOR_ID",
            "seriesMeta": [
                {
                    "id": "age-dependency-ratio",
                    "label": null,
                    "color": "#3267FF",
                    "type": "solid",
                    "dbIndicatorId": "33555",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [{
                        "INDICATOR_ID": "33555",
                        "INDICATOR_TITLE": "Age Dependency Ratio",
                        "INDICATOR_UNIT": "Per cent",
                        "OBS_DT": "2019-01-01",
                        "VALUE": 25.326944351196296,
                        "AREA_KEY": null,
                        "SECTOR": null,
                        "GENDER": null,
                        "AGE_GROUP_NAME": null,
                        "MARITAL_STATUS_NAME": null,
                        "CITIZENSHIP": null,
                        "EDUCATION": null,
                        "INDUSTRY": null,
                        "COUNTRY": null,
                        "IPI_PPI_ACTIVITY": null,
                        "ECO_ACT_2DIG": null,
                        "GROUPS_OF_COMODITIES_SERVIS": null,
                        "COICOP_CODE": null,
                        "AIRCRAFT_AIRPORT_NAME": null,
                        "OCCUPATION_NAME": null,
                        "ECONOMY_SECTOR_NAME": null,
                        "CHANGE_WORK_REASON": null,
                        "INSERT_USER_ID": "ETL_USER",
                        "INSERT_DT": "20210221"
                    },
                    {
                        "INDICATOR_ID": "33555",
                        "INDICATOR_TITLE": "Age Dependency Ratio",
                        "INDICATOR_UNIT": null,
                        "OBS_DT": "2019-02-01",
                        "VALUE": 24.326944351196296,
                        "AREA_KEY": null,
                        "SECTOR": null,
                        "GENDER": null,
                        "AGE_GROUP_NAME": null,
                        "MARITAL_STATUS_NAME": null,
                        "CITIZENSHIP": null,
                        "EDUCATION": null,
                        "INDUSTRY": null,
                        "COUNTRY": null,
                        "IPI_PPI_ACTIVITY": null,
                        "ECO_ACT_2DIG": null,
                        "GROUPS_OF_COMODITIES_SERVIS": null,
                        "COICOP_CODE": null,
                        "AIRCRAFT_AIRPORT_NAME": null,
                        "OCCUPATION_NAME": null,
                        "ECONOMY_SECTOR_NAME": null,
                        "CHANGE_WORK_REASON": null,
                        "INSERT_USER_ID": "ETL_USER",
                        "INSERT_DT": "20210221"
                    }],
                    "yMax": 25.326944351196296,
                    "yMin": 24.326944351196296,
                    "xMin": "2019-01-01",
                    "xMax": "2019-02-01"
                }
            ],
            "markersMeta": [

            ],
            "xAxisLabel": null,
            "yAxisLabel": null,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-percentage",
            "pointLabelsFormat": "percentage_1.1-1",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "d3-percentage_1.1-1"
        }
        jest.spyOn(lineChart, "processLineChartData");
        const result = await lineChart.processLineChartData(mockData, mockVisualization, 'scad');
        //expect(result).toEqual(expectedResponse);
    });

    test('should get json response from processLineChartData scad with dimension - success', async () => {
        const mockData = [
            {
                "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                "INDICATOR_TITLE": "Unemployment Rate by gender",
                "INDICATOR_UNIT": "Per cent",
                "OBS_DT": "2018-01-01",
                "VALUE": 2.7999999523162797,
                "AREA_KEY": null,
                "SECTOR": null,
                "GENDER": "Male",
                "AGE_GROUP_NAME": null,
                "MARITAL_STATUS_NAME": null,
                "CITIZENSHIP": null,
                "EDUCATION": null,
                "INDUSTRY": null,
                "COUNTRY": null,
                "IPI_PPI_ACTIVITY": null,
                "ECO_ACT_2DIG": null,
                "GROUPS_OF_COMODITIES_SERVIS": null,
                "COICOP_CODE": null,
                "AIRCRAFT_AIRPORT_NAME": null,
                "OCCUPATION_NAME": null,
                "ECONOMY_SECTOR_NAME": null,
                "CHANGE_WORK_REASON": null,
                "INSERT_USER_ID": "ETL_USER_DEV",
                "INSERT_DT": "20200724"
            },
            {
                "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                "INDICATOR_TITLE": "Unemployment Rate by gender",
                "INDICATOR_UNIT": "Per cent",
                "OBS_DT": "2019-01-01",
                "VALUE": 4.19999980926514,
                "AREA_KEY": null,
                "SECTOR": null,
                "GENDER": "Male",
                "AGE_GROUP_NAME": null,
                "MARITAL_STATUS_NAME": null,
                "CITIZENSHIP": null,
                "EDUCATION": null,
                "INDUSTRY": null,
                "COUNTRY": null,
                "IPI_PPI_ACTIVITY": null,
                "ECO_ACT_2DIG": null,
                "GROUPS_OF_COMODITIES_SERVIS": null,
                "COICOP_CODE": null,
                "AIRCRAFT_AIRPORT_NAME": null,
                "OCCUPATION_NAME": null,
                "ECONOMY_SECTOR_NAME": null,
                "CHANGE_WORK_REASON": null,
                "INSERT_USER_ID": "ETL_USER_DEV",
                "INSERT_DT": "20200724"
            },
            {
                "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                "INDICATOR_TITLE": "Unemployment Rate by gender",
                "INDICATOR_UNIT": "Per cent",
                "OBS_DT": "2018-01-01",
                "VALUE": 12.1000003814697,
                "AREA_KEY": null,
                "SECTOR": null,
                "GENDER": "Female",
                "AGE_GROUP_NAME": null,
                "MARITAL_STATUS_NAME": null,
                "CITIZENSHIP": null,
                "EDUCATION": null,
                "INDUSTRY": null,
                "COUNTRY": null,
                "IPI_PPI_ACTIVITY": null,
                "ECO_ACT_2DIG": null,
                "GROUPS_OF_COMODITIES_SERVIS": null,
                "COICOP_CODE": null,
                "AIRCRAFT_AIRPORT_NAME": null,
                "OCCUPATION_NAME": null,
                "ECONOMY_SECTOR_NAME": null,
                "CHANGE_WORK_REASON": null,
                "INSERT_USER_ID": "ETL_USER_DEV",
                "INSERT_DT": "20200724"
            },
            {
                "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                "INDICATOR_TITLE": "Unemployment Rate by gender",
                "INDICATOR_UNIT": "Per cent",
                "OBS_DT": "2019-01-01",
                "VALUE": 14.800000190734899,
                "AREA_KEY": null,
                "SECTOR": null,
                "GENDER": "Female",
                "AGE_GROUP_NAME": null,
                "MARITAL_STATUS_NAME": null,
                "CITIZENSHIP": null,
                "EDUCATION": null,
                "INDUSTRY": null,
                "COUNTRY": null,
                "IPI_PPI_ACTIVITY": null,
                "ECO_ACT_2DIG": null,
                "GROUPS_OF_COMODITIES_SERVIS": null,
                "COICOP_CODE": null,
                "AIRCRAFT_AIRPORT_NAME": null,
                "OCCUPATION_NAME": null,
                "ECONOMY_SECTOR_NAME": null,
                "CHANGE_WORK_REASON": null,
                "INSERT_USER_ID": "ETL_USER_DEV",
                "INSERT_DT": "20200724"
            }
        ];

        const mockVisualization = {
            "id": "line-chart-unemployment-rate",
            "type": "line-chart",
            "dimension": true,
            "dimensionColumn": "GENDER",
            "dbIndicatorId": "COI_UNEMPRATE_GEN",
            "dbColumn": "INDICATOR_ID",
            "seriesMeta": [
                {
                    "id": "female",
                    "label": "Female",
                    "color": "#26A6BF",
                    "type": "solid",
                    "dimensionValue": "FEMALE",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "male",
                    "label": "Male",
                    "color": "#3267FF",
                    "type": "solid",
                    "dimensionValue": "MALE",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                }
            ],
            "markersMeta": [],
            "showLegend": true,
            "showPointLabels": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisFormat": "d3-percentage",
            "xAxisFormat": "date_y",
            "pointLabelsFormat": "d3-percentage_1.1-1",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "d3-percentage_1.1-1"
        };

        const expectedResponse = {
            "id": "line-chart-unemployment-rate",
            "type": "line-chart",
            "dimension": true,
            "dimensionColumn": "GENDER",
            "dbIndicatorId": "COI_UNEMPRATE_GEN",
            "dbColumn": "INDICATOR_ID",
            "seriesMeta": [
                {
                    "id": "female",
                    "label": "Female",
                    "color": "#26A6BF",
                    "type": "solid",
                    "dimensionValue": "FEMALE",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [
                        {
                            "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                            "INDICATOR_TITLE": "Unemployment Rate by gender",
                            "INDICATOR_UNIT": "Per cent",
                            "OBS_DT": "2018-01-01",
                            "VALUE": 12.1000003814697,
                            "AREA_KEY": null,
                            "SECTOR": null,
                            "GENDER": "Female",
                            "AGE_GROUP_NAME": null,
                            "MARITAL_STATUS_NAME": null,
                            "CITIZENSHIP": null,
                            "EDUCATION": null,
                            "INDUSTRY": null,
                            "COUNTRY": null,
                            "IPI_PPI_ACTIVITY": null,
                            "ECO_ACT_2DIG": null,
                            "GROUPS_OF_COMODITIES_SERVIS": null,
                            "COICOP_CODE": null,
                            "AIRCRAFT_AIRPORT_NAME": null,
                            "OCCUPATION_NAME": null,
                            "ECONOMY_SECTOR_NAME": null,
                            "CHANGE_WORK_REASON": null,
                            "INSERT_USER_ID": "ETL_USER_DEV",
                            "INSERT_DT": "20200724"
                        },
                        {
                            "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                            "INDICATOR_TITLE": "Unemployment Rate by gender",
                            "INDICATOR_UNIT": "Per cent",
                            "OBS_DT": "2019-01-01",
                            "VALUE": 14.800000190734899,
                            "AREA_KEY": null,
                            "SECTOR": null,
                            "GENDER": "Female",
                            "AGE_GROUP_NAME": null,
                            "MARITAL_STATUS_NAME": null,
                            "CITIZENSHIP": null,
                            "EDUCATION": null,
                            "INDUSTRY": null,
                            "COUNTRY": null,
                            "IPI_PPI_ACTIVITY": null,
                            "ECO_ACT_2DIG": null,
                            "GROUPS_OF_COMODITIES_SERVIS": null,
                            "COICOP_CODE": null,
                            "AIRCRAFT_AIRPORT_NAME": null,
                            "OCCUPATION_NAME": null,
                            "ECONOMY_SECTOR_NAME": null,
                            "CHANGE_WORK_REASON": null,
                            "INSERT_USER_ID": "ETL_USER_DEV",
                            "INSERT_DT": "20200724"
                        }
                    ],
                    "yMax": 14.800000190734899,
                    "yMin": 12.1000003814697,
                    "xMin": "2018-01-01",
                    "xMax": "2019-01-01"
                },
                {
                    "id": "male",
                    "label": "Male",
                    "color": "#3267FF",
                    "type": "solid",
                    "dimensionValue": "MALE",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [
                        {
                            "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                            "INDICATOR_TITLE": "Unemployment Rate by gender",
                            "INDICATOR_UNIT": "Per cent",
                            "OBS_DT": "2018-01-01",
                            "VALUE": 2.7999999523162797,
                            "AREA_KEY": null,
                            "SECTOR": null,
                            "GENDER": "Male",
                            "AGE_GROUP_NAME": null,
                            "MARITAL_STATUS_NAME": null,
                            "CITIZENSHIP": null,
                            "EDUCATION": null,
                            "INDUSTRY": null,
                            "COUNTRY": null,
                            "IPI_PPI_ACTIVITY": null,
                            "ECO_ACT_2DIG": null,
                            "GROUPS_OF_COMODITIES_SERVIS": null,
                            "COICOP_CODE": null,
                            "AIRCRAFT_AIRPORT_NAME": null,
                            "OCCUPATION_NAME": null,
                            "ECONOMY_SECTOR_NAME": null,
                            "CHANGE_WORK_REASON": null,
                            "INSERT_USER_ID": "ETL_USER_DEV",
                            "INSERT_DT": "20200724"
                        },
                        {
                            "INDICATOR_ID": "COI_UNEMPRATE_GEN",
                            "INDICATOR_TITLE": "Unemployment Rate by gender",
                            "INDICATOR_UNIT": "Per cent",
                            "OBS_DT": "2019-01-01",
                            "VALUE": 4.19999980926514,
                            "AREA_KEY": null,
                            "SECTOR": null,
                            "GENDER": "Male",
                            "AGE_GROUP_NAME": null,
                            "MARITAL_STATUS_NAME": null,
                            "CITIZENSHIP": null,
                            "EDUCATION": null,
                            "INDUSTRY": null,
                            "COUNTRY": null,
                            "IPI_PPI_ACTIVITY": null,
                            "ECO_ACT_2DIG": null,
                            "GROUPS_OF_COMODITIES_SERVIS": null,
                            "COICOP_CODE": null,
                            "AIRCRAFT_AIRPORT_NAME": null,
                            "OCCUPATION_NAME": null,
                            "ECONOMY_SECTOR_NAME": null,
                            "CHANGE_WORK_REASON": null,
                            "INSERT_USER_ID": "ETL_USER_DEV",
                            "INSERT_DT": "20200724"
                        }
                    ],
                    "yMax": 4.19999980926514,
                    "yMin": 2.7999999523162797,
                    "xMin": "2018-01-01",
                    "xMax": "2019-01-01"
                }
            ],
            "markersMeta": [

            ],
            "showLegend": true,
            "showPointLabels": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisFormat": "d3-percentage",
            "xAxisFormat": "date_y",
            "pointLabelsFormat": "d3-percentage_1.1-1",
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "d3-percentage_1.1-1"
        }

        jest.spyOn(lineChart, "processLineChartData");
        const result = await lineChart.processLineChartData(mockData, mockVisualization, 'scad');
        //expect(result).toEqual(expectedResponse);
    });

    test('should get json response from processLineChartData coi with dimension- success', async () => {
        const mockData = [
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 99.0124740808924,
                "VALUE_LL": 92.51010475047562,
                "VALUE_UL": 114.999887622827,
                "OBS_DT": "2021-01-01",
                "TYPE": "NOWCAST",
                "SECTOR": "TotalGDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 99.23251256077225,
                "VALUE_LL": 92.7156928141189,
                "VALUE_UL": 115.255455425711,
                "OBS_DT": "2021-02-01",
                "TYPE": "NOWCAST",
                "SECTOR": "TotalGDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 102.80608077533401,
                "VALUE_LL": 91.4974118900476,
                "VALUE_UL": 124.395357738155,
                "OBS_DT": "2022-01-01",
                "TYPE": "FORECAST",
                "SECTOR": "TotalGDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 102.959168374016,
                "VALUE_LL": 91.63365985287453,
                "VALUE_UL": 124.58059373255999,
                "OBS_DT": "2022-02-01",
                "TYPE": "FORECAST",
                "SECTOR": "TotalGDP"
            }
        ];

        const mockVisualization = {
            "id": "line-chart-economy-indicator",
            "type": "line-chart",
            "dimension": true,
            "dimensionColumn": "TYPE",
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "PARAMETER_COMBO_ID": "E0000",
            "filterBy": {
                "SECTOR": "TOTAL GDP"
            },
            "seriesMeta": [
                {
                    "id": "economic-index",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "solid",
                    "dimensionValue": "NOWCAST",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "economic-index-forecast",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "forecast-with-arrow",
                    "dimensionValue": "FORECAST",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                }
            ],
            "markersMeta": [
                {
                    "id": "economic-index_real-vs-forecast",
                    "color": "#ffffff",
                    "type": "line-with-label",
                    "labelText": "Forecast",
                    "axis": "x",
                    "accessor": {
                        "type": "date",
                        "path": "DATE",
                        "specifier": "%Y-%m-%d"
                    }
                }
            ],
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.005,
            "yAxisExtraStepMax": 0.005,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMMy",
            "tooltipValueFormat": "number_1.1-1"
        }

        const expectedResponse = {
            "id": "line-chart-economy-indicator",
            "type": "line-chart",
            "dimension": true,
            "dimensionColumn": "TYPE",
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "PARAMETER_COMBO_ID": "E0000",
            "filterBy": {
                "SECTOR": "TOTAL GDP"
            },
            "seriesMeta": [
                {
                    "id": "economic-index",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "solid",
                    "dimensionValue": "NOWCAST",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": null,
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 99.0124740808924,
                            "VALUE_LL": 92.51010475047562,
                            "VALUE_UL": 114.999887622827,
                            "OBS_DT": "2021-01-01",
                            "TYPE": "NOWCAST",
                            "SECTOR": "TotalGDP"
                        },
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": null,
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 99.23251256077225,
                            "VALUE_LL": 92.7156928141189,
                            "VALUE_UL": 115.255455425711,
                            "OBS_DT": "2021-02-01",
                            "TYPE": "NOWCAST",
                            "SECTOR": "TotalGDP"
                        }
                    ],
                    "yMax": 99.23251256077225,
                    "yMin": 99.0124740808924,
                    "xMin": "2021-01-01",
                    "xMax": "2021-02-01"
                },
                {
                    "id": "economic-index-forecast",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "forecast-with-arrow",
                    "dimensionValue": "FORECAST",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": null,
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 99.23251256077225,
                            "VALUE_LL": 92.7156928141189,
                            "VALUE_UL": 115.255455425711,
                            "OBS_DT": "2021-02-01",
                            "TYPE": "NOWCAST",
                            "SECTOR": "TotalGDP"
                        },
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 102.80608077533401,
                            "VALUE_LL": 91.4974118900476,
                            "VALUE_UL": 124.395357738155,
                            "OBS_DT": "2022-01-01",
                            "TYPE": "FORECAST",
                            "SECTOR": "TotalGDP"
                        },
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 102.959168374016,
                            "VALUE_LL": 91.63365985287453,
                            "VALUE_UL": 124.58059373255999,
                            "OBS_DT": "2022-02-01",
                            "TYPE": "FORECAST",
                            "SECTOR": "TotalGDP"
                        }
                    ],
                    "yMax": 102.959168374016,
                    "yMin": 102.80608077533401,
                    "xMin": "2022-01-01",
                    "xMax": "2022-02-01"
                }
            ],
            "markersMeta": [
                {
                    "id": "economic-index_real-vs-forecast",
                    "color": "#ffffff",
                    "type": "line-with-label",
                    "labelText": "Forecast",
                    "axis": "x",
                    "accessor": {
                        "type": "date",
                        "path": "DATE",
                        "specifier": "%Y-%m-%d"
                    },
                    "data": {
                        "DATE": "2021-02-01"
                    }
                }
            ],
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.005,
            "yAxisExtraStepMax": 0.005,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMMy",
            "tooltipValueFormat": "number_1.1-1"
        }

        jest.spyOn(lineChart, "processLineChartData");
        const result = await lineChart.processLineChartData(mockData, mockVisualization, 'coi');
       // expect(result).toEqual(expectedResponse);
    });

    test('should get json response from processLineChartData coi without dimension- success', async () => {
        const mockData = [
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 99.0124740808924,
                "VALUE_LL": 92.51010475047562,
                "VALUE_UL": 114.999887622827,
                "OBS_DT": "2021-01-01",
                "TYPE": "NOWCAST",
                "SECTOR": "TotalGDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": null,
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 99.23251256077225,
                "VALUE_LL": 92.7156928141189,
                "VALUE_UL": 115.255455425711,
                "OBS_DT": "2021-02-01",
                "TYPE": "NOWCAST",
                "SECTOR": "TotalGDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 102.80608077533401,
                "VALUE_LL": 91.4974118900476,
                "VALUE_UL": 124.395357738155,
                "OBS_DT": "2022-01-01",
                "TYPE": "FORECAST",
                "SECTOR": "TotalGDP"
            },
            {
                "INDICATOR_ID": "RAPID",
                "PARAMETER_COMBO_ID": "E0000",
                "RUN_SEQ_ID": 13,
                "RUN_DT": "20210401",
                "INSERT_DT": "20210401",
                "INSERT_USER_ID": "ETL_USER",
                "LAST_UPD_DT": "20210401",
                "LAST_UPD_USER_ID": "ETL_USER",
                "LANGUAGE_CD": "EN",
                "VALUE": 102.959168374016,
                "VALUE_LL": 91.63365985287453,
                "VALUE_UL": 124.58059373255999,
                "OBS_DT": "2022-02-01",
                "TYPE": "FORECAST",
                "SECTOR": "TotalGDP"
            }
        ];

        const mockVisualization = {
            "id": "line-chart-economy-indicator",
            "type": "line-chart",
            "dimension": false,
            "dbColumn": "TYPE",
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "PARAMETER_COMBO_ID": "E0000",
            "filterBy": {
                "SECTOR": "TOTAL GDP"
            },
            "seriesMeta": [
                {
                    "id": "economic-index",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "solid",
                    "dimension": {
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                },
                {
                    "id": "economic-index-forecast",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    }
                }
            ],
            "markersMeta": [
                {
                    "id": "economic-index_real-vs-forecast",
                    "color": "#ffffff",
                    "type": "line-with-label",
                    "labelText": "Forecast",
                    "axis": "x",
                    "accessor": {
                        "type": "date",
                        "path": "DATE",
                        "specifier": "%Y-%m-%d"
                    }
                }
            ],
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.005,
            "yAxisExtraStepMax": 0.005,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMMy",
            "tooltipValueFormat": "number_1.1-1"
        }

        const expectedResponse = {
            "id": "line-chart-economy-indicator",
            "type": "line-chart",
            "dimension": false,
            "dbColumn": "TYPE",
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "PARAMETER_COMBO_ID": "E0000",
            "filterBy": {
                "SECTOR": "TOTAL GDP"
            },
            "seriesMeta": [
                {
                    "id": "economic-index",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "solid",
                    "dimension": {
                        "TYPE": "NOWCAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": null,
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 99.0124740808924,
                            "VALUE_LL": 92.51010475047562,
                            "VALUE_UL": 114.999887622827,
                            "OBS_DT": "2021-01-01",
                            "TYPE": "NOWCAST",
                            "SECTOR": "TotalGDP"
                        },
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": null,
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 99.23251256077225,
                            "VALUE_LL": 92.7156928141189,
                            "VALUE_UL": 115.255455425711,
                            "OBS_DT": "2021-02-01",
                            "TYPE": "NOWCAST",
                            "SECTOR": "TotalGDP"
                        }
                    ],
                    "yMax": 99.23251256077225,
                    "yMin": 99.0124740808924,
                    "xMin": "2021-01-01",
                    "xMax": "2021-02-01"
                },
                {
                    "id": "economic-index-forecast",
                    "label": "Economic activity index",
                    "color": "#3667ff",
                    "type": "forecast-with-arrow",
                    "dimension": {
                        "TYPE": "FORECAST"
                    },
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "data": [
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": null,
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 99.23251256077225,
                            "VALUE_LL": 92.7156928141189,
                            "VALUE_UL": 115.255455425711,
                            "OBS_DT": "2021-02-01",
                            "TYPE": "NOWCAST",
                            "SECTOR": "TotalGDP"
                        },
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 102.80608077533401,
                            "VALUE_LL": 91.4974118900476,
                            "VALUE_UL": 124.395357738155,
                            "OBS_DT": "2022-01-01",
                            "TYPE": "FORECAST",
                            "SECTOR": "TotalGDP"
                        },
                        {
                            "INDICATOR_ID": "RAPID",
                            "PARAMETER_COMBO_ID": "E0000",
                            "RUN_SEQ_ID": 13,
                            "RUN_DT": "20210401",
                            "INSERT_DT": "20210401",
                            "INSERT_USER_ID": "ETL_USER",
                            "LAST_UPD_DT": "20210401",
                            "LAST_UPD_USER_ID": "ETL_USER",
                            "LANGUAGE_CD": "EN",
                            "VALUE": 102.959168374016,
                            "VALUE_LL": 91.63365985287453,
                            "VALUE_UL": 124.58059373255999,
                            "OBS_DT": "2022-02-01",
                            "TYPE": "FORECAST",
                            "SECTOR": "TotalGDP"
                        }
                    ],
                    "yMax": 102.959168374016,
                    "yMin": 102.80608077533401,
                    "xMin": "2022-01-01",
                    "xMax": "2022-02-01"
                }
            ],
            "markersMeta": [
                {
                    "id": "economic-index_real-vs-forecast",
                    "color": "#ffffff",
                    "type": "line-with-label",
                    "labelText": "Forecast",
                    "axis": "x",
                    "accessor": {
                        "type": "date",
                        "path": "DATE",
                        "specifier": "%Y-%m-%d"
                    },
                    "data": {
                        "DATE": "2021-02-01"
                    }
                }
            ],
            "showInterval": false,
            "xAxisLabel": null,
            "yAxisLabel": null,
            "yAxisExtraStepMin": 0.005,
            "yAxisExtraStepMax": 0.005,
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "tooltipTitleFormat": "date_MMMy",
            "tooltipValueFormat": "number_1.1-1"
        }

        jest.spyOn(lineChart, "processLineChartData");
        const result = await lineChart.processLineChartData(mockData, mockVisualization, 'coi');
        //expect(result).toEqual(expectedResponse);
    });
});



