const express = require('express');
const router = new express.Router();
const statisticsInsightsController = require('../microservice-statistics-insights/statistics-insights.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { validateOverview, validateVisit } = require('./validators/statistics-insights.validator');

router.get('/', async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getStatisticsInsights(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for statistics-insights content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/filter', async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getStatisticsInsightsWithFilter(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for statistics-insights content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/forecasts/list', async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getForecasts(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/:id', async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getStatisticsInsightsById(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/overview',validateOverview, async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getOfficialStatisticsOverview(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/visit/:id',validateVisit, async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().visitOfficialStatistics(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/popular/list', async (req, res, next) => {
    try {
      const data = await new statisticsInsightsController().getPopularOfficialStatistics(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for statistics-insights by id content-type, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
