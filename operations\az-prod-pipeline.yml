trigger: none

variables:
  agentCachePath: '$(Agent.BuildDirectory)/.npm/ifpv2/be'
  app_name: 'scad-insights-be'
  userCache: 'false'
  registry: '$(OpenShift.Registry)/azure-devops'
  ouser: '$(OpenShift.Registry.Username)'
  opassword: '$(OpenShift.SA.Token)'
  openshift_url: '$(OpenShift.API.Server)'
  old_registry: '$(OpenShift.PROD.Registry)/azure-devops'
  old_ouser: '$(OpenShift.PROD.Registry.Username)'
  old_opassword: '$(OpenShift.PROD.SA.Token)'
  old_openshift_url: '$(OpenShift.PROD.API.Server)'

jobs:
- job: Init
  pool: 'IFP'
  steps:
  - script: |
        id
        version_info=$(podman run --privileged --rm -v "$(pwd):/repo" docker.io/gittools/gitversion:6.0.0 /repo)
        semver=$(echo $version_info | jq -r '.MajorMinorPatch')
        prerelease=$(echo $version_info | jq -r '.PreReleaseLabel')
        metadata=$(echo $version_info | jq -r '.BuildMetaData')
        version="${semver}-${prerelease}.${metadata}"
        echo "build number is $version"
        echo "##vso[task.setvariable variable=version;isOutput=true]$version"
        echo "##vso[build.updatebuildnumber]$version"
    name: setvarStep
    displayName: 'Generating version'
    failOnStderr: "true"



- job: DockerBuild
  pool: 'IFP'
  dependsOn: Init
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'

  - script: |
      if [ -d "$(agentCachePath)/node_modules" ]; then
        cp -r "$(agentCachePath)/node_modules" .
      fi
    condition: and(succeeded(), ne(variables['userCache'], 'false'))
    displayName: 'checking npm cache'

  - task: Npm@1
    inputs:
      command: 'install'
      verbose: false
      customRegistry: 'useFeed'
      customFeed: '4ac6f6d3-9411-4182-b105-79854fc1587c'
    displayName: 'npm install'
  - script: |
      mkdir -p "$(agentCachePath)/node_modules" && cp -r node_modules "$(agentCachePath)/node_modules"
    condition: and(succeeded(), ne(variables['userCache'], 'false'))
    displayName: 'setting npm cache'

  - script: |
      tar -czvf node_modules.tar.gz node_modules
    displayName: 'TAR node_modules'

  - script: |
      podman login --tls-verify=false $(registry) --username $(ouser) --password $(opassword)
    failOnStderr: "true"
    displayName: 'login to podman registry'

  - script: |
      podman build -t $(registry)/$(app_name):$(version) -f operations/dockerfiles/dockerfile . \
      --build-arg NPM_FEED_USERNAME=$(NPM_FEED_USERNAME) \
      --build-arg NPM_FEED_opassword=$(NPM_FEED_opassword)
      podman push --tls-verify=false $(registry)/$(app_name):$(version)
    displayName: 'Building and pushing image to podman registry $(registry)/$(app_name):$(version)'

  - script: |
      podman login --tls-verify=false $(old_registry) --username $(old_ouser) --password $(old_opassword)
    failOnStderr: "true"
    displayName: 'login to old podman registry'

  - script: |
      podman tag $(registry)/$(app_name):$(version) $(old_registry)/$(app_name):$(version)
      podman push --tls-verify=false $(old_registry)/$(app_name):$(version)
      podman untag $(registry)/$(app_name):$(version) $(old_registry)/$(app_name):$(version)
    failOnStderr: "false"
    displayName: 'Tagging and pushing image to old podman registry'

  - script: |
      podman rmi $(registry)/$(app_name):$(version)
    failOnStderr: "true"
    displayName: 'Cleaning old images'

- job: Deploy
  pool: 'IFP'
  dependsOn: [DockerBuild,Init]
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - script: |
      echo -e "\033[1;34mLogging in to OpenShift...\033[0m"
      oc login --token $(old_opassword) --insecure-skip-tls-verify=true $(old_openshift_url)
    displayName: 'Login to OpenShift'
    failOnStderr: "true"
  - script: |
      echo -e "\033[1;34mUpdating deployment...\033[0m"
      oc set image deployment/$(app_name) $(app_name)=image-registry.openshift-image-registry.svc:5000/azure-devops/$(app_name):$(version) -n app-ifpv2-prod
      oc rollout status deployment/$(app_name) -n app-ifpv2-prod
    displayName: 'Update Deployment'
    failOnStderr: "true"

  - script: |
      echo -e "\033[1;34mHealth Check...\033[0m"
      oc get pods -l app=$(app_name) -n app-ifpv2-prod
    displayName: 'Health Check'
    failOnStderr: "true"
  - checkout: self
    persistCredentials: true
    clean: true
  - script: |
          git tag -a $(version) $SHORT_LATEST_COMMIT_ID -m "Updated Application Version tag $(version) and Deployed to Production"
          git push -u origin HEAD:$(Build.SourceBranchName) --tags
