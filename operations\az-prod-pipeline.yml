trigger: none

variables:
  app_name: 'scad-insights-be'
  registry: '$(OpenShift.Registry)/azure-devops'
  ouser: '$(OpenShift.Registry.Username)'
  opassword: '$(OpenShift.SA.Token)'
  openshift_url: '$(OpenShift.API.Server)'
  application_labels: "ifp,be"
jobs:
- job: Init
  pool: 'IFP'
  steps:
  - script: |
        id
        version_info=$(podman run --privileged --rm -v "$(pwd):/repo" docker.io/gittools/gitversion:6.0.0 /repo)
        semver=$(echo $version_info | jq -r '.MajorMinorPatch')
        prerelease=$(echo $version_info | jq -r '.PreReleaseLabel')
        metadata=$(echo $version_info | jq -r '.BuildMetaData')
        version="${semver}-${prerelease}.${metadata}"
        echo "build number is $version"
        echo "##vso[task.setvariable variable=version;isOutput=true]$version"
        echo "##vso[build.updatebuildnumber]$version"
    name: setvarStep
    displayName: 'Generating version'
    failOnStderr: "true"



- job: DockerBuild
  pool: 'IFP'
  dependsOn: Init
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'

  - task: Npm@1
    inputs:
      command: 'install'
      verbose: false
      customRegistry: 'useFeed'
      customFeed: '4ac6f6d3-9411-4182-b105-79854fc1587c'
    displayName: 'npm install'

  - script: |
      tar -czvf node_modules.tar.gz node_modules
    displayName: 'TAR node_modules'

  - script: |
      podman login --tls-verify=false $(registry) --username $(ouser) --password $(opassword)
    failOnStderr: "true"
    displayName: 'login to podman registry'

  - script: |
      podman build -t $(registry)/$(app_name):$(version) -f operations/dockerfiles/dockerfile . \
      --build-arg NPM_FEED_USERNAME=$(NPM_FEED_USERNAME) \
      --build-arg NPM_FEED_opassword=$(NPM_FEED_opassword)
      podman push --tls-verify=false $(registry)/$(app_name):$(version)
    displayName: 'Building and pushing image to podman registry $(registry)/$(app_name):$(version)'

  - script: |
      podman rmi $(registry)/$(app_name):$(version)
    failOnStderr: "true"
    displayName: 'Cleaning old images'

- job: Deploy
  pool: 'IFP'
  dependsOn: [DockerBuild,Init]
  variables:
    version: $[ dependencies.Init.outputs['setvarStep.version'] ]
  steps:
  - checkout: self
    persistCredentials: true
    clean: true
  - script: |
          git tag -a $(version) $SHORT_LATEST_COMMIT_ID -m "Updated Application Version tag $(version) and Deployed to Production"
          git push -u origin HEAD:$(Build.SourceBranchName) --tags
  # - template: release-tracking.yml
  #   parameters:
  #     jiraBaseUrl: $(jiraBaseUrl)
  #     jiraUsername: $(jiraUsername)
  #     jiraPassword: $(jiraPassword)
  #     projectKey: $(projectKey)
  #     issueType: 'Release'
  #     summaryPrefix: 'Release v$(version)'
  #     description: 'Build Source - $(Build.SourceVersion)'
  #     labels: $(application_labels)
