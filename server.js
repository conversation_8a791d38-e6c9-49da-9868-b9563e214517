const express = require('express');
const Logger = require('scad-library').logger;
const passport = require('passport');
const cors = require('cors');
const metricsMonitor = require('express-status-monitor');
var pjson = require('./package.json');
const moment = require('moment')
const clkdb = require('./services/clk-database.service');
const mysqldb = require('./services/mysql-database.service');
const { sequelize } = require("./database/models/index");
const cron = require('node-cron');
const nunjucks = require('nunjucks');

require('dotenv').config();
const externalRouter = require('./routes/external.router');
const contentTypeRouter = require('./routes/content-type.router');
const officialStatusRouter = require('./routes/official-status-check.router')
const kpiRouter = require('./routes/kpi.router');
const approvalRequestRouter = require('./routes/approval-request.router');
const commonRouter = require('./routes/common.router');
const userOnboardingRouter = require('./routes/user-onboardingv3.router')
const geoSpatialRevamp = require('./routes/geospatial-revamp.router');
const cmsRouter = require('./routes/cms.router');
const redisRouter = require('./routes/redis.router')
const alteryxRouter = require('./routes/alteryx.router');
const uaePassRouter = require("./routes/uae-pass.router");
const dataGovernanceRouter = require("./routes/data-governance.router")
const dmpRouter = require("./routes/dmp.router")
const database = require('./services/database.service');
const constants = require('./config/constants.json');
const { bearerStrategy, jwtMobileStrategy, getArcGISToken,alteryxAuthStrategy,  apiStrategy, jwtUAEPassStrategy, authStrategySelector } = require('./services/authorization.service');
const { formatReqToLogMsg } = require('./services/common-service');
const { getSearchData } = require('./microservice-search/searchInCMS');
const { sendEmail } = require('./microservice-email/email.controller');
const jwt = require('jsonwebtoken');

const log = new Logger().getInstance();

const swaggerUi = require('swagger-ui-express')
const { importSwaggerJson } = require('./swagger/functions');
const { ifpErrorMiddleware } = require('./utils/error');
const swaggerDocument = importSwaggerJson()

// 

const metricsConfig = {
    title: 'Express Status',  // Default title
    theme: 'default.css',     // Default styles
    path: '/api/status',
    spans: [{
        interval: 1,            // Every second
        retention: 60           // Keep 60 datapoints in memory
    }, {
        interval: 5,            // Every 5 seconds
        retention: 60
    }, {
        interval: 15,           // Every 15 seconds
        retention: 60
    }, {
        interval: 60,           // Every 1 minute
        retention: 60
    },
    {
        interval: 600,          // Every 10 minutes
        retention: 60
    }],
    chartVisibility: {
        cpu: true,
        mem: true,
        load: true,
        eventLoop: true,
        heap: true,
        responseTime: true,
        rps: true,
        statusCodes: true
    },
    healthChecks: [
        {
            protocol: 'http',
            host: 'localhost',
            path: '/api/healthcheck',
            port: '3000'
        },
        {
            protocol: 'http',
            host: 'localhost',
            path: '/api/version',
            port: '3000'
        }
    ],
    ignoreStartsWith: '/admin',

}


async function initDatabase() {
    log.debug(`>>>>> Enter server.initDatabase`);
    log.info(`Initializing database module`);
    try {
        await database.initialize();
        await clkdb.initialize();
				mysqldb.initialize();
				await sequelize.authenticate();
        log.info(`Initialised database`);
        log.debug(`<<<<< Exit server.initDatabase successfully`);
    } catch (err) {
        
        log.error(`<<<<< Exit server.initDatabase with error ${err}`);
        //process.exit(1); // Non-zero failure code
    }
}

async function initServer() {
	log.debug(`>>>>> Enter server.initServer`);
	log.info(`Initializing server`);

	log.debug(`Worker ${process.pid} started`);
	const app = express();

    app.use(cors({
        origin: '*'
    }));; //allow cors
    app.use(passport.initialize()); // Starts passport
    app.use(passport.session()); // Provides session support
    app.use(express.json({limit: '50mb'}));
    passport.use(bearerStrategy);
    passport.use(alteryxAuthStrategy);
	passport.use('jwt-mobile',jwtMobileStrategy);
	passport.use('jwt-uaepass',jwtUAEPassStrategy);
  passport.use("xapikey", apiStrategy);

	nunjucks.configure(process.cwd() + '/emails', {
		autoescape: true,
		noCache: process.env.NODE_ENV == "DEV" ? true : false,
	});

	//Gives Metrics at path /status of node app
	app.use(metricsMonitor(metricsConfig));

	//Helps logging all incoming requests to server
	app.all("/api/*", (req, res, next) => {
		if (!("accept-language" in req.headers))
			req.headers["accept-language"] = "en";

		res.setHeader("X-Content-Type-Options", "nosniff");
		res.setHeader("X-XSS-Protection", "1; mode=block");
		res.setHeader("X-Frame-Options", "DENY");
		//res.setHeader('Cache-Control','private, max-age=604800');
		res.setHeader(
			"Strict-Transport-Security",
			"max-age=31536000; includeSubDomains; preload"
		);
		res.setHeader(
			"Content-Security-Policy",
			"default-src 'self' 'unsafe-eval' 'unsafe-inline' *.beinex.com; script-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' *.accenture.com data:; connect-src 'self' *.accenture.com; upgrade-insecure-requests; block-all-mixed-content"
		);
		res.setHeader("Access-Control-Allow-Private-Network", true);
		res.setHeader("Access-Control-Allow-Origin", "*");
		res.removeHeader("X-Powered-By");
		res.removeHeader("Server");
		// res.removeHeader('Access-Control-Allow-Origin');
		let requestLog = formatReqToLogMsg(req);
		log.info(`Incoming request ---> ${requestLog}`);
		next();
	});

	//checks api health and accessible without access token
	app.get("/api/healthCheck", async function (req, res, next) {
		await database.simpleExecute("SELECT 1 FROM DUAL");
		res.send(`Health Check`);
		next();
	});

	//checks deployed api verion without access token
	app.get("/api/version", function (req, res, next) {
		res.send(pjson.version);
		next();
	});

	app.get("/api/envcheck", function (req, res, next) {
		res.send(process.env);
		next();
	});

    app.use('/api/user-onboarding',userOnboardingRouter)
          
    //routes all api with /api/content-type basepath to content-type.router.js
	app.use('/api/services',externalRouter);
    app.use('/api/mobile/content-type', passport.authenticate('jwt-mobile', { session: false }), contentTypeRouter);
    app.use('/api/content-type/alteryx/official-check', passport.authenticate('bearer', { session: false }), officialStatusRouter);
    app.use('/api/content-type', authStrategySelector, contentTypeRouter);
    app.all('/api/content-type/*', (req, res, next) => {
        if (!['en', 'ar'].includes(req.headers['accept-language']))
            res.status(400).send({ message: `Unsupported Language: ${req.headers['accept-language']}` });
        next()
    })

	//routes all api with /api/content-type basepath to content-type.router.js
	app.use(
		"/api/common",
		authStrategySelector,
		commonRouter
	);
	app.use(
		"/api/mobile/common",
		passport.authenticate("jwt-mobile", { session: false }),
		commonRouter
	);
	app.use("/api/cms", authStrategySelector, cmsRouter);

	app.use('/api/mobile/geospatial-revamp', geoSpatialRevamp);

	//search api
	app.post(
		"/api/indicator-search?",
		authStrategySelector,
		async (req, res, next) => {
			try {
				// Default apiVer will be 2.0 for Bayaan web consumer
				const apiVersion = req.get("Accept-Version");
				if (!apiVersion) {
					req.apiVersion = 2;
				} else {
					req.apiVersion = +apiVersion;
				}
				const data = await getSearchData(req);
				res.send(data);
				next();
			} catch (err) {
				log.error(`Error executing search api ${err}`);
				next(err);
			}
		}
	);

	app.post(
		"/api/mobile/indicator-search?",
		passport.authenticate("jwt-mobile", { session: false }),
		async (req, res, next) => {
			try {
				// Default api version will be 1.0 for mobile app consumer
				const apiVersion = req.get("Accept-Version");
				if (apiVersion) {
					req.apiVersion = +apiVersion;
				}
				const data = await getSearchData(req);
				res.send(data);
				next();
			} catch (err) {
				log.error(`Error executing search api ${err}`);
				next(err);
			}
		}
	);

	app.post(
		"/api/send-email",
		authStrategySelector,
		async (req, res, next) => {
			sendEmail(req)
				.then(() => {
					res.status(200).send({ message: "Email sent successfully" });
					next();
				})
				.catch((err) => {
					
					next(err);
				});
		}
	);

	app.get(
		"/api/arc-gis-auth",
		authStrategySelector,
		async (req, res, next) => {
			try {
				gis_response = await getArcGISToken();
				if (gis_response.data.access_token) res.send(gis_response.data);
				else {
					res.statusCode = gis_response.data.error.code
						? gis_response.data.error.code
						: 400;
					res.send({ message: gis_response.data.error, status: "failed" });
				}
				next();
			} catch (err) {
				log.error(`Error executing gis auth api ${err}`);
				
				next(err);
			}
		}
	);
	

	app.get(
		"/api/mobile/arc-gis-auth",
		passport.authenticate("jwt-mobile", { session: false }),
		async (req, res, next) => {
			try {
				gis_response = await getArcGISToken();
				if (gis_response.data.access_token) res.send(gis_response.data);
				else {
					res.statusCode = gis_response.data.error.code
						? gis_response.data.error.code
						: 400;
					res.send({ message: gis_response.data.error, status: "failed" });
				}
				next();
			} catch (err) {
				log.error(`Error executing gis auth api ${err}`);
				
				next(err);
			}
		}
	);

	app.get(
		"/api/login-time",
		authStrategySelector,
		async (req, res, next) => {
			let user = req.user.preferred_username;
			let login_time = moment();
			log.info(`[Auth] Logged in user: ${user} | Time: ${login_time}`);
			res.status(200).send({ message: "Login time logged successfully" });
			next();
		}
	);

	app.get(
		"/api/logout-time",
		authStrategySelector,
		async (req, res, next) => {
			let user = req.user.preferred_username;
			let logout_time = moment();
			log.info(`[Auth] Logged out user: ${user} | Time: ${logout_time}`);
			res.status(200).send({ message: "Logout time logged successfully" });
			next();
		}
	);

	app.use("/api/flush-redis", redisRouter);

	// data governance router
	app.use(
		"/api/data-governance",
		authStrategySelector,
		dataGovernanceRouter
	);

	// KPI Routes
	app.use(
		"/api/kpi",
		authStrategySelector,
		kpiRouter
	);
	
	app.use(
		"/api/mobile/kpi",
		passport.authenticate('jwt-mobile', { session: false }),
		kpiRouter
	);

	// Approval request router
	app.use(
		"/api/approval-requests",
		authStrategySelector,
		approvalRequestRouter
	);

	app.get(
		"/api/login-time",
		passport.authenticate("oauth-bearer", { session: false }),
		async (req, res, next) => {
			let user = req.user.preferred_username;
			let login_time = moment();
			log.info(`[Auth] Logged in user: ${user} | Time: ${login_time}`);
			res.status(200).send({ message: "Login time logged successfully" });
			next();
		}
	);

	app.get(
		"/api/logout-time",
		passport.authenticate("oauth-bearer", { session: false }),
		async (req, res, next) => {
			let user = req.user.preferred_username;
			let logout_time = moment();
			log.info(`[Auth] Logged out user: ${user} | Time: ${logout_time}`);
			res.status(200).send({ message: "Logout time logged successfully" });
			next();
		}
	);

	app.use("/api/flush-redis", redisRouter);

    app.use("/api/uae-pass", uaePassRouter);

    app.use(
		"/api/alteryx",
		authStrategySelector,
		alteryxRouter
	);

    //Swagger Middleware
    if (['dev', 'local'].includes(process.env.NODE_ENV)) {
        app.use(
            '/api/docs',
            swaggerUi.serve,
            swaggerUi.setup(swaggerDocument)
        );
    }

	process.on("unhandledRejection", (err, origin) => {
		
		log.error(
			`Received uncaught exception closing server ${err} Origin: ${origin}`
		);
		console.error(
			`Received uncaught exception closing server ${err} Origin: ${origin}`
		);
	});

    app.use(ifpErrorMiddleware)

    app.use((req, res, next) => {
        res.status(400).send({
            message: 'Bad Request',
            error: 400,
            reason: `Invalid route path ${req.url}`
        });
        // next();
    });
    // Handle all application error
    app.use(function (err, req, res, next) {
        try {
            if (err) {
                if (constants.errorMessage.hasOwnProperty(err[0])) {
                    const errorMessage = constants.errorMessage[err[0]];
                    log.error(`Handling Error ${JSON.stringify(err[1])} ${err[1].stack}`);
                    let reason = err[1] ? err[1] : '';
                    res.status(err[0]).send({ message: errorMessage, error: err[0], reason: reason.message });
                }
                else if (err.type == 'entity.parse.failed') {
                    res.status(400).send({ message: 'Bad request body', error: err.message, body: err.body });
                }
                else {
                    log.info(`Request ${req.url} Completed`);
                }
            }
        } catch (err) {
            log.error(`Error handling error message ${err}`)
        }
        next();
    });

    log.debug(`Server configuration Start`);
    // Start server
    const port = process.env.HTTP_PORT || 3000;
    const server = app.listen(port)
        .on('listening', () => {
            log.info(`Server Started on localhost:${port}`);
        })
        .on('error', err => {
            log.error(`Server Start Error ${err}`);
        });
    process.stdin.resume();
    //catches all system terminate signal
    process.on('SIGTERM', () => {
        server.close(() => {
            log.error(`Received SIGTERM closing server`);
            console.error(`Received SIGTERM closing server`);
            initialize();
        })
    });

    //catches all uncaught exception  
    process.on('unCaughtException', (err, origin) => {
        server.close(() => {
            log.error(`Received uncaught exception closing server ${err} Origin: ${origin}`);
            console.error(`Received uncaught exception closing server ${err} Origin: ${origin}`);
        })
    });

    process.on('unhandledRejection', (err, origin) => {
        log.error(`Received uncaught exception closing server ${err} Origin: ${origin}`);
        console.error(`Received uncaught exception closing server ${err} Origin: ${origin}`);
    });

    log.debug(`<<<<< Exit server.initServer`);

}

/**
 * Starts the server
 *
 */

async function initialize() {
    await initServer();
    await initDatabase();    
}

initialize()
