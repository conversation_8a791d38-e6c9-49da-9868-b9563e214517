const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getSuperUserStatusQuery(userEmail) {
    try {
        let query = `SELECT * FROM IFP_USER_MASTER WHERE EMAIL = '${userEmail}'`
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.listMyAppsDataQuery with error ${err} `);
        throw err;
    }
}

async function getUserFeatureFlagQuery(userEmail) {
    try {
        let query = `SELECT * FROM USER_FEATURE_FLAG WHERE USER_EMAIL = '${userEmail}'`
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getUserFeatureFlagQuery with error ${err} `);
        throw err;
    }
}

async function getMaintenanceStatusQuery(){
    try {
        let query = `SELECT * FROM BAYAAN_PLATFORM_OPS`
        return query
    } catch (err) {
        log.error(`<<<<<< Exited microservice-myapps.services.getQuery.service.getMaintenanceStatus with error ${err} `);
        throw err;
    }
}

module.exports = { 
    getSuperUserStatusQuery,
    getUserFeatureFlagQuery,
    getMaintenanceStatusQuery
 };
