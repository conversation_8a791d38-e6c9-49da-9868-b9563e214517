const { PutObjectCommand } = require("@aws-sdk/client-s3");
const { awsClient } = require("./config/awsClient");
const { randomUUID } = require("crypto");
const fs = require('fs');
const path = require('path');

// Configuration - update these values as needed
const IMAGE_BUCKET = process.env.S3_IMAGE_BUCKET || "your-image-bucket";
const IMAGE_FOLDER = "publication-images";

/**
 * Uploads an image to S3 and returns the public URL
 * @param {string} imagePath - Path to the local image file
 * @param {string} publicationId - Publication ID to associate with the image
 * @returns {Promise<string|null>} - Returns the public URL of the uploaded image or null if failed
 */
async function uploadImageLocal(imagePath, publicationId) {
  try {
    // Check if file exists
    if (!fs.existsSync(imagePath)) {
      console.error(`File not found: ${imagePath}`);
      return null;
    }

    // Read the image file
    const imageBuffer = fs.readFileSync(imagePath);
    const fileName = path.basename(imagePath);
    const fileExtension = path.extname(fileName);
    
    // Determine MIME type based on file extension
    const mimeType = getMimeType(fileExtension);
    if (!mimeType) {
      console.error(`Unsupported file type: ${fileExtension}`);
      return null;
    }

    // Generate unique filename
    const uniqueFileName = `${publicationId}_${randomUUID()}${fileExtension}`;
    const key = `${IMAGE_FOLDER}/${uniqueFileName}`;

    console.log(`Uploading ${fileName} to S3...`);

    const command = new PutObjectCommand({
      Bucket: IMAGE_BUCKET,
      Key: key,
      Body: imageBuffer,
      ContentType: mimeType,
      // Make the object publicly readable (optional, depending on your bucket policy)
      ACL: 'public-read'
    });

    await awsClient.send(command);
    
    // Construct the public URL
    const imageUrl = constructPublicUrl(key);
    
    console.log(`✅ Image uploaded successfully!`);
    console.log(`📁 Original file: ${fileName}`);
    console.log(`🔗 Image URL: ${imageUrl}`);
    console.log(`📋 Publication ID: ${publicationId}`);
    
    return imageUrl;

  } catch (error) {
    console.error(`❌ Error uploading image: ${error.message}`);
    return null;
  }
}

/**
 * Constructs the public URL for the uploaded image
 * @param {string} key - S3 object key
 * @returns {string} - Public URL
 */
function constructPublicUrl(key) {
  const endpoint = process.env.S3_ENDPOINT;
  const region = process.env.S3_REGION;
  
  if (endpoint) {
    // For custom S3-compatible storage (like MinIO)
    return `${endpoint}/${IMAGE_BUCKET}/${key}`;
  } else {
    // For AWS S3
    return `https://${IMAGE_BUCKET}.s3.${region}.amazonaws.com/${key}`;
  }
}

/**
 * Get MIME type based on file extension
 * @param {string} extension - File extension (e.g., '.jpg')
 * @returns {string|null} - MIME type or null if unsupported
 */
function getMimeType(extension) {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };
  return mimeTypes[extension.toLowerCase()] || null;
}

/**
 * Main function to run the upload
 */
async function main() {
  // Get command line arguments
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log(`
📖 Usage: node upload-image-local.js <image-path> <publication-id>

Examples:
  node upload-image-local.js ./my-image.jpg pub123
  node upload-image-local.js "C:\\Users\\<USER>\\Pictures\\photo.png" publication-456
  node upload-image-local.js /path/to/image.jpeg my-publication

Supported formats: JPG, JPEG, PNG, GIF, WebP
    `);
    process.exit(1);
  }

  const imagePath = args[0];
  const publicationId = args[1];

  console.log(`🚀 Starting image upload...`);
  console.log(`📁 Image path: ${imagePath}`);
  console.log(`📋 Publication ID: ${publicationId}`);
  console.log(`🪣 S3 Bucket: ${IMAGE_BUCKET}`);
  console.log('');

  const imageUrl = await uploadImageLocal(imagePath, publicationId);
  
  if (imageUrl) {
    console.log('\n🎉 Upload completed successfully!');
    console.log(`You can now use this URL: ${imageUrl}`);
  } else {
    console.log('\n💥 Upload failed!');
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  uploadImageLocal
};
