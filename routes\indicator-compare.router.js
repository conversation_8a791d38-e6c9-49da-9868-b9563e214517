const express = require('express');
const router = new express.Router();
const { body, validationResult } = require('express-validator');
const {validateIndicatorCompareNodes, validateIndicatorCompareDetail, validateIndicatorCompareAddToMyApps} = require('./validators/indicator-compare.validator')
const compareController = require('../microservice-indicator-compare/indicator-compare.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.post('/create',[body('nodes').notEmpty().custom(validateIndicatorCompareNodes)], async (req, res, next) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const data = await compareController.compareIndicators(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for indicator-compare by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.post('/add-to-myapps', validateIndicatorCompareAddToMyApps, async (req, res, next) => {
    try {
        const data = await compareController.addToMyApps(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for indicator-compare by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/list', async (req, res, next) => {
    try {
        const data = await compareController.listMyApps(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for indicator-compare by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.get('/:id', validateIndicatorCompareDetail, async (req, res, next) => {
    try {
        const data = await compareController.getCompareApps(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for indicator-compare by id content-type, ERROR: ${err}`);
        next(err);
    }
});

router.use((err, req, res, next) => {
    if (err){
      if (req.user && req.user.preferred_username)
        err.user=req.user.preferred_username
      else
        err.user = 'NOUSER'
      if (!err.statusCode)
        err.statusCode = 500
      err.enhanced = true;
    }
    next(err);
  });


module.exports = router;
