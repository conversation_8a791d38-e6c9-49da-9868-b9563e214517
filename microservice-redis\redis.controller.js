const { clearCacheUser } = require('../services/helpers/helper');
const {flushKeys, flushGatewayCache} = require('../services/redis.service');
const { IFPError } = require('../utils/error');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const moment = require('moment')

async function flushRedis(req){
    try{
        let user = clearCacheUser(req.query.key);
        let type;
        if (user){
            switch (req.params.type) {
                case 'all':
                    await flushKeys(`${process.env.NODE_ENV}_*`)
                    await flushGatewayCache(`${process.env.NODE_ENV}*`)
                    break;
                case 'data':
                    await flushKeys(`${process.env.NODE_ENV}_data*`)
                    await flushKeys(`${process.env.NODE_ENV}_clk_data*`)
                    await flushKeys(`${process.env.NODE_ENV}_cms*`)
                    await flushGatewayCache(`${process.env.NODE_ENV}*`)
                    break;
                case 'cms':
                    await flushKeys(`${process.env.NODE_ENV}_responseService*`)
                    await flushGatewayCache(`${process.env.NODE_ENV}*`)
                    break;
                default:
                    type='invalid'
            }
            if (type == 'invalid'){
                throw new IFPError(400,'Invalid Type')
            }
            else{
                log.info(`[Redis] Keys flush by the user: ${user}  | Time: ${moment()}`);
                return { message: 'Cleared redis cache successfully' }
            }   
        }
        else{
            throw new IFPError(401,"You're not authorized to perform this action" );
        }
    }
    catch(err){
        throw err;
    }
}

module.exports = {
    flushRedis
}