const express = require('express');
const router = new express.Router();
const pagesController = require('../microservice-pages/pages.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
    try {
      const data = await pagesController.getPages(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/:pageType', async (req, res, next) => {
    try {
      const data = await pagesController.getPagesByID(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/:pageType/:id', async (req, res, next) => {
    try {
      const data = await pagesController.getPagesByID(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.get('/:domainPageType/:pageType/:id', async (req, res, next) => {
    try {
      const data = await pagesController.getPagesByID(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
