{"paths": {"/content-type/myapps/": {"get": {"tags": ["My Apps"], "summary": "Retrieves list of favourite apps of a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"stateChange": {"type": "integer", "description": "Indicates the change in state."}, "classificationNodes": {"type": "object", "properties": {"official_statistics": {"type": "array", "items": {"type": "string"}, "description": "List of node IDs classified under official statistics."}, "analytical_apps": {"type": "array", "items": {"type": "string"}, "description": "List of node IDs classified under analytical apps."}, "reports": {"type": "array", "items": {"type": "string"}, "description": "List of node IDs classified under reports."}, "Innovative Insights": {"type": "array", "items": {"type": "string"}, "description": "List of node IDs classified under innovative insights."}}}, "data": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the domain or category."}, "items": {"type": "array", "items": {}, "description": "Array of items directly under the domain, if any."}, "interaction_status": {"type": "integer", "description": "Interaction status of the domain."}, "sub_domains": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the sub-domain."}, "items": {"type": "array", "items": {}, "description": "Array of items directly under the sub-domain, if any."}, "sub_themes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the sub-theme."}, "items": {"type": "array", "items": {}, "description": "Array of items directly under the sub-theme, if any."}, "products": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the product."}, "items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the classification."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"NODE_ID": {"type": "string", "description": "Unique identifier for the node."}, "CONTENT_TYPE": {"type": "string", "description": "Content type of the node."}, "STATUS": {"type": "string", "description": "Status of the node."}, "USER_EMAIL": {"type": "string", "description": "Email of the user who interacted with the node."}, "INSERT_DT": {"type": "string", "format": "date-time", "description": "Insertion date of the node data."}, "TITLE": {"type": "string", "description": "Title of the node."}, "MOD_TITLE": {"type": "string", "description": "Modified title of the node, if applicable."}, "DOMAIN": {"type": "string", "description": "Domain of the node."}, "CLASSIFICATION": {"type": "string", "description": "Classification of the node."}, "MOD_FLAG": {"type": "integer", "description": "Modification flag of the node."}, "SORT_ORDER": {"type": "integer", "description": "Sort order of the node."}, "TOPIC": {"type": "string", "description": "Topic associated with the node."}, "THEME": {"type": "string", "description": "Theme associated with the node."}, "SUBTHEME": {"type": "string", "description": "Subtheme associated with the node."}, "PRODUCT": {"type": "string", "description": "Product associated with the node."}, "SOURCE_NAME": {"type": "string", "description": "Source name of the node data."}, "INTERACTION_STATUS": {"type": "integer", "description": "Interaction status of the node."}, "PUBLISH_DATE": {"type": "string", "format": "date-time", "description": "Publish date of the node data."}}}}}}}}}}}}}}}}}}}}, "required": ["stateChange", "classificationNodes", "data"]}}}}}}}, "/content-type/myapps/submit": {"post": {"tags": ["My Apps"], "summary": "Modifies the favourite apps of a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "My Apps Submit data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyAppsSubmitData"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "A message indicating the outcome of the request."}, "status": {"type": "string", "enum": ["success", "error"], "description": "Indicates whether the request was processed successfully or encountered an error."}}, "required": ["message", "status"]}}}}}}}, "/content-type/myapps/apps-list": {"get": {"tags": ["My Apps"], "summary": "Retrieves list of apps added to favourites", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string", "description": "Unique identifier for each element, representing different types of content or indicators."}, "description": "An array of unique identifiers, each corresponding to a specific content item or indicator within the system."}}}}}}}, "/content-type/myapps/draft": {"post": {"tags": ["My Apps"], "summary": "Draft favourite apps of a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "My Apps Draft data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyAppsDraftData"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Confirmation message indicating the success of the draft creation."}}, "required": ["message"], "description": "An object representing the successful creation of a draft, including a confirmation message."}}}}}}}, "/content-type/myapps/draft-list": {"get": {"tags": ["My Apps"], "summary": "Retrieves drafts of the favourite apps created by the user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "The unique identifier for the draft."}, "name": {"type": "string", "description": "The name of the draft, including the date and time it was saved."}}, "required": ["id", "name"]}, "description": "An array of objects, each representing a saved draft. Each draft has a unique ID and a name that includes the date and time it was saved."}}}}}}}, "/content-type/myapps/draft-nodes-list/{id}": {"get": {"tags": ["My Apps"], "summary": "Retrieves list of apps added as draft, using draft id", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Draft"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"Industry & Business": {"type": "array", "items": {"type": "object", "properties": {"NODE_ID": {"type": "string", "description": "Unique identifier for the node."}, "CONTENT_TYPE": {"type": "string", "description": "Type of the content (e.g., statistics-insights)."}, "STATUS": {"type": "string", "description": "Current status of the content (e.g., DRAFT)."}, "USER_EMAIL": {"type": "string", "description": "Email of the user who created or modified the draft."}, "INSERT_DT": {"type": "string", "description": "Insertion date of the draft. Null if not set."}, "TITLE": {"type": "string", "description": "Title of the content."}, "DRAFT_ID": {"type": "string", "description": "Unique identifier of the draft."}, "TYPE": {"type": "string", "description": "Action type associated with the draft (e.g., ADD)."}, "TOPIC": {"type": "null", "description": "Topic associated with the content. Null if not applicable."}, "THEME": {"type": "null", "description": "Theme associated with the content. Null if not applicable."}, "SUBTHEME": {"type": "null", "description": "Subtheme associated with the content. Null if not applicable."}, "PRODUCT": {"type": "null", "description": "Product associated with the content. Null if not applicable."}, "SOURCE_NAME": {"type": "null", "description": "Source name of the content. Null if not applicable."}}, "required": ["NODE_ID", "CONTENT_TYPE", "STATUS", "USER_EMAIL", "TITLE", "DRAFT_ID", "TYPE"]}, "description": "Array of content items belonging to the 'Industry & Business' domain."}, "Economy": {"type": "array", "items": {"$ref": "#/properties/Industry & Business/items"}, "description": "Array of content items belonging to the 'Economy' domain."}}, "description": "A structured representation of draft content items categorized under specific domains such as 'Industry & Business' and 'Economy'. Each item includes details like the node ID, content type, status, and more."}}}}}}}, "/content-type/myapps/draft/delete/{id}": {"post": {"tags": ["My Apps"], "summary": "Delete the draft from favourites", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The ID of the Draft"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Confirmation message indicating the successful deletion of the draft."}}, "required": ["message"], "description": "A response schema indicating the successful deletion of a draft with a confirmation message."}}}}}}}, "/content-type/myapps/state": {"post": {"tags": ["My Apps"], "summary": "Update the state of apps", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "My Apps State data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyAppsStateData"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/myapps/state/reset": {"post": {"tags": ["My Apps"], "summary": "Reset the state of favourite apps", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "State removed successfully"}, "status": {"type": "string", "example": "success"}}}}}}}}}}, "components": {"schemas": {"MyAppsSubmitData": {"type": "object", "properties": {"add": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the content to be added."}, "contentType": {"type": "string", "description": "Type of content to be added, indicating its category or classification."}}, "required": ["id", "contentType"]}, "description": "An array of objects representing the content to be added."}, "remove": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the content to be removed."}}}, "description": "An array of objects representing the content to be removed. This example array is empty, indicating no content is set to be removed."}}, "required": ["add", "remove"]}, "MyAppsDraftData": {"type": "object", "properties": {"add": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the content to be added."}, "contentType": {"type": "string", "description": "Type of content to be added, indicating its category or classification."}}, "required": ["id", "contentType"]}, "description": "An array of objects representing the content to be added."}, "remove": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the content to be removed."}}}, "description": "An array of objects representing the content to be removed. This example array is empty, indicating no content is set to be removed."}}, "required": ["add", "remove"]}, "MyAppsStateData": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the domain or category."}, "expand": {"type": "boolean", "description": "Indicates if the domain is expanded to show its contents."}, "interaction_status": {"type": "integer", "description": "The interaction status of the domain, indicating various states like active, inactive, etc."}, "nodeData": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the classification or sub-category within the domain."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"NODE_ID": {"type": "string", "description": "The unique identifier of the node."}, "CONTENT_TYPE": {"type": "string", "description": "The type of content represented by the node."}, "STATUS": {"type": "string", "description": "The current status of the node (e.g., draft, published, etc.)."}, "USER_EMAIL": {"type": "string", "description": "The email of the user who created or last modified the node."}, "INSERT_DT": {"type": ["string", "null"], "format": "date-time", "description": "The date and time when the node was created or last modified."}, "TITLE": {"type": "string", "description": "The title of the node."}, "MOD_TITLE": {"type": ["string", "null"], "description": "The modified title of the node, if any."}, "DOMAIN": {"type": ["string", "null"], "description": "The domain to which the node belongs."}, "CLASSIFICATION": {"type": ["string", "null"], "description": "The classification or sub-category of the node within its domain."}, "MOD_FLAG": {"type": "integer", "description": "Flag indicating if the node has been modified."}, "SORT_ORDER": {"type": ["integer", "null"], "description": "The sort order of the node within its classification."}, "TOPIC": {"type": ["string", "null"], "description": "The topic associated with the node, if applicable."}, "THEME": {"type": ["string", "null"], "description": "The theme associated with the node, if applicable."}, "SUBTHEME": {"type": ["string", "null"], "description": "The sub-theme associated with the node, if applicable."}, "PRODUCT": {"type": ["string", "null"], "description": "The product associated with the node, if applicable."}, "SOURCE_NAME": {"type": ["string", "null"], "description": "The source name of the node's data."}, "INTERACTION_STATUS": {"type": "integer", "description": "The interaction status of the node."}, "PUBLISH_DATE": {"type": ["string", "null"], "format": "date-time", "description": "The publish date of the node, if published."}}, "required": ["NODE_ID", "CONTENT_TYPE", "STATUS", "TITLE", "MOD_FLAG", "INTERACTION_STATUS"]}, "description": "An array of nodes belonging to the classification."}, "edit": {"type": "boolean", "description": "Indicates if the classification is editable."}}, "required": ["name", "nodes"]}, "description": "An array of data nodes grouped by classification within the domain."}, "items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the item within the domain or classification."}, "nodes": {"type": "array", "items": {"type": "object", "properties": {"NODE_ID": {"type": "string", "description": "The unique identifier of the node."}, "CONTENT_TYPE": {"type": "string", "description": "The type of content represented by the node."}, "STATUS": {"type": "string", "description": "The current status of the node (e.g., draft, published, etc.)."}, "USER_EMAIL": {"type": "string", "description": "The email of the user who created or last modified the node."}, "INSERT_DT": {"type": ["string", "null"], "format": "date-time", "description": "The date and time when the node was created or last modified."}, "TITLE": {"type": "string", "description": "The title of the node."}, "MOD_TITLE": {"type": ["string", "null"], "description": "The modified title of the node, if any."}, "DOMAIN": {"type": ["string", "null"], "description": "The domain to which the node belongs."}, "CLASSIFICATION": {"type": ["string", "null"], "description": "The classification or sub-category of the node within its domain."}, "MOD_FLAG": {"type": "integer", "description": "Flag indicating if the node has been modified."}, "SORT_ORDER": {"type": ["integer", "null"], "description": "The sort order of the node within its classification."}, "TOPIC": {"type": ["string", "null"], "description": "The topic associated with the node, if applicable."}, "THEME": {"type": ["string", "null"], "description": "The theme associated with the node, if applicable."}, "SUBTHEME": {"type": ["string", "null"], "description": "The sub-theme associated with the node, if applicable."}, "PRODUCT": {"type": ["string", "null"], "description": "The product associated with the node, if applicable."}, "SOURCE_NAME": {"type": ["string", "null"], "description": "The source name of the node's data."}, "INTERACTION_STATUS": {"type": "integer", "description": "The interaction status of the node."}, "PUBLISH_DATE": {"type": ["string", "null"], "format": "date-time", "description": "The publish date of the node, if published."}}, "required": ["NODE_ID", "CONTENT_TYPE", "STATUS", "TITLE", "MOD_FLAG", "INTERACTION_STATUS"]}, "description": "An array of nodes belonging to the item."}}, "required": ["name", "nodes"]}, "description": "An array of items within the domain or classification."}}, "required": ["name", "expand", "interaction_status", "nodeData", "items"]}, "description": "A schema representing a payload containing domains and their classifications, items, and nodes, including details such as the content type, status, user email, insert date, title, and more."}}}}