const { IFPError } = require("../utils/error");
const UAEPassService = require("../services/uae-pass.service");
const { createUAEPassPlatformAccessToken, createUAEPassPlatformRefreshToken, decodeJWT } = require("../services/authorization.service");
const { getEIDInvitationMappingByEID, updateUserData, getUserDataV2, updateUserAccessRequestUserId, updateUserAccessUserId } = require("../microservice-users-v3/services/executeQuery");
const { hashEmiratesId, cleanEIDName } = require("../microservice-users-v3/helper");
const { encryptPhone } = require("../services/encryption.service");
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const service = new UAEPassService();

async function getUserInfoController(req) {
  const name = req.headers['x-upass-name'];
  const phone = req.headers['x-upass-phone'];
  const idn = req.headers['x-upass-idn'];
  const uuid = req.headers['x-upass-uuid'];
  let userInfo = {};
  const accessCode = req.headers["x-upass-access-code"];
  let accessToken = req.headers["x-upass-access-token"];
  if(['dev','test','prod'].includes(process.env.NODE_ENV)){
    
    if (!accessCode && !accessToken) {
      throw new IFPError(400, "Access CODE/TOKEN Required");
    }

    if (!accessToken) {
      accessToken = await service.getAccessToken(accessCode);
      if (!accessToken) {
        throw new IFPError(400, "Invalid/Expired Access CODE");
      }
    }

    userInfo = await service.getUserInfo(accessToken);
    if (!userInfo) {
      throw new IFPError(401, "Invalid/Expired Access TOKEN");
    }
  }
  else{
     userInfo = {
      idn:uuid,
      name,
      phone,
      idn
    }
  }
  token_data = {}
  try {
    // Clean EID Name
    userInfo.fullnameEN = cleanEIDName(userInfo.fullnameEN)

    // Update existing users ID with uaepass uuid
    const { idn, uuid, mobile } = userInfo;

    // [Existing User] Link by EID Mapping
    const hashedEid = hashEmiratesId(idn);
    const eidMappingEmail = await getEIDInvitationMappingByEID(hashedEid);
    if (eidMappingEmail && eidMappingEmail.length > 0) {
      const userData = await getUserDataV2({ EMAIL: eidMappingEmail[0].EMAIL });

      // Update user with UUID. TODO: Optimize, update for every request not required.
      if (userData.length && userData[0].EXISTING_USER == "EXISTING_LINK_INITIATED") {
        await updateUserData("EMAIL", eidMappingEmail[0].EMAIL, {
          ID: uuid,
          PHONE_NUMBER: encryptPhone(mobile),
          EXISTING_USER: "EXISTING_LINKED"
        });
        await updateUserAccessUserId(userData[0].ID, uuid);
        await updateUserAccessRequestUserId(userData[0].ID, uuid);
      }
    } else {
      // [Existing User] Link by Mobile
      const encryptedPhone = encryptPhone(mobile);
      const userData = await getUserDataV2({ PHONE_NUMBER: encryptedPhone });
      if (userData.length > 0 && userData[0].EXISTING_USER == "EXISTING_LINK_INITIATED") {
        await updateUserData("MOBILE", encryptedPhone, { ID: uuid, EXISTING_USER: "EXISTING_LINKED" });
        await updateUserAccessUserId(userData[0].ID, uuid);
        await updateUserAccessRequestUserId(userData[0].ID, uuid);
      }
    }

    const apiToken = await createUAEPassPlatformAccessToken(userInfo);
    if (!apiToken) {
      return { accessToken, userInfo, linked: false };
    }
    const appRefreshToken = await createUAEPassPlatformRefreshToken(userInfo);

    await updateUserData("ID", uuid, { LAST_UAEPASS_LOGIN_AT: new Date() })
    token_data["apiToken"] = apiToken;
    token_data["refreshToken"] = appRefreshToken;
  } catch (e) {
    log.info(`Error while fecthing generating token${e}`);
  }
  token_data["linked"]= true;
  return { accessToken, userInfo, ...token_data};
}

async function getAccessTokenController(req) {
  let refreshToken = req.headers["x-upass-refresh-token"];
  if(!refreshToken){
    throw new IFPError(400, "Refresh Token Required");
  }

  const decodedRefreshToken = decodeJWT(refreshToken);
  const userInfo = {
    uuid: decodedRefreshToken.sub,
    fullnameEN: decodedRefreshToken.name,
  }
  const apiToken = await createUAEPassPlatformAccessToken(userInfo);
  const newRefreshToken = await createUAEPassPlatformRefreshToken(userInfo);
  return { apiToken, refreshToken: newRefreshToken };
}

module.exports = {
  getUserInfoController,
  getAccessTokenController
};
