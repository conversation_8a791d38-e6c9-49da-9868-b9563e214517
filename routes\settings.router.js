const express = require('express');
const router = new express.Router();
const userSettingsController = require('../microservice-user-settings/user-settings.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
    try {
      const data = await userSettingsController.getUserSettings(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/', async (req, res, next) => {
    try {
      const data = await userSettingsController.updateUserSettings(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for pages content-type for ID ${req.params.id}, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
