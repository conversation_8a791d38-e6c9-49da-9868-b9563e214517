const { Sequelize, DataTypes, Model } = require("sequelize");
const ApprovalRequest = require("./approval-request.model");

/**
 * Model for storing comments on approval requests in the Bayaan approval system.
 * Comments can be added by requestors, approvers, or other stakeholders.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const ApprovalRequestComment = sequelize.define(
    "ApprovalRequestComment",
    {
      id: {
        type: DataTypes.UUID,
        allowNull: false,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4,
      },
      request_id: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      user_id: {
        type: DataTypes.STRING(20),
        allowNull: false,
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      comment_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        defaultValue: 'comment',
      },
    },
    {
      tableName: "bayaan_approval_request_comments",
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );
  
  ApprovalRequestComment.belongsTo(ApprovalRequest(sequelize, DataTypes), {
    foreignKey: 'request_id',
    targetKey: 'id',
    as: 'request',
  });
  
  return ApprovalRequestComment;
}

module.exports = model;