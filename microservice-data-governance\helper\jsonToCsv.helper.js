// Helper function to convert JSON to CSV
const jsonToCsv = (jsonData) => {
  if (!jsonData || jsonData.length === 0) return '';
  
  // Get headers from first object
  const headers = Object.keys(jsonData[0]);
  
  // Create CSV header row
  const csvHeaders = headers.join(',');
  
  // Create CSV data rows
  const csvRows = jsonData.map(row => {
    return headers.map(header => {
      const value = row[header];
      // Handle values that might contain commas or quotes
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value || '';
    }).join(',');
  });
  
  return [csvHeaders, ...csvRows].join('\n');
};

// Export the function
module.exports = jsonToCsv;