const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const { createClient } = require('@clickhouse/client');
const { generateClkCacheKey } = require('./helpers/helper');
const constants = require('../config/constants.json')
const { setRedis, getRedis } = require('./redis.service')


const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

let  client;
async function initialize() {
    try {
        client = createClient({
            host: process.env.CLICKHOUSE_HOST,
            username: process.env.CLICKHOUSE_USER,
            password: process.env.CLICKHOUSE_PASSWORD,
            database: process.env.CLICKHOUSE_DATABASE,
            max_open_connections:50,
            keep_alive: {
                enabled: true,
                socket_ttl: 29000,
                retry_on_expired_socket: true,
              }
          })
        log.info(`Clickhouse DB Pool created successfully`)
    } catch (err) {
        
        log.error(`Error creating DB Pool ${err}`);
        log.info(`<PERSON>rror creating DB Pool ${err}`);
    }
}

async function close() {
    await oracledb.getPool().close();
}

async function simpleExecute(statement, binds = {}, cache=true) {
	const log = new Logger().getInstance();
	try {
		log.debug(`>>>>> Enter services.database.service.simpleExecute`);
		let retryCount = 0;
		let viewPrefix = "";
		if (statement.includes("* FROM")) {
			querySplit = statement.split("* FROM");
			viewNameSplit =
				querySplit.length >= 2 ? querySplit[1].trim().split(" ") : [];
			viewPrefix = viewNameSplit.length >= 2 ? `${viewNameSplit[0]}` : "";
		}
		
		const cacheKey = generateClkCacheKey(statement, binds);
		if (cache) {
			const cachedResult = await getRedis(cacheKey);
			if (cachedResult) {
				log.info(`[Clickhouse] Query to be executed: ${statement} with binds : ${JSON.stringify(binds)}`);
				log.info(`Cache found for the clickhouse query.`);
				return JSON.parse(cachedResult);
			}
		}

		while (retryCount < MAX_RETRIES) {
			try {
                if (Object.keys(binds).length)
                    log.info(`[Clickhouse] Query to be executed: ${statement} with binds : ${JSON.stringify(binds)}`);
                else
				    log.info(`[Clickhouse] Query to be executed: ${statement}`);

				const resultSet = await client.query({
					query: statement,
					query_params: binds,
					format: "JSONEachRow",
				});
				const result = await resultSet.json();

				if (cache) {
					setRedis(
						cacheKey,
						JSON.stringify(result),
						constants.redis.dataResponseTTL
					);
				}

				log.debug(
					`<<<<< Exit services.database.service.simpleExecute successfully`
				);

				return result;
			} catch (err) {
				log.error(
					`<<<<< Exit services.database.service.simpleExecute with error ${err}`
				);
				log.info(
					`<<<<< Exit services.database.service.simpleExecute with error ${err}`
				);

				if (err.code == "ECONNRESET") {
					log.error(
						`Attempt ${retryCount + 1}: Error executing query - ${err}`
					);
					if (retryCount === MAX_RETRIES - 1) {
						log.error(`Max retry attempts reached. Failing query.`);
						throw err;
					}
					retryCount++;
					log.info(
						`Retrying query (Attempt ${retryCount + 1}) after ${RETRY_DELAY}ms`
					);
					await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
				} else throw err;
			}
		}
	} catch (err) {
		
		throw err;
	}
}


module.exports = {
    simpleExecute: simpleExecute,
    close: close,
    initialize: initialize
};
