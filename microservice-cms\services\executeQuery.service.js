
const clkdb = require('../../services/clk-database.service');
const { getDomainsQuery, getPeriodicitiesQuery, getDataClassificationsQuery } = require('./getQuery.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getDomains(domainName = '') {
  try {
    const query = await getDomainsQuery(domainName);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-cms.executeQuery.service.getPopStatsData with error ${err}`);
    throw err;
  }
}

async function getPeriodicities(){
  try{
    const query = await getPeriodicitiesQuery();
    const data = await getData(query);
    return data;
  }
  catch(err){
    log.error(`<<<<< Exited microservice-cms.executeQuery.service.getPeriodicities with error ${err}`);
    throw err;
  }
}

async function getDataClassifications(){
  try{
    const query = await getDataClassificationsQuery();
    const data = await getData(query);
    return data;
  }
  catch(err){
    log.error(`<<<<< Exited microservice-cms.executeQuery.service.getPeriodicities with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-cms.services.executeQuery.service.getData`);
    const data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-cms.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-cms.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}


module.exports = {
  getDomains,
  getPeriodicities,
  getDataClassifications
};
