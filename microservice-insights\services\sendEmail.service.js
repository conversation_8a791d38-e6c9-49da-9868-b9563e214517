const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json')
const messages = require('./messages')

async function sendInsightsEmail(req,purpose,insight={},email="",business_name="") {
    new Promise((resolve, reject) => {
        let reqBody = req.body;
    
        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });

        let businessFullname;
        let setBusinessName;
        let approverFullname;
        let setApproverName;
        let approverActName;
        
        switch(purpose){
            case 'ADD':
                const nodeTitle = reqBody.nodeTitle
                const nodeLink = reqBody.nodeLink
                const nodeInsight = reqBody.insight
                businessFullname = req.user.preferred_username.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: req.user.preferred_username,
                    subject: `Thank you for your valuable insight!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.businessAddInsightsMessage(req.user.name,nodeTitle,nodeLink,nodeInsight)
                };
                

                // approverFullname = process.env.INSIGHT_APPROVER.toLowerCase().split('@')[0];
                // setApproverName = approverFullname.split('.');
                // if (setApproverName.length > 1) {
                //     setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                // }

                // var approverMailOptions = {
                //     from: process.env.SYSTEM_MAILID,
                //     to: process.env.INSIGHT_APPROVER,
                //     subject: `Approval process for Insight!`,
                //     attachments: [
                //         {
                //             filename: 'IFP_logo.png',
                //             path: __dirname + '/images/IFP_logo.png',
                //             cid: 'ifp-logo'
                //         },
                //         {
                //             filename: 'SCAD_logo.png',
                //             path: __dirname + '/images/SCAD_logo.png',
                //             cid: 'scad-logo'
                //         },
                //     ],
                //     html: messages.approverAddInsightsMessage(setBusinessName,setApproverName,nodeTitle,nodeLink)
                // };

                // transporter.sendMail(approverMailOptions, function (error, info) {
                //     if (error) {
                //         reject(error);
                //     }
                //     if (info) {
                //         transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                //             if (error) {
                //                 reject(error);
                //             }
                //             log.info(`Email sent successfully to user ${req.user.preferred_username} \n${info.response}`);
                //             resolve();
                //         })
                //     }
                //     log.info(`Email sent successfully to Approver \n${info.response}`);
                //     resolve();
                // })

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    log.info(`Email sent successfully to user ${req.user.preferred_username} \n${info.response}`);
                    resolve();
                })

                break;

                case 'UPDATE':
                    const nodeUpdateTitle = reqBody.nodeTitle
                    const nodeUpdateLink = reqBody.nodeLink
                    const nodeUpdateInsight = reqBody.insight
                    const prevInsight =   insight.prevInsight
                    businessFullname = insight.email.toLowerCase().split('@')[0];
                    setBusinessName = businessFullname.split('.');
                    if (setBusinessName.length > 1) {
                        setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                    }
                    
                    let subject;
                    let message;

                    if (req.user.preferred_username == process.env.INSIGHT_APPROVER){
                        subject = 'Insight Updated by Approver!'
                        message = messages.businessUpdateByApproverInsightsMessage(setBusinessName,nodeUpdateTitle)
                    }
                    else{
                        subject = 'Insight Updated!'
                        message = messages.businessUpdateInsightsMessage(req.user.name,nodeUpdateTitle,nodeUpdateLink,nodeUpdateInsight,prevInsight)
                    }
                    var businessMailOptionsResponse = {
                        from: process.env.SYSTEM_MAILID,
                        to: insight.email,
                        subject: subject,
                        attachments: [
                            {
                                filename: 'IFP_logo.png',
                                path: __dirname + '/images/IFP_logo.png',
                                cid: 'ifp-logo'
                            },
                            {
                                filename: 'SCAD_logo.png',
                                path: __dirname + '/images/SCAD_logo.png',
                                cid: 'scad-logo'
                            },
                        ],
                        html: message
                    };
    
                    // approverFullname = process.env.INSIGHT_APPROVER.toLowerCase().split('@')[0];
                    // setApproverName = approverFullname.split('.');
                    // if (setApproverName.length > 1) {
                    //     setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                    // }
    
                    // var approverMailOptions = {
                    //     from: process.env.SYSTEM_MAILID,
                    //     to: process.env.INSIGHT_APPROVER,
                    //     subject: `Approval process for updated Insight!`,
                    //     attachments: [
                    //         {
                    //             filename: 'IFP_logo.png',
                    //             path: __dirname + '/images/IFP_logo.png',
                    //             cid: 'ifp-logo'
                    //         },
                    //         {
                    //             filename: 'SCAD_logo.png',
                    //             path: __dirname + '/images/SCAD_logo.png',
                    //             cid: 'scad-logo'
                    //         },
                    //     ],
                    //     html: messages.approverUpdateInsightsMessage(setBusinessName,setApproverName,nodeUpdateTitle,nodeUpdateLink)
                    // };

                    if (req.user.preferred_username == process.env.INSIGHT_APPROVER){
                        transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                            if (error) {
                                reject(error);
                            }
                            log.info(`Email sent successfully to user ${insight.email} \n${info.response}`);
                            resolve();
                        })
                    }
                    else{
                        // transporter.sendMail(approverMailOptions, function (error, info) {
                        //     if (error) {
                        //         reject(error);
                        //     }
                        //     if (info) {
                        //         transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                        //             if (error) {
                        //                 reject(error);
                        //             }
                        //             log.info(`Email sent successfully to user ${insight.email} \n${info.response}`);
                        //             resolve();
                        //         })
                        //     }
                        //     log.info(`Email sent successfully to Approver \n${info.response}`);
                        //     resolve();
                        // })

                        transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                            if (error) {
                                reject(error);
                            }
                            log.info(`Email sent successfully to user ${insight.email} \n${info.response}`);
                            resolve();
                        })

                    }
    
                    
                    break;
            
            case 'DELETE':
                const nodeDelTitle = insight.NODE_TITLE
                const nodeDelLink = insight.NODE_LINK
                const nodeDelInsight = insight.INSIGHT

                approverActName = process.env.INSIGHT_APPROVER_NAME
                
                businessFullname = insight.EMAIL.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }
                let delMessage;

                if (req.user.preferred_username == process.env.INSIGHT_APPROVER)
                    delMessage = messages.businessDeleteByApproverInsightsMessage(insight.USER_NAME,nodeDelTitle,nodeDelLink,nodeDelInsight,approverActName)
                else
                    delMessage = messages.businessDeleteInsightsMessage(req.user.name,nodeDelTitle,nodeDelLink,nodeDelInsight)

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: insight.EMAIL,
                    subject: `Insight Deleted!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: delMessage
                };

                approverFullname = process.env.INSIGHT_APPROVER.toLowerCase().split('@')[0];
                setApproverName = approverFullname.split('.');
                if (setApproverName.length > 1) {
                    setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                var approverMailOptions = {
                    from: process.env.SYSTEM_MAILID,
                    to: process.env.INSIGHT_APPROVER,
                    subject: `Insight Deleted!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.approverDeleteInsightsMessage(req.user.name,approverActName,nodeDelTitle,nodeDelLink,nodeDelInsight)
                };

                if (req.user.preferred_username == process.env.INSIGHT_APPROVER){
                    transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                        if (error) {
                            reject(error);
                        }
                        log.info(`Email sent successfully to user ${insight.EMAIL} \n${info.response}`);
                        resolve();
                    })
                }
                else{
                    transporter.sendMail(approverMailOptions, function (error, info) {
                        if (error) {
                            reject(error);
                        }
                        if (info) {
                            transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                                if (error) {
                                    reject(error);
                                }
                                log.info(`Email sent successfully to user ${insight.EMAIL} \n${info.response}`);
                                resolve();
                            })
                        }
                        log.info(`Email sent successfully to Approver \n${info.response}`);
                        resolve();
                    })
                }

                
                break;
            
            case 'APPROVE':
                approverActName = process.env.INSIGHT_APPROVER_NAME
                businessFullname = email.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                approverFullname = req.user.preferred_username.toLowerCase().split('@')[0];
                setApproverName = approverFullname.split('.');
                if (setApproverName.length > 1) {
                    setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }
                let approveInsights = insight.filter(element => element.EMAIL == email)
                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: email,
                    subject: `Insight Approval`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.businessApproveInsightsMessage(business_name,approverActName,insight[0].NODE_TITLE,insight[0].NODE_LINK,approveInsights)
                };

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        log.info(`Email sent successfully to user ${email} \n${info.response}`);
                        resolve();
                        
                    }
                })
                break;
            
            case 'REJECT':
                approverActName = process.env.INSIGHT_APPROVER_NAME
                businessFullname = email.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                approverFullname = req.user.preferred_username.toLowerCase().split('@')[0];
                setApproverName = approverFullname.split('.');
                if (setApproverName.length > 1) {
                    setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }
                let insights = insight.filter(element => element.EMAIL == email)
                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: email,
                    subject: `Insight Rejected`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.businessRejectInsightsMessage(business_name,approverActName,insights[0].NODE_TITLE,insights[0].NODE_LINK,insights)
                };

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        log.info(`Email sent successfully to user ${email} \n${info.response}`);
                        resolve();
                        
                    }
                })
                break;
            case 'REQUEST_EDIT':
                approverActName = process.env.INSIGHT_APPROVER_NAME
                businessFullname = email.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                setComments = req.body.comments
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                approverFullname = req.user.preferred_username.toLowerCase().split('@')[0];
                setApproverName = approverFullname.split('.');
                if (setApproverName.length > 1) {
                    setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                let requestEditInsights = insight.filter(element => element.EMAIL == email)

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: email,
                    subject: `Insight Edit Request`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.businessRequestEditInsightsMessage(business_name,approverActName,insight[0].NODE_TITLE,insight[0].NODE_LINK,requestEditInsights,setComments)
                };

                transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        log.info(`Email sent successfully to user ${email} \n${info.response}`);
                        resolve();
                        
                    }
                })
                break;
            case 'SUBMIT_REQUEST':
                approverActName = process.env.INSIGHT_APPROVER_NAME
                businessFullname = req.user.preferred_username.toLowerCase().split('@')[0];
                setBusinessName = businessFullname.split('.');
                approveRequesterName = req.user.name
                if (setBusinessName.length > 1) {
                    setBusinessName = setBusinessName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                var businessMailOptionsResponse = {
                    from: process.env.SYSTEM_MAILID,
                    to: req.user.preferred_username,
                    subject: `Approval Request Sent!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.businessSubmitApprovalInsightsMessage(setBusinessName,insight,approverActName)
                };

                approverFullname = process.env.INSIGHT_APPROVER.toLowerCase().split('@')[0];
                setApproverName = approverFullname.split('.');
                if (setApproverName.length > 1) {
                    setApproverName = setApproverName.map(i => i.charAt(0).toUpperCase() + i.slice(1)).join(' ');
                }

                var approverMailOptions = {
                    from: process.env.SYSTEM_MAILID,
                    to: process.env.INSIGHT_APPROVER,
                    subject: `Approval process for Insight!`,
                    attachments: [
                        {
                            filename: 'IFP_logo.png',
                            path: __dirname + '/images/IFP_logo.png',
                            cid: 'ifp-logo'
                        },
                        {
                            filename: 'SCAD_logo.png',
                            path: __dirname + '/images/SCAD_logo.png',
                            cid: 'scad-logo'
                        },
                    ],
                    html: messages.approverSubmitRequestInsightsMessage(approveRequesterName,approverActName,insight)
                };

                transporter.sendMail(approverMailOptions, function (error, info) {
                    if (error) {
                        reject(error);
                    }
                    if (info) {
                        transporter.sendMail(businessMailOptionsResponse, function (error, info) {
                            if (error) {
                                reject(error);
                            }
                            log.info(`Email sent successfully to user ${req.user.preferred_username} \n${info.response}`);
                            resolve();
                        })
                    }
                    log.info(`Email sent successfully to Approver \n${info.response}`);
                    resolve();
                })
                break;
                
        }
    });
}


module.exports = {sendInsightsEmail}