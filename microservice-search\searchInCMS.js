const { getMetaFromCMS,getUserGroupMatrixPattern } = require('../services/common-service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../config/constants.json');
require('dotenv').config();

const axios = require('axios');
const encodeUrl = require('encodeurl');
const { getGlossaryData, getExperimentalIndicatorsData,officialIndicatorSearch } = require('./services/executeQuery.service');
const { fetchScreenerNodes } = require('./services/helper');

async function getSearchData(req) {

    const { apiVersion } = req;
    const queryString = req.body.query;
    let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
    let cmsSearchUrlPath = constants.cmsGroupUrl.CMS_SEARCH_URL;
    if (apiVersion == 2) {
      cmsSearchUrlPath = constants.cmsGroupUrl.CMS_SEARCH_URL_V2;
    }
    let cmsSearchUrl = `${process.env.CMS_BASEPATH}${lang}${cmsSearchUrlPath}${queryString}`;
    
    const cmsClassificationsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_CLASSIFICATION_LIST}`;
    let cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V2;
    if (apiVersion == 2) {
        cmsDomainsUrlPath = constants.cmsGroupUrl.CMS_NAVIGATION_V3
    }
    const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${cmsDomainsUrlPath}`;

    return new Promise(async (resolve, reject) => {
        try {
            if (! queryString.trim().length){
                return resolve({
                    "query": queryString,
                    "numberOfResults": 0,
                    "result": {},
                    "suggestions": []
                })
            }
            const glossaryLang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
            cmsSearchUrl = encodeUrl(cmsSearchUrl);
            const [
                classifications, 
                queryResponse,
                navigationResponse,
                glossary
              ] = await Promise.all([
                getMetaFromCMS(req,cmsLoginUrl, cmsClassificationsUrl, req.user.groups),
                getMetaFromCMS(req,cmsLoginUrl, cmsSearchUrl, req.user.groups),
                getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups),
                getGlossaryData(glossaryLang,1,10,{},{},queryString)
              ]);
            
            let official_data=await officialIndicatorSearch(req,queryString);
            
            let nodeCount = 0;
            let excludeTypes = ["page","geospatial_analysis","scad_what_s_new","product_page","sub_theme_page","sub_domain_page","domain_page"]
            let classificationObj = {}
            let results = []
           
            let classificationByName = {}
            let classificationByKey = {}
            Object.values(classifications.classification).map(classification =>{
                classificationByName[classification.name] = classification
                classificationByKey[classification.key] = classification
                classificationObj[classification.name] = {
                    'title': classification.name,
                    'contentType' : classification.name,
                    'key': classification.key,
                    'dark_icon':`${process.env.CMS_BASEPATH_URL}${classification.icon_path}`,
                    'light_icon':`${process.env.CMS_BASEPATH_URL}${classification.light_icon_path}`,
                    'items': [],
                    'categories' : {},
                    'isSelected': false,
                    'machineName': classification.name
                }
            })

            let cmsSearchResponse = queryResponse.results;
            let contentClassKey = "content_classification";
            let categoryKey = "category";
            if (apiVersion == 2) {
              cmsSearchResponse = queryResponse;
              contentClassKey = "contentClassification";
              categoryKey = "pageCategory";
              subTitleKey = "subTitle";
            }

            if (cmsSearchResponse && cmsSearchResponse.length > 0) {
                cmsSearchResponse.forEach(element => {
                    if (element[contentClassKey]){
                        if (element[contentClassKey]=="Official Statistics"){
                            classificationObj[element[contentClassKey]].items=[]
                        }
                        if (! excludeTypes.includes(element.type) && element[contentClassKey]!="Official Statistics"){
                            if (req.baseUrl.includes("mobile") || req.originalUrl.includes("mobile")) {
                                const ECI_NODE_ID =
                                    process.env.NODE_ENV == "prod" ? "6779" : "6821";
                                if (element.id != ECI_NODE_ID) {
                                    classificationObj[element[contentClassKey]].items.push(element)
                                }
                            } else {
                                classificationObj[element[contentClassKey]].items.push(element)
                            }
                            nodeCount += 1
                            if (!(element[categoryKey]))
                                element[categoryKey] = 'Others'
                            if (!(element[categoryKey] in classificationObj[element[contentClassKey]].categories))
                                classificationObj[element[contentClassKey]].categories[element[categoryKey]] = 0;
                            classificationObj[element[contentClassKey]].categories[element[categoryKey]] += 1;
                        }
                    }
                });
            }
            if (official_data){
                for (const key in classificationObj) {
                    if (classificationObj[key].key === "official_statistics") {
                        classificationObj[key].items = official_data;
                        nodeCount += official_data.length;
                      }
                }
            }

            let experimentalKey = 'experimental_statistics'
            let officialKey = 'official_statistics'
            let reportsKey = 'reports'
            let experimentalClassificationName = null;
            let officialClassificationName = null;
            let reportsClassificationName = null;
            
            try{experimentalClassificationName=classificationByKey[experimentalKey].name}catch(exp){experimentalClassificationName=null}
            try{officialClassificationName=classificationByKey[officialKey].name}catch(exp){officialClassificationName=null}
            try{reportsClassificationName=classificationByKey[reportsKey].name}catch(exp){reportsClassificationName=null}

            await Promise.all(Object.values(classificationObj).map(async classification => {
                if ([officialClassificationName,reportsClassificationName].includes(classification.machineName)){
                    classification.categories = {}
                }
                else if (experimentalClassificationName == classification.machineName){
                    classification = await fetchScreenerNodes(queryString,navigationResponse,classification,glossaryLang, apiVersion)
                }
                results.push(classification)
                
            }))

            const classificationOrder = ['official_statistics','experimental_statistics','analytical_apps','reports']

            const  classificationComparator = (a, b) => {
                const indexA = classificationOrder.indexOf(a.key);
                const indexB = classificationOrder.indexOf(b.key);
                
                return indexA - indexB;
              };
            
            results = results.sort(classificationComparator);


            
            results.push({
                contentType: glossaryLang=='EN'? "Glossary":"قائمة المصطلحات",
                isSelected: false,
                machineName: "Glossary",
                title: glossaryLang=='EN'? "Glossary":"قائمة المصطلحات",
                items: glossary.map(g=>{
                    let item = {
                      title: g[`TITLE_${glossaryLang}`],
                      subtitle: g[`DESCRIPTION_${glossaryLang}`],
                      domains: [g[`TOPIC_${glossaryLang}`]],
                      type: "glossary",
                    };
                    if (apiVersion == 2) {
                      item = { ...item, subTitle: item.subtitle };
                      delete item.subtitle;
                    }
                    return item;
                })
            })

            nodeCount += glossary.length
            results = results.filter(result => result.items.length > 0)
            if (results.length){
                return resolve({
                    "query": queryString,
                    "numberOfResults": nodeCount,
                    "result": {
                        "contentTypes": results
                    }
                })

            }
            else{
                return resolve({
                            "query": queryString,
                            "numberOfResults": 0,
                            "result": {},
                            "suggestions": []
                        })
            }
            
        } catch (err) {
            
            log.error(`Error while processing Search data from CMS - ${cmsSearchUrl}${queryString} ERROR: ${err}`);
            reject([422, err]);
        }
    });
}

module.exports = { getSearchData };
