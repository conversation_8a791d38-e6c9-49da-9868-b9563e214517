const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function acceptTermsAndConditionsQuery(userData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_USERS_TC ( USER_ID, TC_ID, TC_ACCEPT_DT, INSERT_USER, INSERT_DT, USER_ORGANIZATION, LANGUAGE ) VALUES ( '${userData.userId}', '${userData.tcVersion}', '${userData.tcAcceptDate}','${process.env.DB_USER}', '${userData.tcAcceptDate}', '${userData.organization}', '${userData.language}' )`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function checkAcceptedTermsAndConditionsQuery(userId,organization,tcVersion) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_USERS_TC WHERE USER_ID = '${userId}' AND USER_ORGANIZATION = '${organization}' AND TC_ID = '${tcVersion}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateUserLangPreferrenceQuery(userData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_USERS_TC SET LANGUAGE= '${userData.language}', LAST_UPDATE_DT= '${userData.updatedDate}' where USER_ID = '${userData.userId}' AND TC_ID= '${userData.tcVersion}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.updateUserLangPreferrenceQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = { acceptTermsAndConditionsQuery, checkAcceptedTermsAndConditionsQuery, updateUserLangPreferrenceQuery };
