const mailer = require('nodemailer');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const nunjucks = require('nunjucks');

async function sendShareDashboardEmail(data) {
    new Promise((resolve, reject) => {

        const transporter = mailer.createTransport({
            host: process.env.SMTP_HOST,
            port: process.env.SMTP_PORT,
            secure: false, //true for 465, false for other ports
            logger: true,
            debug: true,
            tls: {
                // do not fail on invalid certs
                rejectUnauthorized: false
            }
        });


        const recepient = data.recepient
        const sender = data.senderName
        

        var shareDashboardMailOptionsResponse = {
            from: process.env.SYSTEM_MAILID,
            to: recepient,
            subject: `Dashboard shared by ${sender}`,
            attachments: [
                {
                    filename: "new-message-banner.png",
                    path: process.cwd() + "/emails/email-new-message-banner.png",
                    cid: "new-message-banner"
                },
            ],
            html: nunjucks.render(
                "microservice-dashboard/dashboard-share.njk",
                { ...data, emailBanner: "new-message-banner" }
            )
        };

        transporter.sendMail(shareDashboardMailOptionsResponse, function (error, info) {
            if (error) {
                reject(error);
            }

            log.info(`Email sent successfully to ${recepient} \n${info.response}`);
            resolve();
        })
    });
}

module.exports = { sendShareDashboardEmail }