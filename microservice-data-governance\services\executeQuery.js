const db = require("../../services/database.service");
const clkdb = require("../../services/clk-database.service");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const {
  getGovnernanceChartQuery,
  getDataSceinceInputQuery,
  getDataSceinceOutputQuery,
  generateUseCaseQuery,
  generateDataSourceQuery,
  generateDataScienceQuery,
  generateWorkflowDetailsQuery,
  generateBayaanSvsQuery,
  generateStatisticalIndicatorQuery,
  genarateUseCaseDataCountQuery,
  generateDataSourceDataCountQuery,
  generateDataSceinceDataCountQuery,
  generateWorkflowDetailsDataCountQuery,
  generateBayaanSvDataCountQuery,
  getSCADproductionCountQuery,
  getBayaanProductionQuery,
  getNotApprovedQuery,
  getProductionCountQuery,
  getNotApprovedCountQuery,
  notApprovedDetailQuery,
  generateUseCaseDropDownValuesQuery,
  generateDataSourceDropDownValuesQuery,
  generateDataSceinceDropDownValuesQuery,
  generateWorkflowDetailsDropDownValuesQuery,
  generateSvCalenderData,
  generateEventDetail,
  generateUseCaseFrequencyQuery,
  generateStatisticalIndicatordropdownQuery,
  generateStatisticalIndicatorProductsQuery,
  generateStatisticalIndicatorstatusQuery,
  generateUseCaseDropDownTypeValuesQuery,
  generateUseCaseStatusQuery,
  generateBayaanSvStatusQuery
} = require("./getQuery.service");

async function getDataGovernanceHomeChart() {
  try {
    const query = getGovnernanceChartQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataGovernanceHomeChart with error ${err}`
    );
    throw err;
  }
}

async function getDataSceinceInputData(page, limit, filterName) {
  try {
    const query = getDataSceinceInputQuery(page, limit, filterName);
    const data = await getData(query.query, query.binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataSceinceInputData with error ${err}`
    );
    throw err;
  }
}

async function getDataSceinceOutputData(page, limit, filterName) {
  try {
    const query = getDataSceinceOutputQuery(page, limit, filterName);
    const data = await getData(query.query, query.binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataSceinceOutputData with error ${err}`
    );
    throw err;
  }
}

async function getUseCaseData(page, limit, filter) {
  try {
    const tableDataQuery = generateUseCaseQuery(page, limit, filter);
    const tableDataCountQuery = genarateUseCaseDataCountQuery(
      filter.USECASE_TYPE_EN
    );
    const results = await Promise.all([
      getData(tableDataQuery.query, tableDataQuery.binds),
      getData(tableDataCountQuery.query, tableDataQuery.binds),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getUseCaseData with error ${err}`
    );
    throw err;
  }
}

async function getDataSourceData(page, limit, filterName) {
  try {
    const tableDataQuery = generateDataSourceQuery(page, limit, filterName);
    const tableDataCountQuery = generateDataSourceDataCountQuery();
    const results = await Promise.all([
      getData(tableDataQuery.query, tableDataQuery.binds),
      getData(tableDataCountQuery),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataSourceData with error ${err}`
    );
    throw err;
  }
}

async function getDataScienceData(page, limit, filterName) {
  try {
    const tableDataQuery = generateDataScienceQuery(page, limit, filterName);
    const tableDataCountQuery = generateDataSceinceDataCountQuery();
    const results = await Promise.all([
      getData(tableDataQuery.query, tableDataQuery.binds),
      getData(tableDataCountQuery),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataScienceData with error ${err}`
    );
    throw err;
  }
}

async function getWorkflowDetailsData(page, limit, filterName) {
  try {
    const tableDataQuery = generateWorkflowDetailsQuery(
      page,
      limit,
      filterName
    );
    const tableDataCountQuery = generateWorkflowDetailsDataCountQuery();
    const results = await Promise.all([
      getData(tableDataQuery.query, tableDataQuery.binds),
      getData(tableDataCountQuery),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getWorkflowDetailsData with error ${err}`
    );
    throw err;
  }
}

async function getBayaanSvsData(page, limit, filter) {
  try {
    const tableDataQuery = generateBayaanSvsQuery(page, limit, filter);
    const tableDataCountQuery = generateBayaanSvDataCountQuery();
    const results = await Promise.all([
      getData(tableDataQuery.query, tableDataQuery.binds),
      getData(tableDataCountQuery),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getBayaanSvsData with error ${err}`
    );
    throw err;
  }
}

async function getStatisticalIndicatorData(page, limit, filters) {
  try {
    const query = generateStatisticalIndicatorQuery(page, limit, filters);
    const data = await getData(query.query, query.binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getStatisticalIndicatorData with error ${err}`
    );
    throw err;
  }
}

async function getComparisonChartData() {
  try {
    const scadProduction = getSCADproductionCountQuery();
    const bayaanProduction = getBayaanProductionQuery();
    const notApproved = getNotApprovedQuery();
    const NotApprovedList = getProductionCountQuery();
    const notApprovedCount = getNotApprovedCountQuery();
    const notApprovedTableQuery = notApprovedDetailQuery();
    const results = await Promise.all([
      getData(scadProduction),
      getData(bayaanProduction),
      getData(notApproved),
      getData(NotApprovedList),
      getData(notApprovedCount),
      getData(notApprovedTableQuery),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getComparisonChartData with error ${err}`
    );
    throw err;
  }
}

async function getUseCaseDropDownData() {
  try {
    const useCaseQuery = generateUseCaseDropDownValuesQuery();
    const typeQuery = generateUseCaseDropDownTypeValuesQuery();
    const useCasetatusQuery = generateUseCaseStatusQuery();
    const useCaseFrequencyQuery = generateUseCaseFrequencyQuery();
    const results = await Promise.all([
      getData(useCaseQuery),
      getData(typeQuery),
      getData(useCasetatusQuery),
      getData(useCaseFrequencyQuery),
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getUseCaseDropDownData with error ${err}`
    );
    throw err;
  }
}

async function getDataSourceDropDownData() {
  try {
    const query = generateDataSourceDropDownValuesQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataSourceDropDownData with error ${err}`
    );
    throw err;
  }
}

async function getDataScienceDropDownData() {
  try {
    const query = generateDataSceinceDropDownValuesQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getDataScienceDropDownData with error ${err}`
    );
    throw err;
  }
}

async function getWorkflowDetailsDropDownData() {
  try {
    const query = generateWorkflowDetailsDropDownValuesQuery();
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getWorkflowDetailsDropDownData with error ${err}`
    );
    throw err;
  }
}

async function getBayaanSvsDropDownData() {
  try {
    const statusQuery = generateBayaanSvStatusQuery();
    const results = await getData(statusQuery)
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getBayaanSvsDropDownData with error ${err}`
    );
    throw err;
  }
}

async function getStatisticalIndicatorDropDownData() {
  try {
    const hirarchyQuery = generateStatisticalIndicatordropdownQuery();
    const productsQuery = generateStatisticalIndicatorProductsQuery();
    const statusQuery = generateStatisticalIndicatorstatusQuery();

    const results = await Promise.all([
      getData(hirarchyQuery),
      getData(productsQuery),
      getData(statusQuery)
    ]);
    return results;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getStatisticalIndicatorDropDownData with error ${err}`
    );
    throw err;
  }
}

async function getSvCalenderData(startDate, endDate) {
  try {
    const { query, binds } = generateSvCalenderData(startDate, endDate);
    const data = await getData(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getSvCalenderData with error ${err}`
    );
    throw err;
  }
}

async function getEventDetail(limit, offset, filter) {
  try {
    const { query, binds } = generateEventDetail(limit, offset, filter);
    const data = await getData(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.data-governance.executeQuery.service.getSvCalenderData with error ${err}`
    );
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(
      `>>>>> microservice.data-governance.executeQuery.service.executeQuery.service.getData`
    );
    let data = await clkdb.simpleExecute(query, binds);
    log.debug(
      `<<<<< Exit microservice.data-governance.executeQuery.service.executeQuery.service.getData successfully`
    );
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice.data-governance.executeQuery.service.executeQuery.service.getData with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getDataGovernanceHomeChart,
  getDataSceinceInputData,
  getDataSceinceOutputData,
  getUseCaseData,
  getDataSourceData,
  getDataScienceData,
  getWorkflowDetailsData,
  getBayaanSvsData,
  getStatisticalIndicatorData,
  getComparisonChartData,
  getUseCaseDropDownData,
  getDataSourceDropDownData,
  getDataScienceDropDownData,
  getWorkflowDetailsDropDownData,
  getBayaanSvsDropDownData,
  getStatisticalIndicatorDropDownData,
  getSvCalenderData,
  getEventDetail,
};
