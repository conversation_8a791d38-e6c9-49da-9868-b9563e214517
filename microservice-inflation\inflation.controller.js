const Logger = require('scad-library').logger;
require('dotenv').config();
const log = new Logger().getInstance();

const constants = require('../config/constants.json');
const { IFPError } = require('../utils/error');

const { getCombinationData, getForecastCombinationData, getMetricsModelData, getMetricsTrainTestData, getBaselineForecastData } = require('./services/executeQuery');


async function getYearlyForecastBaseline(req) {
    log.debug(`>>>>>Entered microservice.inflation.controller.getYearlyForecastBaseline`);

    try {
        let baselineData = [];
        let cpiActuals = []
        let cpiForecasted = []
        let actuals = []
        let forecasted = []

        let forecastBaselineData = await getBaselineForecastData()
        forecastBaselineData.forEach(element=> {
            if (element.INDICATOR_ID == "CPI_EMIRATE_OF_ABU_DHABI" && element.TYPE == "REAL") {
                cpiActuals.push({
                    YEAR: (new Date(element.OBS_DT)).getFullYear(),
                    VALUE: element.VALUE
                })
            }
            if (element.INDICATOR_ID == "CPI_EMIRATE_OF_ABU_DHABI" && element.TYPE == "FORECAST") {
                cpiForecasted.push({
                    YEAR: (new Date(element.OBS_DT)).getFullYear(),
                    VALUE: element.VALUE
                })
            }
            if (element.INDICATOR_ID == "INFLATION_EMIRATE_OF_ABU_DHABI" && element.TYPE == "REAL") {
                actuals.push({
                    YEAR: (new Date(element.OBS_DT)).getFullYear(),
                    VALUE: element.VALUE
                })
            }
            if (element.INDICATOR_ID == "INFLATION_EMIRATE_OF_ABU_DHABI" && element.TYPE == "FORECAST") {
                forecasted.push({
                    YEAR: (new Date(element.OBS_DT)).getFullYear(),
                    VALUE: element.VALUE
                })
            }
        })
        cpiActuals.forEach(element => {
            baselineData.push({
                YEAR: element.YEAR,
                CPI_ACTUALS: element.VALUE,
                CPI_FORECASTED: null,
                ACTUALS: actuals.find((ele)=>ele.YEAR==element.YEAR) ? actuals.find((ele)=>ele.YEAR==element.YEAR).VALUE : null,
                FORECASTED: null,
            });
        });
        
        cpiForecasted.forEach(element => {
            baselineData.push({
                YEAR: element.YEAR,
                CPI_ACTUALS: null,
                CPI_FORECASTED: cpiForecasted.find((ele)=>ele.YEAR==element.YEAR) ? cpiForecasted.find((ele)=>ele.YEAR==element.YEAR).VALUE : null,
                ACTUALS: null,
                FORECASTED: forecasted.find((ele)=>ele.YEAR==element.YEAR) ? forecasted.find((ele)=>ele.YEAR==element.YEAR).VALUE : null,
            });
        })
        return baselineData  
    
    } catch (err) {
        log.error(`<<<<< Exited microservice.inflation.controller.getYearlyForecastBaseline with error ${err} `)
        throw err;
    }
}

async function getForecastData(req) {
    log.debug(`>>>>>Entered microservice.inflation.controller.getYearlyForecastBaseline`);

    try {
        let drivers = {
            oilPrice: req.query.Oil_Price ? req.query.Oil_Price : 1,
            governmentSpending: req.query.Government_Spending ? req.query.Government_Spending : 1,
            geoPoliticalIndex: req.query.Geo_political_index ? req.query.Geo_political_index : 1,
            singaporeGDP: req.query.Singapore_GDP ? req.query.Singapore_GDP : 1,
            tacIndex: req.query.TAC_Index ? req.query.TAC_Index : 1,
            financeFuturePMI: req.query.Finance_Future_PMI ? req.query.Finance_Future_PMI : 1,
            inrReer: req.query.INR_REER ? req.query.INR_REER : 1,
            usDollarPrice: req.query.US_Dollar_Price ? req.query.US_Dollar_Price : 1,
        }

        let combinationData = await getCombinationData(drivers)
        if (!combinationData.length)
            throw new IFPError(404,"No comboId found for the given set of drivers",{"drivers":drivers})
        let combinationId = combinationData[0]["PARAMETER_COMBO_ID"]

        let results = await getForecastCombinationData(combinationId)
        var data = [];
        results.forEach(element =>{
            data.push({
                "RUN_SEQ_ID": element.RUN_SEQ_ID,
                "INDICATOR_ID": element.INDICATOR_ID,
                "PARAMETER_COMBO_ID": element.PARAMETER_COMBO_ID,
                "RUN_DT": element.RUN_DT,
                "VALUE": element.VALUE,
                "OBS_DT": (new Date(element.OBS_DT)).getFullYear(),
                "TYPE": element.TYPE,
                "OPT": element.OPT,
                "UNIT": element.UNIT,
                "INSERT_DT": element.INSERT_DT,
                "INSERT_USER_ID": element.INSERT_USER_ID
            });
        });
        return data

    } catch (err) {
        log.error(`<<<<< Exited microservice.inflation.controller.getForecastData with error ${err} `)
        throw err;
    }
}

async function getMetricsModel(req) {
    log.debug(`>>>>>Entered microservice.inflation.controller.getMetricsModel`);

    try {

        let results = await getMetricsModelData();

        let data = [];
        results.forEach(element=> {
            data.push({
                Year: element.YEAR,
                Metrics: element.METRICS,
                Train: element.TRAIN,
                Test: element.TEST,
                Deviations: element.DEVIATIONS,
            });
        })

        return data;

    } catch (err) {
        log.error(`<<<<< Exited microservice.inflation.controller.getMetricsModel with error ${err} `)
        throw err;
    }
}

async function getMetricsTrainTest(req) {
    log.debug(`>>>>>Entered microservice.inflation.controller.getMetricsTrainTest`);
    try {
        let results = await getMetricsTrainTestData();
        let data = [];
        results.forEach(element=> {
            data.push({
                Train: element.TRAIN,
                Test: element.TEST,
            });
        })
        return data;

    } catch (err) {
        log.error(`<<<<< Exited microservice.inflation.controller.getMetricsTrainTest with error ${err} `)
        throw err;
    }
}


module.exports = {
    getYearlyForecastBaseline,
    getForecastData,
    getMetricsModel,
    getMetricsTrainTest
};
