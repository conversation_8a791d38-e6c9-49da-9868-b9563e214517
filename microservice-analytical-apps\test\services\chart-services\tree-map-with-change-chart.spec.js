const treeChart = require("../../../services/chart-services/tree-map-with-change-chart");

describe('Service', () => {
    afterEach(() => {
        jest.restoreAllMocks();
    });
    test('should get json response from getTreeSeries - success', async () => {
        const mockVisualization = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                }
            ]
        }
        const mockData = [{
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: null,
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 102.59962351526501,
            VALUE_LL: 97.4986397394764,
            VALUE_UL: 109.186692771471,
            OBS_DT: '2020-12-01',
            TYPE: 'NOWCAST',
            SECTOR: 'Business services'
        },
        {
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: 'E0000',
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 105.254073144911,
            VALUE_LL: 93.67612509897097,
            VALUE_UL: 122.094724848097,
            OBS_DT: '2022-02-01',
            TYPE: 'FORECAST',
            SECTOR: 'Business services'
        },
        {
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: null,
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 85.51685054756452,
            VALUE_LL: 79.26120474785789,
            VALUE_UL: 94.6291017796259,
            OBS_DT: '2020-12-01',
            TYPE: 'NOWCAST',
            SECTOR: 'Leisure'
        },
        {
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: 'E0000',
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 92.36019125684874,
            VALUE_LL: 80.35336639345839,
            VALUE_UL: 112.67943333335501,
            OBS_DT: '2022-02-01',
            TYPE: 'FORECAST',
            SECTOR: 'Leisure'
        }]
        const mockTreeSeries = [
            {
                "SECTOR_SIZE": 0.12971480190753898,
                "SECTOR": "Business services",
                "OBS_DT": "20200401"
            },
            {
                "SECTOR_SIZE": 0.010952708311379001,
                "SECTOR": "Leisure",
                "OBS_DT": "20200401"
            }
        ]
        const expectedResponse = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": "2022-02-01",
                            "SECTOR": "Business services",
                            "VALUE": 2.59,
                            "SECTOR_SIZE": 12.97
                        }
                    ]
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": "2022-02-01",
                            "SECTOR": "Leisure",
                            "VALUE": 8,
                            "SECTOR_SIZE": 1.1
                        }
                    ]
                }
            ]
        }
        jest.spyOn(treeChart, "getTreeSeries");
        const result = await treeChart.getTreeSeries(mockVisualization, mockData, mockTreeSeries);
        //expect(result).toEqual(expectedResponse);
    });

    test('should get json response from getTreeSeries - success data unavaialable', async () => {
        const mockVisualization = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                }
            ]
        }
        const mockData = []
        const mockTreeSeries = [
            {
                "SECTOR_SIZE": 0.12971480190753898,
                "SECTOR": "Business services",
                "OBS_DT": "20200401"
            },
            {
                "SECTOR_SIZE": 0.010952708311379001,
                "SECTOR": "Leisure",
                "OBS_DT": "20200401"
            }
        ]
        const expectedResponse = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": null,
                            "SECTOR": null,
                            "VALUE": null,
                            "SECTOR_SIZE": 12.97
                        }
                    ]
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": null,
                            "SECTOR": null,
                            "VALUE": null,
                            "SECTOR_SIZE": 1.1
                        }
                    ]
                }
            ]
        }
        jest.spyOn(treeChart, "getTreeSeries");
        const result = await treeChart.getTreeSeries(mockVisualization, mockData, mockTreeSeries);
        //expect(result).toEqual(expectedResponse);
    });

    test('should get json response from getTreeSeries - failure invalid chart configuration', async () => {
        const mockVisualization = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "series": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                }
            ]
        }
        const mockData = []
        const mockTreeSeries = []
        try {
            jest.spyOn(treeChart, "getTreeSeries");
            await treeChart.getTreeSeries(mockVisualization, mockData, mockTreeSeries);
        } catch (err) {
            //expect(err[0]).toEqual(422);
        }
    });

    test('should get json response from getTreeSeries - success handled null data', async () => {
        const mockVisualization = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                }
            ]
        }
        const mockData = [
            {
                INDICATOR_ID: 'RAPID',
                PARAMETER_COMBO_ID: null,
                RUN_SEQ_ID: 13,
                RUN_DT: '20210201',
                INSERT_DT: '20210201',
                INSERT_USER_ID: 'ETL_USER',
                LAST_UPD_DT: '20210201',
                LAST_UPD_USER_ID: 'ETL_USER',
                LANGUAGE_CD: 'EN',
                VALUE: 102.59962351526501,
                VALUE_LL: 97.4986397394764,
                VALUE_UL: 109.186692771471,
                OBS_DT: '2020-12-01',
                TYPE: 'NOWCAST',
                SECTOR: 'Business services'
            },
            {
                INDICATOR_ID: 'RAPID',
                PARAMETER_COMBO_ID: null,
                RUN_SEQ_ID: 13,
                RUN_DT: '20210201',
                INSERT_DT: '20210201',
                INSERT_USER_ID: 'ETL_USER',
                LAST_UPD_DT: '20210201',
                LAST_UPD_USER_ID: 'ETL_USER',
                LANGUAGE_CD: 'EN',
                VALUE: 102.59962351526501,
                VALUE_LL: 97.4986397394764,
                VALUE_UL: 109.186692771471,
                OBS_DT: '2020-12-01',
                TYPE: 'NOWCAST TEST',
                SECTOR: 'Business services'
            },
            {
                INDICATOR_ID: 'RAPID',
                PARAMETER_COMBO_ID: 'E0000',
                RUN_SEQ_ID: 13,
                RUN_DT: '20210201',
                INSERT_DT: '20210201',
                INSERT_USER_ID: 'ETL_USER',
                LAST_UPD_DT: '20210201',
                LAST_UPD_USER_ID: 'ETL_USER',
                LANGUAGE_CD: 'EN',
                VALUE: 105.254073144911,
                VALUE_LL: 93.67612509897097,
                VALUE_UL: 122.094724848097,
                OBS_DT: '2022-02-01',
                TYPE: 'FORECAST',
                SECTOR: 'Business services'
            },
            {
                INDICATOR_ID: 'RAPID',
                PARAMETER_COMBO_ID: null,
                RUN_SEQ_ID: 13,
                RUN_DT: '20210201',
                INSERT_DT: '20210201',
                INSERT_USER_ID: 'ETL_USER',
                LAST_UPD_DT: '20210201',
                LAST_UPD_USER_ID: 'ETL_USER',
                LANGUAGE_CD: 'EN',
                VALUE: 85.51685054756452,
                VALUE_LL: 79.26120474785789,
                VALUE_UL: 94.6291017796259,
                OBS_DT: '2020-12-01',
                TYPE: 'NOWCAST',
                SECTOR: null
            },
            null]
        const mockTreeSeries = []
        const expectedResponse = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": "2022-02-01",
                            "SECTOR": "Business services",
                            "VALUE": 2.59
                        }
                    ]
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": null,
                            "SECTOR": null,
                            "VALUE": null
                        }
                    ]
                }
            ]
        }
        jest.spyOn(treeChart, "getTreeSeries");
        const result = await treeChart.getTreeSeries(mockVisualization, mockData, mockTreeSeries);
        //expect(result).toEqual(expectedResponse);
    });
    test('should get json response from getTreeSeries - success data unavaialable', async () => {
        const mockVisualization = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    }
                }
            ]
        }
        const mockData = [{
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: null,
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 102.59962351526501,
            VALUE_LL: 97.4986397394764,
            VALUE_UL: 109.186692771471,
            OBS_DT: '2020-12-01',
            TYPE: 'NOWCAST',
            SECTOR: 'Business services'
        },
        {
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: 'E0000',
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 105.254073144911,
            VALUE_LL: 93.67612509897097,
            VALUE_UL: 122.094724848097,
            OBS_DT: '2022-02-01',
            TYPE: 'FORECAST',
            SECTOR: 'Business services'
        },
        {
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: null,
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 85.51685054756452,
            VALUE_LL: 79.26120474785789,
            VALUE_UL: 94.6291017796259,
            OBS_DT: '2020-12-01',
            TYPE: 'NOWCAST',
            SECTOR: 'Leisure'
        },
        {
            INDICATOR_ID: 'RAPID',
            PARAMETER_COMBO_ID: 'E0000',
            RUN_SEQ_ID: 13,
            RUN_DT: '20210201',
            INSERT_DT: '20210201',
            INSERT_USER_ID: 'ETL_USER',
            LAST_UPD_DT: '20210201',
            LAST_UPD_USER_ID: 'ETL_USER',
            LANGUAGE_CD: 'EN',
            VALUE: 92.36019125684874,
            VALUE_LL: 80.35336639345839,
            VALUE_UL: 112.67943333335501,
            OBS_DT: '2022-02-01',
            TYPE: 'FORECAST',
            SECTOR: 'Leisure'
        }]
        const mockTreeSeries = [
            {
                "SECTOR_SIZE": 0.12971480190753898,
                "SECTOR": "Business services",
                "OBS_DT": "20200401"
            },
            {
                "SECTOR_SIZE": 0.010952708311379001,
                "SECTOR": null,
                "OBS_DT": "20200401"
            }
        ]
        const expectedResponse = {
            "id": "tree-chart-economy-sector-non-oil-indicator",
            "type": "tree-map-with-change-chart",
            "sortOrder": 3,
            "viewName": "VW_RI_FORECAST_CONSTANT",
            "comboIdTable": "DIM_RI_PARAM_COMBO",
            "sectorSizeViewName": "VW_STAT_INDICATORS",
            "sectorSizeIndicatorId": "COI_SECTOR_PROP_VALUE_ADDED",
            "xAxisLabel": "Other non-oil sectors (Government, Utilities, and Household Production) are not included in this forecast.",
            "seriesMeta": [
                {
                    "id": "business-services",
                    "label": "Business Services",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "BUSINESS SERVICES"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": "2022-02-01",
                            "SECTOR": "Business services",
                            "VALUE": 2.59,
                            "SECTOR_SIZE": 12.97
                        }
                    ]
                },
                {
                    "id": "leisure",
                    "label": "Leisure",
                    "color": "rgba(56, 101, 255, 0.3)",
                    "type": "forecast",
                    "dimension": {
                        "SECTOR": "LEISURE"
                    },
                    "valueAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "changeAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },
                    "changeSubtitleAccessor": {
                        "type": "value",
                        "path": "SECTOR"
                    },
                    "proportionAccessor": {
                        "type": "value",
                        "path": "SECTOR_SIZE"
                    },
                    "data": [
                        {
                            "OBS_DT": "2022-02-01",
                            "SECTOR": "Leisure",
                            "VALUE": 8
                        }
                    ]
                }
            ]
        }
        jest.spyOn(treeChart, "getTreeSeries");
        const result = await treeChart.getTreeSeries(mockVisualization, mockData, mockTreeSeries);
        //expect(result).toEqual(expectedResponse);
    });
});