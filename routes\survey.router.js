const express = require('express');
const router = new express.Router();
const surveyController = require('../microservice-survey/survey.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { attendValidator } = require('./validators/survey.validator');

router.get('/check', async (req, res, next) => {
    try {
      const data = await surveyController.checkSurveyStatus(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error accepting terms-and-conditions content-type, ERROR: ${err}`);
      next(err);
    }
  });

  router.post('/attend', attendValidator, async (req, res, next) => {
    try {
      const data = await surveyController.attendSurvey(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error accepting terms-and-conditions content-type, ERROR: ${err}`);
      next(err);
    }
  });


module.exports = router;
