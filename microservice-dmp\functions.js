const {
  processLineChartData,
} = require("../microservice-statistics-insights/services/chart-services/line-chart");
const {
  getAllFilterDataFromDB,
  getValuesData,
} = require("../microservice-statistics-insights/services/getGraphData");
const constants = require("../config/constants.json");
const { IFPError } = require("../utils/error");
const { getAssetTableDataWrapper } = require("./services/asset.service");

/**
 *
 * @param {Error | import("axios").AxiosError} error
 * @param {*} actor
 */
function handleAxiosError(error, actor) {
  if (error.isAxiosError) {
    if (error.response) {
      throw new IFPError(
        error.response?.status,
        `${actor} failed with: ${JSON.stringify(error.response.data)}`
      );
    }
  } else {
    throw error;
  }
}

async function processVisualization(req, indicatorData, visualizations, assetMap, shareToken) {
  const counter = [];
  indicatorData.indicatorVisualizations.visualizationsMeta = [];
  // const operations = visualizations.map(async (visualization) => {
  for (let visualization of visualizations) {
    let results;
    let yearlyData;
    try {
      let isFilterPanelEnabled = indicatorData.filterPanel
        ? indicatorData.filterPanel.isEnabled
        : "";
      // if (Object.keys(censusNodeDuplicationMap).includes(originalNode)){
      //     visualization.isDuplicated = true
      // }

      // if(isFilterPanelEnabled && query.minimal == 'true'){
      //     let filterBy = {}
      //     indicatorData.filterPanel.properties.forEach(property=>{
      //         filterBy[property.path] = property.default
      //     })
      //     if (Object.keys(filterBy).length > 0){
      //         visualization.filterBy = filterBy
      //     }
      // }
      // results = await getGraphData( // VW_STATISTICAL_INDICATORS
      //   visualization,
      //   indicatorData.type,
      //   isFilterPanelEnabled,
      //   indicatorData.filterPanel
      // );
      results = await getAssetTableDataWrapper(req, shareToken, assetMap["data_table"])

      if (visualization.yearlyData) {
        yearlyData = await getAllFilterDataFromDB(visualization.yearlyData);
        yearlyData.forEach((e) => {
          e["YEAR"] = "ALL";
        });
      }
      results =
        yearlyData && yearlyData.length > 0
          ? results.concat(yearlyData)
          : results;
      // if (Object.keys(censusNodeDuplicationMap).includes(originalNode)){
      //     indicatorData.id = originalNode
      // }
    } catch (err) {
      console.error(
        `Error executing getGraphData from statistics-insights.controller ${err}`
      );
      throw err;
    }

    if (
      visualization.type === "dual-line-bar-chart" ||
      visualization.type === "sunburst-with-line-chart" ||
      visualization.type === "double-line-bar-chart"
    ) {
      chartType = visualization.subtype;
    } else {
      chartType = visualization.type;
    }

    if (indicatorData.minLimitYAxis) {
      visualization["minLimitYAxis"] = indicatorData.minLimitYAxis;
      delete indicatorData.minLimitYAxis;
    }
    switch (chartType) {
      case "line-chart": {
        if (indicatorData.enableCompare) {
          let compareMeta;
          let compareData;
          let compareCode = [];
          indicatorData["benchmarks"] = [];
          try {
            compareMeta = JSON.parse(indicatorData.compare_data).compareMeta;
            compareData = await getCompareSeries(compareMeta);
            compareMeta.seriesMeta.forEach((element) => {
              if (element.iconId.length > 0) {
                compareCode.push(element.iconId);
              }
            });
            if (compareCode.length > 0) {
              let compareCodes = compareCode.map((i) => i).join(",");
              const cmsCompareUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsUrl.CMS_COUNTRY_LIST}${compareCodes}`;
              const iconData = await getMetaFromCMS(
                req,
                cmsLoginUrl,
                cmsCompareUrl,
                req.user.groups
              );
              compareMeta.seriesMeta.forEach((obj) => {
                iconData.forEach((element) => {
                  if (
                    obj.iconId.toLowerCase() ===
                    element.country_id.toLowerCase()
                  ) {
                    indicatorData.benchmarks.push({
                      country_id: obj.id.toUpperCase(),
                      country_name: obj.label,
                      country_flag: element.country_flag,
                      description: element.description,
                      attachment: element.attachment,
                    });
                  }
                });
              });
              visualization.seriesMeta[0]["isDefault"] = true;
            } else {
              compareMeta.seriesMeta.forEach((series) => {
                indicatorData.benchmarks.push({
                  country_id: series.id.toUpperCase(),
                  country_name: series.label,
                  country_flag: "",
                  description: "",
                  attachment: "",
                });
              });
            }
            visualization.seriesMeta = visualization.seriesMeta.concat(
              compareMeta.seriesMeta
            );
            if (compareData.length > 0) {
              results = results.concat(compareData);
            }
          } catch (err) {
            this.log.error(
              `Error preparing compare data in statistics-insights.controller ${err}`
            );
          }
        }

        let data;
        try {
          data = await processLineChartData(
            results,
            visualization,
            indicatorData.type,
            indicatorData.maxPointLimit
          );
        } catch (err) {
          this.log.error(
            `Error executing processLineChartData from statistics-insights.controller ${err}`
          );
          throw err;
        }
        if (
          indicatorData.indicatorValues &&
          indicatorData.indicatorValues.valuesMeta &&
          indicatorData.indicatorValues.valuesMeta.length > 0 &&
          !newValuesMeta
        ) {
          try {
            await getValuesData(
              indicatorData.indicatorValues.valuesMeta,
              data,
              visualization,
              results
            );
          } catch (err) {
            this.log.error(
              `Error executing getValuesData from statistics-insights.controller line-chart${err}`
            );
            throw err;
          }
        }

        indicatorData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "stacked-vertical-bar-chart": {
        let data;
        try {
          data = await getStackedVerticalBarData(
            results,
            visualization,
            indicatorData.maxPointLimit
          );
        } catch (err) {
          this.log.error(
            `Error executing getStackedVerticalBarData from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        if (
          indicatorData.indicatorValues.valuesMeta &&
          indicatorData.indicatorValues.valuesMeta.length > 0 &&
          !newValuesMeta
        ) {
          try {
            await getValuesData(
              indicatorData.indicatorValues.valuesMeta,
              results,
              visualization,
              results
            );
          } catch (err) {
            this.log.error(
              `Error executing getValuesData from statistics-insights.controller stacked-vertical-bar-chart ${err}`
            );
            reject(err);
          }
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        this.log.debug(`Processed data for chartId  successfully`);
        counter.push(1);
        break;
      }
      case "single-horizontal-bar-chart": {
        let data;
        try {
          data = await getSingleHorizontalBar(
            results,
            visualization,
            indicatorData.type
          );
        } catch (err) {
          this.log.error(
            `Error executing getSingleHorizontalBar from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        if (
          indicatorData.indicatorValues.valuesMeta &&
          indicatorData.indicatorValues.valuesMeta.length > 0 &&
          !newValuesMeta
        ) {
          try {
            await getValuesData(
              indicatorData.indicatorValues.valuesMeta,
              results,
              visualization
            );
          } catch (err) {
            this.log.error(
              `Error executing getValuesData from statistics-insights.controller single-horizontal-bar-chart ${err}`
            );
            reject(err);
          }
        }
        if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
          await getColors(data);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        this.log.debug(`Processed data for chartId successfully`);
        counter.push(1);
        break;
      }
      case "sunburst-with-change-chart": {
        let data;
        try {
          data = await getSunBurstSeries(visualization, results);
        } catch (err) {
          this.log.error(
            `Error executing getSunBurstSeries from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        if (
          indicatorData.indicatorValues.valuesMeta &&
          indicatorData.indicatorValues.valuesMeta.length > 0 &&
          !newValuesMeta
        ) {
          try {
            await getValuesData(
              indicatorData.indicatorValues.valuesMeta,
              data,
              visualization
            );
          } catch (err) {
            this.log.error(
              `Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`
            );
            reject(err);
          }
        }
        if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
          await getColors(data);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "single-circular-bar-chart": {
        let data;
        try {
          data = await getSingleCircularBarSeries(visualization, results);
        } catch (err) {
          this.log.error(
            `Error executing getSingleCircularBarSeries from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        if (
          indicatorData.indicatorValues.valuesMeta &&
          indicatorData.indicatorValues.valuesMeta.length > 0 &&
          !newValuesMeta
        ) {
          try {
            await getValuesData(
              indicatorData.indicatorValues.valuesMeta,
              data,
              visualization
            );
          } catch (err) {
            this.log.error(
              `Error executing getValuesData from statistics-insights.controller single-circular-bar-chart ${err}`
            );
            reject(err);
          }
        }
        if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
          await getColors(data);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "single-pie-chart": {
        let data;
        try {
          data = await getSinglePieSeries(
            visualization,
            results,
            indicatorData.type
          );
          if (
            indicatorData.indicatorValues.valuesMeta &&
            indicatorData.indicatorValues.valuesMeta.length > 0 &&
            !newValuesMeta
          ) {
            try {
              await getValuesData(
                indicatorData.indicatorValues.valuesMeta,
                data,
                visualization
              );
            } catch (err) {
              this.log.error(
                `Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`
              );
              reject(err);
            }
          }
        } catch (err) {
          this.log.error(
            `Error executing getSinglePieSeries from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "single-pie-chart-with-filter": {
        let data;
        try {
          data = await getSinglePieSeriesWithFilter(
            visualization,
            results,
            indicatorData.type
          );
          if (
            indicatorData.indicatorValues.valuesMeta &&
            indicatorData.indicatorValues.valuesMeta.length > 0 &&
            !newValuesMeta
          ) {
            try {
              await getValuesData(
                indicatorData.indicatorValues.valuesMeta,
                data,
                visualization
              );
            } catch (err) {
              this.log.error(
                `Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`
              );
              reject(err);
            }
          }
        } catch (err) {
          this.log.error(
            `Error executing getSinglePieSeries from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "single-scatterplot-chart": {
        let data;
        try {
          data = await getScatterPlotSeries(visualization, results);
          if (
            indicatorData.indicatorValues.valuesMeta &&
            indicatorData.indicatorValues.valuesMeta.length > 0 &&
            !newValuesMeta
          ) {
            try {
              await getValuesData(
                indicatorData.indicatorValues.valuesMeta,
                data,
                visualization
              );
            } catch (err) {
              this.log.error(
                `Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`
              );
              reject(err);
            }
          }
        } catch (err) {
          this.log.error(
            `Error executing getScatterPlotSeries from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "table-view": {
        let data;
        try {
          data = await getTableSeries(visualization, results);
        } catch (err) {
          this.log.error(
            `Error executing table view from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        counter.push(1);
        break;
      }
      case "pyramid-chart": {
        let data;
        try {
          data = await getPyramidData(
            results,
            visualization,
            indicatorData.type
          );
        } catch (err) {
          this.log.error(
            `Error executing getPyramidData from statistics-insights.controller ${err}`
          );
          reject(err);
        }
        if (
          indicatorData.indicatorValues.valuesMeta &&
          indicatorData.indicatorValues.valuesMeta.length > 0 &&
          !newValuesMeta
        ) {
          try {
            await getValuesData(
              indicatorData.indicatorValues.valuesMeta,
              results,
              visualization,
              results
            );
          } catch (err) {
            this.log.error(
              `Error executing getValuesData from statistics-insights.controller pyramid-chart ${err}`
            );
            reject(err);
          }
        }
        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
        this.log.debug(`Processed data for chartId  successfully`);
        counter.push(1);
        break;
      }
      case "tree-map-with-change-chart": {
        try {
          const data = await getTreeSeries(visualization, results);
          if (
            indicatorData.indicatorValues.valuesMeta.length > 0 &&
            !newValuesMeta
          ) {
            await getValuesData(indicatorData.indicatorValues.valuesMeta, data);
          }
          if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
            await getColors(data);
          }
          this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
          counter.push(1);
        } catch (err) {
          reject(err);
        }
        break;
      }
      default: {
        this.log.debug(`Chart type not available`);
        counter.push(1);
        break;
      }
    }

    try {
      if (indicatorData.type == "coi" || indicatorData.type == "scad") {
        await this.getModelMetrics();
        indicatorData.indicatorVisualizations.visualizationsMeta.forEach(
          (meta) => {
            if (meta.type == "line-chart") {
              if (indicatorData.type == "coi") {
                meta.seriesMeta.forEach((series) => {
                  if (series.id.includes("forecast")) {
                    indicatorData.updated = moment(
                      series.xMax,
                      "YYYY-MM-DD"
                    ).format("DD/MM/YYYY");
                    indicatorData.updatedDateFromDB = true;
                    return;
                  }
                });
              } else {
                indicatorData.updated = moment(
                  meta.seriesMeta[0].xMax,
                  "YYYY-MM-DD"
                ).format("DD/MM/YYYY");
                indicatorData.updatedDateFromDB = true;
              }
            }
          }
        );
      }
    } catch (exp) {}

    if (
      indicatorData.type == "official_statistics" ||
      indicatorData.type == "innovative_statistics"
    )
      indicatorData.type = "scad";
    // if (counter.length === indicatorData.indicatorVisualizations.visualizationsMeta.length) {
    //   setRedis(
    //     cmsCacheKey,
    //     JSON.stringify(this.graphData),
    //     constants.redis.cmsResponseTTL,
    //     req.headers
    //   );
    //   return indicatorData;
    // }
    // });
  }
  return indicatorData;
}

module.exports = {
  handleAxiosError,
  processVisualization,
};
