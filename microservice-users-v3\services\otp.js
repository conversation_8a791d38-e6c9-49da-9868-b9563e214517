const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const { setGlobalRedis, getGlobalRedis , delGlobalRedis} = require('../../services/redis.service');
const { IFPError } = require('../../utils/error');
const { sendEmail } = require("./email/sendEmail.service");

function generateOTP() {
  return Math.floor(1000 + Math.random() * 9000).toString();
}

const sendOTP = async (emailData, otp) => {
    try {

      await sendEmail(emailData)
      // Store the OTP in Redis with an expiration time of 10 minutes (600 seconds)
      const otpKey = `OTP_${process.env.NODE_ENV}_${emailData.recepientEmail}`
      await setGlobalRedis(otpKey,otp,600);
  
      log.info(`OTP sent to ${emailData.recepientEmail}`);
    } catch (error) {
      log.error('Error sending OTP email:', error);
      throw new IFPError(500,'Failed to send OTP email');
    }
  };

  const verifyOTP = async (email, otp,requestId,delete_otp=false) => {
    try {
        const otpKey = `OTP_${process.env.NODE_ENV}_${email}`
        const storedOTP = await getGlobalRedis(otpKey);
        if (!storedOTP) {
            log.info(`No OTP for this email: ${email}, Request ID: ${requestId}`)
            return false; // No OTP record found for this email or OTP has expired
        }
        if (storedOTP === otp.toString()) {
            if (delete_otp)
              await delGlobalRedis(otpKey); // Invalidate OTP after successful verification
            const verifiedKey = `verified_${otpKey}`
            await setGlobalRedis(verifiedKey,true,600);
            log.info(`Successfully verified OTP for email: ${email}, Request ID: ${requestId}`)
            return true; // OTP is valid
        }
        return false; // OTP does not match
    } catch (error) {
        log.error(`Error verifying OTP, Request ID: ${requestId}`, error);
        return false;
    }
  };

  const deleteVerifyFlag = async (email) => {
    const otpKey = `OTP_${process.env.NODE_ENV}_${email}`
    const verifiedKey = `verified_${otpKey}`
    await delGlobalRedis(verifiedKey);
  }

module.exports = {
  generateOTP,
  sendOTP,
  verifyOTP,
  deleteVerifyFlag
};
