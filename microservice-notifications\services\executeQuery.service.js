const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');

const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  listNotificationsDataQuery, getNotificationMappingsDataQuery, subscribeNotificationsDataQuery,subscribeEmailNotificationsDataQuery, 
  unsubscribeNotificationsDataQuery, unsubscribeEmailNotificationsDataQuery, readNotificationDataDataQuery, getCompareNodesByIdDataQuery, getIndicatorsMetaDataQuery, getMappingsDataQuery, getMappingsDataNoPageQuery, getNotificationCountQuery } = require('./getQuery.service');

/**
 * Function to get notifications
 * @param {*} nodeId - node id
 * @param {*} userEmail - user email 
 */
async function listNotificationsData(userEmail, status,lang, page, limit) {
  return new Promise((resolve, reject) => {
    listNotificationsDataQuery(userEmail, status,lang, page, limit).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.listNotificationsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.listNotificationsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to get notifications count
 * @param {*} userEmail - user email 
 */
async function getNotificationCount(userEmail) {
  try {
    const query = getNotificationCountQuery(userEmail);
    const data = await getData(query);
    return data;
  } catch(err) {
    log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getNotificationCount with error ${err}`);
    throw err
  }
}

/**
 * Function to get notifications mapping
 * @param {*} userEmail - user email 
 */
async function getNotificationMappingsData(userEmail) {
  return new Promise((resolve, reject) => {
    getNotificationMappingsDataQuery(userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getNotificationMappingsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getNotificationMappingsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to insert notifications mappings
 * @param {*} nodeId - node id 
 * @param {*} userEmail - user email
 */
async function subscribeNotificationsData(nodeId, userEmail, contentType, appType,viewName='', isEmail) {
  return new Promise((resolve, reject) => {
    subscribeNotificationsDataQuery(nodeId, userEmail, contentType, appType,viewName, isEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.subscribeNotificationsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.subscribeNotificationsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to insert notifications mappings
 * @param {*} nodeId - node id 
 * @param {*} userEmail - user email
 */
async function subscribeEmailNotificationsData(nodeId, userEmail, contentType, appType, isEmail) {
  return new Promise((resolve, reject) => {
    subscribeEmailNotificationsDataQuery(nodeId, userEmail, contentType, appType, isEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.subscribeNotificationsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.subscribeNotificationsData with error ${err}`);
      reject(err);
    })
  })
}


/**
 * Function to insert notifications mappings
 * @param {*} nodeId - node id 
 * @param {*} userEmail - user email
 */
async function unsubscribeNotificationsData(nodeId, userEmail) {
  return new Promise((resolve, reject) => {
    unsubscribeNotificationsDataQuery(nodeId, userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.unsubscribeNotificationsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.unsubscribeNotificationsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to insert notifications mappings
 * @param {*} nodeId - node id 
 * @param {*} userEmail - user email
 */
async function unsubscribeEmailNotificationsData(nodeId, userEmail, contentType, appType, isEmail) {
  return new Promise((resolve, reject) => {
    unsubscribeEmailNotificationsDataQuery(nodeId, userEmail, contentType, appType, isEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.subscribeNotificationsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.subscribeNotificationsData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to read notification
 * @param {*} nodeId - node id 
 * @param {*} userEmail - user email
 */
async function readNotificationData(notificationId, userEmail,isShareApp) {
  return new Promise((resolve, reject) => {
    readNotificationDataDataQuery(notificationId, userEmail,isShareApp).then((results) => {
      getData(results.query,results.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.readNotificationData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.readNotificationData with error ${err}`);
      reject(err);
    })
  })
}

async function getCompareNodesById(notificationId, userEmail) {
  return new Promise((resolve, reject) => {
    getCompareNodesByIdDataQuery(notificationId, userEmail).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.readNotificationData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.readNotificationData with error ${err}`);
      reject(err);
    })
  })
}
async function readNotificationData(notificationId, userEmail,isShareApp) {
  return new Promise((resolve, reject) => {
    readNotificationDataDataQuery(notificationId, userEmail,isShareApp).then((results) => {
      getData(results.query,results.binds).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.readNotificationData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.readNotificationData with error ${err}`);
      reject(err);
    })
  })
}

async function getIndicatorsMetaData(indicatorIds,lang) {
  return new Promise((resolve, reject) => {
    getIndicatorsMetaDataQuery(indicatorIds,lang).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery`);
      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getIndicatorMetaDataQuery with error ${err}`);
      reject(err);
    })
  })
}

async function getMappingsData(userEmail,page,limit) {
  return new Promise((resolve, reject) => {
    getMappingsDataQuery(userEmail,page,limit).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getMappingsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getMappingsData with error ${err}`);
      reject(err);
    })
  })
}

async function getMappingsNoPageData(userEmail,page,limit) {
  return new Promise((resolve, reject) => {
    getMappingsDataNoPageQuery(userEmail,page,limit).then((query) => {
      getData(query).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getMappingsNoPageData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-notifications.executeQuery.service.getMappingsNoPageData with error ${err}`);
      reject(err);
    })
  })
}

async function getData(query,binds={}) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-notifications.services.executeQuery.service.getData`);
    db.simpleExecute(query,binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-notifications.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-notifications.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = {
  listNotificationsData, getNotificationMappingsData, subscribeNotificationsData, subscribeEmailNotificationsData, unsubscribeNotificationsData, unsubscribeEmailNotificationsData, readNotificationData,
  getCompareNodesById,getIndicatorsMetaData,getMappingsData, getMappingsNoPageData, getNotificationCount
};
