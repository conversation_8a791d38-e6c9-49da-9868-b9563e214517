const axios = require("axios").default;

const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
require('dotenv').config();
const { sendEmail } = require("../services/email/sendEmail.service");
const { getRoleName } = require("../helper");

/**
 * Sends Bayaan platform welcome email to the user.
 *
 * Contains standard platform login url along with links and
 * QR code to apple store and user specific android download
 * url and QR code.
 * Fails silently
 * @param {*} userInfo
 * @param {*} mftDetails
 */
async function sendWelcomeEmail(userInfo, mftDetails) {
  try {
    const emailData = {
      recepientName: userInfo.name,
      recepientEmail: userInfo.email,
      recipientRole: getRoleName(userInfo.role),
      userType: "PRODUCT_ENGAGEMENT",
      emailType: "USER_WELCOME_EMAIL",
      subject: "Bayaan - Welcome to Bayaan",
      bayaanWebUrl: process.env.PLATFORM_BASEPATH + "/login",
      appleStoreUrl: "https://apps.apple.com/app/bayaan-gov/id6475820691",
      androidMFTUrlAr: mftDetails.android_mft_url_ar,
      androidMFTQrCodeAr: mftDetails.android_mft_url_ar,
      androidMFTPassword: mftDetails.android_mft_password,
      androidMFTUrl: mftDetails.android_mft_url,
      androidMFTUrlQrCode: mftDetails.android_mft_url,
      currentYear: new Date().getFullYear(),
      isDgUser: userInfo.role == "DG" ? true : false,
      isPrimarySuperuser: userInfo.role == "SUPERUSER" ? true : false,
    };
    await sendEmail(emailData);
  } catch (error) {
    log.error(
      `Something went wrong when during welcome email for: ${userInfo.email}: ${error}`
    );
  }
}

/**
 * Create user in Bayaan mobile backend and sends welcome
 * email with unique download links (Android specific) to
 * the user's registered email.
 *
 * This step is required for making the onboarded user
 * be able to login to Bayaan Mobile.
 *
 * TODO: track failed attempts and retry via cron
 * @param {{
 * id: string,
 * name: string,
 * email: string,
 * entityId: string,
 * designation: string,
 * phoneNumber?: string | number,
 * role: string
 * }} user
 */
async function syncBayaanMobile(user) {
  const mobielSyncPath = "/api/v1/users/sync/";
  const url = process.env.MOBILE_BASEPATH + mobielSyncPath;
  const headers = {
    "Content-Type": "application/json",
    "x-api-key": process.env.MOBILE_API_KEY,
  };
  const body = {
    name: user.name,
    email: user.email,
    role: "normal-user",
    uae_pass_uuid: user.id,
    user_type: "g",
    entity: user.entityId,
    designation: user.designation,
    phone: user.phoneNumber,
    primary_super_user: user.role == "PRIMARY_SUPERUSER" ? true : false,
    dg_user: user.role == "DG" ? true : false,
  };

  await axios
    .post(url, body, { headers })
    .then(async (response) => {
      const { android_mft_url, android_mft_url_ar, android_mft_password } =
        response.data;
      const userDetails = {
        name: user.name,
        email: user.email,
        role: user.role,
      };
      const mftDetails = {
        android_mft_url,
        android_mft_url_ar,
        android_mft_password,
      };
      await sendWelcomeEmail(userDetails, mftDetails);
    })
    .catch((error) => {
      log.error(
        `Something went wrong when syncing user ${user.email}: ${error}`
      );
    })
    .finally(() => {
      log.info(`Sync attempt complete for: ${user.email}`);
    });
}

async function deleteMobileUser(userId) {
  const host = process.env.MOBILE_BASEPATH;
  const path = "/api/v1/users/sync/";
  const url = host + path + userId;
  const xAPIKey = process.env.MOBILE_API_KEY;
  await axios
    .delete(url, {
      headers: {
        "X-API-KEY": xAPIKey,
      },
    })
    .then((response) => {
      if (response.status == 200) {
        log.info(`User: ${userId} successfully deleted from mobile backend`);
      }
    })
    .catch((error) => {
      log.error(
        `Something went wrong during deletion of user ${userId}: ${error}`
      );
    });
}

module.exports = {
  deleteMobileUser,
  syncBayaanMobile,
};
