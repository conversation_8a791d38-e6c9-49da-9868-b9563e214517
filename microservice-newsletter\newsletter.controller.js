const Logger = require('scad-library').logger;
const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const constants = require('../config/constants.json');

const log = new Logger().getInstance();
/**
 * function to get newsletter content from CMS
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function getNewsletters(req) {
  log.debug('>>>>>Entered newsletter-microservice.newsletter.controller.getNewsletters');
  return new Promise(async (resolve, reject) => {
    try{
      const queryParams = req.query || {};
      // const lang = req.headers["accept-language"] || "en";
      const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      // If language is "en", setting it to an empty string
      // const langPrefix = lang === "en" ? "" : lang;

      const cmsNewsLetterUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_NEWSLETTER_URL}?${new URLSearchParams(queryParams).toString()}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

      // Fetching data from CMS
      const cmsResponse = await getMetaFromCMS(req, cmsLoginUrl, cmsNewsLetterUrl, req.user.groups)

      // Define keys to modify in CMS response
      const cmsPathKeys = ["image", "publicationAttachment"]
      // Prefixing paths with base path URL
      cmsResponse.forEach(item => {
        Object.keys(item).forEach(key => {
          if (cmsPathKeys.includes(key) && item[key]) {
            item[key] = `${process.env.CMS_BASEPATH_URL}${item[key]}`;
          }
        })
      })
      return resolve(cmsResponse)
    }
    catch(err){
      reject(err)
    }
  });
}

module.exports = { getNewsletters };
