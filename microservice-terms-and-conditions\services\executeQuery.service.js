const db = require('../../services/database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const { acceptTermsAndConditionsQuery, checkAcceptedTermsAndConditionsQuery, updateUserLangPreferrenceQuery } = require('./getQuery.service');

/**
 * Function to get user accepted terms and conditions flag from db values provided in userData
 * @param {*} userData - user details object 
 */
async function getAcceptedTermsAndConditions(userId,organization,tcVersion) {
  return new Promise((resolve, reject) => {
    checkAcceptedTermsAndConditionsQuery(userId,organization,tcVersion).then((query) => {
      getData(query).then(async (data) => {
        resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.getAcceptedUserData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.getAcceptedUserData with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to accept terms and condition by the user and insert values to db provided in userData
 * @param {*} userData - user details object 
 */
async function acceptUserTermsAndConditions(userData) {
  return new Promise((resolve, reject) => {
    acceptTermsAndConditionsQuery(userData).then((query) => {
      getData(query).then((data) => {
        resolve(data);
      }).catch(err => {
        
        log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.userAcceptedTermsAndConditions with error ${err}`);
        reject(err);
      })
    }).catch(err => {
      
      log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.userAcceptedTermsAndConditions with error ${err}`);
      reject(err);
    })
  })
}

/**
 * Function to update lang preferrence by the user and update values to db provided in userData
 * @param {*} userData - user details object 
 */
 async function updateUserLangPreferrence(userData) {
  return new Promise((resolve, reject) => {
    updateUserLangPreferrenceQuery(userData).then((query) => {
      getData(query).then((data) => {
        resolve(data);
      }).catch(err => {
        
        log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.updateUserLangPreferrence with error ${err}`);
        reject(err);
      })
    }).catch(err => {
      
      log.error(`<<<<< Exited microservice-terms-and-conditions.executeQuery.service.updateUserLangPreferrence with error ${err}`);
      reject(err);
    })
  })
}

async function getData(query) {
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-terms-and-conditions.services.executeQuery.service.getData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-terms-and-conditions.services.executeQuery.service.getData with error ${err}`);
        log.error(`Error Executing Query:- ${query}`);
        reject([423, err]);
      })
  });
}

module.exports = { getAcceptedTermsAndConditions, getData, acceptUserTermsAndConditions, updateUserLangPreferrence };
