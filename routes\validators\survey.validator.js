const { IFPError } = require("../../utils/error");

const attendValidator = (req,res,next)=>{
    const requiredKeys = [
        "id",
        "title_en",
        "title_ar",
        "desc_en",
        "desc_ar",
        "launch_date",
        "feedback",
        "overall_rating",
        "queries"
    ];

    const missingKeys = requiredKeys.filter(key => !(key in req.body));
    if (missingKeys.length > 0) {
        throw new IFPError(400,`Missing keys: ${missingKeys.join(', ')}`)
    }

    if (Array.isArray(req.body.queries) && req.body.queries.length > 0) {
        const queryRequiredKeys = [
            "query_en",
            "query_ar",
            "response",
            "question_feedback"
        ];

        for (const query of req.body.queries) {
            const missingQueryKeys = queryRequiredKeys.filter(key => !(key in query));
            if (missingQueryKeys.length > 0) {
                throw new IFPError(400,`Missing keys in a query object: ${missingQueryKeys.join(', ')}`)
            }
        }
    } else {
            throw new IFPError(400,`The 'queries' key is either not an array or is empty.`)
    }
    
    next()
}

module.exports = {
    attendValidator
}