const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  getRegionDataQuery,
  getAllFiltersDataQuery,
  getDistinctGenderDataQuery,
  getDistinctCitizenshipDataQuery,
  getDistinctMaritalStatusDataQuery,
  getDistinctAttainmentTypeDataQuery,
  // getDistinctHouseHoldTypeDataQuery,
  getDistinctBuildingTypeDataQuery,
  getDistinctBuildingUseDataQuery,
  getDistinctUnitTypeDataQuery,
  getDistinctUnitUseDataQuery,
  getSummaryDataQuery,
  getPopulationSummaryDataQuery,
  getLaborForceSummaryDataQuery,
  getRealEstateSummaryDataQuery,
  getCensusAccessDataQuery
} = require('./getQuery.service');

/**
 * Retrieves glossary domains
 * @param {*} lang - Language
 */
async function getregionsData(lang) {
  try {
    const query = await getRegionDataQuery(lang);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getregionsData with error ${err}`);
    throw err;
  }
}

async function getAllFiltersData(params) {
  try {
    const geoQuery = await getAllFiltersDataQuery(params);
    const geoData = await getData(geoQuery);
    const genderQuery = await getDistinctGenderDataQuery(params);
    const genderData = await getData(genderQuery);
    const citizenQuery = await getDistinctCitizenshipDataQuery(params);
    const citizenData = await getData(citizenQuery);
    const maritalQuery = await getDistinctMaritalStatusDataQuery(params);
    const maritalData = await getData(maritalQuery);
    const attainmentQuery = await getDistinctAttainmentTypeDataQuery(params);
    const attainmentData = await getData(attainmentQuery); 
    // const houseHoldQuery = await getDistinctHouseHoldTypeDataQuery(params);
    // const houseHoldData = await getData(houseHoldQuery);
    const buildingsTypeQuery = await getDistinctBuildingTypeDataQuery(params);
    const buildingsTypeData = await getData(buildingsTypeQuery);
    const buildingsUseQuery = await getDistinctBuildingUseDataQuery(params);
    const buildingsUseData = await getData(buildingsUseQuery);

    
    const unitsUseQuery = await getDistinctUnitUseDataQuery(params);
    const unitsUseData = await getData(unitsUseQuery);

    const unitsTypeQuery = await getDistinctUnitTypeDataQuery(params);
    const unitsTypeData = await getData(unitsTypeQuery);
    

    return {
      "geo": geoData, 
      "gender": genderData,
      "citizens": citizenData,
      "marital": maritalData,
      "attainment": attainmentData,
      // "houseHold": houseHoldData,
      "buildingsType": buildingsTypeData,
      "buildingsUse": buildingsUseData,
      "unitsUse": unitsUseData,
      "unitsType": unitsTypeData
    };
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getAllFilters with error ${err}`);
    throw err;
  }
}

async function getSummaryData(params) {
  try {
    const query = await getSummaryDataQuery(params);
    log.info(query);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getSummaryData with error ${err}`);
    throw err;
  }
}

async function getPopulationSummaryData(params) {
  try {
    const query = await getPopulationSummaryDataQuery(params);
    log.info(query);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getPopulationSummaryData with error ${err}`);
    throw err;
  }
}

async function getLaborForceSummaryData(params) {
  try {
    const query = await getLaborForceSummaryDataQuery(params);
    log.info(query);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getLaborForceSummaryData with error ${err}`);
    throw err;
  }
}

async function getRealEstateSummaryData(params) {
  try {
    const query = await getRealEstateSummaryDataQuery(params);
    log.info(query);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getRealEstateSummaryData with error ${err}`);
    throw err;
  }
}

async function getCensusAccessData(email, enabled) {
  try {
    const { query, binds } = await getCensusAccessDataQuery(email, enabled);
    log.info(query);
    const data = await getData(query, binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-geospatial-revamp.executeQuery.service.getCensusAccessData with error ${err}`);
    throw err;
  }
}

/**
 * Retrieves data for a given query
 * @param {*} query - Query to be executed
 * @param {*} binds - Binds variables for the query
 */
async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    const data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getregionsData,
  getAllFiltersData,
  getSummaryData,
  getPopulationSummaryData,
  getLaborForceSummaryData,
  getRealEstateSummaryData,
  getCensusAccessData
};
