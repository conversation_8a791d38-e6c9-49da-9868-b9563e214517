const Logger = require("scad-library").logger;
require("dotenv").config();
const {
  getDashboardListData,
  getDashboardDetailData,
  createDashboardData,
  createDashboardNodesData,
  editDashboardData,
  deleteDashboardNodesData,
  deleteDashboardData,
  getDashboardDetailWithThumbnailData,
  createShareDashboardData,
  createShareDashboardNodesData,
  getDashboardShareDetailData,
  getShareDashboardListData,
  deleteShareDashboardData,
  getAppsRequestData,
} = require("./services/executeQuery");
const uuid = require("uuid");
const { IFPError } = require("../utils/error");
const { sendShareDashboardEmail } = require("./services/sendEmail.service");
const {
  saveDashboardDataService,
  updateDashboardDataService,
  getDashboardCardDataService,
} = require("./services/dashboard-card-data.service");

class DashboardBuilder{
  constructor(req){
    this.lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
    this.userEmail = req.user.preferred_username;
    this.log = new Logger().getInstance();
  }

  /**
   * Process node properties and cleans up response.
   * @param {Array} dashboardNodes An array of dashboard nodes.
   * @returns {Array} An array of cleaned nodes with processed properties field.
   * @throws {Error} Throws an error if processing nodes fail.
   */
  processDasboardNodes(dashboardNodes){
    try{
      return dashboardNodes.map(d=>{
        delete d.name;
        delete d.id;
        d.properties = JSON.parse(d.properties.toString('utf8'))
        return d
      })
    }
    catch(err){
      this.log.error(`Error while processing the nodes ${err}`)
      throw err;
    }
  }

  processDasboardList(dashboards){
    try{
      return dashboards.map(d=>{
        delete d.totalCount
        if (d.logo)
          d.logo = {
            content:d.logo.toString('base64'),
            type:d.logoType
          }
        if (d.thumbnailLight)
          d.thumbnailLight = {
            content:d.thumbnailLight.toString('base64'),
            type:d.thumbnailLightType
          }
        if (d.thumbnailDark)
          d.thumbnailDark = {
            content:d.thumbnailDark.toString('base64'),
            type:d.thumbnailDarkType
          }
        delete d.imageType
        delete d.thumbnailLightType
        delete d.thumbnailDarkType
        return d
      })
    }
    catch(err){
      this.log.error(`Error while processing the dashboards ${err}`)
      throw err;
    }
  }


  /**
   * Group nodes into an object with content types as keys.
   * @param {Array} ungroupNodes A flattened array of nodes.
   * @returns {Object} An object with content types as keys and array of nodes as its values.
   * @throws {Error} Throws an error if grouping nodes fail.
   */
  groupNodes(ungroupNodes){
    return ungroupNodes.reduce((acc, node) => {

      if (!acc[node.contentType]) {
        acc[node.contentType] = [];
      }

      acc[node.contentType].push({
        id: node.nodeId,
        group: node.nodeGroup,
        properties: node.properties,
        nodeRequest: node.requested?node.requested:false
      });

      return acc;
    }, {});
  }

  /**
   * Ungroup nodes into a flattened array of nodes.
   * @param {Object} groupedNodes An object with content types as keys and value as array of nodes.
   * @returns {Object} A flattened array of nodes with each node having content type key
   * @throws {Error} Throws an error if ungrouping nodes fail.
   */
  ungroupNodes(groupedNodes){
    return Object.entries(groupedNodes).reduce((acc, [contentType, nodes]) => {
      const nodesList = nodes.map(node => {
        return {
          id: node.id,
          contentType: contentType,
          properties: node.properties
        };
      });
      return acc.concat(nodesList);
    }, []);
  }

  async getDashboardsList(req){
    try{
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const search = req.query.search
      const sort = req.query.sort

      let dashboardData = await getDashboardListData(this.userEmail,page,limit,search,sort)
      let totalCount;

      if (dashboardData.length)
        totalCount = dashboardData[0].totalCount
      else
        totalCount = 0

      let results = {
        totalCount: totalCount,
        data: this.processDasboardList(dashboardData)
      }
      return results
    }
    catch(exp){
      this.log.error(`Error while executing getDashboardsList ${exp}`)
      throw exp
    }
  }

  async getDashboardDetail(req){
    try{
      const dashboard = {
        id: req.params.id,
        userEmail: this.userEmail
      }
      let data = await getDashboardDetailData(dashboard)
      if (data.length<1)
        throw new IFPError(404,`The dashboard with id ${dashboard.id} is not found`)

      data = this.processDasboardList(data)
      let results = {
        id: dashboard.id,
        name: data[0].name,
        logo: data[0].logo
      }

      data = this.processDasboardNodes(data)
      const groupedNodes = this.groupNodes(data)
      results.nodes=groupedNodes

      return results
    }
    catch(exp){
      this.log.error(`Error while executing getDashboardDetail ${exp}`)
      throw exp
    }
  }

  async createDashboard(req){
    try{
      const dashboard = {
        id: uuid.v4().toString(),
        name: req.body.name,
        userEmail: this.userEmail,
        logo: req.logo,
        thumbnailLight: req.thumbnailLight,
        thumbnailDark: req.thumbnailDark
      }

      const nodesData = this.ungroupNodes(req.body.nodes)
      await createDashboardData(dashboard)
      await createDashboardNodesData(dashboard.id,nodesData)
      return {"message":"The dashboard has been created successfully","data":dashboard.id}
    }
    catch(exp){
      this.log.error(`Error while executing createDashboard ${exp}`)
      throw exp
    }
  }

  async editDashboard(req){
    try{
      const dashboard = {
        id: req.params.id,
        name: req.body.name,
        userEmail: this.userEmail,
        logo: req.logo,
        thumbnailLight: req.thumbnailLight,
        thumbnailDark: req.thumbnailDark
      }
      await Promise.all([
        editDashboardData(dashboard),
        deleteDashboardNodesData(dashboard.id)
      ])
      const nodesData = this.ungroupNodes(req.body.nodes)
      await createDashboardNodesData(dashboard.id,nodesData)
      return {"message":"The dashboard has been edited successfully"}
    }
    catch(exp){
      this.log.error(`Error while executing editDashboard ${exp}`)
      throw exp
    }
  }

  async deleteDashboard(req){
    try{
      const dashboard = {
        id: req.params.id,
        userEmail: this.userEmail
      }
      await Promise.all([
        deleteDashboardData(dashboard),
        deleteDashboardNodesData(dashboard.id)
      ])
      return {"message":"The dashboard has been deleted successfully"}
    }
    catch(exp){
      this.log.error(`Error while executing deleteDashboard ${exp}`)
      throw exp
    }
  }

  async shareDashboard(req){
    try{
      const userEmail = req.user.preferred_username
      let shareComment = req.body.comment
      const userName = req.user.name
     

      let shareList = req.body.shareList?req.body.shareList:[]

      if (shareList.includes(userEmail))
        throw new IFPError(400,`You can't share your app to yourself`)

      if (shareList.length<1)
        throw new IFPError(400,'Please provide a list of users to be shared')

      let sharedDashboards = []

      const dashboardPromises = req.body.dashboards.map(async shareDashboard=>{
          const shareDashboardData = {
            id: shareDashboard,
            userEmail: this.userEmail
          }
          const shareId = String(uuid.v4());

          this.log.info(`${req.user.preferred_username} | Fetching dashboard data for shareId: ${shareId}`);
          let dashboardDetailData = await getDashboardDetailWithThumbnailData(shareDashboardData)

          const dashboard = dashboardDetailData[0]
          let shareDashboardName = dashboard.name

          sharedDashboards.push({id:shareId,link:`${process.env.PLATFORM_BASEPATH}/store/dashboard-builder?id=${shareId}&share=1`,name:shareDashboardName})

          let thumbnailDark = {
            buffer:dashboard.thumbnailDark,
            type:dashboard.thumbnailDarkType
          }
          let thumbnailLight = {
            buffer:dashboard.thumbnailLight,
            type:dashboard.thumbnailLightType
          }

          let logo = {
            buffer:dashboard.logo,
            type:dashboard.logoType
          }

          this.log.info(`${req.user.preferred_username} | Creating shareDashboard record`);
          const createPromises = shareList.map(async recepient=>{
            const shareDashboard = {
              id: shareId,
              name: shareDashboardName,
              logo: logo,
              thumbnailLight: thumbnailLight,
              thumbnailDark: thumbnailDark,
              shareEmail:userEmail,
              shareName: userName,
              recepientEmail: recepient,
              token: shareId
            }
            await createShareDashboardData(shareDashboard);
          })
          await Promise.all(createPromises)
          let nodesData = dashboardDetailData.map(nodeData=>{
            return {
              id: nodeData.nodeId,
              contentType: nodeData.contentType,
              nodeGroup: nodeData.nodeGroup,
              properties: nodeData.properties
        }
      })
          await createShareDashboardNodesData(shareId,nodesData)
        })

      await Promise.all(dashboardPromises)

      shareList.forEach(recepient =>{
        let shareEmailData = {
          recepient: recepient,
          shareComment: shareComment,
          shareData: sharedDashboards,
          sender: userEmail,
          senderName: userName
        }
        sendShareDashboardEmail(shareEmailData)
      })

      return {"message":"The dashboard has been shared successfully"}
    }
    catch(exp){
      this.log.error(`Error while executing shareDashboard ${exp}`)
      throw exp
    }
  }

  async getSharedDashboard(req) {
    try{
      const dashboard = {
        id: req.params.id,
        userEmail: this.userEmail
      }

      const [shareRecepientData,shareSenderData,appRequestData] = await Promise.all([
          getDashboardShareDetailData(dashboard,'recepient'),
          getDashboardShareDetailData(dashboard,'sender'),
          getAppsRequestData(this.userEmail)
        ])
    

      let recepientAccess = true
      let senderAccess = true

      if (!shareRecepientData.length){
        recepientAccess = false
      }

      if (!shareSenderData.length){
        senderAccess = false
      }

      if (recepientAccess == false && senderAccess == false)
        throw new IFPError(403,`You don't have access to this share app`)

      let data;

      if (recepientAccess)
        data = shareRecepientData
      if (senderAccess)
        data = shareSenderData

      data = this.processDasboardList(data)
      let results = {
        id: dashboard.id,
        name: data[0].name,
        logo: data[0].logo
      }

      let requestedNodes = appRequestData.map(node=>node.NODE)

      data = this.processDasboardNodes(data)
      data.forEach(d=>{
        if (requestedNodes.includes(d.nodeId))
          d.requested = true
      })
      const groupedNodes = this.groupNodes(data)
      results.nodes=groupedNodes
      return results
    }
    catch(exp){
      this.log.error(`Error while executing getSharedDashboard ${exp}`)
      throw exp
    }
  }

  async getShareDashboardList(req){
    try{
      const type=req.params.type
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const search = req.query.search
      const sort = req.query.sort

      let dashboardData = await getShareDashboardListData(type,this.userEmail,page,limit,search,sort)
      let totalCount;

      if (dashboardData.length)
        totalCount = dashboardData[0].totalCount
      else
        totalCount = 0

      dashboardData = this.processDasboardList(dashboardData)

      if (type == 'sent'){
        dashboardData = dashboardData.map(app =>{
          app.recepientEmails = app.recepientEmails.split(';')
          return app
        })
      }

      let results = {
        totalCount: totalCount,
        data: dashboardData
      }
      
      return results

    }
    catch(exp){
      this.log.error(`Error while executing getShareDashboardList ${exp}`)
      throw exp
    }
  }

  async deleteShareDashboard(req) {
    this.log.debug(`>>>>>Entered microservice-myapps.controller.acceptShareApp`);
    try{
      const shareId=req.params.id
      const type = req.params.type

      switch(type) {
        case 'sent':
          await deleteShareDashboardData(shareId,'sender',this.userEmail)
          break;
        case 'received':
          await deleteShareDashboardData(shareId,'recepient',this.userEmail)
          break;
        default:
          throw new Error(`Invalid requestor type: ${type}`)
      }


      return{
            'message':'The shared dashboard has been deleted',
            'status':'success'
        }
    }
    catch(err){
      this.log.error(`<<<<<Exited microservice-myapps.controller.requestAccessShareApp with error ${err}`);
      throw err;
    }
  }

  /**
   * Dashboard builder card data save controller.
   * This controller takes the json payload sent from client
   * and saves it to S3 as a .json file for retreival later.
   * @param {*} req
   * @returns
   */
  async saveDashboardCardData(req) {
    const { body: records } = req;
    try {
      const uploadResponse = await saveDashboardDataService(records);
      return { dataId: uploadResponse, records };
    } catch (err) {
      this.log.error(err);
      throw err;
    }
  }

  /**
   * Dashboard Builder Card Data Update Controller
   * This controller updates a given json data file stored in S3
   */
  async updateDashboardCardData(req) {
    const { dataId } = req.params;
    const { body: records } = req;
    try {
      const uploadResponse = await updateDashboardDataService(dataId, records);
      return { dataId: uploadResponse, records };
    } catch (err) {
      this.log.error(err);
      throw err;
    }
  }

  /**
   * Dashboard Builder Card Data Get Data Controller
   * This controllers fetches the json data for the given
   * dataId (AWS Object key / file name) and returns it.
   */
  async getDashboardCardData(req) {
    const { dataId } = req.params;
    try {
      const data = await getDashboardCardDataService(dataId);
      return { dataId, records: data };
    } catch (err) {
      this.log.error(err);
      throw err;
    }
  }
}

module.exports = DashboardBuilder;
