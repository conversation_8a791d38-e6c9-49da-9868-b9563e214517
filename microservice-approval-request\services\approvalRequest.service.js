const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const { Op } = require('sequelize');
const { v4: uuidv4 } = require('uuid');
const pgModels = require('../../database/postgres/models'); // PostgreSQL models
const { IFPError } = require("../../utils/error");
const {
  formatApprovalRequest,
  getUserDetails,
  userRoleMapping,
  getObjectDetails
} = require("../helper/approvalRequestObject");

// PostgreSQL models
const { ApprovalRequest } = pgModels;

/**
 * Create a new approval request
 */
exports.createApprovalRequest = async (req) => {
  log.debug(`>>>>>Entered services.approvalRequest.createApprovalRequest`);
  try {
    const { objectId, objectType, approverId, metadata } = req.body;
    const requestorId = req.user.preferred_username;

    // Validate required fields
    if (!objectId || !objectType) {
      throw new IFPError(400, "Missing required fields: objectId and objectType are required");
    }

    // Validate objectType is one of the allowed types
    const allowedTypes = ['KPI', 'DASHBOARD', 'ENTITY'];
    if (!allowedTypes.includes(objectType)) {
      throw new IFPError(400, `Invalid objectType. Must be one of: ${allowedTypes.join(', ')}`);
    }
    
    // Check approver request already exists
    const existingRequest = await ApprovalRequest.findOne({
      where: {
        object_id: objectId,
        object_type: objectType,
        status: { [pgModels.Sequelize.Op.ne]: 'deleted' }, // status is not 'deleted'
        deleted_at: null // Ensure not deleted
      }
    });
    if (existingRequest) {
      throw new IFPError(400, `Approval request already exists for the object.`);
    }

    // Create new approval request
    const approvalRequest = await ApprovalRequest.create({
      id: uuidv4(),
      object_id: objectId,
      object_type: objectType,
      requestor_id: requestorId,
      approver_id: approverId,
      assignee_id: approverId,
      status: 'pending',
      metadata: metadata || {}
    });

    return formatApprovalRequest(approvalRequest, {});
  } catch (error) {
    log.error(`Error creating approval request: ${error}`);
    throw error;
  }
};

/**
 * Get list of approval requests with filtering
 */
exports.getApprovalRequests = async (req) => {
  log.debug(`>>>>>Entered service.approval-request.getApprovalRequests`);
  try {
    // Extract query parameters with defaults
    const {
      status,
      requestorId,
      assigneeId,
      approverId,
      unassigned,
      objectType,
      page = 1,
      limit = 20
    } = req.query;
    
    const parsedPage = parseInt(page);
    const parsedLimit = parseInt(limit);
    const offset = (parsedPage - 1) * parsedLimit;

    // Build filter conditions
    const where = {};
    
    if (status) where.status = status;
    if (requestorId) where.requestor_id = requestorId;
    if (assigneeId) where.assignee_id = assigneeId;
    if (approverId) where.approver_id = approverId;
    if (unassigned === 'true') where.assignee_id = null;
    if (objectType) where.object_type = objectType;

    // Fetch data and count
    const { count, rows } = await ApprovalRequest.findAndCountAll({
      where,
      limit: parsedLimit,
      offset,
      order: [['last_action_at', 'DESC']]
    });
    
    // Parallelize object detail lookups
    const data = await Promise.all(rows.map(async (request) => ({
      id: request.id,
      objectDetails: await getObjectDetails(request.object_type, request.object_id),
      assigneeId: request.assignee_id,
      approverId: request.approver_id,
      status: request.status,
      createdAt: request.created_at,
      updatedAt: request.updated_at,
      lastActionAt: request.last_action_at
    })));

    return {
      totalCount: count,
      page: parsedPage,
      limit: parsedLimit,
      data
    };
  } catch (error) {
    log.error(`Error fetching approval requests: ${error}`);
    throw new IFPError(500, "Failed to fetch approval requests", error);
  }
};

/**
 * Get detailed information about a specific approval request
 */
exports.getApprovalRequestDetails = async (req) => {
  log.debug(`>>>>>Entered service.approval-request.getApprovalRequestDetails`);
  try {
    const { id } = req.params;
    
    // Find approval request
    const approvalRequest = await ApprovalRequest.findByPk(id);

    if (!approvalRequest) {
      throw new IFPError(404, "Approval request not found");
    }
    return formatApprovalRequest(approvalRequest, {});
  } catch (error) {
    log.error(`Error fetching approval request details: ${error}`);
    throw error;
  }
};

/**
 * Get history of actions performed on a specific approval request
 */
exports.getRequestHistory = async (req) => {
  log.debug(`>>>>>Entered service.approval-request.getRequestHistory`);
  try {
    const { id } = req.params;
    
    // Find approval request
    const approvalRequest = await ApprovalRequest.findByPk(id);

    if (!approvalRequest) {
      throw new IFPError(404, "Approval request not found");
    }
    
    // Fetch history from the database
    const history = await pgModels.ApprovalRequestHistory.findAll({
      where: { request_id: id },
      order: [['changed_at', 'DESC']]
    });

    // Fetch history with joined comment content in a single query
    const historyWithComments = await pgModels.ApprovalRequestHistory.findAll({
      where: { request_id: id },
      order: [['changed_at', 'DESC']],
      include: [
        {
          model: pgModels.ApprovalRequestComment,
          as: 'comment',
          attributes: ['content']
        }
      ]
    });

    return historyWithComments.map(item => ({
      id: item.id,
      requestId: item.request_id,
      previousStatus: item.previous_status,
      newStatus: item.new_status,
      previousAssignee: item.previous_assignee,
      newAssignee: item.new_assignee,
      changedBy: item.changed_by,
      changedAt: item.changed_at,
      comment: item.comment ? item.comment.content : null
    }));
  } catch (error) {
    log.error(`Error fetching approval request history: ${error}`);
    throw error;
  }
}

/**
 * Get tab count for the current user
 */
exports.getTabCount = async (req) => {
  log.debug(`>>>>>Entered service.approval-request.getTabCount`);
  try {
    const currentUserEmail = req.user.preferred_username;
    const currentUser = await getUserDetails(currentUserEmail);
    const currentUserRole = currentUser.role;
    const { view, objectType, requesterId, assigneeId, page = 1, limit = 20 } = req.query;

    // Determine user role category
    const userRoleCategory = Object.keys(userRoleMapping).find(category => 
      userRoleMapping[category].includes(currentUserRole)
    );

    // Build base filter
    const baseFilter = {
      ...(objectType && { object_type: objectType }),
      ...(requesterId && userRoleCategory === 'APPROVER' && { requestor_id: requesterId }),
      ...(assigneeId && userRoleCategory === 'APPROVER' && { assignee_id: assigneeId }),
      ...(userRoleCategory === 'REQUESTOR' && { requestor_id: currentUserEmail })
    };

    // Helper function to build query
    const buildQuery = (status, additionalConditions = {}, includeDeleted = false) => ({
      where: { ...baseFilter, status, ...additionalConditions },
      ...(includeDeleted && { paranoid: false }),
      ...(page && limit && {
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        order: [['last_action_at', 'DESC']]
      })
    });

    // Helper function to execute query
    const executeQuery = async (query, includeData = false) => {
      if (includeData) {
        const { count, rows } = await ApprovalRequest.findAndCountAll(query);
        
        const data = await Promise.all(
          rows.map(async (request) => ({
            id: request.id,
            objectDetails: await getObjectDetails(
              request.object_type,
              request.object_id
            ),
            requestorId: request.requestor_id,
            assigneeId: request.assignee_id,
            approverId: request.approver_id,
            status: request.status,
            createdAt: request.created_at,
            updatedAt: request.updated_at,
            lastActionAt: request.last_action_at
          }))
        );
    
        return { count, data };
      } else {
        const count = await ApprovalRequest.count(query);
        return { count, data: null };
      }
    };

    // Define tab configurations based on user role
    const tabConfigs = {
      APPROVER: {
        new_requests: () => buildQuery('pending', {
          [Op.and]: [{ assignee_id: null }, { approver_id: null }]
        }),
        pending: () => buildQuery('pending', {
          [Op.and]: [
            { assignee_id: { [Op.not]: null } },
            { approver_id: null }
          ]
        }),
        reverted: () => buildQuery('reverted'),
        deleted: () => buildQuery('deleted', {}, true)
      },
      REQUESTOR: {
        new_requests: () => buildQuery('reverted'),
        pending: () => buildQuery('pending'),
        reverted: () => ({ count: 0, data: page && limit ? [] : null }),
        deleted: () => buildQuery('deleted', {}, true)
      }
    };

    // Execute queries for all tabs
    const results = {};
    const configs = tabConfigs[userRoleCategory];
    
    for (const [tabName, configFn] of Object.entries(configs)) {
      const config = configFn();
      if (config.count !== undefined) {
        // Handle special case for requestor reverted tab
        results[tabName] = config;
      } else {
        results[tabName] = await executeQuery(config, tabName === view && page && limit);
      }
    }

    // Extract counts and data
    const counts = Object.fromEntries(
      Object.entries(results).map(([key, value]) => [key, value.count])
    );
    
    const data = Object.fromEntries(
      Object.entries(results)
        .filter(([, value]) => value.data !== null)
        .map(([key, value]) => [key, value.data])
    );

    // Build response
    if (view && counts.hasOwnProperty(view)) {
      const response = counts;

      if (page && limit) {
        response.data = data[view] || [];
        response.page = parseInt(page);
        response.limit = parseInt(limit);
        response.totalCount = counts[view];
      }
      
      return response;
    }

    // Return all counts and optionally data
    const response = { ...counts };
    
    if (page && limit && Object.keys(data).length > 0) {
      response.data = data;
      response.page = parseInt(page);
      response.limit = parseInt(limit);
    }
    return response;

  } catch (error) {
    log.error(`Error fetching tab count: ${error}`);
    throw error;
  }
}