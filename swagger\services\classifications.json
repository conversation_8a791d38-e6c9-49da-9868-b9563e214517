{"paths": {"/content-type/classifications/": {"get": {"tags": ["Classifications"], "summary": "Retrieve classifications information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the category."}, "name": {"type": "string", "description": "Name of the category."}, "icon_path": {"type": "string", "format": "uri", "description": "URL to the category's icon suitable for dark themes."}, "light_icon_path": {"type": "string", "format": "uri", "description": "URL to the category's icon suitable for light themes."}, "node_count": {"type": "integer", "description": "Number of nodes or items within the category."}, "isSelected": {"type": "boolean", "description": "Indicates whether the category is currently selected by the user."}}, "required": ["id", "name", "icon_path", "light_icon_path", "node_count", "isSelected"], "additionalProperties": false}, "description": "Schema for a response containing an array of categories with detailed information."}}}}}}}}}