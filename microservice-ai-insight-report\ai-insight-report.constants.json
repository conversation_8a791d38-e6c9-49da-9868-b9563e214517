{"dev": {"AI_INSIGHT_REPORT_BUCKET": "app-bayaangov-staging-31635793-3e70-4f73-8fe7-fd2095d39e16", "AI_INSIGHT_REPORT_CACHE_PARENT_KEY": "ai_insight_report", "CP4D_BASE_URL": "https://datafabric-cpd-nonp.scad.gov.ae", "CP4D_USERNAME": "<PERSON><PERSON><PERSON><PERSON>", "CP4D_PASSWORD": "Camaraderie@2025", "CP4D_API_TOKEN_CACHE_KEY": "cp4d_access_token", "CP4D_SANADKOM_TICKET_JOB_ID": "a7dc174a-def1-41af-b78d-abe2ea893119", "CP4D_PROJECT": "bd744a4f-5987-4a6a-ad1c-a32aa9737633", "AZURE_OPENAI_BASE_URL": "https://api.core42.ai", "AZURE_OPENAI_API_KEY": "6b1a17184c3143f184d306ac5d6f9e8c", "AZURE_OPENAI_API_VERSION": "2024-08-01-preview", "AZURE_OPENAI_MODEL_NAME": "gpt-4o", "SMART_PUBLISHER_BASE_URL": "https://data-sharing-api-uat.adsmartsupport.ae", "SANADKOM_CLIENT_ID": "1gVEEUigRdJthoFenMC/BhpB7y5pelrpoNy", "SANADKOM_CLIENT_SECRET": "uS7coVT2zcrzBZp5WNs5LzWea2f6", "SANADKOM_SERVICE_ID": "2409", "SANADKOM_IP": "************"}, "test": {"AI_INSIGHT_REPORT_BUCKET": "app-bayaangov-staging-31635793-3e70-4f73-8fe7-fd2095d39e16", "AI_INSIGHT_REPORT_CACHE_PARENT_KEY": "ai_insight_report", "CP4D_BASE_URL": "https://datafabric-cpd-nonp.scad.gov.ae", "CP4D_USERNAME": "<PERSON><PERSON><PERSON><PERSON>", "CP4D_PASSWORD": "Camaraderie@2025", "CP4D_API_TOKEN_CACHE_KEY": "cp4d_access_token", "CP4D_SANADKOM_TICKET_JOB_ID": "a7dc174a-def1-41af-b78d-abe2ea893119", "CP4D_PROJECT": "bd744a4f-5987-4a6a-ad1c-a32aa9737633", "AZURE_OPENAI_BASE_URL": "https://api.core42.ai", "AZURE_OPENAI_API_KEY": "6b1a17184c3143f184d306ac5d6f9e8c", "AZURE_OPENAI_API_VERSION": "2024-08-01-preview", "AZURE_OPENAI_MODEL_NAME": "gpt-4o", "SMART_PUBLISHER_BASE_URL": "https://data-sharing-api-uat.adsmartsupport.ae", "SANADKOM_CLIENT_ID": "1gVEEUigRdJthoFenMC/BhpB7y5pelrpoNy", "SANADKOM_CLIENT_SECRET": "uS7coVT2zcrzBZp5WNs5LzWea2f6", "SANADKOM_SERVICE_ID": "2409", "SANADKOM_IP": "************"}, "prod": {"AI_INSIGHT_REPORT_BUCKET": "app-bayaangov-staging-31635793-3e70-4f73-8fe7-fd2095d39e16", "AI_INSIGHT_REPORT_CACHE_PARENT_KEY": "ai_insight_report", "CP4D_BASE_URL": "https://datafabric-cpd-nonp.scad.gov.ae", "CP4D_USERNAME": "<PERSON><PERSON><PERSON><PERSON>", "CP4D_PASSWORD": "Camaraderie@2025", "CP4D_API_TOKEN_CACHE_KEY": "cp4d_access_token", "CP4D_SANADKOM_TICKET_JOB_ID": "a7dc174a-def1-41af-b78d-abe2ea893119", "CP4D_PROJECT": "bd744a4f-5987-4a6a-ad1c-a32aa9737633", "AZURE_OPENAI_BASE_URL": "https://api.core42.ai", "AZURE_OPENAI_API_KEY": "6b1a17184c3143f184d306ac5d6f9e8c", "AZURE_OPENAI_API_VERSION": "2024-08-01-preview", "AZURE_OPENAI_MODEL_NAME": "gpt-4o", "SMART_PUBLISHER_BASE_URL": "https://data-sharing-api-uat.adsmartsupport.ae", "SANADKOM_CLIENT_ID": "1gVEEUigRdJthoFenMC/BhpB7y5pelrpoNy", "SANADKOM_CLIENT_SECRET": "uS7coVT2zcrzBZp5WNs5LzWea2f6", "SANADKOM_SERVICE_ID": "2409", "SANADKOM_IP": "************"}, "demo": {"AI_INSIGHT_REPORT_BUCKET": "app-bayaangov-staging-31635793-3e70-4f73-8fe7-fd2095d39e16"}}