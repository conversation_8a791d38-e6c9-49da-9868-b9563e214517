const { IFPError } = require("../../utils/error");

const validateOverview = (req, res, next) => {
    const allowedTypes = [
        "official_statistics",
        "experimental_statistics_screener"
    ]
    if (req.body.type)
        if (!allowedTypes.includes(req.body.type))
            throw new IFPError(400,`'${req.body.type}' is not a valid type for overview`)
    if (!('ids' in req.body))
        throw new IFPError(400,`'Please provide ids field`)

    if (!Array.isArray(req.body.ids))
        throw new IFPError(400,`'ids' field should be an array`)

    req.body.ids.forEach(id=>{
        if (!Number(id))
            throw new IFPError(400,`'ids' field should be an array of numbers`)
    })
        
    next()
  };

  const validateVisit = (req, res, next) => {
    if (!Number(req.params.id))
        throw new IFPError(400,`'${req.params.id}' is not a valid id for indicators`)
    next()
  };
module.exports = {
    validateOverview,
    validateVisit
}