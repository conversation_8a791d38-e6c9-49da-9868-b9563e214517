const clkdb = require("../../../services/clk-database.service");
const db = require('../../../services/database.service');
const constants = require("../../../config/constants.json");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const {
  getTotalEntitiesQuery,
  getEntitiesCountByMonthQuery,
  getTopEntitiesByUsageQuery,
  generateEntityClassifications,
  generateActiveDomainsQuery,
  generateDownloadReportQuery,
  generateEntitiesListWithUsersCountQuery,
  generateEntityDomains,
  generateEntityNameQuery
} = require("./getQuery.service")

async function getTotalEntities(filter) {
  const { query, binds } = getTotalEntitiesQuery(filter)
  try {
    const data = await clkdb.simpleExecute(query, binds);
    const { count } = data && data[0];
    return count;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getTotalEntities with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getEntitiesCountByMonth(filter) {
  const { query, binds } = getEntitiesCountByMonthQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getEntitiesCountByMonth with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getTopEntitiesByUsage(filter) {
  const { query, binds } = getTopEntitiesByUsageQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getTopEntitiesByUsage with error ${err}`
    );
    throw err;
  }
}

async function getEntityByClassification(entityId) {
  const { query, binds } = generateEntityClassifications(entityId);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getEntityByClassification with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getEntityByDomain(entityId, classification) {
  const { query, binds } = generateEntityDomains(entityId, classification);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getEntityByDomain with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}



async function getEntityActiveList(filter) {
  const { query, binds } = generateActiveDomainsQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.entity.service.getEntityActiveList with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}


async function getDownloadIndicatorList(filter) {
  const { query, binds } = generateDownloadReportQuery(filter);
  try {
    const data = await clkdb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.entity.service.getDownloadIndicatorList with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getEntityFullName(domain) {
  const { query, binds } = generateEntityNameQuery(domain);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.entity.service.getEntityFullName with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getEntitiesListWithUsersCount(offset, limit, entityId, selectedColumns, isPaginated) {
  const { query, binds } = generateEntitiesListWithUsersCountQuery(offset, limit, entityId, selectedColumns, isPaginated);
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice-kpi.services.users.service.getEntitiesListWithUsersCount with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}



module.exports = {
  getTotalEntities,
  getEntitiesCountByMonth,
  getTopEntitiesByUsage,
  getEntityByClassification,
  getEntityActiveList,
  getDownloadIndicatorList,
  getEntitiesListWithUsersCount,
  getEntityByDomain,
  getEntityFullName
};
