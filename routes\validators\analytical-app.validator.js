const { IFPError } = require('../../utils/error');
const constants = require("../../config/constants.json");
const { getMetaFromCMS } = require("../../services/common-service");

const validateAnalyticalAppById = (req,res,next)=>{
    if (!Number(req.params.id))
        throw new IFPError(400,'Invalid id format')
    next()
}

const validateGeospatialPermissions = async (req,res,next)=>{
    try{
        const geospatialAccess = req.headers['geospatial-access']
        if (geospatialAccess && geospatialAccess === 'true') {
            next();
        }
        if (geospatialAccess && geospatialAccess === 'false') {
            throw new Error(`Geospatial modules access is not allowed for this user`);
        }

        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        let page = req.query.page ? req.query.page : 1;
        let limit = req.query.limit ? req.query.limit : 10;

        const cmsGetGeoRevampIconsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_GeoRevamp_Icons_URL}?page=${page}&limit=${limit}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        await getMetaFromCMS(req,cmsLoginUrl, cmsGetGeoRevampIconsUrl, req.user.groups);
        next();
    }
    catch(err){
        next(new IFPError(403, `Error validating geospatial permissions: ${err.message}`));
    }
}

module.exports = {
    validateAnalyticalAppById,
    validateGeospatialPermissions
}