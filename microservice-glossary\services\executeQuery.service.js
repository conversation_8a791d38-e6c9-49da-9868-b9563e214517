const clkdb = require('../../services/clk-database.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const {
  getGlossaryDataQuery, getDomainsDataQuery, getAlphabetsDataQuery } = require('./getQuery.service');

/**
 * Retrieves glossary data based on the given parameters
 * @param {*} lang - Language
 * @param {*} page - Page
 * @param {*} limit - Limit 
 * @param {*} filters - Filters
 * @param {*} sortBy - Sort
 * @param {*} searchTerm - Search Term
 */
async function getGlossaryData(lang, page = 1, limit = 10, filters = {}, sortBy = {}, searchTerm = null) {
  try {
    const queryResult = await getGlossaryDataQuery(lang, page, limit, filters, sortBy, searchTerm);
    const data = await getData(queryResult.query, queryResult.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-glossary.executeQuery.service.getGlossaryData with error ${err}`);
    throw err;
  }
}

/**
 * Retrieves glossary domains
 * @param {*} lang - Language
 */
async function getDomainsData(lang) {
  try {
    const query = await getDomainsDataQuery(lang);
    const data = await getData(query);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-glossary.executeQuery.service.getDomainsData with error ${err}`);
    throw err;
  }
}

/**
 * Retrieves glossary alphabets
 * @param {*} lang - Language
 * @param {*} searchTerm - Search Term
 * @param {*} filters - Filters
 */
async function getAlphabetsData(lang, searchTerm = '', filters = {}) {
  try {
    const {query,binds} = await getAlphabetsDataQuery(lang, searchTerm, filters);
    const data = await getData(query,binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-glossary.executeQuery.service.getAlphabetsData with error ${err}`);
    throw err;
  }
}

/**
 * Retrieves data for a given query
 * @param {*} query - Query to be executed
 * @param {*} binds - Binds variables for the query
 */
async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-domains.services.executeQuery.service.getData`);
    const data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-domains.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-domains.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getGlossaryData,
  getDomainsData,
  getAlphabetsData
};
