const db = require("../../services/database.service");
const mysqldb = require("../../services/mysql-database.service");
const { IFPError } = require("../../utils/error");
const oracledb = require('oracledb');
const {
  getEntityDetailsQuery,
  createInvitationQuery,
  listPendingInvitationsQuery,
  deleteInvitationQuery,
  updateInvitationQuery,
  getEntityListQuery,
  getInvitationQuery,
  getInvitationV2Query,
  checkExistingInvitationsQuery,
  getDGOnboardingStatusQuery,
  createUserQuery,
  setUserActivationStatusQuery,
  getActiveSuperuserQuery,
  updateInvitationStatusQuery,
  createUserAccessQuery,
  removeUserAccessQuery,
  getUserDataQuery,
  updateUserAccessApprovalQuery,
  createUserAccessApprovalQuery,
  getActiveUserByIdQuery,
  updateUserStatusQuery,
  getUserDataV2Query,
  getUserDataV3Query,
  setUserDeleteStatusQuery,
  getDisseminationAccessPolicyQuery,
  createUserAccessRequestQuery,
  updateUserAccessRequestStatusQuery,
  getUserAccessRequestsQuery,
  getUserAccessLevelsQuery,
  updateUserAccessRequestIntraIdStatusQuery,
  updateUserAccessRequestUserIdQuery,
  getApprovedAccessQuery,
  getIntraIDPendingAccessQuery,
  getActiveDGUserQuery,
  getDGPendingAccessQuery,
  getUserAccessApprovalsQuery,
  getPENewAccessQuery,
  getActivePEUserQuery,
  getSUPendingAccessQuery,
  createEIDUserQuery,
  createEIDInvitationMappingQuery,
  getEIDInvitationMappingQuery,
  getEIDInvitationMappingByEIDQuery,
  getDGNewAccessQuery,
  getDGExistingAccessQuery,
  getPEExistingAccessQuery,
  getSUExistingAccessQuery,
  getSUNewAccessQuery,
  deleteUserAccessQuery,
  updateUserAccessUserIdQuery,
  deleteUserDataQuery,
  deleteUserAccessRequestsQuery,
  updatePrimarySUQuery,
  getSecondarySUQuery,
  updatePrimaryPEQuery,
  getSecondaryPEQuery,
  updateSecondarySUQuery,
  updateSecondaryPEQuery,
  updateUserDataQuery,
  getEntityUsersForExportQuery,
  getEntityInvitesQuery,
  getSensitiveAccessDataQuery,
  getDeletedUsersListQuery,
  getUserAccessV2Query,
  getRequestIDsforPendingAccessQuery,
  deleteInvitationByEmailQuery,
  deleteUserAccessApprovalQuery,
  deleteEidMappingByEmailQuery,
} = require("./getQuery");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

async function getEntityList() {
  try {
    let { query, binds } = await getEntityListQuery();
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users-v3.executeQuery.service.getEntityList with error ${err}`
    );
    throw err;
  }
}
async function getEntityDetails(entityId) {
  try {
    let { query, binds } = await getEntityDetailsQuery(entityId);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0) {
      throw new IFPError(404, `Entity with given id: '${entityId}'' not found`);
    }

    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getEntityDetails with error ${err}`
    );
    throw err;
  }
}

async function createInvitationData(
  requestId,
  phone,
  userId,
  emailMasked,
  entityID,
  inviteType,
  designation,
  platformAccess,
  requestedAccess,
  superUserId
) {
  try {
    let { query, binds } = await createInvitationQuery(
      requestId,
      phone,
      userId,
      emailMasked,
      entityID,
      inviteType,
      designation,
      platformAccess,
      requestedAccess,
      superUserId
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.createInvitationData with error ${err}`
    );
    throw err;
  }
}

async function listPendingInvitations(entityId, roles) {
  try {
    let { query, binds } = await listPendingInvitationsQuery(entityId, roles);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with listPendingInvitations: ${err}`);
    throw err;
  }
}

async function deleteInvitation(requestId) {
  try {
    let { query, binds } = deleteInvitationQuery(requestId);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with deleteInvitation: ${err}`);
    throw err;
  }
}

async function updateInvitation(requestId, newData) {
  try {
    let { query, binds } = updateInvitationQuery(requestId, newData);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with updateInvitation: ${err}`);
    throw err;
  }
}

async function createEIDInvitationMapping(
  requestId,
  hashedEID,
  encryptedEmail
) {
  try {
    let { query, binds } = await createEIDInvitationMappingQuery(
      requestId,
      hashedEID,
      encryptedEmail
    );
    let data = await mysqldb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.createEIDInvitationMapping with error ${err}`
    );
    throw err;
  }
}

async function getEIDInvitationMapping(
  requestId,
  hashedEID
) {
  try {
    let { query, binds } = await getEIDInvitationMappingQuery(
      requestId
    );
    let data = await mysqldb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getEIDInvitationMapping with error ${err}`
    );
    throw err;
  }
}

async function getEIDInvitationMappingByEID(hashedEID) {
  try {
    let { query, binds } = await getEIDInvitationMappingByEIDQuery(hashedEID);
    let data = await mysqldb.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error at getEIDInvitationMappingByEID with error ${err}`);
    throw err;
  }
}

async function getInvitationData(requestId) {
  try {
    let { query, binds } = await getInvitationQuery(requestId);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0) {
      throw new IFPError(
        404,
        `Invitation with given id: '${requestId}'' not found`
      );
    }

    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getInvitationData with error ${err}`
    );
    throw err;
  }
}

async function getInvitationDataV2(filters) {
  try {
    let { query, binds } = getInvitationV2Query(filters);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with getInvitationDataV2: ${err}`);
    throw err;
  }
}

async function checkExistingInvitations(userEmail, phoneNumber) {
  try {
    let { query, binds } = checkExistingInvitationsQuery(
      userEmail,
      phoneNumber
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with checkExistingInvitations: ${err}`);
    throw err;
  }
}

async function getEntityInvites(type, status, entity) {
  try {
    let { query, binds } = getEntityInvitesQuery(
      type,
      status,
      entity
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with getEntityInvites: ${err}`);
    throw err;
  }
}

async function getDGOnboardingStatus(entityId) {
  try {
    let { query, binds } = await getDGOnboardingStatusQuery(entityId);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0) {
      throw new IFPError(
        404,
        `DG Records for the entity: ${entityId} not found`
      );
    }
    return data[0];
  } catch (err) {
    log.error(`Error with getDGOnboardingStatus: ${err}`);
    throw err;
  }
}

async function createUserData(
  userId,
  name,
  email,
  phoneNumber,
  role,
  entityId,
  mobileSyncStatus,
  designation = null,
  linkAndNdaStatus = "",
  requestJustification = "",
) {
  try {
    let { query, binds } = await createUserQuery(
      userId,
      name,
      email,
      phoneNumber,
      role,
      entityId,
      designation,
      linkAndNdaStatus,
      requestJustification,
      mobileSyncStatus
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.createUserData with error ${err}`
    );
    throw err;
  }
}

async function createEIDUser(
  userId,
  maskedEID,
  email
) {
  try {
    let { query, binds } = await createEIDUserQuery(
      userId,
      maskedEID,
      email
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.createEIDUser with error ${err}`
    );
    throw err;
  }
}

async function getUserData(email) {
  try {
    let { query, binds } = await getUserDataQuery(email);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0)
      throw new IFPError(404, `User with given id: '${email}'' not found`);
    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getUserData with error ${err}`
    );
    throw err;
  }
}

async function getUserDataV2(filters) {
  try {
    let { query, binds } = await getUserDataV2Query(filters);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getUserData with error ${err}`
    );
    throw err;
  }
}

/**
 * Filter user either ANDing or ORing.
 * @param {*} filters object with key as column and value as filter value
 * @param {string} operator either 'AND' or 'OR'. Default = 'AND'
 * @returns 
 */
async function getUserDataV3(filters, operator = 'AND') {
  try {
    let { query, binds } = await getUserDataV3Query(filters, operator);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error with getUserDataV3: ${err}`);
    throw err;
  }
}

async function updateUserStatus(userId, status) {
  try {
    let { query, binds } = await updateUserStatusQuery(userId, status);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updateUserStatus with error ${err}`
    );
    throw err;
  }
}

/**
 * Update user
 * @param {string} updateByField Field which record to be updated.
 * Either ID, EMAIL or MOBILE. Default is ID
 * @param {string} identifier Value for the updateByField
 * @param {object} updateFields Object of key value pairs where key
 * is the column name and value the value to be updated
 * @returns 
 */
async function updateUserData(updateByField = 'ID', identifier, updateFields) {
  try {
    let { query, binds } = updateUserDataQuery(
      updateByField,
      identifier,
      updateFields
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(`Error updateUserData with error ${err}`);
    throw err;
  }
}

async function setUserDeleteStatus(userId, deletedUserId) {
  try {
    let { query, binds } = await setUserDeleteStatusQuery(
      userId,
      deletedUserId
    );
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.setUserDeleteStatus with error ${err}`
    );
    throw err;
  }
}

async function setUserActivationStatus(userId, status) {
  try {
    let { query, binds } = await setUserActivationStatusQuery(userId, status);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.setUserActivationStatus with error ${err}`
    );
    throw err;
  }
}

async function getActiveSuperuser(userEmail) {
  try {
    let { query, binds } = await getActiveSuperuserQuery(userEmail);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getActiveSuperuser with error ${err}`
    );
    throw err;
  }
}

async function getActiveDGUser(userEmail) {
  try {
    let { query, binds } = await getActiveDGUserQuery(userEmail);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getActiveDGUser with error ${err}`
    );
    throw err;
  }
}

async function getActivePEUser(userEmail) {
  try {
    let { query, binds } = await getActivePEUserQuery(userEmail);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getActivePEUser with error ${err}`
    );
    throw err;
  }
}

async function getActiveUserByEmail(userEmail) {
  try {
    let { query, binds } = await getActiveUserByEmailQuery(userEmail);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0)
      throw new IFPError(
        404,
        `User with given email: '${userEmail}'' not found`
      );
    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getActiveUserByEmail with error ${err}`
    );
    throw err;
  }
}

async function getActiveUserById(userId) {
  try {
    let { query, binds } = await getActiveUserByIdQuery(userId);
    let data = await db.simpleExecute(query, binds);
    if (data.length === 0)
      throw new IFPError(404, `User with given id: '${userId}'' not found`);
    return data[0];
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getActiveUserById with error ${err}`
    );
    throw err;
  }
}

async function updateInvitationStatus(requestId, status) {
  try {
    let { query, binds } = await updateInvitationStatusQuery(requestId, status);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updateInvitationStatus with error ${err}`
    );
    throw err;
  }
}

async function createUserAccessRequest(requestId, userId) {
  try {
    let { query, binds } = await createUserAccessRequestQuery(
      requestId,
      userId
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.createUserAccessRequest with error ${err}`
    );
    throw err;
  }
}

async function updateUserAccessRequestStatus(requestId, status) {
  try {
    let { query, binds } = await updateUserAccessRequestStatusQuery(
      requestId,
      status
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updateUserAccessRequestStatus with error ${err}`
    );
    throw err;
  }
}

async function updateUserAccessRequestIntraIdStatus(requestId, status) {
  try {
    let { query, binds } = await updateUserAccessRequestIntraIdStatusQuery(
      requestId,
      status
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updateUserAccessRequestIntraIdStatus with error ${err}`
    );
    throw err;
  }
}

/**
 * Enable UAE Pass Linking of existing scad users.
 * @param {string} currUserId: id assigned during sync of user from
 * `IFP_USER_INFO`, this is the SCAD Entra ID 
 * @param {*} newUserId  UAE Pass ID , to be replaced instead of Entra ID ID.
 * @returns 
 */
async function updateUserAccessRequestUserId(currUserId, newUserId) {
  try {
    let { query, binds } = await updateUserAccessRequestUserIdQuery(
      currUserId,
      newUserId
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(`Error with updateUserAccessRequestUserId: ${err}`);
    throw err;
  }
}

async function createUserAccess(
  requestId,
  accessId,
  userId,
  domain,
  accessLevel,
  accessOperation='GRANT'
) {
  try {
    let { query, binds } = await createUserAccessQuery(
      requestId,
      accessId,
      userId,
      domain,
      accessLevel,
      accessOperation
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.createUserAccessQuery with error ${err}`
    );
    throw err;
  }
}

async function deleteUserAccess(  
  userId,
  filters={}
) {
  try {
    let { query, binds } = await deleteUserAccessQuery(
      userId,
      filters
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.deleteUserAccess with error ${err}`
    );
    throw err;
  }
}

/**
 * Enable UAE Pass Linking of existing scad users.
 * @param {string} currUserId: id assigned during sync of user from
 * `IFP_USER_INFO`, this is the SCAD Entra ID 
 * @param {*} newUserId  UAE Pass ID , to be replaced instead of Entra ID ID.
 * @returns 
 */
async function updateUserAccessUserId(currUserId, newUserId) {
  try {
    let { query, binds } = await updateUserAccessUserIdQuery(
      currUserId,
      newUserId
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(`Error with updateUserAccessUserId: ${err}`);
    throw err;
  }
}

async function updatePrimarySU(userId) {
  try {
    let { query, binds } = await updatePrimarySUQuery(userId);
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updatePrimarySU with error ${err}`
    );
    throw err;
  }
}

async function updateSecondarySU(userId) {
  try {
    let { query, binds } = await updateSecondarySUQuery(userId);
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updateSecondarySU with error ${err}`
    );
    throw err;
  }
}

async function getSecondarySU(userId) {
  try {
    let { query, binds } = await getSecondarySUQuery(userId);
    let data = await db.simpleExecute(query, binds);
    return data
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getSecondarySU with error ${err}`
    );
    throw err;
  }
}

async function updatePrimaryPE(userId) {
  try {
    let { query, binds } = await updatePrimaryPEQuery(userId);
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updatePrimaryPE with error ${err}`
    );
    throw err;
  }
}

async function getSecondaryPE() {
  try {
    let { query, binds } = await getSecondaryPEQuery();
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getSecondaryPE with error ${err}`
    );
    throw err;
  }
}

async function updateSecondaryPE(userId) {
  try {
    let { query, binds } = await updateSecondaryPEQuery(userId);
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.updateSecondaryPE with error ${err}`
    );
    throw err;
  }
}

async function deleteUserData(userId) {
  try {
    let { query, binds } = await deleteUserDataQuery(userId);
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.deleteUserData with error ${err}`
    );
    throw err;
  }
}

async function deleteUserAccessRequests(userId) {
  try {
    let { query, binds } = await deleteUserAccessRequestsQuery(userId);
    let data = await db.simpleExecute(query, binds);
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.deleteUserAccessRequests with error ${err}`
    );
    throw err;
  }
}

async function removeUserAccess(user) {
  try {
    let { query, binds } = await removeUserAccessQuery(user);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.removeUserAccess with error ${err}`
    );
    throw err;
  }
}

async function createUserAccessApproval(
  id,
  userAccessId,
  status,
  nextApprovalLevel,
  approverId = null,
  reason = null
) {
  try {
    let { query, binds } = await createUserAccessApprovalQuery(
      id,
      userAccessId,
      status,
      nextApprovalLevel,
      approverId,
      reason
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.removeUserAccess with error ${err}`
    );
    throw err;
  }
}

async function updateUserAccessApproval(userAccessId, status, approvalLevel) {
  try {
    let { query, binds } = await updateUserAccessApprovalQuery(
      userAccessId,
      status,
      approvalLevel
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.removeUserAccess with error ${err}`
    );
    throw err;
  }
}

async function getUserAccessApprovals(filters) {
  try {
    let { query, binds } = await getUserAccessApprovalsQuery(
    filters 
    );
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getUserAccessApprovals with error ${err}`
    );
    throw err;
  }
}

async function getDisseminationAccessPolicy(entityId) {
  try {
    let { query, binds } = await getDisseminationAccessPolicyQuery(entityId);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getDisseminationAccessPolicy with error ${err}`
    );
    throw err;
  }
}

async function getUserAccessRequests(filters) {
  try {
    let { query, binds } = await getUserAccessRequestsQuery(filters);
    let data = await db.simpleExecute(query, binds);

    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-users.executeQuery.service.getUserAccessRequests with error ${err}`
    );
    throw err;
  }
}

async function getUserAccessLevels(filters){
  try{
    let {query,binds} = await getUserAccessLevelsQuery(filters)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getRegistrationRequest with error ${err}`);
    throw err;
  }
}

async function getIntraIDPendingAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getIntraIDPendingAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getIntraIDPendingAccess with error ${err}`);
    throw err;
  }
}

async function getSUNewAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getSUNewAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getSUNewAccess with error ${err}`);
    throw err;
  }
}

async function getSUPendingAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getSUPendingAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getSUPendingAccess with error ${err}`);
    throw err;
  }
}

async function getSUExistingAccess(page, limit, search, entity, sortBy, order, currentSURole, userType){
  try{
    let {query,binds} = await getSUExistingAccessQuery(page, limit, search, entity, sortBy, order, currentSURole, userType)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getSUExistingAccess with error ${err}`);
    throw err;
  }
}

async function getEntityUsersForExport(entityId, userType){
  try {
    let { query, binds } = await getEntityUsersForExportQuery(entityId, userType);
    let data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.getEntityUsersForExport with error ${err}`
    );
    throw err;
  }
}


async function getApprovedAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getApprovedAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getApprovedAccess with error ${err}`);
    throw err;
  }
}

async function getDGPendingAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getDGPendingAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getDGPendingAccess with error ${err}`);
    throw err;
  }
}

async function getDGNewAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getDGNewAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getDGNewAccess with error ${err}`);
    throw err;
  }
}

async function getDGExistingAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getDGExistingAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getDGExistingAccess with error ${err}`);
    throw err;
  }
}

async function getPENewAccess(page, limit, search, entity, sortBy, order){
  try{
    let {query,binds} = await getPENewAccessQuery(page, limit, search, entity, sortBy, order)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getPENewAccess with error ${err}`);
    throw err;
  }
}

async function getPEExistingAccess(page, limit, search, entity, sortBy, order, userType){
  try{
    let {query,binds} = await getPEExistingAccessQuery(page, limit, search, entity, sortBy, order, userType)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getPEExistingAccess with error ${err}`);
    throw err;
  }
}

//To Remove

async function getAllMappingsData() {
  try {
    const query = 'SELECT * FROM ifp_eid_request_mapping';
    const rows = await mysqldb.simpleExecute(query);
    return rows;
  } catch (error) {
    throw error
  }
}

async function getMappingByRequestIdsData(requestIds) {
  try {
    const query = 'SELECT * FROM ifp_eid_request_mapping WHERE request_id IN (?)';
    const rows = await mysqldb.simpleExecute(query, [requestIds]);
    return rows;
  } catch (error) {
    throw error
  }
}

async function getMappingByEmailsData(emails) {
  try {
    const query = 'SELECT * FROM ifp_eid_request_mapping WHERE email IN (?)';
    const rows = await mysqldb.simpleExecute(query, [emails]);
    return rows;
  } catch (error) {
    throw error
  }
}

async function bulkDeleteMappingsData(requestIds) {
  try {
    const query = 'DELETE FROM ifp_eid_request_mapping WHERE request_id IN (?)';
    await mysqldb.simpleExecute(query, [requestIds]);
    return { message: 'Mappings deleted successfully' };
  } catch (error) {
    throw error
  }
}

async function getSensitiveAccessData(userIds){
  try{
    let {query,binds} = await getSensitiveAccessDataQuery(userIds)
    let data = await db.simpleExecute(query,binds)
    return data
  }
  catch(err){
    log.error(`<<<<< Exited microservice-user.executeQuery.service.getSensitiveAccessData with error ${err}`);
    throw err;
  }
}

async function getDeletedUsersList(entityId) {
  try {
    const { query, binds } = getDeletedUsersListQuery(entityId);
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (error) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.getDeletedUsersList with error ${err}`
    );
    throw err;
  }
}

/**
 * Query that extracts user's access records for the different request
 * types (new, pending & existing).
 * 
 * Example: Access records for -> Superuser Pending requests, DG New Requests,
 * PE New requests, Superuser New requests etc.
 * 
 * The query consists of join between requests, access records and approval
 * records. The result is a list of all the users access request, the access
 * records part of said requests, their grant status (GRANT or REVOKE), their
 * approval status (APPROVED or PENDING) and who it's pending with (DG, PE, SUPERUSER).
 * 
 * @param {string} userId User ID whose details are required.
 * @param {string} requestType Must be one of: 'new', 'pending' or 'existing'
 * @param {string} approvalLevel Only used for new request,
 * use this to get access records pending at various user levels. Eg:
 * For New requests at DG, specify approvalLevel as 'DG'. For new requests
 * at Superuser, specify as 'SUPERUSER'
 * @param {string} requestorRole Only used for pending requests,
 * Used to filter access records which the requestorRole is waiting for.
 * Example: If value is "SUPERUSER", returns access records pending at either
 * PE or DG.
 */
async function getUserAccessV2(userId, requestType, approvalLevel, requestorRole) {
  const { query, binds } = getUserAccessV2Query(
    userId,
    requestType,
    approvalLevel,
    requestorRole
  );
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.getUserAccessV2 with error ${err}`
    );
    throw err;
  }
}

/**
 * 
 * @param {string} userId 
 * @param {Array<string>} accessIds 
 */
async function getRequestIDsforPendingAccess(userId, accessIds) {
  const { query, binds } = getRequestIDsforPendingAccessQuery(
    userId,
    accessIds
  );
  try {
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (error) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.getRequestIDsforPendingAccess with error ${error}`
    );
    throw error;
  }
}

async function deleteInvitationByEmail(userEmail) {
  try {
    const { query, binds } = deleteInvitationByEmailQuery(userEmail);
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (error) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.deleteInvitationByEmail with error ${err}`
    );
    throw err;
  }
}

async function deleteUserAccessApproval(userId) {
  try {
    const { query, binds } = deleteUserAccessApprovalQuery(userId);
    const data = await db.simpleExecute(query, binds);
    return data;
  } catch (error) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.deleteUserAccessApproval with error ${err}`
    );
    throw err;
  }
}

async function resetUserData(userId) {
  try {
    await Promise.all([
      deleteUserData(userId),
      deleteUserAccessRequests(userId),
      deleteUserAccessApproval(userId),
    ]);
    await deleteUserAccess(userId);
  } catch (error) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.resetUserData with error ${err}`
    );
    throw err;
  }
}

async function deleteUserEIDMapping(userEmail) {
  try {
    const { query, binds } = deleteEidMappingByEmailQuery(userEmail);
    await db.simpleExecute(query, binds);
  } catch (error) {
    log.error(
      `<<<<< Exited microservice-user.executeQuery.service.deleteUserEIDMapping with error ${err}`
    );
    throw err;
  }
}

module.exports = {
  getEntityList,
  getEntityDetails,
  createInvitationData,
  listPendingInvitations,
  deleteInvitation,
  updateInvitation,
  getInvitationData,
  getInvitationDataV2,
  checkExistingInvitations,
  getDGOnboardingStatus,
  createUserData,
  createEIDUser,
  getUserDataV2,
  getUserDataV3,
  getUserData,
  setUserActivationStatus,
  getActiveSuperuser,
  getActiveDGUser,
  getActivePEUser,
  getActiveUserByEmail,
  getActiveUserById,
  updateInvitationStatus,
  updateUserStatus,
  setUserDeleteStatus,
  createUserAccess,
  deleteUserAccess,
  updateUserAccessUserId,
  removeUserAccess,
  createUserAccessApproval,
  updateUserAccessApproval,
  getDisseminationAccessPolicy,
  createUserAccessRequest,
  updateUserAccessRequestStatus,
  updateUserAccessRequestIntraIdStatus,
  updateUserAccessRequestUserId,
  getUserAccessApprovals,
  getUserAccessRequests,
  getUserAccessLevels,
  getIntraIDPendingAccess,
  getSUNewAccess,
  deleteUserAccessRequests,
  getSUPendingAccess,
  getSUExistingAccess,
  getApprovedAccess,
  deleteUserData,
  getDGPendingAccess,
  getDGNewAccess,
  getDGExistingAccess,
  getSecondarySU,
  updatePrimarySU,
  getPENewAccess,
  getPEExistingAccess,
  getSecondaryPE,
  updatePrimaryPE,
  getIntraIDPendingAccess,
  createEIDInvitationMapping,
  getEIDInvitationMapping,
  getEIDInvitationMappingByEID,
  updateSecondaryPE,
  updateSecondarySU,
  updateUserData,

  getAllMappingsData,
  getMappingByRequestIdsData,
  getMappingByEmailsData,
  bulkDeleteMappingsData,
  getEntityUsersForExport,
  getEntityInvites,
  getSensitiveAccessData,
  getDeletedUsersList,
  getUserAccessV2,
  getRequestIDsforPendingAccess,
  deleteInvitationByEmail,
  resetUserData,
  deleteUserEIDMapping,
};
