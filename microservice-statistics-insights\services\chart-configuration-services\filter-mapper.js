const {getFilterPanelData} = require('../getGraphData')
const Logger = require('scad-library').logger;
const constants = require('../../../config/constants.json')

async function getFilterConfiguration(indicatorId,type){
    return new Promise(async (resolve, reject) => {
        let log = new Logger().getInstance();
        if (indicatorId == undefined)
            return resolve(false)
        let filterPanelData = await getFilterPanelData(indicatorId,type)
        if (!filterPanelData.length)
            return resolve(false)
        let configurationView = {
            [constants.dynamicConfiguration.officialTag]:"VW_STATISTICAL_IND_ATTRIBUTES",
            [constants.dynamicConfiguration.ifpTag]:"VW_IFP_INDICATOR_ATTRIBUTES"
            }
        let filterPanel = {
            "id": "filter-panel",
            "isEnabled": true,
            "label": "FILTER BY",
            "viewName": configurationView[type],
            "dimension": {
                "INDICATOR_ID": indicatorId
            },
            "properties": []
        }
        let genericProperty = {
            "label": null,
            "path": null,
            "isMultiSeries": false,
            "isRanked": true,
            "default": null,
            "type": "checkbox"
        }
        filterPanelData.forEach(element => {
            let property = {
                ...genericProperty
            };
            property['label'] = element['LABEL']
            property['path'] = element['DIMENSION']
            property['default'] = element['VALUE']
            if (property['path'] == 'OBS_DT')
                property['format'] = 'YYYY'
            filterPanel.properties.push(property)
        })
        return resolve(filterPanel)
    });
    

}

module.exports = { getFilterConfiguration }