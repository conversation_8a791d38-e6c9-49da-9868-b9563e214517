{"paths": {"/content-type/glossary/": {"post": {"tags": ["Glossary"], "summary": "Retrieve glossary information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "page", "in": "query", "required": true, "schema": {"type": "integer", "minimum": 1}, "description": "Page"}, {"name": "limit", "in": "path", "required": true, "schema": {"type": "integer", "minimum": 10}, "description": "Number of items per page"}], "requestBody": {"description": "Filter Data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GlossaryFilter"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"totalCount": {"type": "integer", "description": "The total number of items available."}, "page": {"type": "integer", "description": "The current page number."}, "limit": {"type": "integer", "description": "The maximum number of items per page."}, "alphabets": {"type": "object", "additionalProperties": {"type": "integer"}, "description": "Counts of items starting with each alphabet letter."}, "results": {"type": "array", "items": {"type": "object", "properties": {"TITLE_EN": {"type": "string", "description": "The title of the item in English."}, "TITLE_AR": {"type": "string", "description": "The title of the item in Arabic."}, "DESCRIPTION_EN": {"type": "string", "description": "The description of the item in English."}, "DESCRIPTION_AR": {"type": "string", "description": "The description of the item in Arabic."}, "TOPIC_EN": {"type": "string", "description": "The topic of the item in English."}, "THEME_EN": {"type": "string", "description": "The theme of the item in English."}, "TOPIC_AR": {"type": "string", "description": "The topic of the item in Arabic."}, "THEME_AR": {"type": "string", "description": "The theme of the item in Arabic."}, "TYPE": {"type": "string", "description": "The type of the item, indicating its official status."}, "TOTAL": {"type": "integer", "description": "The total count related to the item."}}, "required": ["TITLE_EN", "DESCRIPTION_EN", "TOPIC_EN", "THEME_EN", "TYPE", "TOTAL"]}, "description": "An array of detailed entries."}}, "required": ["totalCount", "page", "limit", "alphabets", "results"], "description": "Schema for a response containing pagination details, alphabet counts, and detailed entries."}}}}}}}, "/content-type/glossary/filters": {"get": {"tags": ["Glossary"], "summary": "Retrieve filters for glossary", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"domains": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the domain."}, "items": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the item within the domain."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of statistics types associated with the item."}}, "required": ["name", "items"], "description": "An item within the domain, which may represent a subdomain or category."}}}, "required": ["name", "items"], "description": "A domain containing multiple items, each of which may have its own set of statistics types."}}}, "required": ["domains"], "description": "Schema for a response containing a hierarchical structure of domains and their items."}}}}}}}}, "components": {"schemas": {"GlossaryFilter": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"filters": {"type": "object", "properties": {"TOPIC_EN": {"type": "array", "items": {"type": "string"}, "description": "Filter for topics in English."}, "THEME_EN": {"type": "array", "items": {"type": "string"}, "description": "Filter for themes in English."}, "TITLE_EN": {"type": "array", "items": {"type": "string"}, "description": "Filter for titles in English."}}, "required": ["TOPIC_EN", "THEME_EN", "TITLE_EN"], "description": "Filters for selecting content based on topic, theme, and title."}, "sortBy": {"type": "object", "properties": {"alphabetical": {"type": "string", "enum": ["ASC", "DESC"], "description": "Defines the alphabetical sorting order."}}, "required": ["alphabetical"], "description": "Criteria for sorting the filtered results."}}, "required": ["filters", "sortBy"], "description": "Schema for a request body that includes filters for topics, themes, titles, and sorting criteria."}}}}