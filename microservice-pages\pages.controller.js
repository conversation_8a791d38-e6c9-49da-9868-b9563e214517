const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
let constants = require('../config/constants.json');

const crypto = require('crypto')
const {setRedis, getRedis} = require('../services/redis.service')

/**
 * function to get pages content from CMS
 * @param {*} req 
 * @param {*} res
 * @param {*} next
 */

async function getPagesByID(req) {
  log.debug(`>>>>>Entered pages-microservice.pages.controller.getPagesByID`);
  return new Promise(async (resolve, reject) => {
    try {
      let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      let cmsPagesByIDUrl;
      switch (req.params.pageType) {
        case 'domainpageold':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_DOMAIN_URL}${req.params.id}`;
          break;
        case 'listingpage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_LISTING_URL}${req.params.id}`;
          break;
        case 'homepage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_HOME_URL}${req.params.id}`;
          break;
        // case 'whatsnewpage':
        //   cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_WHATS_NEW_URL}`;
        //   break;
        case 'whatsnewpage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_WHATS_NEW_V2}`;
          break;
        case 'tcpage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_TC_URL}${req.params.id}`;
          break;
        case 'categorypage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_CATEGORY_URL}${req.params.id}`;
          break;
        case 'subdomainpage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_SUB_DOMAIN_URL}${req.params.id}`;
          break;
        case 'domainpage':
          cmsPagesByIDUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_DOMAIN_URL_V2}${req.params.id}`;
          break;
      }
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      let data = await getMetaFromCMS(req,cmsLoginUrl, cmsPagesByIDUrl, req.user.groups);

      let cmsCacheKey = 'cmsPagesRes_'+crypto.createHash('md5').update(JSON.stringify(data)).digest("hex")
      const cacheResults = await getRedis(cmsCacheKey,req.headers);
      if (cacheResults){
        log.info(`<<<<<Cache found for pages-microservice.pages.controller.getPagesByID`);
        return resolve(JSON.parse(cacheResults))
      }

      if (req.params.pageType == 'whatsnewpage'){
        data.subsections = data.subSections
        delete data.subSections;
        data.subsections.forEach(subsection => {
          subsection.title = subsection.sectionTitle
          //To be removed once CMS Modification complete
          subsection.key = constants.classificationKeys[subsection.sectionTitle]
          delete subsection.sectionTitle
        })
        return resolve(data)

      }

      if (data[0].pageType === 'subdomainpage') {
        removeDuplicate(data).then(async (removedDuplicateData) => {
          removedDuplicateData.forEach(obj => {
            let route = '/domain-exploration';
            let domainName = obj.parentDomainName.toLowerCase().split(' ').join('-');
            let subdomainPageName = obj.title.toLowerCase().split(' ').join('-');
            obj["route"] = `${route}/${domainName}/${subdomainPageName}`;
            obj['categories'] = obj['categories'].filter((category) => (category.items.length > 0 ));
            
            obj['categories'].forEach(category => {
              category['items'].forEach(item => {
                item.sortOrder = item.sortOrder=='' ? 99999 : Number(item.sortOrder)
              })
              category['items'].sort((a, b) => Number(a.sortOrder) > Number(b.sortOrder) ? 1 : -1)
            })
          });
          log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
          setRedis(cmsCacheKey, JSON.stringify(removedDuplicateData),constants.redis.cmsResponseTTL, req.headers);
          return resolve(removedDuplicateData);
        });
      }
      else if (data[0].pageType === 'domainpage') {
        data.forEach(element => {
          let categories = element.category
          Object.keys(categories).forEach((key) => {
            let nodes = categories[key].nodes
            let contents = []
            if (nodes){
              Object.values(nodes).forEach( node=>{
                contents.push(...node)
              })
            }
            categories[key].nodes = contents
          })
        })
        removeDuplicate(data).then(async (removedDuplicateData) => {
          removedDuplicateData.forEach(obj => {
            let route = '/domain-exploration';
            let domainName = obj.title.toLowerCase().split(' ').join('-');
            obj["route"] = `${route}/${domainName}`;
            obj['categories'] = obj['categories'].filter((category) => (category.items.length > 0 ));
            
            obj['categories'].forEach(category => {
              category['items'].forEach(item => {
                item.sortOrder = item.sortOrder=='' ? 99999 : Number(item.sortOrder)
              })
              category['items'].sort((a, b) => Number(a.sortOrder) > Number(b.sortOrder) ? 1 : -1)
            })
          });
          log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
          setRedis(cmsCacheKey, JSON.stringify(removedDuplicateData),constants.redis.cmsResponseTTL, req.headers);
          resolve(removedDuplicateData);
        });
      } else if (data[0].pageType === 'domainpagebak') {
        removeDuplicate(data).then(async (removedDuplicateData) => {
          removedDuplicateData.forEach(obj => {
            let route = '/domain-exploration';
            let domainPageName = obj.title.toLowerCase().split(' ').join('-');
            obj["route"] = `${route}/${domainPageName}`;
          });
          if (removedDuplicateData[0].title === "Economy Insights") {
            removedDuplicateData[0]["staticContent"] = {
              "indicatorId": "GDP01",
              "contentType": "analytical_apps",
              "appType": "Internal",
              "type": ""
            }
          }
          log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
          setRedis(cmsCacheKey, JSON.stringify(removedDuplicateData),constants.redis.cmsResponseTTL, req.headers);
          return resolve(removedDuplicateData);
        });
      } else if (data[0].pageType === 'homepage') {
        removeDuplicate(data).then(async (removedDuplicateData) => {
          removedDuplicateData.forEach(obj => {
            obj["route"] = '/home';
          });
          log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
          setRedis(cmsCacheKey, JSON.stringify(removedDuplicateData),constants.redis.cmsResponseTTL, req.headers);
          return resolve(removedDuplicateData);
        });
      } else if (data[0].pageType === 'whatsnewpage') {
        removeDuplicate(data).then(async (removedDuplicateData) => {
          removedDuplicateData.forEach(obj => {
            obj["route"] = '/home';
            obj["subsections"].forEach( subsection => {
              subsection.title = subsection.listingPages.listingLabel
              delete subsection.listingPages
            })
          });
          log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
          setRedis(cmsCacheKey, JSON.stringify(removedDuplicateData[0]),constants.redis.cmsResponseTTL, req.headers);
          return resolve(removedDuplicateData[0]);
        });
      }
      else if (data[0].pageType === 'tcpage') {
        removeDuplicate(data).then(async (removedDuplicateData) => {
          removedDuplicateData.forEach(obj => {
            obj["route"] = '/privacy-and-terms';
          });
          log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
          setRedis(cmsCacheKey, JSON.stringify(removedDuplicateData),constants.redis.cmsResponseTTL, req.headers);
          return resolve(removedDuplicateData);
        });
      } else if (data[0].pageType === 'listingpage') {
        let filteredData = data.filter((value, index, array) => array.findIndex(t => t.id == value.id) == index);
        filteredData.forEach( element => {
          element.page_light_icon = element.field_page_light_icon
        })
        log.info(`<<<<<Exited pages-microservice.pages.controller.getPagesByID successfully `);
        setRedis(cmsCacheKey, JSON.stringify(filteredData),constants.redis.cmsResponseTTL, req.headers);
        return resolve(filteredData);
      } else if (data[0].pageType === 'categorypage') {
        const page_icon = process.env.CMS_BASEPATH_URL.concat(data[0].icon_path);
        const page_light_icon = process.env.CMS_BASEPATH_URL.concat(data[0].light_icon_path);
        delete data[0].icon_path;
        delete data[0].light_icon_path;
        data[0].page_icon = page_icon;
        data[0].page_light_icon = page_light_icon;
        const nodesData = data[0].nodes;
        let result = [];
        data[0]['page_description'] = data[0]['description'];
        delete data[0]['description'];
        for (const node of nodesData) {
          if (data[0].categorySectiontype === 'indicatorList') {
            const node_obj = new Object();
            node_obj.id = node.id;
            node_obj.contentType = (node.content_type === null) ? "" : node.content_type;
            node_obj.appType = (node.app_type === null) ? "" : node.app_type;
            node_obj.type = (node.type === null) ? "" : node.type;
            node_obj.type = (node.app_type === null) ? "" : node.app_type.toLowerCase().split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('-');
            node_obj.title = (node.title === null) ? "" : node.title;
            result.push(node_obj);
          }
          else if (data[0].categorySectiontype === 'thumbnails') {
            for (var key in node) {
              if (node.hasOwnProperty(key)) {
                if (node[key] === null) {
                  node[key] = "";
                }
              }
            }
            node.type = (node.app_type === null) ? "" : node.app_type.toLowerCase().split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('-');
            node.imgSrc = (node.imgSrc) ? process.env.CMS_BASEPATH_URL.concat(node.imgSrc) : "";
          }
        }
        result = (result.length > 0) ? result : data[0].nodes;
        delete data[0].nodes;
        result_obj = { "items": result };
        data[0].categories = [result_obj];
        setRedis(cmsCacheKey, JSON.stringify(data),constants.redis.cmsResponseTTL, req.headers);
        return resolve(data);
      }
    } catch (err) {
      
      log.error(`<<<<<Exited pages-microservice.pages.controller.getPagesByID ${req.params.pageType} on getting CMS data with error ${err}`);
      reject(err);
    }
  })
}

async function getPages(req) {
  log.debug(`>>>>>Entered pages-microservice.pages.controller.getPages`);
  return new Promise(async (resolve, reject) => {
    try {
      let lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
      let cmsPagesUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_PAGES_BYID_HOME_URL}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
      let data = await getMetaFromCMS(req,cmsLoginUrl, cmsPagesUrl, req.user.groups);
      
      let pageData = await removeDuplicate(data)
      pageData.forEach(obj => {
            obj["route"] = '/home';
          });

      pageData.forEach(element => {
        element["disabled"] = false;
        element["navigable"] = true;
      });

      let result = [
        {
          "label": "",
          "pageList": pageData
        }
      ];
      log.info(`<<<<<Exited pages-microservice.pages.controller.getPages successfully `);
      return resolve(result);
    } catch (err) {
      
      log.error(`<<<<<Exited pages-microservice.pages.controller.getPages on getting CMS data with error ${err}`);
      reject(err);
    }
  })
}


const groupBy = (arr, property) => {
  return arr.reduce((acc, cur) => {
    acc[cur[property]] = [...acc[cur[property]] || [], cur];
    return acc;
  }, {});
}

async function removeDuplicate(data) {
  return new Promise((resolve, reject) => {
    try {
      const pages = [];
      const pagesObj = groupBy(data, 'title');
      Object.keys(pagesObj).forEach((key) => {
        if (pagesObj[key][0].pageType === 'homepage') {
          pages.push({
            title: key, 
            id: pagesObj[key][0].id, 
            pageDescription: pagesObj[key][0].page_description, 
            pageMenuIcon: pagesObj[key][0].page_menu_icon, 
            pageMenuLightIcon: pagesObj[key][0].field_page_menu_light_icon, 
            pageIcon: pagesObj[key][0].page_icon, 
            pageLightIcon: pagesObj[key][0].field_page_light_icon, 
            pageType: pagesObj[key][0].pageType, 
            publication_date: pagesObj[key][0].publication_date, 
            subsections: pagesObj[key] 
          });
        }
        else if (pagesObj[key][0].pageType === 'whatsnewpage') {
            pages.push({
              title: key, 
              id: pagesObj[key][0].id, 
              pageDescription: pagesObj[key][0].page_description, 
              pageMenuIcon: pagesObj[key][0].page_menu_icon, 
              pageMenuLightIcon: pagesObj[key][0].field_page_menu_light_icon, 
              pageIcon: pagesObj[key][0].page_icon, 
              pageLightIcon: pagesObj[key][0].field_page_light_icon, 
              pageType: pagesObj[key][0].pageType, 
              publication_date: pagesObj[key][0].publication_date, 
              subsections: pagesObj[key] 
            });
        } else if (pagesObj[key][0].pageType === 'tcpage') {
          pages.push({ 
            title: key, 
            tcVersion: pagesObj[key][0].tcVersion, 
            id: pagesObj[key][0].id, 
            pageType: pagesObj[key][0].pageType, 
            publication_date: pagesObj[key][0].publicationDate, 
            sections: pagesObj[key] 
          });
        } else if (pagesObj[key][0].pageType === 'subdomainpage') {
          pages.push({ 
            title: key, 
            id: pagesObj[key][0].id, 
            pageType: pagesObj[key][0].pageType, 
            pageDescription: pagesObj[key][0].page_description, 
            pageMenuIcon: pagesObj[key][0].page_menu_icon, 
            pageMenuLightIcon: pagesObj[key][0].page_menu_light_icon, 
            pageIcon: pagesObj[key][0].page_icon, 
            pageLightIcon: pagesObj[key][0].page_light_icon, 
            parentDomainName: pagesObj[key][0].parent_domain_name, 
            categories: pagesObj[key][0].category, 
            highlights: pagesObj[key][0].highlights, 
            likeSections: pagesObj[key][0].like_section,
            highlightsFlag: pagesObj[key][0].highlightsFlag, likeSectionFlag: pagesObj[key][0].likeSectionFlag });
        } else if (pagesObj[key][0].pageType === 'domainpage') {
          pages.push({ 
            title: key, 
            id: pagesObj[key][0].id, 
            pageType: pagesObj[key][0].pageType, 
            pageDescription: pagesObj[key][0].page_description, 
            pageMenuIcon: pagesObj[key][0].page_menu_icon, 
            pageMenuLightIcon: pagesObj[key][0].page_menu_light_icon, 
            pageIcon: pagesObj[key][0].page_icon, 
            pageLightIcon: pagesObj[key][0].page_light_icon, 
            parentDomainName: pagesObj[key][0].parent_domain_name, 
            categories: pagesObj[key][0].category, 
            highlights: [], 
            highlightsFlag: pagesObj[key][0].highlightsFlag, likeSectionFlag: pagesObj[key][0].likeSectionFlag });
        }else {
          pages.push({ 
            title: key, id: pagesObj[key][0].id, 
            pageType: pagesObj[key][0].pageType, 
            pageDescription: pagesObj[key][0].page_description,
            pageMenuIcon: pagesObj[key][0].page_menu_icon, 
            pageMenuLightIcon: pagesObj[key][0].page_menu_light_icon, 
            pageIcon: pagesObj[key][0].page_icon, 
            pageLightIcon: pagesObj[key][0].page_light_icon, 
            domains: pagesObj[key][0].domains, 
            subsections: pagesObj[key], 
            enableSubmenu: pagesObj[key][0].enableSubmenu });
        }
      })
      pages.forEach(page => {
        if (page.pageType === 'subdomainpage' || page.pageType === 'domainpage' ) {
          let categories = Object.values(page.categories);
          categories.filter(category => {
            category.icon_path = category.icon_path ? `${process.env.CMS_BASEPATH_URL}${category.icon_path}` : "";
            category.light_icon_path = category.light_icon_path ? `${process.env.CMS_BASEPATH_URL}${category.light_icon_path}` : "";
            category.items = [];
            if (category.categorySectiontype === "indicatorList") {
              category.nodes.map(n => {
                category.items.push({
                  indicatorId: n.id,
                  contentType: n.content_type ? n.content_type : '',
                  sortOrder: n.sortOrder ? n.sortOrder : '',
                  appType: n.app_type ? n.app_type : '',
                  type: n.type ? n.type : ''
                })
              })
            } else { //thumbnails
              category.items = category.nodes;
              for (var key in category.items) {
                if (category.items.hasOwnProperty(key)) {
                  if (category.items[key] === null) {
                    category.items[key] = "";
                  }
                }
              }
              category.items.forEach(obj => Object.keys(obj).forEach(key => {
                obj[key] = (obj[key] === null) ? "" : obj[key];
              }));
              category.items.forEach(i => i.imgSrc = i.imgSrc ? `${process.env.CMS_BASEPATH_URL}${i.imgSrc}` : "");
              category.items.forEach(i => i.type = i.app_type ? i.app_type.toLowerCase().split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('-') : "");
            }
            delete category.nodes;
          })
          page.categories = categories;
          let highlights = [];
          if (Object.keys(page.highlights).length > 0) {
            page.highlights.items = [];
            if (page.highlights.categorySectiontype === "indicatorList") {
              page.highlights.nodes.filter(n => {
                page.highlights.items.push({
                  indicatorId: n.id,
                  contentType: n.content_type ? n.content_type : '',
                  appType: n.app_type ? n.app_type : '',
                  type: n.type ? n.type : ''
                })
              })
            } else {
              page.highlights.items = page.highlights.nodes;
              page.highlights.items.forEach(obj => Object.keys(obj).forEach(key => {
                obj[key] = (obj[key] === null) ? "" : obj[key];
              }));
              page.highlights.items.forEach(i => i.imgSrc = i.imgSrc ? `${process.env.CMS_BASEPATH_URL}${i.imgSrc}` : "");
            }
            delete page.highlights.nodes;
            highlights.push(page.highlights);
            page.highlights = highlights;
          }
          if (page.likeSections) {
            let section = [];
            let newsletters = page.likeSections.nodes.filter(n => n.content_type === 'newsletters');
            newsletters.forEach(obj => Object.keys(obj).forEach(key => {
              obj[key] = (obj[key] === null) ? "" : obj[key];
            }));
            newsletters.forEach(o => {
              o.imgSrc = o.imgSrc ? `${process.env.CMS_BASEPATH_URL}${o.imgSrc}` : "";
              o.publication_attachment = o.publication_attachment ? `${process.env.CMS_BASEPATH_URL}${o.publication_attachment}` : "";
              o.excel_attachment = o.excel_attachment ? `${process.env.CMS_BASEPATH_URL}${o.excel_attachment}` : "";
            });
            let publications = page.likeSections.nodes.filter(n => n.content_type === 'publications');
            publications.forEach(obj => Object.keys(obj).forEach(key => {
              obj[key] = (obj[key] === null) ? "" : obj[key];
            }));
            publications.forEach(o => {
              o.imgSrc = o.imgSrc ? `${process.env.CMS_BASEPATH_URL}${o.imgSrc}` : "";
              o.publication_attachment = o.publication_attachment ? `${process.env.CMS_BASEPATH_URL}${o.publication_attachment}` : "";
              o.excel_attachment = o.excel_attachment ? `${process.env.CMS_BASEPATH_URL}${o.excel_attachment}` : "";
            });
            section.push({
              categorySectiontype: "newsletterthumbnails",
              items: newsletters
            })
            section.push({
              categorySectiontype: "publicationthumbnails",
              items: publications
            })
            page.likeSections = section;
          }
          delete page.category;
        } else {
          let sectionObj = (page.pageType === 'homepage')
            ? groupBy(page.subsections, 'sectionOrder')
              :(page.pageType === 'whatsnewpage')
              ? groupBy(page.subsections, 'sectionOrder')
                : (page.pageType === 'domainpage')
                  ? groupBy(page.subsections, 'subsection_title')
                  : (page.pageType === 'tcpage')
                    ? groupBy(page.sections, 'sectionOrder')
                    : [];
          page.subsections = (page.subsections) ? [] : undefined;
          page.sections = (page.sections) ? [] : undefined;
          Object.keys(sectionObj).forEach((key) => {
            let indicatorLists = [];
            let indicators = [];
            let sectionSubObj = [];
            if (page.pageType === 'tcpage') {
              let innerSubSectionObj = groupBy(sectionObj[key], 'subsectionOrder');
              Object.keys(innerSubSectionObj).forEach((key) => {
                sectionSubObj.push({
                  subsectionContent: innerSubSectionObj[key][0].subsectionContent,
                  subsectionOrder: innerSubSectionObj[key][0].subsectionOrder,
                  subsectionTitle: innerSubSectionObj[key][0].subsectionTitle
                })
              })
              page.sections.push({
                sectionOrder: sectionObj[key][0].sectionOrder,
                sectionTitle: sectionObj[key][0].sectionTitle,
                subsections: sectionSubObj
              })
            } else {
              sectionObj[key].forEach(obj => {
                if (obj.pageType === 'homepage' && obj.indicator_list) {
                  indicators = obj.indicator_list.split(',').map(item => item.trim());
                  indicators.forEach(i => {
                    if (i === obj.nodeId) {
                      indicatorLists.push({
                        indicatorId: i,
                        contentType: obj.contentType,
                        appType: obj.appType,
                        type: obj.type,
                      })
                    }
                  })
                }
                else if (obj.pageType === 'whatsnewpage' && obj.indicator_list) {
                  indicators = obj.indicator_list.split(',').map(item => item.trim());
                  indicators.forEach(i => {
                    if (i === obj.nodeId) {
                      indicatorLists.push({
                        indicatorId: i,
                        contentType: obj.contentType,
                        appType: obj.appType,
                        type: obj.type,
                      })
                    }
                  })
                } else if (obj.pageType === 'domainpage') {
                  indicatorLists.push({
                    indicatorId: obj.indicatorId,
                    contentType: obj.contentType,
                    appType: obj.appType,
                    type: obj.type
                  })
                }
              });
              indicatorLists = [...new Map(indicatorLists.map(item => [item['indicatorId'], item])).values()];
              indicatorOrder = sectionObj[key][0].indicator_list.split(',').map(item => item.trim());
              indicatorLists.sort((a, b) => indicatorOrder.indexOf(a.indicatorId) - indicatorOrder.indexOf(b.indicatorId));
              if (page.pageType === 'homepage') {
                page.subsections.push({
                  sectionOrder: sectionObj[key][0].sectionOrder,
                  sectionType: sectionObj[key][0].sectionType,
                  sectionTitle: sectionObj[key][0].sectionTitle,
                  indicatorList: indicatorLists,
                  listingPages: {
                    listingType: sectionObj[key][0].listingType,
                    listingLimit: sectionObj[key][0].listingLimit,
                    listingLabel: sectionObj[key][0].listingLabel
                  },
                });
              }
              else if (page.pageType === 'whatsnewpage') {
                page.subsections.push({
                  sectionOrder: sectionObj[key][0].sectionOrder,
                  sectionType: sectionObj[key][0].sectionType,
                  indicatorList: indicatorLists,
                  listingPages: {
                    listingType: sectionObj[key][0].listingType,
                    listingLimit: sectionObj[key][0].listingLimit,
                    listingLabel: sectionObj[key][0].listingLabel
                  },
                });
              } else {
                page.subsections.push({
                  sectionOrder: sectionObj[key][0].sectionOrder,
                  subsection_title: key,
                  subsection_id: key.toLowerCase().split(' ').join('-'),
                  subsection_subtitle: sectionObj[key][0].subsection_subtitle,
                  indicatorList: indicatorLists
                });
              }
            }
          });
        }
      });
      resolve(pages);
    } catch (err) {
      
      reject(err);
    }
  });
}

module.exports = { getPagesByID, removeDuplicate, groupBy, getPages };
