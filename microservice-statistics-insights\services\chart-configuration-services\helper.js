const charMock = require('../chart-configuration-services/chart-conf.json')
const getCategoricalDimensionsData = require('../../services/getGraphData').getCategoricalDimensionsData
const colorCodes = require('../chart-configuration-services/color-map.json')
const { getVisualizationSeriesData, getPeriodFilterData } = require('../getGraphData')
const Logger = require('scad-library').logger;

async function generateSeriesMeta(visualization, chartId, genericSeriesData) {
    let log = new Logger().getInstance();
    // let genericSeriesData = await getVisualizationSeriesData(nodeId,type)
    if (!genericSeriesData.length) {
        log.info("[DYNAMIC JSON] Series data not found")
        return []
    }

    genericSeriesData.forEach(series => {
        series.SERIES_KEY = series.JSON_KEY
        delete series.JSON_KEY
        series.SERIES_VALUE = series.JSON_VALUE
        delete series.JSON_VALUE
    })

    let generatedSeriesMeta = []
    let genericSeries = {
        "type": "",
        "xAccessor": {},
        "yAccessor": {}
    }
    genericSeriesData.forEach(element => {
        if (element.SERIES_KEY.includes('_')) {
            split_keys = element.SERIES_KEY.split('_')

            if (!(split_keys[0] in genericSeries))
                genericSeries[split_keys[0]] = {}

            genericSeries[split_keys[0]][split_keys[1]] = element.SERIES_VALUE
        }
        else
            genericSeries[element.SERIES_KEY] = element.SERIES_VALUE

    })

    let dimensionMeta = {
        'viewName': visualization.viewName,
        'dimensionColumn': visualization.dimensionColumn,
        'dbColumn': visualization.dbColumn,
        'dbIndicatorId': visualization.dbIndicatorId
    }
    if (dimensionMeta.dimensionColumn) {
        dimensionValues = await getCategoricalDimensionsData(dimensionMeta)

        let rawDimensionValues = []
        dimensionValues.forEach(dimension => {
            rawDimensionValues.push(dimension[visualization.dimensionColumn].toUpperCase())
        })

        if (genericSeries.limitDimensionValues) {
            rawDimensionValues = rawDimensionValues.filter(value => charMock.dimensionValue[seriesMetaId].includes(value));
        }

        rawDimensionValues.forEach((dimValue, index) => {
            let seriesLabel = dimValue.toLowerCase().replace(/\w\S*/g,
                function (txt) {
                    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
                }
            )
            let series = {
                ...genericSeries
            };
            series['id'] = chartId + index.toString()
            series['label'] = seriesLabel
            series['color'] = colorCodes[index]
            series['dimensionValue'] = dimValue.toUpperCase()
            delete series['limitDimensionValues']
            generatedSeriesMeta.push(series)
        })
    }
    else {
        let series = {
            ...genericSeries
        };
        delete visualization.dbIndicatorId
        series['id'] = chartId
        series['dbIndicatorId'] = dimensionMeta.dbIndicatorId
        series['label'] = null
        series['color'] = colorCodes[0]
        generatedSeriesMeta.push(series)
    }

    return generatedSeriesMeta
}

function generatePeriodFilters(data) {
    // let period_filter_data = await getPeriodFilterData(indicatorId,type)
    let period_filter_data = data
    const convertedArray = [];
    const groupedObjects = {};

    for (const obj of period_filter_data) {
        const filterGroup = obj.FILTER_GROUP;
        if (!groupedObjects[filterGroup]) {
            groupedObjects[filterGroup] = {};
        }
        const group = groupedObjects[filterGroup];
        group[obj.FILTER_KEY] = obj.FILTER_VALUE !== null ? obj.FILTER_VALUE : null;
    }

    for (const filterGroup in groupedObjects) {
        const group = groupedObjects[filterGroup];
        const convertedObj = {
            id: group["id"],
            label: group["label"],
            unit: group["unit"],
            value: group["value"] !== null ? parseInt(group["value"]) : null,
        };
        if (group["isSelected"]) {
            convertedObj.isSelected = group["isSelected"].toLowerCase()=="true"?true:false;
        }
        convertedArray.push(convertedObj);
    }

    return convertedArray
}

function assignConfigurationValuesResponse(cmsResponse,dynamicVisualization,lang){
    
    const dv = dynamicVisualization[0];

    if (dv.filterPanelData)
        dv.filterPanelData.properties = dv.filterPanelData.properties.filter(property=> property.lang == (lang?lang.toUpperCase():'EN'))

    const response = {
        unit: (lang?dv[`unit_${lang}`]:dv['unit']) || "",
        data_source: (lang?dv[`data_source_${lang}`]:dv['data_source']) || "",
        viewName: dv.viewName || null,
        indicatorId: dv.indicatorId || null,
        isMultiDimension: !!dv.isMultiDimension,
        component_title: (lang?dv[`componentTitle_${lang}`]:dv['componentTitle']) || "",
        component_subtitle: (lang?dv[`componentSubtitle_${lang}`]:dv['componentSubtitle']) || "",
        indicatorFilters: dv.indicatorFilters || null,
        filterPanel: dv.filterPanelData || false
      };
    dv.componentTitle = (lang? dv[`componentTitle_${lang}`]:dv['componentTitle']) || ""
    dv.yAxisLabel=(lang?dv[`yAxisLabel_${lang}`]:dv['yAxisLabel']) || ""
    cmsResponse = {...cmsResponse,...response}
    
    return cmsResponse
}

module.exports = { generateSeriesMeta, generatePeriodFilters, assignConfigurationValuesResponse }