const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');

const {getIndicatorDataQuery,createCompareAppDataQuery,listMyAppsDataQuery,getCompareDataQuery,addAppToMyAppsDataQuery,getCompareNodesInfoQuery} = require('./getQuery.service')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function getIndicatorData(indicatorId, viewName, lang) {
  try {
    const results = await getIndicatorDataQuery(indicatorId, viewName, lang);
    const data = await getClkData(results.query, results.binds);
    log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData with error ${err}`);
    throw [423, err];
  }
}

async function createCompareAppData(myAppsData) {
  try {
    const results = await createCompareAppDataQuery(myAppsData);
    const data = await getData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.createCompareAppData with error ${err}`);
    throw err;
  }
}

async function listMyAppsData(email) {
  try {
    const results = await listMyAppsDataQuery(email);
    const data = await getData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.listMyAppsData with error ${err}`);
    throw err;
  }
}

async function getCompareData(id, email) {
  try {
    const results = await getCompareDataQuery(id, email);
    const data = await getData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.getCompareData with error ${err}`);
    throw err;
  }
}

/**
 * Function to add an app to my apps
 * @param {*} id - node id
 * @param {*} userEmail - user email 
 */
async function addAppToMyAppsData(node, userEmail) {
  try {
    const results = await addAppToMyAppsDataQuery(node, userEmail);
    const data = await getData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.addAppsToMyAppsData with error ${err}`);
    throw err;
  }
}

async function getCompareNodesInfo(nodes) {
  try {
    const results = await getCompareNodesInfoQuery(nodes);
    const data = await getClkData(results.query, results.binds);
    return data;
  } catch (err) {
    log.error(`<<<<< Exited microservice-indicator-compare.executeQuery.service.addAppsToMyAppsData with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-indicator-compare.services.executeQuery.service.getData`);
    const data = await db.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

async function getClkData(query, binds = {}) {
  try {
    log.debug(`>>>>> Enter microservice-indicator-compare.services.executeQuery.service.getData`);
    const data = await clkdb.simpleExecute(query, binds);
    log.debug(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData successfully`);
    return data;
  } catch (err) {
    log.error(`<<<<< Exit microservice-indicator-compare.services.executeQuery.service.getData with error ${err}`);
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getIndicatorData,
  createCompareAppData,
  listMyAppsData,
  getCompareData,
  addAppToMyAppsData,
  getCompareNodesInfo
}