'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create history table
    await queryInterface.createTable('bayaan_approval_request_history', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false
      },
      request_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'bayaan_approval_requests',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      previous_status: {
        type: Sequelize.STRING(20),
        allowNull: true
      },
      new_status: {
        type: Sequelize.STRING(20),
        allowNull: false
      },
      previous_assignee: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      new_assignee: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      changed_by: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      changed_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      comment_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'bayaan_approval_request_comments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      }
    });
    
    // Add indexes
    await queryInterface.addIndex('bayaan_approval_request_history', ['request_id']);
    await queryInterface.addIndex('bayaan_approval_request_history', ['comment_id']);
    await queryInterface.addIndex('bayaan_approval_request_history', ['changed_by']);
  },
  
  async down(queryInterface, Sequelize) {
    // Drop the history table
    await queryInterface.dropTable('bayaan_approval_request_history');
  }
};