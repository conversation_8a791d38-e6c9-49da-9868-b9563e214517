{"id": "", "component_title": "", "component_subtitle": "", "domain": "", "type": "innovative_statistics", "note": "", "maxPointLimit": "", "minLimitYAxis": "", "content_classification": "الإحصاءات التجريبية", "domain_id": "", "tagName": "Innovative Statistics", "security": {"id": "1", "name": "1 - خ<PERSON>و<PERSON>ي", "label": "خصوصي", "description": "بيانات مصرح الوصول لها لجميع الجهات الحكومية المحلية والاتحادية، والاجهزة الإحصائية التابعة للإمارات الأخرى، والشركات الحكومية المملوكة بشكل تام أو جزئي لإمارة أبوظبي."}, "language": "EN", "indicatorTools": [{"id": "export-png", "disabled": true, "label": "Export PNG"}, {"id": "export-csv", "label": "Export CSV"}], "indicatorFilters": [{"id": "period-filter", "options": [{"id": "3-years", "label": "3 YEARS", "unit": "years", "value": 3, "isSelected": true}, {"id": "5-years", "label": "5 YEARS", "unit": "years", "value": 5}, {"id": "All", "label": "ALL", "unit": null, "value": null}]}], "indicatorDrivers": [], "indicatorValues": {}, "indicatorVisualizations": {"visualizationsMeta": [{}], "visualizationDefault": ""}}