const express = require('express');
const router = new express.Router();
const newsletterController = require('../microservice-newsletter/newsletter.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/', async (req, res, next) => {
    try {
      const data = await newsletterController.getNewsletters(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for newsletter content-type, ERROR: ${err}`);
      next(err);
    }
});

module.exports = router;