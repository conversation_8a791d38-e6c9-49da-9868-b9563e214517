const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


/**
 * Generates a SQL query to fetch all chart insights for a given node ID, ordered by the add date.
 * @param {string} nodeId - The node ID for which to fetch the chart insights.
 * @returns {Promise<string>} - The SQL query string.
 */
async function listChartInsightDataQuery(nodeId, isSubNode) {
    return new Promise((resolve, reject) => {
        try {
            const whereCondition = isSubNode ? `SUBNODE_ID = '${nodeId}'` : `NODE_ID = ${nodeId}`
            const query = `SELECT * FROM IFP_CHART_INSIGHTS_V3 WHERE ${whereCondition} ORDER BY ADD_DT DESC`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.listChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to insert a new chart insight into the database.
 * @param {Object} insightData - The data for the new chart insight.
 * @returns {Promise<string>} - The SQL query string to insert the chart insight.
 */
async function addChartInsightDataQuery(insightData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_CHART_INSIGHTS_V3 ( EMAIL, USER_NAME, NODE_ID, SUBNODE_ID, NODE_TITLE, NODE_LINK, INSIGHT, STATUS, ADD_DT ) VALUES ( '${insightData.email}','${insightData.user}',${insightData.nodeId}, '${insightData.subNodeId}', '${insightData.nodeTitle}','${insightData.nodeLink}','${insightData.insight}', 'PENDING', TO_TIMESTAMP('${insightData.date}','DD/MM/YYYY HH24:MI:SS') )`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.addChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to fetch a chart insight by its ID.
 * @param {string} id - The ID of the chart insight to fetch.
 * @returns {Promise<string>} - The SQL query string to fetch the chart insight.
 */
async function getChartInsightDataQuery(id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_CHART_INSIGHTS_V3 WHERE ID = ${id}`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to fetch multiple chart insights by their IDs.
 * @param {Array<string>} ids - The list of chart insight IDs to fetch.
 * @returns {Promise<string>} - The SQL query string to fetch the chart insights.
 */
async function getChartInsightsDataQuery(ids) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_CHART_INSIGHTS_V3 WHERE ID IN (${ids.toString()})`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

/**
 * Generates a SQL query to update an existing chart insight with new data.
 * @param {Object} insightData - The new data for the chart insight.
 * @returns {Promise<string>} - The SQL query string to update the chart insight.
 */
async function updateChartInsightDataQuery(insightData) {
    return new Promise((resolve, reject) => {
        try {
            let query
            query = `UPDATE IFP_CHART_INSIGHTS_V3 SET NODE_TITLE='${insightData.nodeTitle}', NODE_LINK='${insightData.nodeLink}',  INSIGHT='${insightData.insight.replace(/'/g, "''")}', STATUS='${insightData.status}', ADD_DT=TO_TIMESTAMP('${insightData.date}','DD/MM/YYYY HH24:MI:SS'), EMAILSENT=0 WHERE ID='${insightData.id}'`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.updateChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to delete a chart insight by its ID.
 * @param {string} id - The ID of the chart insight to delete.
 * @returns {Promise<string>} - The SQL query string to delete the chart insight.
 */
async function deleteChartInsightDataQuery(id) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM IFP_CHART_INSIGHTS_V3 WHERE ID = ${id} `
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.deleteInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

/**
 * Generates a SQL query to fetch chart insights that need to be submitted for approval.
 * @param {string} email - The email of the user requesting the insights.
 * @param {Array<string>} insightIds - The IDs of the insights to check.
 * @returns {Promise<string>} - The SQL query string to fetch the chart insights.
 */
async function getSubmitRequestChartInsightsDataQuery(email, insightIds) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_CHART_INSIGHTS_V3 WHERE EMAIL = '${email}' AND EMAILSENT = 0 AND STATUS = 'PENDING' AND ID IN (${insightIds.toString()})`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getSubmitRequestChartInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to update chart insights to mark them as submitted for approval.
 * @param {string} email - The email of the user submitting the insights.
 * @param {Array<string>} insightIds - The IDs of the insights to update.
 * @returns {Promise<string>} - The SQL query string to update the chart insights.
 */
async function updateSubmitRequestChartInsightsDataQuery(email, insightIds) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_CHART_INSIGHTS_V3 SET EMAILSENT = 1 WHERE EMAIL = '${email}' AND EMAILSENT = 0 AND STATUS = 'PENDING' AND ID IN (${insightIds.toString()})`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getSubmitRequestInsightsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to approve chart insights based on their IDs and the given date.
 * @param {Array<string>} ids - The list of insight IDs to approve.
 * @param {string} date - The date of approval.
 * @returns {Promise<string>} - The SQL query string to approve the chart insights.
 */
async function approveChartInsightDataQuery(ids,date) {
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_CHART_INSIGHTS_V3 SET STATUS = 'APPROVED', ADD_DT=TO_TIMESTAMP('${date}','YYYY-MM-DD HH24:MI:SS'), EMAILSENT = 1 WHERE ID IN (${ids.toString()}) `
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.approveChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

/**
 * Generates a SQL query to reject chart insights based on their IDs and the given date.
 * @param {Array<string>} ids - The list of insight IDs to reject.
 * @param {string} date - The date of rejection.
 * @returns {Promise<string>} - The SQL query string to reject the chart insights.
 */
async function rejectChartInsightDataQuery(ids,date){
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_CHART_INSIGHTS_V3 SET STATUS = 'REJECTED', ADD_DT=TO_TIMESTAMP('${date}','YYYY-MM-DD HH24:MI:SS'), EMAILSENT = 0 WHERE ID IN (${ids.toString()}) `
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.rejectChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


/**
 * Generates a SQL query to request edit for chart insights based on their IDs and the given date.
 * @param {Array<string>} ids - The list of insight IDs to request edits.
 * @param {string} date - The date of edit request.
 * @returns {Promise<string>} - The SQL query string to request the chart insight edit.
 */
async function requestEditChartInsightDataQuery(ids,date){
    return new Promise((resolve, reject) => {
        try {
            let query = `UPDATE IFP_CHART_INSIGHTS_V3 SET STATUS = 'PENDING', ADD_DT=TO_TIMESTAMP('${date}','YYYY-MM-DD HH24:MI:SS'), EMAILSENT = 0 WHERE ID IN (${ids.toString()}) `
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.requestEditChartInsightDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

/**
 * Generates a SQL query to fetch all insight users .
 * @returns {Promise<string>} - The SQL query string to fetch all the insight users.
 */
async function getInsightUsersQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT INSIGHT_USERS FROM IFP_CHART_INSIGHTS_APPROVALS WHERE ROWNUM = 1`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightUsersQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

/**
 * Generates a SQL query to fetch the insight approver .
 * @returns {Promise<string>} - The SQL query string to fetch the insight approver.
 */
async function getInsightApproverQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT INSIGHT_APPROVER FROM IFP_CHART_INSIGHTS_APPROVALS WHERE ROWNUM = 1`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightApproverQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

/**
 * Generates a SQL query to fetch the insight approver name.
 * @returns {Promise<string>} - The SQL query string to fetch the insight approver name.
 */
async function getInsightApproverNameQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT INSIGHT_APPROVER_NAME FROM IFP_CHART_INSIGHTS_APPROVALS WHERE ROWNUM = 1`
            return resolve(query);
        } catch (err) {
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getInsightApproverNameQuery with error ${err} `);
            reject([424, err]);
        }
    });
}
module.exports = { 
    listChartInsightDataQuery, addChartInsightDataQuery, getChartInsightDataQuery, updateChartInsightDataQuery, deleteChartInsightDataQuery, 
    getChartInsightsDataQuery, getSubmitRequestChartInsightsDataQuery, updateSubmitRequestChartInsightsDataQuery, approveChartInsightDataQuery, rejectChartInsightDataQuery,
    requestEditChartInsightDataQuery,getInsightUsersQuery, getInsightApproverQuery, getInsightApproverNameQuery
};
