const Logger = require('scad-library').logger;
const { getMetaFromCMS } = require('../services/common-service');
require('dotenv').config();
const constants = require('../config/constants.json');
const axios = require('axios');


const log = new Logger().getInstance();
/**
 * function to get whats-new content from CMS
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function getImages(req) {
  log.debug('>>>>>Entered images-microservice.images.controller.getImages');
  return new Promise((resolve, reject) => {
    const iconId = req.params.iconId
    const cmsCompareUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_COUNTRY_LIST}${iconId}`;
    axios.get(cmsCompareUrl).then(iconData => {
      let iconUrl = `${process.env.CMS_BASEPATH}${((req.path.includes('default')) && iconData.data.length === 0)
        ? '/'+iconId+req.path
        : iconData.data[0].country_flag}`;
      axios.get(iconUrl).then(response => {
        resolve(response.data);
      }).catch(err => {
        
        log.error(`<<<<<Exited images-microservice.images.controller.getImages on getting CMS data with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<<Exited images-microservice.images.controller.getImages on getting CMS data with error ${err}`);
      reject(err);
    });
  });
}
module.exports = { getImages };
