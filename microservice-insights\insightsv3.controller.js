require('dotenv').config();

const Logger = require('scad-library').logger;
const moment = require('moment')
const log = new Logger().getInstance();
const { 
    listChartInsightData, addChartInsightData, getChartInsightData, updateChartInsightData, deleteChartInsightData, 
    updateSubmitRequestChartInsightsData, getChartInsightsData, getSubmitRequestChartInsightsData, approveChartInsightData,
    rejectChartInsightData, requestEditChartInsightData, getInsightUsers, getInsightApprover, getInsightApproverName
} = require('./servicesv3/executeQuery.service');
const { sendChartInsightsEmail } = require('./servicesv3/sendEmail.service');
const { validateEmailContent } = require('../services/helpers/helper');

/**
 * Function to list chart insights based on user roles.
 * It categorizes users into three roles: Approver, Insight User (SME), and Regular User.
 *
 * @param {Object} req - The HTTP request object containing user details and parameters to fetch chart insights.
 * @param {Object} req.params - Contains the `id` parameter representing the node ID for the chart insights.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user, used to determine user roles.
 * @returns {Promise<Object>} - Resolves with a structured response containing chart insights and role-specific settings.
 *                               If there are any errors during the process, the promise is rejected with an error message.
 * @throws {Error} - If there is an unexpected issue during data fetching or processing.
 */
async function listChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.listChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const nodeId = req.params.id
            const isSubNode = req.query?.isSubNode == 'true'
            let insightUsers = await getInsightUsers()
            if (!(insightUsers && insightUsers[0] && insightUsers[0]?.INSIGHT_USERS)) {
                log.debug("No INSIGHT_USERS found");
            } 

            let insightApprover = await getInsightApprover()
            if (!(insightApprover && insightApprover[0] && insightApprover[0]?.INSIGHT_APPROVER)) {
                log.debug("No INSIGHT_APPROVER found");
            } 

            insightUsers = insightUsers[0]?.INSIGHT_USERS?.split(',')
            insightApprover = insightApprover[0]?.INSIGHT_APPROVER?.split(',')
            
            let isInsightUser = insightUsers.includes(req.user.preferred_username)?true:false
            let showSubmitForApproval = (insightApprover.includes(req.user.preferred_username))?false:insightUsers.includes(req.user.preferred_username)?true:false
            let isApprover = (insightApprover.includes(req.user.preferred_username))?true:false
            let enableSubmitForApproval = false
            let enableRequestForEdit = false
            listChartInsightData(nodeId, isSubNode)
                .then(results => {
                    if (insightApprover.includes(req.user.preferred_username)){
                        log.debug(`List Chart Insights: Request user is an approver`);
                        results = results.filter(insight => {return insight.EMAILSENT == 1})
                        results.forEach(insight => {
                                insight.isEdit = true
                                insight.showApprove = true
                                insight.showApprove = insight.STATUS == 'PENDING'?insight.showApprove:false
                                insight.showDelete = true
                                enableRequestForEdit = enableRequestForEdit || insight.STATUS == 'PENDING'
                            }
                        )
                    }
                    else if(insightUsers.includes(req.user.preferred_username)){
                        log.debug(`List Chart Insights: Request user is an SME`);
                        results.forEach(insight => {
                                insight.isEdit = insight.EMAIL == req.user.preferred_username?true:false
                                insight.showApprove = false
                                insight.showDelete = insight.EMAIL == req.user.preferred_username?true:false
                                enableSubmitForApproval = enableSubmitForApproval || (insight.EMAILSENT == 0)                         }
                        )
                    }
                    else{
                        log.debug(`List Chart Insights: Request user is a reqular user`);
                        results = results.filter(insight => {return insight.STATUS == 'PENDING'?false:true})
                        results.forEach(insight => {
                                insight.isEdit = false
                                insight.showApprove = false
                                insight.showDelete = false                            
                            }
                        )
                    }
                    
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.listChartInsights successfully `);
                    results.sort((a, b) => {
                        if (a.STATUS === "APPROVED" && b.STATUS === "PENDING") {
                          return -1;
                        } else if (a.STATUS === "PENDING" && b.STATUS === "APPROVED") {
                          return 1;
                        } else if (a.STATUS === "APPROVED" && b.STATUS === "APPROVED") {
                          return 0;
                        } else if (a.STATUS === "PENDING" && b.STATUS === "PENDING") {
                          if (a.EMAILSENT > b.EMAILSENT) {
                            return -1;
                          } else if (a.EMAILSENT < b.EMAILSENT) {
                            return 1;
                          }
                        }
                      });
                    
                    return resolve(
                        {
                            "data":results,
                            "isInsightUser":isInsightUser,
                            "isApprover": isApprover,
                            "showSubmitForApproval": showSubmitForApproval,
                            "enableSubmitForApproval":enableSubmitForApproval,
                            "enableRequestForEdit":enableRequestForEdit,
                            "enableApprove":enableRequestForEdit,
                            "enableReject":enableRequestForEdit
                        }
                    );
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.listChartInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.listChartInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to add chart insights based on the request data. 
 * Ensures that the user has the required permissions to add insights and validates the content.
 * If valid, the chart insights data is formatted and stored in the database.
 *
 * @param {Object} req - The HTTP request object, containing the user data and insight details in the body.
 * @param {Object} req.user - The authenticated user's information, including `preferred_username` and `name`.
 * @param {Object} req.body - The request body containing the insight details:
 *      - `insight`: The insight content to be added.
 *      - `nodeId`: The ID of the node associated with the insight.
 *      - `nodeTitle`: The title of the node.
 *      - `nodeLink`: A URL link to the node.
 * @returns {Promise<Object>} - A promise that resolves to an object with success message and status,
 *                              or rejects with an error if the operation fails.
 * @throws {Error} - If the user lacks permissions or there is an issue while processing.
 */
async function addChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.addChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            let insightUsers = await getInsightUsers()
            if (!(insightUsers && insightUsers[0] && insightUsers[0]?.INSIGHT_USERS)) {
                log.debug("No INSIGHT_USERS found");
            } 
            
            if (!insightUsers[0]?.INSIGHT_USERS.split(',').includes(req.user.preferred_username)){
                log.debug(`Add Chart Insights: Request user is a regular user`);
                return reject("You don't have permission to add chart insights")
            }
            log.debug(`Add Chart Insights: Request user is an SME`);
            const reqBody = req.body
            insightData = {}
            insightData.email = req.user.preferred_username;
            insightData.user = req.user.name;
            insightData.insight = reqBody.insight;
            insightData.nodeId = reqBody.nodeId;
            insightData.subNodeId = reqBody.subNodeId;
            insightData.nodeTitle = reqBody.nodeTitle;
            insightData.nodeLink = reqBody.nodeLink;
            insightData.date = moment().format('DD/MM/YYYY HH:mm:ss')

            validateEmailContent(reqBody.insight)
            addChartInsightData(insightData)
                .then(results => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.addChartInsights successfully `);
                    return resolve({
                        "message": "Insights added successfully.",
                        "status": "success"
                    });
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.addChartInsights not successfully `);
                    reject(err);
                });
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.addChartInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to update an existing chart insight based on the request data.
 * Verifies that the chart insight exists and the user has permission to update it.
 * Validates the updated content before applying changes.
 *
 * @param {Object} req - The HTTP request object containing parameters and updated data for the insight.
 * @param {Object} req.params - Contains route parameters, including:
 *      - `id`: The ID of the chart insight to be updated.
 * @param {Object} req.body - The request body containing the updated insight details:
 *      - `insight`: The updated insight content.
 *      - `nodeId`: The ID of the associated node.
 *      - `nodeTitle`: The title of the associated node.
 *      - `nodeLink`: A URL link to the associated node.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user.
 * @returns {Promise<Object>} - Resolves to an object with a success message and status if the update is successful.
 *                               Rejects with an error or resolves with a failure message if the operation fails.
 * @throws {Error} - If there is an unexpected issue during the process.
 */
async function updateChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.updateChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const id = req.params.id
            const reqBody = req.body
            let insight = await getChartInsightData(id)
            insight = insight.length?insight[0]:resolve("No chart insight found with given id")
            if (!insight.EMAIL == req.user.preferred_username)
                return resolve("You don't have permission to update this chart insight")
            
            const date = moment().format('DD/MM/YYYY HH:mm:ss')
            insightData = {}
            insightData.id = insight.ID
            insightData.email = insight.EMAIL;
            insightData.insight = reqBody.insight;
            insightData.nodeId = reqBody.nodeId;
            insightData.nodeTitle = reqBody.nodeTitle;
            insightData.nodeLink = reqBody.nodeLink;
            insightData.prevInsight = insight.INSIGHT;
            insightData.date = date
            insightData.status = insight.STATUS
            
            validateEmailContent(reqBody.insight)
            updateChartInsightData(insightData)
                .then(async results => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.updateChartInsights successfully `);
                    return resolve({"message":"Chart insight successfully updated!","status":"success"});
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.updateChartInsights unsuccessfully `);
                    reject(err);
                });
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.updateChartInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to delete an existing chart insight by its ID.
 * Ensures that the user is authorized to delete the chart insight before proceeding.
 *
 * @param {Object} req - The HTTP request object containing parameters and user details.
 * @param {Object} req.params - Contains route parameters, including:
 *      - `id`: The ID of the chart insight to be deleted.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user.
 * @returns {Promise<Object>} - Resolves to an object with a success message and status if the deletion is successful.
 *                               Resolves with a failure message if the user lacks permissions.
 *                               Rejects with an error if the operation fails unexpectedly.
 * @throws {Error} - If there is an unexpected issue during the deletion process.
 */
async function deleteChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.deleteChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const id = req.params.id
            let insight = await getChartInsightData(id)
            insight = insight[0]
            const insightUsers = await getInsightUsers()
            if (!(insightUsers && insightUsers[0] && insightUsers[0]?.INSIGHT_USERS)) {
                log.debug("No INSIGHT_USERS found");
            } 
            const insightApprover = await getInsightApprover()
            if (!(insightApprover && insightApprover[0] && insightApprover[0]?.INSIGHT_APPROVER)) {
                log.debug("No INSIGHT_APPROVER found");
            } 

            const isApprover = insightApprover[0]?.INSIGHT_APPROVER.split(',').includes(req.user.preferred_username);
            const isInsightUser = insightUsers[0]?.INSIGHT_USERS.split(',').includes(req.user.preferred_username);
            if (!(isApprover || isInsightUser)) {
                return resolve("You don't have permission to delete this chart insights")
            }

            deleteChartInsightData(id)
                .then(async results => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.deleteChartInsights successfully `);
                    return resolve({"message":"Chart Insight deleted","status":"success"});
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.deleteChartInsights unsuccessfully `);
                    reject(err);
                });
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.deleteChartInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to send chart insights for approval.
 * Validates the user's permissions, processes the provided chart insight IDs, and updates their approval status.
 * If valid pending chart insights are found, it sends a request for approval and notifies the relevant recipients.
 *
 * @param {Object} req - The HTTP request object containing user details and chart insights to process.
 * @param {Object} req.body - Contains the IDs of the chart insights to be sent for approval:
 *      - `ids`: An array of chart insight IDs.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user to validate permissions and process data.
 * @returns {Promise<Object>} - Resolves with a success message and status if the request is processed successfully.
 *                               Resolves with a failure message if no valid chart insight IDs are provided or if a request was already sent.
 *                               Rejects with an error if the operation fails unexpectedly.
 * @throws {Error} - If there is an unexpected issue during the process.
 */
async function sendChartInsightsForApproval(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.sendChartInsightsForApproval`);
    return new Promise(async (resolve, reject) => {
        try {
            const insightUsers = await getInsightUsers()
            if (!(insightUsers && insightUsers[0] && insightUsers[0]?.INSIGHT_USERS)) {
                log.debug("No INSIGHT_USERS found");
            } 

            if (!insightUsers[0]?.INSIGHT_USERS?.split(',').includes(req.user.preferred_username)){
                log.debug(`Request approval for Chart Insight: Request user is a regular user`);
                return reject("You don't have permission to request approval for chart insights")
            }

            const insightApproverName = await getInsightApproverName()
            if (!(insightApproverName && insightApproverName[0] && insightApproverName[0]?.INSIGHT_APPROVER_NAME)) {
                log.debug("No INSIGHT_APPROVER_NAME found");
            } 

            const insightApprover = await getInsightApprover()
            if (!(insightApprover && insightApprover[0] && insightApprover[0]?.INSIGHT_APPROVER)) {
                log.debug("No INSIGHT_APPROVER found");
            } 

            log.debug(`Request approval for Chart Insight: Request user is an SME`);
            const chartInsightIds = req.body.ids
            let chartInsightEmail = req.user.preferred_username
            if (!Array.isArray(chartInsightIds) || chartInsightIds.length === 0) {
                log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.sendChartInsightsForApproval early: No valid chart insight IDs provided `);
                return resolve({
                    "message": "No chart insight IDs provided for approval",
                    "status": "failed"
                });
            }
            
            pending_chart_insights = await getSubmitRequestChartInsightsData(chartInsightEmail, chartInsightIds)
            if (!pending_chart_insights.length){
                log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.sendChartInsightsForApproval early: Approve request already sent for the provided chart insights `);
                return resolve({
                    "message": "Request approval already sent",
                    "status": "success"
                });
            }

            const approverList = insightApprover[0]?.INSIGHT_APPROVER.split(',')
            const approverNameList = insightApproverName[0]?.INSIGHT_APPROVER_NAME.split(',')
            updateSubmitRequestChartInsightsData(chartInsightEmail, chartInsightIds)
                .then(results => {
                    sendChartInsightsEmail(req,'SUBMIT_REQUEST',pending_chart_insights, "", "",  approverList, approverNameList)
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.sendChartInsightsForApproval successfully `);
                    return resolve({
                      message: "Approval Request sent successfully",
                      status: "success",
                    });
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.sendChartInsightsForApproval not successfully `);
                    reject(err);
                });
           
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.sendChartInsightsForApproval with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to approve chart insights by an authorized user (Approver).
 * This function checks the user's permissions, processes the chart insights approval, and notifies the relevant recipients.
 * After approving the insights, it sends approval notifications to the associated users.
 *
 * @param {Object} req - The HTTP request object containing user details and the chart insights to approve.
 * @param {Object} req.body - Contains the IDs of the chart insights to approve:
 *      - `ids`: An array of chart insight IDs to be approved.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user, used to validate permissions.
 * @returns {Promise<Object>} - Resolves with a success message and status if the approval is processed successfully.
 *                               Rejects with a failure message if no insights with the provided IDs are found, or if approval fails.
 * @throws {Error} - If there is an unexpected issue during the approval process.
 */
async function approveChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.approveChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const insightApprover = await getInsightApprover()
            if (!(insightApprover && insightApprover[0] && insightApprover[0]?.INSIGHT_APPROVER)) {
                log.debug("No INSIGHT_APPROVER found");
            } 

            if (!(insightApprover[0]?.INSIGHT_APPROVER == req.user.preferred_username))
                return resolve("You don't have permission to approve chart insights")
            
            const insightApproverName = await getInsightApproverName()
            if (!(insightApproverName && insightApproverName[0] && insightApproverName[0]?.INSIGHT_APPROVER_NAME)) {
                log.debug("No INSIGHT_APPROVER_NAME found");
            } 

            const ids = req.body.ids
            const date = moment().format('YYYY-MM-DD HH:mm:ss')
            approveChartInsightData(ids,date)
                .then(async results => {
                    const insights = await getChartInsightsData(ids)
                    if (!insights.length)
                        return reject("No insight with given id found!")

                    let tempEmails = []
                    let userRecords = {}
                    insights.forEach(element => {
                        tempEmails.push(element.EMAIL)
                        userRecords[element.EMAIL] = element.USER_NAME
                    })
                    let emails = [...new Set(tempEmails)]
                    emails.forEach(email => {
                        sendChartInsightsEmail(req,'APPROVE',insights,email,userRecords[email],insightApprover[0]?.INSIGHT_APPROVER,insightApproverName[0]?.INSIGHT_APPROVER_NAME)
                    })
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.approveChartInsights successfully `);
                    return resolve({"message":"Chart Insight has been approved","status":"success"});
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.approveChartInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.approveChartInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to reject chart insights by an authorized user (Approver).
 * This function checks the user's permissions, processes the rejection of chart insights, and notifies the relevant recipients.
 * After rejecting the insights, it sends rejection notifications to the associated users.
 *
 * @param {Object} req - The HTTP request object containing user details and the chart insights to reject.
 * @param {Object} req.body - Contains the IDs of the chart insights to reject:
 *      - `ids`: An array of chart insight IDs to be rejected.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user, used to validate permissions.
 * @returns {Promise<Object>} - Resolves with a success message and status if the rejection is processed successfully.
 *                               Rejects with a failure message if no insights with the provided IDs are found, or if rejection fails.
 * @throws {Error} - If there is an unexpected issue during the rejection process.
 */
async function rejectChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.rejectChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const insightApprover = await getInsightApprover()
            if (!(insightApprover && insightApprover[0] && insightApprover[0]?.INSIGHT_APPROVER)) {
                log.debug("No INSIGHT_APPROVER found");
            } 

            if (!insightApprover[0]?.INSIGHT_APPROVER == req.user.preferred_username)
                return resolve("You don't have permission to reject chart insights")

            const insightApproverName = await getInsightApproverName()
            if (!(insightApproverName && insightApproverName[0] && insightApproverName[0]?.INSIGHT_APPROVER_NAME)) {
                log.debug("No INSIGHT_APPROVER_NAME found");
            } 
            
            const ids = req.body.ids
            const date = moment().format('YYYY-MM-DD HH:mm:ss')
            rejectChartInsightData(ids,date)
                .then(async results => {
                    const insights = await getChartInsightsData(ids)
                    if (!insights.length)
                        return reject("No insight with given id found!")

                    let tempEmails = []
                    let userRecords = {}
                    insights.forEach(element => {
                        tempEmails.push(element.EMAIL)
                        userRecords[element.EMAIL] = element.USER_NAME
                    })
                    let emails = [...new Set(tempEmails)]
                    emails.forEach(email => {
                        sendChartInsightsEmail(req,'REJECT',insights,email,userRecords[email],insightApprover[0]?.INSIGHT_APPROVER,insightApproverName[0]?.INSIGHT_APPROVER_NAME)
                    })

                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.rejectChartInsights successfully `);
                    return resolve({"message":"Chart Insight has been rejected","status":"success"});
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.rejectChartInsights unsuccessfully `);
                    reject(err);
                });
           
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.rejectChartInsights with error ${err}`);
            reject(err);
        }
    })
}

/**
 * Function to request edits on chart insights by an authorized user (Approver).
 * This function processes the request for edit permissions and notifies the relevant users about the request.
 * After processing, it sends notifications to users whose insights are requested for editing.
 *
 * @param {Object} req - The HTTP request object containing user details and the chart insights to request edits on.
 * @param {Object} req.body - Contains the details of chart insights and comments for the request:
 *      - `id`: The ID of the chart insight to be edited.
 *      - `comment`: The comment explaining the reason or request for the edit.
 * @param {Object} req.user - The authenticated user's information, including:
 *      - `preferred_username`: The email of the logged-in user, used to validate permissions.
 * @returns {Promise<Object>} - Resolves with a success message and status if the edit request is processed successfully.
 *                               Rejects with a failure message if no insights with the provided IDs are found, or if the user does not have permission to request edits.
 * @throws {Error} - If there is an unexpected issue during the request process.
 */

async function requestEditChartInsights(req) {
    log.debug(`>>>>>Entered insights-microservice.insightsv3.controller.requestEditChartInsights`);
    return new Promise(async (resolve, reject) => {
        try {
            const insightApprover = await getInsightApprover()
            if (!(insightApprover && insightApprover[0] && insightApprover[0]?.INSIGHT_APPROVER)) {
                log.debug("No INSIGHT_APPROVER found");
            }
            
            if (!(insightApprover[0]?.INSIGHT_APPROVER == req.user.preferred_username))
                return resolve("You don't have permission to request edit")

            const insightApproverName = await getInsightApproverName()
            if (!(insightApproverName && insightApproverName[0] && insightApproverName[0]?.INSIGHT_APPROVER_NAME)) {
                log.debug("No INSIGHT_APPROVER_NAME found");
            } 
            
            const insights_data = req.body
            let insight_ids = []
            insights_data.forEach(element=>{
                insight_ids.push(element.id)
            })
            const date = moment().format('YYYY-MM-DD HH:mm:ss')
            requestEditChartInsightData(insight_ids,date)
                .then(async results => {
                    const insights = await getChartInsightsData(insight_ids)
                    if (!insights.length)
                        return reject("No insight with given id found!")

                    let tempEmails = []
                    let userRecords = {}
                    insights.forEach(element => {
                        element['COMMENT'] = insights_data.find(e=>e.id == element.ID)['comment']
                        tempEmails.push(element.EMAIL)
                        userRecords[element.EMAIL] = element.USER_NAME
                    })
                    let emails = [...new Set(tempEmails)]
                    emails.forEach(email => {
                        sendChartInsightsEmail(req,'REQUEST_EDIT',insights,email,userRecords[email],insightApprover[0]?.INSIGHT_APPROVER,insightApproverName[0]?.INSIGHT_APPROVER_NAME)
                    })
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.requestEditChartInsights successfully `);
                    return resolve({"message":"Requested edit for insights","status":"success"});
                })
                .catch((err) => {
                    log.debug(`<<<<<Exited insights-microservice.insightsv3.controller.requestEditChartInsights unsuccessfully `);
                    reject(err);
                });
        } catch (err) {
            log.error(`<<<<<Exited insights-microservice.insightsv3.controller.requestEditChartInsights with error ${err}`);
            reject(err);
        }
    })
}

module.exports = { listChartInsights, addChartInsights, updateChartInsights, deleteChartInsights, sendChartInsightsForApproval, approveChartInsights, rejectChartInsights, requestEditChartInsights};
