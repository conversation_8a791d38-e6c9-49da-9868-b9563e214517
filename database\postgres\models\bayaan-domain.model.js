const { Sequelize, DataTypes } = require("sequelize");

/**
 * Existing model created for Bayaan UI used to store list
 * of "Domains" in Bayaan.
 * @param {Sequelize} sequelize
 * @param {DataTypes} DataTypes
 */
function model(sequelize, DataTypes) {
  const ChatBayaanDomain = sequelize.define(
    "ChatBayaanDomain",
    {
      id: {
        type: DataTypes.BIGINT,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      name_en: {
        type: DataTypes.STRING(255),
        allowNull: true,
        unique: true,
      },
      name_ar: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      cms_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      tableName: "chat_bayaandomains",
      timestamps: false,
    }
  );
  return ChatBayaanDomain;
}
module.exports = model;
