require('dotenv').config();
const crypto = require('crypto');

const algorithm = 'aes-256-cbc'; // AES encryption algorithm
const iv = Buffer.alloc(16, 0);

function getKey() {
    // Loads the encryption key from the environment variable
    const key = process.env.AES_SECRET_KEY;
    if (!key || key.length !== 32) {
        throw new Error('AES_SECRET_KEY must be set and be 32 characters long in the environment variables.');
    }
    return Buffer.from(key, 'utf8');
}

function encryptEmail(email) {
    let data = email;
    if (typeof email == "string") {
        data = email.toLowerCase();
    }
    const key = getKey();
    const cipher = crypto.createCipheriv(algorithm, key, iv); // Use the fixed IV
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

function decryptEmail(encryptedEmail) {
    let data = encryptedEmail;
    if (typeof encryptedEmail == "string") {
        data = encryptedEmail.toLowerCase();
    }
    const key = getKey();
    const decipher = crypto.createDecipheriv(algorithm, key, iv); // Use the fixed IV
    let decrypted = decipher.update(data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

function encryptPhone(phone) {
    let data = phone;
    if (typeof phone == "string") {
        data = phone.toLowerCase();
    }
    const key = getKey();
    const cipher = crypto.createCipheriv(algorithm, key, iv); // Use the fixed IV
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

function decryptPhone(encryptedPhone) {
    let data = encryptedPhone;
    if (typeof encryptedPhone == "string") {
        data = encryptedPhone.toLowerCase();
    }
    const key = getKey();
    const decipher = crypto.createDecipheriv(algorithm, key, iv); // Use the fixed IV
    let decrypted = decipher.update(data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

module.exports = {
    encryptEmail,
    decryptEmail,
    encryptPhone,
    decryptPhone
};
