const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getRegionDataQuery(lang) {
    try {
        const query = buildPopulationQuery(lang.body.regions, lang.body.district_code, lang.body.gender, lang.body.citizenship);
        return query;
    } catch (err) {
        log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getRegionDataQuery with error ${err} `);
        throw err;
    }
} 

async function getAllFiltersDataQuery(request) {
    try {
        let query = `SELECT DISTINCT YEAR, QUARTER, REGION_CODE, REGION_AR, REGION_EN, DISTRICT_CODE, DISTRICT_AR, DISTRICT_EN, COMMUNITY_CODE, COMMUNITY_AR, COMMUNITY_EN FROM STAT_R08_CENSUS WHERE COMMUNITY_CODE != '' ORDER BY COMMUNITY_EN`;
        return query;

    } catch (err) {
        log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getAllFiltersData with error ${err} `);
        throw err;
    }
}

async function getSummaryDataQuery(request) {
  try {
      let params = request.body;
      const formattedCodesForYears = params.YEAR_CODE.map(code => `'${code}'`).join(',');
      const queryStringForYears = `YEAR_CODE=[${formattedCodesForYears}]`;
      const formattedCodesForQuarter = params.QUARTER_CODE.map(code => `'${code}'`).join(',');
      const queryStringForQuarters = `QUARTER_CODE=[${formattedCodesForQuarter}]`;

      let query = `SELECT * FROM VW_SDE_SUMMARY (REGION_CODE=[${params.REGION_CODE}], ${queryStringForYears}, ${queryStringForQuarters})`;
      return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getSummaryDataQuery with error ${err} `);
      throw err;
  }
}

async function getPopulationSummaryDataQuery(request) {
  try {
      let params = request.body;
      const formattedCodesForGender = params.GENDER_CODE.map(code => `'${code}'`).join(',');
      const queryStringForGender = `GENDER_CODE=[${formattedCodesForGender}]`;
      const formattedCodesForCitizen = params.CITIZEN_CODE.map(code => `'${code}'`).join(',');
      const queryStringForCitizen = `CITIZEN_CODE=[${formattedCodesForCitizen}]`;

      const formattedCodesForMarital = params.MARITAL_CODE.map(code => `'${code}'`).join(',');
      const queryStringForMarital = `MARITAL_CODE=[${formattedCodesForMarital}]`;

      const formattedCodesForAttainment = params.ATTAINMENT_CODE.map(code => `'${code}'`).join(',');
      const queryStringForAttainment = `ATTAINMENT_CODE=[${formattedCodesForAttainment}]`;

      const formattedCodesForEnrollment = params.ENROLLMENT_TYPE_CODE.map(code => `'${code}'`).join(',');
      const queryStringForEnrollment = `ENROLLMENT_TYPE_CODE=[${formattedCodesForEnrollment}]`;

      const formattedCodesForSpecialization = params.SPECIALIZATION_CODE.map(code => `'${code}'`).join(',');
      const queryStringForSpecialization = `SPECIALIZATION_CODE=[${formattedCodesForSpecialization}]`;

      const formattedCodesForHouseHold = params.HOUSEHOLD_CODE.map(code => `'${code}'`).join(',');
      const queryStringForHouseHold = `HOUSEHOLD_CODE=[${formattedCodesForHouseHold}]`;

      const formattedCodesForDistricts = params.DISTRICT_CODE.map(code => `'${code}'`).join(',');
      const queryStringForDistricts = `DISTRICT_CODE=[${formattedCodesForDistricts}]`;
      const formattedCodesForCommunities = params.COMMUNITY_CODE.map(code => `'${code}'`).join(',');
      const queryStringForCommunities = `COMMUNITY_CODE=[${formattedCodesForCommunities}]`;
      
      const formattedCodesForYears = params.YEAR_CODE.map(code => `'${code}'`).join(',');
      const queryStringForYears = `YEAR_CODE=[${formattedCodesForYears}]`;
      const formattedCodesForQuarter = params.QUARTER_CODE.map(code => `'${code}'`).join(',');
      const queryStringForQuarters = `QUARTER_CODE=[${formattedCodesForQuarter}]`;

      let query = `SELECT * FROM VW_SDE_SUMMARY_POP (REGION_CODE=[${params.REGION_CODE}], ${queryStringForDistricts}, ${queryStringForCommunities}, ${queryStringForCitizen}, ${queryStringForGender}, ${queryStringForMarital}, ${queryStringForAttainment}, ${queryStringForHouseHold}, ${queryStringForEnrollment}, ${queryStringForSpecialization}, ${queryStringForYears}, ${queryStringForQuarters})`;
      return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getPopulationSummaryDataQuery with error ${err} `);
      throw err;
  }
}

async function getLaborForceSummaryDataQuery(request) {
  try {
    let params = request.body;
    const formattedCodesForGender = params.GENDER_CODE.map(code => `'${code}'`).join(',');
    const queryStringForGender = `GENDER_CODE=[${formattedCodesForGender}]`;
    const formattedCodesForCitizen = params.CITIZEN_CODE.map(code => `'${code}'`).join(',');
    const queryStringForCitizen = `CITIZEN_CODE=[${formattedCodesForCitizen}]`;

    const formattedCodesForMarital = params.MARITAL_CODE.map(code => `'${code}'`).join(',');
    const queryStringForMarital = `MARITAL_CODE=[${formattedCodesForMarital}]`;

    const formattedCodesForAttainment = params.ATTAINMENT_CODE.map(code => `'${code}'`).join(',');
    const queryStringForAttainment = `ATTAINMENT_CODE=[${formattedCodesForAttainment}]`;

    const formattedCodesForHouseHold = params.HOUSEHOLD_CODE.map(code => `'${code}'`).join(',');
    const queryStringForHouseHold = `HOUSEHOLD_CODE=[${formattedCodesForHouseHold}]`;

    const formattedCodesForEnrollment = params.ENROLLMENT_TYPE_CODE.map(code => `'${code}'`).join(',');
    const queryStringForEnrollment = `ENROLLMENT_TYPE_CODE=[${formattedCodesForEnrollment}]`;

    const formattedCodesForSpecialization = params.SPECIALIZATION_CODE.map(code => `'${code}'`).join(',');
    const queryStringForSpecialization = `SPECIALIZATION_CODE=[${formattedCodesForSpecialization}]`;

    const formattedCodesForDistricts = params.DISTRICT_CODE.map(code => `'${code}'`).join(',');
    const queryStringForDistricts = `DISTRICT_CODE=[${formattedCodesForDistricts}]`;
    const formattedCodesForCommunities = params.COMMUNITY_CODE.map(code => `'${code}'`).join(',');
    const queryStringForCommunities = `COMMUNITY_CODE=[${formattedCodesForCommunities}]`;
    const formattedCodesForYears = params.YEAR_CODE.map(code => `'${code}'`).join(',');
    const queryStringForYears = `YEAR_CODE=[${formattedCodesForYears}]`;
    const formattedCodesForQuarter = params.QUARTER_CODE.map(code => `'${code}'`).join(',');
    const queryStringForQuarters = `QUARTER_CODE=[${formattedCodesForQuarter}]`;
    let query = `SELECT * FROM VW_SDE_SUMMARY_LF (REGION_CODE=[${params.REGION_CODE}], ${queryStringForDistricts}, ${queryStringForCommunities}, ${queryStringForCitizen}, ${queryStringForGender}, ${queryStringForMarital}, ${queryStringForAttainment}, ${queryStringForHouseHold}, ${queryStringForEnrollment}, ${queryStringForSpecialization}, ${queryStringForYears}, ${queryStringForQuarters})`;
    return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getLaborForceSummaryDataQuery with error ${err} `);
      throw err;
  }
}


async function getRealEstateSummaryDataQuery(request) {
  try {
    let params = request.body;
    const formattedCodesForDistricts = params.DISTRICT_CODE.map(code => `'${code}'`).join(',');
    const queryStringForDistricts = `DISTRICT_CODE=[${formattedCodesForDistricts}]`;
    const formattedCodesForCommunities = params.COMMUNITY_CODE.map(code => `'${code}'`).join(',');
    const queryStringForCommunities = `COMMUNITY_CODE=[${formattedCodesForCommunities}]`;
    const formattedCodesForYears = params.YEAR_CODE.map(code => `'${code}'`).join(',');
    const queryStringForYears = `YEAR_CODE=[${formattedCodesForYears}]`;
    const formattedCodesForQuarter = params.QUARTER_CODE.map(code => `'${code}'`).join(',');
    const queryStringForQuarters = `QUARTER_CODE=[${formattedCodesForQuarter}]`;

    const formattedCodesForBuildingsType = params.BUILDING_TYPE_CODE.map(code => `'${code}'`).join(',');
    const queryStringForBuildingsType = `BUILDING_TYPE_CODE=[${formattedCodesForBuildingsType}]`;
    const formattedCodesForBuildingsUse = params.BUILDING_USE_CODE.map(code => `'${code}'`).join(',');
    const queryStringForBuildingsUse = `BUILDING_USE_CODE=[${formattedCodesForBuildingsUse}]`;
  
    const formattedCodesForUnitsType = params.UNITS_TYPE_CODE.map(code => `'${code}'`).join(',');
    const queryStringForUnitsType = `UNITS_TYPE_CODE=[${formattedCodesForUnitsType}]`;
    const formattedCodesForUnitsUse = params.UNITS_USE_CODE.map(code => `'${code}'`).join(',');
    const queryStringForUnitsUse = `UNITS_USE_CODE=[${formattedCodesForUnitsUse}]`;

    let query = `SELECT * FROM VW_SDE_SUMMARY_RE (REGION_CODE=[${params.REGION_CODE}], ${queryStringForDistricts}, ${queryStringForCommunities}, ${queryStringForYears}, ${queryStringForQuarters}, ${queryStringForBuildingsType}, ${queryStringForBuildingsUse}, ${queryStringForUnitsType}, ${queryStringForUnitsUse})`;
    return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getRealEstateSummaryDataQuery with error ${err} `);
      throw err;
  }
}

async function getDistinctGenderDataQuery(request) {
    try {
        let query = `SELECT DISTINCT GENDER_CODE AS SELECT_CODE,GENDER_EN AS SELECT_EN,GENDER_AR AS SELECT_AR FROM STAT_R08_CENSUS ORDER BY GENDER_CODE`;
        return query;

    } catch (err) {
        log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctGenderDataQuery with error ${err} `);
        throw err;
    }
}

async function getDistinctCitizenshipDataQuery(request) {
    try {
        let query = `SELECT DISTINCT CITIZEN_CODE AS SELECT_CODE,CITIZEN_AR AS SELECT_AR,CITIZEN_EN AS SELECT_EN FROM STAT_R08_CENSUS ORDER BY CITIZEN_CODE`;
        return query;

    } catch (err) {
        log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctCitizenshipDataQuery with error ${err} `);
        throw err;
    }
}

async function getDistinctMaritalStatusDataQuery(request) {
    try {
        let query = `SELECT DISTINCT MARITAL_STATUS_CODE AS SELECT_CODE, MARITAL_STATUS_EN AS SELECT_EN, MARITAL_STATUS_AR AS SELECT_AR FROM STAT_R08_CENSUS WHERE MARITAL_STATUS_CODE NOT IN (-99) ORDER BY MARITAL_STATUS_CODE`;
        return query;

    } catch (err) {
        log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctMaritalStatusDataQuery with error ${err} `);
        throw err;
    }
}

async function getDistinctAttainmentTypeDataQuery(request) {
  try {
      let query = `SELECT DISTINCT EDUCATION_ATTAINMENT_MERGED_CODE AS SELECT_CODE, EDUCATION_ATTAINMENT_MERGED_EN AS SELECT_EN, EDUCATION_ATTAINMENT_MERGED_AR AS SELECT_AR FROM STAT_R08_CENSUS ORDER BY EDUCATION_ATTAINMENT_MERGED_CODE`;
      return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctAttainmentTypeDataQuery with error ${err} `);
      throw err;
  }
}

async function getDistinctHouseHoldTypeDataQuery(request) {
  try {
      let query = `SELECT DISTINCT HOUSEHOLD_TYPE_CODE AS SELECT_CODE, HOUSEHOLD_TYPE_EN AS SELECT_EN, HOUSEHOLD_TYPE_AR AS SELECT_AR FROM STAT_R08_CENSUS ORDER BY HOUSEHOLD_TYPE_CODE`;
      return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctHouseHoldTypeDataQuery with error ${err} `);
      throw err;
  }
}


async function getDistinctBuildingTypeDataQuery(request) {
  try {
      let query = `SELECT DISTINCT B_TYPE AS SELECT_CODE, B_TYPE_EN AS SELECT_EN, B_TYPE_AR AS SELECT_AR FROM VW_CENSUS_RE_BUILDING ORDER BY B_TYPE`;
      return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctBuildingTypeDataQuery with error ${err} `);
      throw err;
  }
}


async function getDistinctBuildingUseDataQuery(request) {
  try {
      let query = `SELECT DISTINCT B_USE AS SELECT_CODE, B_USE_EN AS SELECT_EN, B_USE_AR AS SELECT_AR FROM VW_CENSUS_RE_BUILDING ORDER BY B_USE`;
      return query;

  } catch (err) {
      log.error(`<<<<<< Exited microservice-geospatial-revamp.services.getQuery.service.getDistinctBuildingUseDataQuery with error ${err} `);
      throw err;
  }
}

async function getDistinctUnitUseDataQuery(request) {
  try {
    const query = `
      SELECT DISTINCT
        U_USE_3DG AS SELECT_CODE,
        UNIT_USE_EN AS SELECT_EN,
        UNIT_USE_AR AS SELECT_AR
      FROM SYNC_STG.CENSUS_RE_UNIT
      ORDER BY U_USE_3DG
    `;
    return query;
  } catch (err) {
    log.error(`<<<<<< Exited getDistinctUnitUseDataQuery with error ${err}`);
    throw err;
  }
}


async function getDistinctUnitTypeDataQuery(request) {
  try {
    const query = `
      SELECT DISTINCT
        U_TYPE_3DG AS SELECT_CODE,
        UNIT_TYPE_EN AS SELECT_EN,
        UNIT_TYPE_AR AS SELECT_AR
      FROM SYNC_STG.CENSUS_RE_UNIT
      ORDER BY U_TYPE_3DG
    `;
    return query;
  } catch (err) {
    log.error(`<<<<<< Exited getDistinctUnitTypeDataQuery with error ${err}`);
    throw err;
  }
}



function buildPopulationQuery(region, district, community, gender, citizenship) {
    const selectFields = ['ROUND(SUM(POPULATION) / 5) * 5 AS POPULATION'];
    const conditions = [];
    const groupByFields = [];
  
    if (region) {
      conditions.push(`REGION_CODE IN (${region})`);
      groupByFields.push('REGION_CODE', 'REGION_AR', 'REGION_EN');
      selectFields.push('REGION_CODE', 'REGION_AR', 'REGION_EN');
    }
  
    if (district) {
      conditions.push(`DISTRICT_CODE IN ('${district}')`);
      groupByFields.push('DISTRICT_CODE', 'DISTRICT_AR', 'DISTRICT_EN');
      selectFields.push('DISTRICT_CODE', 'DISTRICT_AR', 'DISTRICT_EN');
    }

    if (community) {
      conditions.push(`COMMUNITY_CODE IN ('${community}')`);
      groupByFields.push('COMMUNITY_CODE', 'COMMUNITY_AR', 'COMMUNITY_EN');
      selectFields.push('COMMUNITY_CODE', 'COMMUNITY_AR', 'COMMUNITY_EN');
    }
  
    if (gender) {
      conditions.push(`GENDER_CODE = ${gender}`);
      groupByFields.push('GENDER_CODE', 'GENDER_EN', 'GENDER_AR');
      selectFields.push('GENDER_CODE', 'GENDER_EN', 'GENDER_AR');
    }
  
    if (citizenship) {
      conditions.push(`CITIZEN_CODE = ${citizenship}`);
      groupByFields.push('CITIZEN_CODE', 'CITIZEN_AR', 'CITIZEN_EN');
      selectFields.push('CITIZEN_CODE', 'CITIZEN_AR', 'CITIZEN_EN');
    }
  
    let query = `
      SELECT 
        ${selectFields.join(', ')}
      FROM 
        STAT_R08_CENSUS
    `;
  
    if (conditions.length > 0) {
      query += `WHERE ${conditions.join(' AND ')}`;
    }
  
    if (groupByFields.length > 0) {
      query += ` GROUP BY ${groupByFields.join(', ')}`;
    }
  
    query += ';';

    console.log('query', query);
  
    return query;
  }


module.exports = { 
  getRegionDataQuery, 
  getAllFiltersDataQuery, 
  getDistinctGenderDataQuery, 
  getDistinctCitizenshipDataQuery, 
  getDistinctMaritalStatusDataQuery, 
  getDistinctAttainmentTypeDataQuery, 
  getDistinctHouseHoldTypeDataQuery, 
  getSummaryDataQuery, 
  getPopulationSummaryDataQuery, 
  getLaborForceSummaryDataQuery, 
  getRealEstateSummaryDataQuery, 
  getDistinctBuildingTypeDataQuery,
  getDistinctBuildingUseDataQuery,
  getDistinctUnitTypeDataQuery,
  getDistinctUnitUseDataQuery 
};