const express = require('express');
const router = new express.Router();
const LivabilityDashboardController = require('../microservice-dashboard/livability-dashboard.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.get('/:id', async (req, res, next) => {
    try {
        const data = await new LivabilityDashboardController().getData(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        
        log.error(`Error fetching data for livability-dashboard by id content-type, ERROR: ${err}`);
        next(err);
    }
});

module.exports = router;
