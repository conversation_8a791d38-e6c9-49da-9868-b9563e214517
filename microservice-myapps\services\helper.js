function formatMyApps(myApps,domainInteractionFlag,domain,sub_domain,sub_theme,product){
    try{
        if (domain && !(domain in myApps))
            myApps[domain] = { "items": {}, "sub_domains": {} }

        if (sub_domain && !(sub_domain in myApps[domain]["sub_domains"]))
            myApps[domain]["sub_domains"][sub_domain] = { "items": {}, "sub_themes": {} }

        if (sub_theme && !(sub_theme in myApps[domain]["sub_domains"][sub_domain]["sub_themes"]))
            myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme] = { "items": {}, "products": {} }

        if (product && !(product in myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"]))
            myApps[domain]["sub_domains"][sub_domain]["sub_themes"][sub_theme]["products"][product] = { "items": {} }                                     


        if (domain) {
            const domainPath = myApps[domain];
            
            if (sub_domain) {
                const subDomainPath = domainPath.sub_domains[sub_domain];
        
                if (sub_theme) {
                    const subThemePath = subDomainPath.sub_themes[sub_theme];
        
                    if (product) {
                        const productPath = subThemePath.products[product];
                        productPath.items[classification] ||= [];
                        productPath.items[classification].push(app);
                    } else {
                        subThemePath.items[classification] ||= [];
                        subThemePath.items[classification].push(app);
                    }
                } else {
                    subDomainPath.items[classification] ||= [];
                    subDomainPath.items[classification].push(app);
                }
            } else {
                domainPath.items[classification] ||= [];
                domainPath.items[classification].push(app);
            }
        
            domainInteractionFlag[domain] += app.INTERACTION_STATUS;
        }
    }
    catch (exp){

    }
}

module.exports = {
    formatMyApps
}