const Logger = require("scad-library").logger;
const { getMetaFromCMS } = require("../services/common-service");
require("dotenv").config();
const constants = require("../config/constants.json");
const { getAccessQuery } = require("./services/getQuery");
const jwt = require("jsonwebtoken");
const log = new Logger().getInstance();
/**
 * function to get accesscheck content from CMS
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
async function getAccessCheck(req) {
  log.debug(
    ">>>>>Entered accesscheck-microservice.accesscheck.controller.getAccessCheck"
  );
  return new Promise(async (resolve, reject) => {
    try {
      const cmsAccessCheckUrl = `${process.env.CMS_BASEPATH}${constants.cmsGroupUrl.CMS_ACCESS_CHECK_URL}`;
      const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

      // Fetching data from CMS
      const cmsResponse = await getMetaFromCMS(
        req,
        cmsLoginUrl,
        cmsAccessCheckUrl,
        req.user.groups
      );
      return resolve(cmsResponse);
    } catch (err) {
      reject(err);
    }
  });
}

async function getAccessFromDb(req) {
  log.debug(`>>>>>Entered microservice.accesscheck.controller.getAccessFromDb`);
  const authHeader = req.headers["authorization"];
  const decodedToken = jwt.decode(authHeader.split(" ")[1]);
  try {
    let accessData = await getAccessQuery();
    let allowedKeys = [];
    if (accessData.length > 0) {
      accessData.forEach((element) => {
        const usersList = element.USERS_LIST.split(",");
        if (usersList.includes(decodedToken.preferred_username)) {
          allowedKeys.push({
            feature_name: element.FEATURE_NAME,
            config: JSON.parse(element.CONFIG),
          });
        }
      });
    }
    return allowedKeys;
  } catch (err) {
    log.error(err);
    throw err;
  }
}

module.exports = { getAccessCheck, getAccessFromDb };
