{"paths": {"/content-type/recommendations/": {"post": {"tags": [" Recommendations"], "summary": "Retrieves Recommendations for a user based on Domains", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Recommendations Domain data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecommendationDomains"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"indicatorId": {"type": "string", "description": "Unique identifier for the indicator."}, "title": {"type": "string", "description": "Title of the indicator."}, "contentType": {"type": "string", "description": "Type of content, here indicating it is an official SCAD indicator."}, "appType": {"type": "null", "description": "Application type, if applicable."}, "type": {"type": "string", "description": "Classification of the indicator, such as official statistics."}, "domain": {"type": "string", "description": "Domain or category to which the indicator belongs, such as Economy or Industry & Business."}}, "required": ["indicatorId", "title", "contentType", "type", "domain"], "additionalProperties": false}, "description": "An array of objects, each representing an indicator with its metadata including ID, title, content type, and domain."}}}}}}}}, "components": {"schemas": {"RecommendationDomains": {"type": "array", "items": {"type": "string", "description": "The ID of a domain."}, "description": "An array containing the IDs of selected domains."}}}}