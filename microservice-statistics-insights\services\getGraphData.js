const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service')
const oracledb = require('oracledb');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json')

const { 
  getCoiQuery, getValuesDataQuery, getSeriesDataQuery, getScadQuery, 
  getAccuracyMetricsDataQuery, getScadQueryWithDimension, getPercentageChangeQuery,
  getFilterOptionsQuery, getAllFilterDataQuery, getCategoricalDimensionsQuery,
  getPortfolioDynamicQuery, getPortfolioDynamicQueryWithDimension, getPopStatsDataQuery } = require('./getQuery.service');
const { IFPError } = require('../../utils/error');

  const clkTables = constants.clickhouseTables

async function getGraphData(visualization, indicatorType, isFilterPanelEnabled,filterPanel=[]) {
  try{
    let binds={}
    let query = "";
    if (indicatorType === 'scad') {
      if (visualization.dimension) {
        query = await getScadQueryWithDimension(visualization, isFilterPanelEnabled);
      } else {
        query = await getScadQuery(visualization);
      }
    } else if (indicatorType === 'coi') {
      query = await getCoiQuery(visualization);
    } else if (indicatorType === 'geospacial') {
      // There is no DB query for ARCGIS APIs 
      // For just returning data
      query = '';
    } else if (indicatorType === 'official_statistics' || indicatorType === 'innovative_statistics') {
      if (visualization.isMultiDimension) {
        if (!filterPanel.properties.length)
          throw new IFPError(500,'No data available for filter panel')
        result = await getPortfolioDynamicQueryWithDimension(visualization, isFilterPanelEnabled,filterPanel);
        query = result.query
        binds = result.binds
      } else {
        result = await getPortfolioDynamicQuery(visualization);
        query = result.query
        binds = result.binds
      }
    }

    if (query == "") return visualization;
    let data = await getSeriesData(query,binds)
    return data
  }
  catch(err){
    
    throw err
  }
}

async function getSeriesData(query,binds={}) {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getSeriesData`);
    try{
      const containsClView = clkTables.some(view => query.includes(view));
      let data;
      if (containsClView)
        data = await clkdb.simpleExecute(query,binds)
      else
        data = await db.simpleExecute(query,binds)

      log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getSeriesData successfully`);
      return data
    }
    catch(err){
      
      log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getSeriesData with error ${err}`);
      log.error(`Error Executing Query:- ${err}`);
      throw err;
    }
}

async function getPercentageChange(obj, list) {
    try{
      let query = getPercentageChangeQuery(obj, list)
      let data = getSeriesData(query)
      return data
    }
    catch(err){
      
      throw err
    }
}

async function getFilterOptionsFromDB(filterObj) {
    try{
      let query =  await getFilterOptionsQuery(filterObj)
      let data = await getSeriesData(query)
      return data
    }
    catch(err){
      log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getFilterOptionsFromDB with error ${err}`);
      
      throw err;
    }
}

async function getAllFilterDataFromDB(meta,yearly=false) {
    try{
      let columns = await getTableColumns(meta.viewName)
      let result = await getAllFilterDataQuery(meta,columns,yearly)
      let data = await getSeriesData(result.query,result.binds)
      return data
    }
    catch(err){
      
      log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getAllFilterDataFromDB with error ${err}`);
      throw err
    }

}

async function getAccuracyMetricsData(accuracyMetricsMeta, contentId) {
  return new Promise((resolve, reject) => {
    getAccuracyMetricsDataQuery(accuracyMetricsMeta, contentId).then(query => {
      getSeriesData(query).then(data => {
        resolve(data);
      }).catch(err => {
        
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  });
}

async function getValuesData(meta) {
  return new Promise((resolve, reject) => {
    getValuesDataQuery(meta).then(query => {
      getSeriesData(query).then(data => {
        resolve(data);
      }).catch(err => {
        
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  });
}

async function getDynamicSeriesData(meta){
  return new Promise((resolve, reject) => {
    getSeriesDataQuery(meta).then(query => {
      const seriesMeta = JSON.parse(JSON.stringify(meta.seriesMeta[0]))
      meta.seriesMeta = []
      getSeriesData(query).then(data => {
        data.forEach((d) => {
          Object.values(d).forEach(value => {
            let series = JSON.parse(JSON.stringify(seriesMeta));
            series['id'] = value.toLowerCase().replace(/ /g,"-");
            series['label'] = value
            series['dimensionValue'] = value.toLowerCase()
            let dimension = series.dimension
            baseDimensionValue = dimension.baseDimension
            dimension[baseDimensionValue] = value
            delete dimension['baseDimension']; 
            series['dimension'] = dimension
            meta.seriesMeta.push(series)
          })
        })
        resolve(meta);
      }).catch(err => {
        
        reject(err);
      })
    }).catch(err => {
      
      reject(err);
    })
  });
}

async function getTableColumns(tableName) {
    try {
      let data;
      if (clkTables.includes(tableName)){
        let query = `SELECT name AS NAME FROM system.columns WHERE table='${tableName}' AND name NOT IN ('OBS_DT') AND database='${process.env.CLICKHOUSE_DATABASE}'`
        data = await clkdb.simpleExecute(query) 
      }
      else{
        const binds = {
          tableName: tableName,
          owner: process.env.DB_USER
        };
  
        let query = `SELECT COLUMN_NAME AS NAME
            FROM all_tab_columns
            WHERE table_name = :tableName AND column_name NOT IN ('OBS_DT') AND OWNER = :owner`;
       data = await db.simpleExecute(query,binds)
      }
      log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getTableColumns successfully`);
      return data
    } catch (err) {
      
      log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getTableColumns with error ${err} `);
      throw err;
    }
}

async function getDynamicVisualizationData(nodeId,type) {
  let configurationView = {
    [constants.dynamicConfiguration.officialTag]:"VW_DYNAMIC_JSON_SF",
    [constants.dynamicConfiguration.ifpTag]:"VW_IFP_DYNAMIC_JSON_SF"
  }
  query = `SELECT * FROM ${configurationView[type]} WHERE NODE_ID=${nodeId}`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getDynamicVisualizationData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getDynamicVisualizationData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getDynamicVisualizationData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getCategoricalDimensionsData(meta) {
  return new Promise((resolve, reject) => {
    getCategoricalDimensionsQuery(meta).then(query => {
      getSeriesData(query).then(data => {
        resolve(data);
      })
    }).catch(err => {
      
      reject(err);
    })
  }).catch(err => {
    
    reject(err);
  })
}

async function getVisualizationSeriesData(nodeId,type) {
  let configurationView = {
    [constants.dynamicConfiguration.officialTag]:"VW_SERIES_META",
    [constants.dynamicConfiguration.ifpTag]:"VW_IFP_SERIES_META"
  }
  query = `SELECT * FROM ${configurationView[type]} WHERE NODE_ID=${nodeId}`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getVisualizationSeriesData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getVisualizationSeriesData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getVisualizationSeriesData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getFilterPanelData(indicatorId,type) {
  let configurationView = {
    [constants.dynamicConfiguration.officialTag]:"VW_STATISTICAL_IND_ATTRIBUTES",
    [constants.dynamicConfiguration.ifpTag]:"VW_IFP_INDICATOR_ATTRIBUTES"
  }
  query = `SELECT DIMENSION,LABEL,VALUE FROM ${configurationView[type]} WHERE INDICATOR_ID='${indicatorId}' AND RANK=1`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getFilterPanelData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getFilterPanelData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getFilterPanelData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getPeriodFilterData(indicatorId,type) {
  let configurationView = {
    [constants.dynamicConfiguration.officialTag]:"VW_DYNAMIC_JSON_FILTER",
    [constants.dynamicConfiguration.ifpTag]:"VW_IFP_DYNAMIC_JSON_FILTER"
  }
  query = `SELECT * FROM ${configurationView[type]} WHERE INDICATOR_ID=${indicatorId} ORDER BY FILTER_GROUP`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getPeriodFilterData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getPeriodFilterData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getPeriodFilterData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getBulkDynamicVisualizationData(nodeIds,type) {
  let configurationView = {
    [constants.dynamicConfiguration.officialTag]:"VW_DYNAMIC_JSON",
    [constants.dynamicConfiguration.ifpTag]:"VW_IFP_DYNAMIC_JSON"
  }
  query = `SELECT * FROM ${configurationView[type]} WHERE NODE_ID IN (${nodeIds.join(',')})`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getBulkDynamicVisualizationData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getBulkDynamicVisualizationData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getBulkDynamicVisualizationData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function setPopularStatData(nodeId) {
  query = `MERGE INTO IFP_POPULAR_STATS p\
          USING ( SELECT ${nodeId} AS NODE_ID FROM dual ) s\
          ON (p.NODE_ID = s.NODE_ID)\
          WHEN MATCHED THEN\
            UPDATE SET p.HIT_COUNT = p.HIT_COUNT + 1\
          WHEN NOT MATCHED THEN\
            INSERT (NODE_ID, HIT_COUNT)\
            VALUES (s.NODE_ID, 1)`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.setPopularStatData`);
    db.simpleExecute(query)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.setPopularStatData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.setPopularStatData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getNewOverviewData(nodeIds,type,viewName="") {
  let overViewSources = {
    [constants.dynamicConfiguration.officialTag]:"VW_STATISTICAL_IND_OVERVIEW"
    // [constants.dynamicConfiguration.ifpTag]:"IFP_INNOVATIVE_OVERVIEW"
  }
  const binds = {};
  nodeIds.forEach((id, index) => {
      binds[`nodeId${index + 1}`] = id;
  });
  

  let configurationView;
  let query; 
  let identifier = 'NODE_ID'

  if (type.includes('_screener')){
    identifier = 'INDICATOR_ID'
    configurationView = viewName
    query = `
      WITH
          RankedData
          AS
              (SELECT INDICATOR_ID AS NODE_ID,VALUE_LATEST AS VALUE,
                      MONTHLY_CHANGE_VALUE,
                      MONTHLY_COMPARE_VALUE,
                      QUARTERLY_CHANGE_VALUE,
                      QUARTERLY_COMPARE_VALUE,
                      YEARLY_CHANGE_VALUE,
                      YEARLY_COMPARE_VALUE,
                      UNIT,
                      OBS_DT_LATEST AS OBS_DT,
                      YEARLY,
                      MONTHLY,
                      QUARTERLY,
                      ROW_NUMBER ()
                          OVER (PARTITION BY ${identifier} ORDER BY OBS_DT DESC)    AS RowRank
                FROM ${configurationView}
                WHERE ${identifier} IN (${nodeIds.join(',')}))
      SELECT *
        FROM RankedData
      WHERE RowRank = 1`
  }
  else{
    configurationView = overViewSources[type]
    if (configurationView)
        query = `SELECT * FROM ${configurationView} WHERE ${identifier} IN (${nodeIds.join(',')})`;
    else
      throw new Error(`Cannot process the type. Invalid type: ${type}`)
  }

  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getNewOverviewData`);
    const containsClView = clkTables.some(view => query.includes(view));
    if (containsClView)
      clkdb.simpleExecute(query,binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getNewOverviewData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.setPopularStatData with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
      else
        db.simpleExecute(query,binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getNewOverviewData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.setPopularStatData with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
  });
}

async function getGeneratedVizConfigurationData(nodeId,type) {
  let configurationView = {
    [constants.dynamicConfiguration.officialTag]:"VW_DYNAMIC_JSON_CONFIG",
    [constants.dynamicConfiguration.ifpTag]:"IFP_DYNAMIC_JSON_CONFIG"
  }

  if (!configurationView[type]) {
    log.error(`Invalid type provided: ${type}`);
    throw new Error("Invalid type"); 
  }


  query = `SELECT * FROM ${configurationView[type]} WHERE INDICATOR_ID = {nodeId: String}`
  return new Promise((resolve, reject) => {
    log.debug(`>>>>> Enter microservice-statistics-insights.services.getGraphData.getGeneratedVizConfigurationData`);
    clkdb.simpleExecute(query,{ nodeId: nodeId },{ fetchInfo: { CONFIGURATION: { type: oracledb.STRING },META_DATA: { type: oracledb.STRING } } })
      .then((data) => {
        log.debug(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getGeneratedVizConfigurationData successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-statistics-insights.services.getGraphData.getGeneratedVizConfigurationData with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })
  });
}

async function getIndicatorMetaData(indicatorId) {
  return new Promise((resolve, reject) => {

    let binds ={
        
    }
    let query = `SELECT * FROM VW_INDICATOR_MAP WHERE INDICATOR_ID='${indicatorId}'`
    
    log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery`);
    
    clkdb.simpleExecute(query, binds)
    .then((data) => {
      log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery successfully`);
      resolve(data);
    })
    .catch((err) => {
      
      log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery with error ${err}`);
      log.error(`Error Executing Query:- ${err}`);
      reject([423, err]);
    })
  })
}

async function getPopStatsData(nodeIds) {
  return new Promise((resolve, reject) => {
    getPopStatsDataQuery(nodeIds).then((query) => {
      db.simpleExecute(query, {}).then(async (data) => {
        return resolve(data);
      }).catch((err) => {
        
        log.error(`<<<<< Exited microservice-statistics-insights.executeQuery.service.getPopStatsData with error ${err}`);
        reject(err);
      })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-statistics-insights.executeQuery.service.getPopStatsData with error ${err}`);
      reject(err);
    })
  })
}

async function getLimitIndicators(){
  try{
    let data = await clkdb.simpleExecute("SELECT INDICATOR_ID,OVERVIEW_JSON FROM LIMIT_INDICATOR_DATA")
    return data;
  }
  catch(err){
    log.error(`<<<<<< Exited microservice-statistics-insights.services.getQuery.service.getLimitIndicators with error ${err} `);
    return []
  }
}

module.exports = {
  getGraphData, getValuesData, getSeriesData, 
  getPercentageChange, getFilterOptionsFromDB, 
  getAllFilterDataFromDB, getAccuracyMetricsData,
  getDynamicSeriesData, getDynamicVisualizationData, 
  getBulkDynamicVisualizationData, getCategoricalDimensionsData,
   getVisualizationSeriesData, getFilterPanelData, getPeriodFilterData,
   setPopularStatData,getNewOverviewData,getGeneratedVizConfigurationData,getIndicatorMetaData, getPopStatsData,getLimitIndicators
}