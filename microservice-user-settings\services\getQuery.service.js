const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getUserSettingsDataQuery(userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM IFP_USER_OPTIONS WHERE EMAIL='${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.getUserSettingsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function updateUserSettingsDataQuery(userEmail,setting,value) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO IFP_USER_OPTIONS ( EMAIL, SETTING, VALUE ) VALUES ( '${userEmail}', '${setting}', '${value}' )`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.updateUserSettingsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function deleteUserSettingsDataQuery(userEmail) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM IFP_USER_OPTIONS WHERE EMAIL='${userEmail}'`
            return resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-user-settings.services.getQuery.service.deleteUserSettingsDataQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = { getUserSettingsDataQuery, updateUserSettingsDataQuery, deleteUserSettingsDataQuery };
