const db = require('../../services/database.service');

const clkdb = require('../../services/clk-database.service');
const { getInnovativeIndicatorDataQuery, getInnovativeFilterDataQuery, getInnovativeIndicatorsDataQuery, getIndicatorMetaDataQuery } = require('./getQuery.service')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

const clkTables = require('../../config/constants.json').clickhouseTables;

async function getInnovativeIndicatorData(indicatorId, viewName,lang) {

  return new Promise((resolve, reject) => {
    getInnovativeIndicatorDataQuery(indicatorId, viewName,lang).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData`);
      if (clkTables.includes(viewName))
        clkdb.simpleExecute(results.query,results.binds)
          .then((data) => {
            log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData successfully`);
            resolve(data);
          })
          .catch((err) => {
            
            log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData with error ${err}`);
            log.error(`Error Executing Query:- ${err}`);
            reject([423, err]);
          })
      else
        db.simpleExecute(results.query, results.binds)
          .then((data) => {
            log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData successfully`);
            resolve(data);
          })
          .catch((err) => {
            
            log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeIndicatorData with error ${err}`);
            log.error(`Error Executing Query:- ${err}`);
            reject([423, err]);
          })
    }).catch((err) => {
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getInnovativeIndicatorData with error ${err}`);
      reject(err);
    })
  });
}

async function getInnovativeFilterData(filterIndicator,lang) {

  return new Promise((resolve, reject) => {
    getInnovativeFilterDataQuery(filterIndicator,lang).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getInnovativeFilterData`);
      clkdb.simpleExecute(results.query,results.binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeFilterData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeFilterData with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getInnovativeFilterData with error ${err}`);
      reject(err);
    })

  });
}

async function getInnovativeIndicatorsData(viewName, filters, sortBy = {}, offset, limit,lang) {
  return new Promise((resolve, reject) => {
    getInnovativeIndicatorsDataQuery(viewName, filters, sortBy, offset, limit,lang).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getInnovativeFilterData`);
      clkdb.simpleExecute(results.query,results.binds)
        .then((data) => {
          log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeFilterData successfully`);
          resolve(data);
        })
        .catch((err) => {
          
          log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getInnovativeFilterData with error ${err}`);
          log.error(`Error Executing Query:- ${err}`);
          reject([423, err]);
        })
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getInnovativeFilterData with error ${err}`);
      reject(err);
    })
  });
}

async function getIndicatorMetaData(indicatorId,lang) {
  return new Promise((resolve, reject) => {
    getIndicatorMetaDataQuery(indicatorId,lang).then((results) => {
      log.debug(`>>>>> Enter microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery`);
      clkdb.simpleExecute(results.query, results.binds)
      .then((data) => {
        log.debug(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery successfully`);
        resolve(data);
      })
      .catch((err) => {
        
        log.error(`<<<<< Exit microservice-innovative-insights.services.getGraphData.getIndicatorMetaDataQuery with error ${err}`);
        log.error(`Error Executing Query:- ${err}`);
        reject([423, err]);
      })      
    }).catch((err) => {
      
      log.error(`<<<<< Exited microservice-innovative-insights.executeQuery.service.getIndicatorMetaDataQuery with error ${err}`);
      reject(err);
    })
  })
}



module.exports = {
  getInnovativeIndicatorData,
  getInnovativeFilterData,
  getInnovativeIndicatorsData,
  getIndicatorMetaData
}