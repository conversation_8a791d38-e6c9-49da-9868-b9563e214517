{"paths": {"/content-type/footer/": {"get": {"tags": ["Footer"], "summary": "Retrive Footer information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "The title of the footer section."}, "site_slogan": {"type": "string", "format": "uri", "description": "URL to the site's slogan image for dark themes."}, "site_slogan_light": {"type": "string", "format": "uri", "description": "URL to the site's slogan image for light themes."}, "footer_menu_first": {"type": "array", "items": {"type": "object", "properties": {"menu_label": {"type": "string", "description": "Label of the menu item."}, "menu_link": {"type": "string", "description": "Link associated with the menu item."}, "show_dropdown": {"type": "string", "enum": ["Yes", "No"], "description": "Indicates if the menu item should show a dropdown. Expected values: 'Yes' or 'No'."}}, "required": ["menu_label", "menu_link", "show_dropdown"]}, "description": "The first set of footer menu items."}, "footer_menu_second": {"type": "array", "items": {"type": "object", "properties": {"menu_label": {"type": "string", "description": "Label of the menu item."}, "menu_link": {"type": "string", "description": "Link associated with the menu item."}, "show_dropdown": {"type": "string", "enum": ["Yes", "No"], "description": "Indicates if the menu item should show a dropdown. Expected values: 'Yes' or 'No'."}}, "required": ["menu_label", "menu_link", "show_dropdown"]}, "description": "The second set of footer menu items."}, "copyright": {"type": "string", "description": "Copyright statement for the site."}}, "required": ["title", "site_slogan", "site_slogan_light", "footer_menu_first", "footer_menu_second", "copyright"], "description": "Schema for a response detailing the structure and content of a website footer."}}}}}}}, "/content-type/footer/about-us": {"get": {"tags": ["Footer"], "summary": "Retrieve About Us information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "The title of the page."}, "description": {"type": ["string", "null"], "description": "The description of the page. Can be null if not provided."}, "header_video_link": {"type": "string", "format": "uri", "description": "URL to a header video related to the page."}, "header_video_thumbnail": {"type": "string", "format": "uri", "description": "URL to a thumbnail image for the header video."}, "publication_date": {"type": "string", "format": "date-time", "description": "The publication date and time of the page content."}, "page_content": {"type": "array", "items": {"type": "object", "properties": {"paragraph_content": {"type": "string", "description": "The content of the paragraph."}, "paragraph_title": {"type": "string", "description": "The title of the paragraph."}, "paragraph_type": {"type": "string", "description": "The type of content in the paragraph."}}, "required": ["paragraph_content", "paragraph_title", "paragraph_type"]}, "description": "A collection of paragraphs making up the page content."}, "page_images": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "format": "uri", "description": "URL to an image related to the page content."}, "alt": {"type": "string", "description": "Alternative text describing the image."}}, "required": ["url", "alt"]}, "description": "A collection of images related to the page content."}}, "required": ["title", "header_video_link", "header_video_thumbnail", "publication_date", "page_content", "page_images"], "description": "<PERSON><PERSON>a for a response detailing the content of an 'About us' page, including video, text, and images."}}}}}}}, "/content-type/footer/products": {"get": {"tags": ["Footer"], "summary": "Retrive Products information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "The title of the page."}, "description": {"type": ["string", "null"], "description": "A detailed description of the page content."}, "header_video_link": {"type": "string", "format": "uri", "description": "The URL to the header video."}, "header_video_thumbnail": {"type": "string", "format": "uri", "description": "The URL to the thumbnail image for the header video."}, "publication_date": {"type": "string", "format": "date-time", "description": "The publication date and time of the page."}, "domain": {"type": "object", "properties": {"title": {"type": "string", "description": "The title for the domain section."}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the domain item."}, "title": {"type": "string", "description": "Title of the domain item."}, "page_menu_icon": {"type": "string", "format": "uri", "description": "URL to the icon for the domain item."}, "page_menu_light_icon": {"type": "string", "format": "uri", "description": "URL to the light theme icon for the domain item."}}, "required": ["id", "title", "page_menu_icon", "page_menu_light_icon"]}, "description": "List of items within the domain."}}, "required": ["title", "items"], "description": "Section detailing the domains covered."}, "analytical_apps": {"type": "object", "properties": {"title": {"type": "string", "description": "The title for the analytical apps section."}, "items": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the analytical app item."}, "title": {"type": "string", "description": "Title of the analytical app item."}, "description_1": {"type": ["string", "null"], "description": "Primary description for the analytical app item."}, "description_2": {"type": ["string", "null"], "description": "Secondary description for the analytical app item."}, "image_dark_theme": {"type": "string", "format": "uri", "description": "URL to the image for dark theme."}, "image_light_theme": {"type": "string", "format": "uri", "description": "URL to the image for light theme."}}, "required": ["id", "title", "image_dark_theme", "image_light_theme"]}, "description": "List of items within the analytical apps section."}}, "required": ["title", "items"], "description": "Section detailing analytical apps available."}, "cards": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the card."}, "title": {"type": "string", "description": "Title of the card."}, "description_1": {"type": ["string", "null"], "description": "Primary description for the card."}, "description_2": {"type": ["string", "null"], "description": "Secondary description for the card, if any."}, "image_dark_theme": {"type": "string", "format": "uri", "description": "URL to the card image for dark theme."}, "image_light_theme": {"type": "string", "format": "uri", "description": "URL to the card image for light theme."}}, "required": ["id", "title", "image_dark_theme", "image_light_theme"]}, "description": "A collection of cards with detailed information."}}, "required": ["title", "description", "domain", "analytical_apps", "cards"], "description": "Schema for a detailed response including information about products, domains, analytical apps, and cards."}}}}}}}, "/content-type/content-type/footer/privacy-policy": {"get": {"tags": ["Footer"], "summary": "Retrieve Privacy Policy information", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"title": {"type": "string", "description": "The title of the document or page."}, "description": {"type": "string", "description": "A brief description or summary of the document's content."}, "content_data": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"section_order": {"type": "string", "description": "The order in which this section appears within the document."}, "section_title": {"type": "string", "description": "The title of the section."}, "subsections": {"type": "array", "items": {"type": "object", "properties": {"order": {"type": "string", "description": "The order in which this subsection appears within its section."}, "title": {"type": "string", "description": "The title of the subsection."}, "content": {"type": "string", "description": "The content of the subsection."}}, "required": ["order", "title", "content"]}, "description": "An array of subsections within a section, each with its own title and content."}}, "required": ["section_order", "section_title", "subsections"]}, "description": "An array of sections that make up the document, each containing one or more subsections."}}, "required": ["items"], "description": "The structured content of the document, organized into sections and subsections."}}, "required": ["title", "description", "content_data"], "description": "Schema for a response containing the structured content of a privacy policy document."}}}}}}}}}