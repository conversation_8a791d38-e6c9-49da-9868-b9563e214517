const colorCodes = require('./color-map.json')
const {getDynamicVisualizationData, getBulkDynamicVisualizationData} = require('../getGraphData')
const {generateSeriesMeta,generatePeriodFilters} = require('./helper.js')
const getCategoricalDimensionsData = require('../../services/getGraphData').getCategoricalDimensionsData
const constants = require('../../../config/constants.json')

const Logger = require('scad-library').logger;

async function getVizConfiguration(cmsResponse,nodeId) {
    return new Promise(async (resolve, reject) => {
        let log = new Logger().getInstance();
        let type = constants.classificationKeys[cmsResponse.content_classification] == constants.dynamicConfiguration.officialTag?constants.dynamicConfiguration.officialTag:constants.dynamicConfiguration.ifpTag;
        
        let dynamicJsonWithSeriesData = await getDynamicVisualizationData(nodeId,type)
        let dynamicJsonData = dynamicJsonWithSeriesData.filter(d => d.SERIES == 0 && d.FILTER == 0)

        if (!dynamicJsonData.length)
            log.info("[DYNAMIC JSON] Visualization data not found")

        let visualizationMeta = []
        let chartId = "a"+Date.now().toString()
        let boolKeys = ['dimension','isMultiDimension','showFilter']
        let convertBool = function (x) {if (x.toLowerCase()=="false") return false; else return true}
        let visualization = {
            "id": "line-chart-" + chartId,
            "type": "",
            "seriesMeta": [],
            "tooltipTitleFormat": "",
            "tooltipValueFormat": "",
            "xAxisFormat": "",
            "yAxisFormat": "",
            "xAxisLabel": "",
            "yAxisLabel": "",
            "viewName": "",
            "markersMeta": [],
            "dbColumn": "INDICATOR_ID"
        }
        dynamicJsonData.forEach(element => {
            if (element['JSON_VALUE'] != null)
                visualization[element['JSON_KEY']] = boolKeys.includes(element.JSON_KEY)?convertBool(element['JSON_VALUE']):element['JSON_VALUE']
        });

        if (visualization.timeUnit){
            visualization.timeUnit = visualization.timeUnit.split(',')
        }

        if (visualization.showFilter){
            try{
                cmsResponse.indicatorFilters = []
                let periodFilters = {"id": "period-filter","options": []}
                let filterData = dynamicJsonWithSeriesData.filter(d => d.FILTER == 1)
                periodFilters['options'] = generatePeriodFilters(filterData)
                cmsResponse.indicatorFilters.push(periodFilters)
                delete visualization.showFilter
            }
            catch(err){
                log.info(`[DYNAMIC FILTER] Error when generating filters ${err}`)
            }
        }
        if (visualization.dimension){
            visualization.showLegend = true;
        }
        if (visualization.isMultiDimension){
            visualization.showLegend = true;
            cmsResponse.indicatorIdValue = visualization.dbIndicatorId
        }

        //Generating Series Configuration
        let genericSeriesData = dynamicJsonWithSeriesData.filter(d => d.SERIES == 1)
        visualization.seriesMeta = await generateSeriesMeta(visualization,chartId,genericSeriesData)

        if (!visualization.seriesMeta.length)
            return reject("Invalid series Meta")
 
        visualizationMeta.push(visualization)

        return resolve(visualizationMeta)
    });

}

async function getBulkIndicatorMetaForOverview(nodeIds,type) {
    return new Promise(async (resolve, reject) => {
        let log = new Logger().getInstance();
        let bulkDynamicJsonData = await getBulkDynamicVisualizationData(nodeIds,type)
        let bulkIndicatorMeta = {}

        const groupedBulkDynamicJsonData = bulkDynamicJsonData.reduce((result, record) => {
            const { NODE_ID } = record;
            if (!result[NODE_ID]) {
                result[NODE_ID] = [];
            }
            result[NODE_ID].push(record);
            return result;
        }, {});

        Object.entries(groupedBulkDynamicJsonData).map(([key, dynamicJsonData]) => {
            if (!dynamicJsonData.length)
                log.info("[DYNAMIC JSON] Data not found")
            
            let requiredKeys = ['viewName','dbIndicatorId','timeUnit']
            let indicatorData = dynamicJsonData.filter(record => requiredKeys.includes(record.JSON_KEY))
            let indicatorMeta = {}
            indicatorData.forEach(element => {
                indicatorMeta[element['JSON_KEY']] = element['JSON_VALUE']
            });
            if (indicatorMeta.timeUnit){
                indicatorMeta.timeUnit = indicatorMeta.timeUnit.split(',')
            }

            bulkIndicatorMeta[key]={...indicatorMeta}
            
        })
        return resolve(bulkIndicatorMeta)
    });

}


module.exports = { getVizConfiguration, getBulkIndicatorMetaForOverview }