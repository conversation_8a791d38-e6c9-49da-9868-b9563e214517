const { getRequestAccess, getSanadkomTicketStatus, setRequestStatus, getGroupMembership, getUserDataV2, getDisseminationAccessPolicy, setUserActivationStatus, getUserAccessRequests, getUserAccessLevels, updateUserAccessRequestStatus, updateUserAccessRequestIntraIdStatus } = require("./executeQuery");
const { inviteIntraIdUser, inviteStatusIntraIdUser, assignUserToGroup } = require("./graph");
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const groupMatrix = require('../services/groupMatrix.json')
const domainMatrix = require('../services/domainMatrixMap.json')

/**
 * Polls for SANADKOM Ticket Status
 */
async function sanadkomPoll() {
    try{
        log.info('[SANADKOM CRON] Polling ticket status start')
        //Fetch pending requests with SANADKOM
        let pendingRequests = await getUserDataV2({
            ROLE:['PRIMARY_SUPERUSER','SECONDARY_SUPERUSER'],
            STATUS:'REGISTERED',
            ACTIVATION_STATUS:'PENDING'
        })
        if (pendingRequests.length)
            pendingRequests.forEach(async request => {
                //Fetch ticket status from SANADKOM
                let status = await getSanadkomTicketStatus(request.ID)
                if (status == 'APPROVED'){
                    //Set status of SANADKOM record in the database table to APPROVED
                    await setRequestStatus(request.EMAIL,'SANADKOM','APPROVED')
                    //trigger an email to the Product Engagement
                }
                else if (status == 'REJECTED'){
                    //Set status of SANADKOM record in the database table to REJECTED
                    await setRequestStatus(request.EMAIL,'SANADKOM','REJECTED')
                    //trigger an email to the Product Engagement
                }
            });
        else
            log.info('[SANADKOM CRON] No Pending requests found in SANADKOM')

        log.info('[SANADKOM CRON] Polling ticket status end')
    }
    catch(err){
        throw err;
    }
}

/**
 * Sends Intra ID invites to SANADKOM approved users
 */
async function intraIdInviter() {
    try{
        log.info('[INTRAIDINVITER CRON] Invite Guest Users start')
        //Fetch approved requests from SANADKOM database table
        let sanadkomApprovedRequests = await getRequestAccess(status='APPROVED',statusType='SANADKOM')
        if (sanadkomApprovedRequests.length){
            requestPromises = []
            for (const request of sanadkomApprovedRequests){
                //Check for the intra id invite status for request
                if (request.INTRA_ID_INVITE_STATUS == 'PENDING')
                    requestPromises.push(inviteIntraIdUser(request.EMAIL))
            }
            await Promise.all(requestPromises);
        }
        else
            log.info('[INTRAIDINVITER CRON] No SANADKOM Approved requests found')
        log.info('[INTRAIDINVITER CRON] Invite Guest Users end')
    }
    catch(err){
        throw err;
    }
}

/**
 * Sends Intra ID invites to SANADKOM approved users
 */
async function superuserIntraIdInviter() {
    try{
        log.info('[INTRAIDINVITER CRON] Invite Superuser Users start')
        //Fetch approved requests from SANADKOM database table
        let pendingRequests = await getUserDataV2({
            ROLE:['PRIMARY_SUPERUSER','SECONDARY_SUPERUSER'],
            STATUS:'REGISTERED',
            ACTIVATION_FLAG:'PENDING'
        })
        if (pendingRequests.length){
            requestPromises = []
            for (const request of pendingRequests){
                //Check for the intra id invite status for request
                if (request.ACTIVATION_FLAG == 'PENDING')
                    requestPromises.push(inviteIntraIdUser(request.EMAIL))
            }
            await Promise.all(requestPromises);
        }
        else
            log.info('[INTRAIDINVITER CRON] No Pending Superusers found')
        log.info('[INTRAIDINVITER CRON] Invite Superuser Users end')
    }
    catch(err){
        throw err;
    }
}

/**
 * Sends Intra ID invites to SANADKOM approved users
 */
async function dgIntraIdInviter() {
    try{
        log.info('[DGINTRAIDINVITER CRON] Invite DG Users start')
        //Fetch approved requests from SANADKOM database table
        let pendingRequests = await getUserDataV2({
            ROLE:['DG'],
            STATUS:'REGISTERED',
            ACTIVATION_FLAG:'PENDING'
        })
        if (pendingRequests.length){
            requestPromises = []
            for (const request of pendingRequests){
                //Check for the intra id invite status for request
                if (request.ACTIVATION_FLAG == 'PENDING')
                    requestPromises.push(inviteIntraIdUser(request.EMAIL))
            }
            await Promise.all(requestPromises);
        }
        else
            log.info('[DGINTRAIDINVITER CRON] No DG Users found')
        log.info('[DGINTRAIDINVITER CRON] Invite DG Users end')
    }
    catch(err){
        throw err;
    }
}

/**
 * Sends Intra ID invites to SANADKOM approved users
 */
async function userIntraIdInviter() {
    try{
        log.info('[USERINTRAIDINVITER CRON] Invite Users start')
        //Fetch approved requests from SANADKOM database table
        let pendingRequests = await getUserDataV2({
            ROLE:['USER'],
            STATUS: 'REGISTERED',
            ACTIVATION_FLAG:'PENDING'
        })
        if (pendingRequests.length){
            requestPromises = []
            for (const request of pendingRequests){
                requestPromises.push(inviteIntraIdUser(request.EMAIL))
            }
            await Promise.all(requestPromises);
        }
        else
            log.info('[USERINTRAIDINVITER CRON] No Users found')
        log.info('[USERINTRAIDINVITER CRON] Invite Users end')
    }
    catch(err){
        throw err;
    }
}

/**
 * Polls Intra ID invite acceptance status
 */
async function intraIdInviteAcceptancePoll() {
    try{
        log.info('[INTRAIDINVITEACCEPTPOLL CRON] Polling Invite Status start')
        let intraIdInviteSentRequests = await getUserDataV2({
            STATUS:'REGISTERED',
            ACTIVATION_FLAG:'ENTRA_ID_INVITE_SENT'
        })
        requestPromises = []

        for (const request of intraIdInviteSentRequests){
            let intraIdStatus = await inviteStatusIntraIdUser(request.EMAIL)
            if (intraIdStatus.inviteStatus && intraIdStatus.accountEnabled){
                log.info(`[INTRAIDINVITEACCEPTPOLL CRON] User ${request.EMAIL} has accepted invite`)
                await setUserActivationStatus(request.EMAIL,'ACTIVE')

                //Assign groups for Superusers based on Dissemination Policy
                if (['PRIMARY_SUPERUSER','SECONDARY_SUPERUSER','DG'].includes(request.ROLE)){
                    const accessPolicies = await getDisseminationAccessPolicy(request.ENTITY_ID)
                    let matrix_groups = []
                    
                    accessPolicies.forEach(policy=>{
                        matrix_groups.push(`${domainMatrix[policy.DOMAIN.toUpperCase()]}__${policy.CLASSIFICATION.toUpperCase()}`)  
                        })

                    log.info(`Groups to be assigned to ${request.EMAIL} : [${matrix_groups}]`)

                    let matrixGroupIds = matrix_groups.map(g=>{
                        groups = Object.entries(groupMatrix).map(([id,name]) =>{
                            if (g==name)
                                return id
                        })
                        groups = groups.filter(id=>id !=undefined)
                        if (groups.length)
                            return groups[groups.length - 1]
                        else
                            throw null;
                    })
                    if (matrixGroupIds.length){
                        await assignUserToGroup(intraIdStatus.id,matrixGroupIds)
                        log.info(`Groups assigned to ${request.EMAIL} : [${matrix_groups}]`)
                    }
                }
                //Assign groups for users will be done using another job on request basis

            }
        }
        
    await Promise.all(requestPromises);
        log.info('[INTRAIDINVITEACCEPTPOLL CRON] Polling Invite Status start')
    }
    catch(err){
        throw err;
    }
}

/**
 * Polls Intra ID invite acceptance status
 */
async function userIntraIdGroupAssign() {
    try{
        log.info('[INTRAIDGROUPASSIGN CRON] Polling Invite Status start')
        let intraIdAcceptedRequests = await getUserAccessRequests({
            STATUS:'COMPLETED',
            INTRA_ID_ASSIGN_STATUS:'PENDING'
        })
        requestPromises = []

        for (const request of intraIdAcceptedRequests){
            let userData = await getUserDataV2({
                ID:request.USER_ID
            })
            userData = userData[0]
            let intraIdStatus = await inviteStatusIntraIdUser(userData.EMAIL)
            if (intraIdStatus.inviteStatus && intraIdStatus.accountEnabled){
                log.info(`[INTRAIDGROUPASSIGN CRON] User ${userData.EMAIL} is part of SCAD Intra ID`)
            
                let userAccessLevels = await getUserAccessLevels({
                    REQUEST_ID: request.REQUEST_ID
                })
                let grantLevels = userAccessLevels
                                    .filter(l=>l.ACCESS_OPERATION=='GRANT')
                                    .map(l => ({
                                        domain: l.DOMAIN,
                                        classification: l.ACCESS_LEVEL
                                    }));
                let revokeLevels = userAccessLevels
                                    .filter(l=>l.ACCESS_OPERATION=='REVOKE')
                                    .map(l => ({
                                        domain: l.DOMAIN,
                                        classification: l.ACCESS_LEVEL
                                    }));

                const classificationLevels = ["open", "confidential", "sensitive", "secret"];
                const classificationRank = {
                    open: 1,
                    confidential: 2,
                    sensitive: 3,
                    secret: 4,
                };

                const expandClassifications = (arr) => {
                    const result = [];
                    const domainMap = {};
                  
                    arr.forEach(({ domain, classification }) => {
                      const currentRank = classificationRank[classification.toLowerCase()];
                      if (!domainMap[domain] || classificationRank[domainMap[domain].toLowerCase()] < currentRank) {
                        domainMap[domain] = classification;
                      }
                    });
                  
                    for (const [domain, highestClassification] of Object.entries(domainMap)) {
                      const highestRank = classificationRank[highestClassification.toLowerCase()];
                      classificationLevels.forEach((classification) => {
                        if (classificationRank[classification] <= highestRank) {
                          result.push({ domain, classification });
                        }
                      });
                    }
                  
                    return result;
                };
                  
                grantLevels = expandClassifications(grantLevels);

                //Assign groups for Users based on requests
                let matrix_groups = []
                
                grantLevels.forEach(level=>{
                    matrix_groups.push(`${domainMatrix[level.domain.toUpperCase()]}__${level.classification.toUpperCase()}`)  
                    })

                log.info(`Groups to be assigned to ${userData.EMAIL} : [${matrix_groups}]`)

                let matrixGroupIds = matrix_groups.map(g=>{
                    groups = Object.entries(groupMatrix).map(([id,name]) =>{
                        if (g==name)
                            return id
                    })
                    groups = groups.filter(id=>id !=undefined)
                    if (groups.length)
                        return groups[groups.length - 1]
                    else
                        throw null;
                })
                if (matrixGroupIds.length){
                    await assignUserToGroup(intraIdStatus.id,matrixGroupIds)
                    await updateUserAccessRequestIntraIdStatus(request.REQUEST_ID,'COMPLETED')
                    log.info(`Groups assigned to ${userData.EMAIL} : [${matrix_groups}]`)
                }

                //Revoke access to be implemented
            }
        }
        
    await Promise.all(requestPromises);
        log.info('[INTRAIDINVITEACCEPTPOLL CRON] Polling Invite Status start')
    }
    catch(err){
        throw err;
    }
}


module.exports = {
    sanadkomPoll,
    intraIdInviter,
    superuserIntraIdInviter,
    dgIntraIdInviter,
    userIntraIdInviter,
    intraIdInviteAcceptancePoll,
    userIntraIdGroupAssign
}

