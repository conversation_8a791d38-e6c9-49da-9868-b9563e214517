const Logger = require('scad-library').logger;
const moment = require('moment');
require('dotenv').config();

const { getMetaFromCMS, getIndicatorValuesIcon, getColors } = require('../services/common-service');
const { getGraphData, getAllFilterDataFromDB, getAccuracyMetricsData,getDynamicSeriesData, setPopularStatData, getNewOverviewData, getGeneratedVizConfigurationData, getIndicatorMetaData, getPopStatsData, getLimitIndicators } = require('./services/getGraphData');
const { getValuesData } = require('./services/getValuesData.service');
const { getNewValuesData, isNewValuesMeta } = require('./services/getNewValuesData.service');
const { getCompareSeries } = require('./services/getCompareData');
const { processLineChartData } = require('./services/chart-services/line-chart');
const { getStackedVerticalBarData } = require('./services/chart-services/stacked-vertical-bar-chart');
const { getPyramidData } = require('./services/chart-services/pyramid-chart');
const { getSingleHorizontalBar } = require('./services/chart-services/single-horizontal-bar-chart');
const { nonChartData } = require('./services/nonchart.service')
const { getSunBurstSeries } = require('./services/chart-services/sunburst-with-change-chart');
const { getSingleCircularBarSeries } = require('./services/chart-services/single-circular-bar-chart');
const { getSinglePieSeries,getSinglePieSeriesWithFilter } = require('./services/chart-services/single-pie-chart');
const { getScatterPlotSeries } = require('./services/chart-services/single-scatterplot-chart');
const { getTableSeries } = require('./services/chart-services/table-view');
const { getTreeSeries } = require('./services/chart-services/tree-map-with-change-chart');
const { getFilterOptions, getAllFilterData, getFilterDimensionTypes } = require('./services/getFilterOptions');
const { checkUploadedDataPublished, getUploadCompareData } = require('../services/executeQuery.service');
const { assignConfigurationValuesResponse }  = require('./services/chart-configuration-services/helper');
const inflationController = require('../microservice-inflation/inflation.controller');
// const { getVizConfiguration, getBulkIndicatorMetaForOverview} = require('./services/chart-configuration-services/viz-mapper')
// const { getFilterConfiguration } = require('./services/chart-configuration-services/filter-mapper')
const constants = require('../config/constants.json');
const groupMatrix = require('../services/helpers/groupMatrix.json')

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { constructCmsUrl, getLangPrefix } = require('../utils/functions');
const { IFPError } = require('../utils/error');

class StatisticsInsightsData {
    constructor() {
        this.contentId = '';
        this.cmsResponse = {};
        this.reqBody = [];
        this.log = {};
        this.graphData = [];
        this.lang = '';
    }


    getStatisticsInsightsById = (req) => {
        this.log = this.log = new Logger().getInstance();
        const counter = [];
        return new Promise(async (resolve, reject) => {
            try {
                this.lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
                let langCode = req.headers["accept-language"]

                let originalNode = 0;
                let censusNodeDuplicationMap = constants.censusNodeDuplicationMap
                if (Object.keys(censusNodeDuplicationMap).includes(req.params.id)){
                    originalNode = req.params.id 
                    req.params.id=censusNodeDuplicationMap[req.params.id]
                }

                this.contentId = req.params.id;
                let query = req.query;
                const cmsStatisticsInsightsByIdUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsGroupUrl.CMS_STATISTICS_INSIGHTS_URL_BYID}${this.contentId}`;
                const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
                this.cmsResponse = await getMetaFromCMS(req,cmsLoginUrl, cmsStatisticsInsightsByIdUrl, req.user.groups,'',req);

                if (!this.cmsResponse || (Array.isArray(this.cmsResponse) && this.cmsResponse.length === 0) || (typeof this.cmsResponse === 'object' && Object.keys(this.cmsResponse).length === 0)) {
                    throw new IFPError(404, 'Data not found in CMS');
                }
                const censusNodes= constants.censusOfficialIndicators
                const reqLang = req.headers["accept-language"]
                if (censusNodes.includes(this.contentId) ){
                    this.cmsResponse["footnote"] = constants.footnote[reqLang]
                }

                if (originalNode){
                    this.cmsResponse.domain="Census"
                    this.cmsResponse.domain_id = '6622'
                    this.cmsResponse.domainId = '6622'
                }
                
                let cmsCacheKey = `cmsMetaStatisticsInsights_${this.contentId}_${crypto.createHash('md5').update(JSON.stringify(this.cmsResponse)+JSON.stringify(query)).digest("hex")}`
                const cmsCacheResults = await getRedis(cmsCacheKey,req.headers);
                if (cmsCacheResults){
                    this.log.info(`<<<<<Cache found for microservice-statistics-insights.getStatisticsInsightsById`);
                    return resolve(JSON.parse(cmsCacheResults));
                }

                if ((this.cmsResponse.type == "innovative_statistics" || this.cmsResponse.type == "official_statistics") && req.query.screener == 'true'){
                    resolve(this.cmsResponse)
                }

                this.cmsResponse.isUploadCompareData = this.cmsResponse.enableUploadAndCompare == true
                    ? await checkUploadedDataPublished({
                        "nodeId": this.contentId,
                        "userId": req.user.preferred_username,
                    })
                    : false;
                
                if (req.query.view == 'detail')
                    setPopularStatData(this.contentId)

                if (req.headers && req.headers.apptype === 'insights-discovery' && this.cmsResponse.filterPanel) {
                    this.cmsResponse.filterPanel.isEnabled = true;
                }
                
                if (this.cmsResponse.filterPanel && this.cmsResponse.filterPanel.isEnabled) {
                    if (!(this.cmsResponse.filterPanel.static && this.cmsResponse.filterPanel.static==true))
                        this.cmsResponse.filterPanel = await getFilterOptions(this.cmsResponse.filterPanel, this.lang);
                }
                if (this.cmsResponse.filterDimensionTypes){
                    this.cmsResponse.filterDimensionTypes = await getFilterDimensionTypes(this.cmsResponse.filterDimensionTypes);
                }
                if (this.cmsResponse) {
                    let newValuesMeta;

                    if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0) {
                        newValuesMeta = await isNewValuesMeta(this.cmsResponse.indicatorValues.valuesMeta);
                        if (newValuesMeta) {
                            await getNewValuesData(this.cmsResponse.indicatorValues.valuesMeta, req.user.groups);
                        }
                    }
                    if (this.cmsResponse.indicatorVisualizations && this.cmsResponse.indicatorVisualizations.visualizationsMeta.length > 0) {
                        if (this.cmsResponse.type == "official_statistics" || this.cmsResponse.type == "innovative_statistics"){

                            let generatedDynamicVisualization = await getGeneratedVizConfigurationData(this.contentId,this.cmsResponse.content_classification_key)
                            let dynamicVisualization = JSON.parse(generatedDynamicVisualization[0].CONFIGURATION)

                            if (generatedDynamicVisualization[0].META_DATA){
                                try{
                                    let metaData = generatedDynamicVisualization[0].META_DATA
                                    let parsedMetaData= JSON.parse(metaData.replace(/\\/g, '').replace(/\n/g, "\\n").replace(/\t/g, "\\t").replace(/_/g, ' '));
                                    this.cmsResponse.metaData =  langCode == 'en' ? parsedMetaData['EN'] : parsedMetaData['AR'];
                                }
                                catch(exp){
                                    this.cmsResponse.metaData = {}
                                }
                            }

                            let clang = req.headers["accept-language"] === 'en' ? '' : `${req.headers["accept-language"]}`;
                            this.cmsResponse= assignConfigurationValuesResponse(this.cmsResponse,dynamicVisualization,clang)

                            this.cmsResponse.indicatorVisualizations.visualizationsMeta = dynamicVisualization;
                            this.cmsResponse.indicatorVisualizations.visualizationDefault = this.cmsResponse.indicatorVisualizations.visualizationsMeta[0].id
                            

                            if (!dynamicVisualization[0].dimension && req.query.overview == "true"){
                                try{
                                    let whatsnewMeta = {
                                            "id": "whatsnew",
                                            "title": "{dateStart}",
                                            "type": "static-with-title-template",
                                            "compareFilters": dynamicVisualization[0].timeUnit?dynamicVisualization[0].timeUnit:["Yearly"],
                                            "valueFormat": "number_1.1-1",
                                            "templateFormat": "date_y",
                                            "color": "#3667ff",
                                            // "viewName": dynamicVisualization[0].viewName,
                                            "dimension": {
                                              "INDICATOR_ID": dynamicVisualization[0].seriesMeta[0].dbIndicatorId
                                            }
                                          
                                    }
                                    
                                    this.cmsResponse.indicatorValues.overviewValuesMeta =  [
                                        whatsnewMeta
                                    ]
                                    await getNewValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, req.user.groups);
                                }
                                catch(exception){
                                    
                                }
                            }
                        }

                
                        let tableFields = [
                            {label:( langCode == 'en' ? "INDICATOR ID" : 'معرف المؤشر'),path:"INDICATOR_ID"}
                        ]

                        if (this.cmsResponse.filterPanel){
                            this.cmsResponse.filterPanel.properties.forEach(property => {
                                tableFields.push({label:property.label,path:property.path})
                            })
                        }

                        tableFields.push({label:( langCode == 'en' ? "VALUE": 'قيمة'),path:"VALUE"})
                        let dateFields = tableFields.filter(field=>{return field.path == 'OBS_DT'})

                        if (! dateFields.length){
                            tableFields.push({label:( langCode == 'en' ? "DATE" : 'تاريخ'),path:"OBS_DT"})
                        }

                        this.cmsResponse.tableFields = tableFields

                        if (this.cmsResponse.indicatorVisualizations && this.cmsResponse.indicatorVisualizations.tableFields){
                            this.cmsResponse.tableFields = this.cmsResponse.indicatorVisualizations.tableFields
                        }

                        if (this.cmsResponse.content_classification_key == constants.classifications.analyticalApps && req.query.overview == "true"){
                            if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.overviewValuesMeta && this.cmsResponse.indicatorValues.overviewValuesMeta.length > 0) {
                                this.cmsResponse.indicatorValues.overviewValuesMeta = await getNewValuesData(this.cmsResponse.indicatorValues.overviewValuesMeta, req.user.groups);
                            }
                        }
                        
                        const indicatorVisualizationsMeta = this.cmsResponse.indicatorVisualizations.visualizationsMeta;

                        let visualizationLen = this.cmsResponse.indicatorVisualizations.visualizationsMeta.length;
                        this.graphData = this.cmsResponse;
                        this.graphData.indicatorVisualizations.visualizationsMeta = [];
                        let chartType = '';

                        try{
                            const operations =  indicatorVisualizationsMeta.map(async visualization => {
                                let results; let yearlyData;
                                try {
                                    
                                    let isFilterPanelEnabled = this.cmsResponse.filterPanel ? this.cmsResponse.filterPanel.isEnabled : "";
                                    if (Object.keys(censusNodeDuplicationMap).includes(originalNode)){
                                        visualization.isDuplicated = true
                                    }

                                    if(isFilterPanelEnabled && query.minimal == 'true'){
                                        let filterBy = {}
                                        this.cmsResponse.filterPanel.properties.forEach(property=>{
                                            filterBy[property.path] = property.default
                                        })
                                        if (Object.keys(filterBy).length > 0){
                                            visualization.filterBy = filterBy
                                        }
                                    }
                                    results = await getGraphData(visualization, this.cmsResponse.type, isFilterPanelEnabled,this.cmsResponse.filterPanel);
                                    if (visualization.yearlyData) {
                                        yearlyData = await getAllFilterDataFromDB(visualization.yearlyData);
                                        yearlyData.forEach(e => {
                                            e["YEAR"] = "ALL";
                                        });
                                    }
                                    results = yearlyData && yearlyData.length > 0 ? results.concat(yearlyData) : results;
                                    if (Object.keys(censusNodeDuplicationMap).includes(originalNode)){
                                        this.cmsResponse.id = originalNode
                                    }

                                } catch (err) {
                                    this.log.error(`Error executing getGraphData from statistics-insights.controller ${err}`);
                                    throw err;
                                }
                                
                                if (visualization.type === "dual-line-bar-chart" || visualization.type === "sunburst-with-line-chart" || visualization.type === "double-line-bar-chart") {
                                    chartType = visualization.subtype;
                                } else {
                                    chartType = visualization.type;
                                }

                                if (this.cmsResponse.minLimitYAxis) {
                                    visualization["minLimitYAxis"] = this.cmsResponse.minLimitYAxis;
                                    delete this.cmsResponse.minLimitYAxis;
                                }
                                switch (chartType) {

                                    case "line-chart": {


                                        if (this.cmsResponse.enableCompare) {
                                            let compareMeta;
                                            let compareData;
                                            let compareCode = [];
                                            this.cmsResponse["benchmarks"] = [];
                                            try {
                                                compareMeta = JSON.parse(this.cmsResponse.compare_data).compareMeta;
                                                compareData = await getCompareSeries(compareMeta);
                                                compareMeta.seriesMeta.forEach(element => {
                                                    if (element.iconId.length > 0) {
                                                        compareCode.push(element.iconId);
                                                    }
                                                });
                                                if (compareCode.length > 0) {
                                                    let compareCodes = compareCode.map(i => i).join(',');
                                                    const cmsCompareUrl = `${process.env.CMS_BASEPATH}${this.lang}${constants.cmsUrl.CMS_COUNTRY_LIST}${compareCodes}`
                                                    const iconData = await getMetaFromCMS(req,cmsLoginUrl, cmsCompareUrl, req.user.groups);
                                                    compareMeta.seriesMeta.forEach(obj => {
                                                        iconData.forEach(element => {
                                                            if (obj.iconId.toLowerCase() === element.country_id.toLowerCase()) {
                                                                this.cmsResponse.benchmarks.push({
                                                                    "country_id": obj.id.toUpperCase(),
                                                                    "country_name": obj.label,
                                                                    "country_flag": element.country_flag,
                                                                    "description": element.description,
                                                                    "attachment": element.attachment
                                                                })
                                                            }
                                                        })
                                                    })
                                                    visualization.seriesMeta[0]["isDefault"] = true;
                                                } else {
                                                    compareMeta.seriesMeta.forEach(series => {
                                                        this.cmsResponse.benchmarks.push({
                                                            "country_id": series.id.toUpperCase(),
                                                            "country_name": series.label,
                                                            "country_flag": '',
                                                            "description": '',
                                                            "attachment": ''
                                                        })
                                                    })
                                                }
                                                visualization.seriesMeta = visualization.seriesMeta.concat(compareMeta.seriesMeta);
                                                if (compareData.length > 0) {
                                                    results = results.concat(compareData);
                                                }
                                            } catch (err) {
                                                this.log.error(`Error preparing compare data in statistics-insights.controller ${err}`);
                                            }
                                        }


                                        let data;
                                        try {
                                            data = await processLineChartData(results, visualization, this.cmsResponse.type, this.cmsResponse.maxPointLimit);
                                        } catch (err) {
                                            this.log.error(`Error executing processLineChartData from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        if (this.cmsResponse.indicatorValues && this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                            try {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, visualization, results);
                                            } catch (err) {
                                                this.log.error(`Error executing getValuesData from statistics-insights.controller line-chart${err}`);
                                                reject(err);
                                            }
                                        }
                                        
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "stacked-vertical-bar-chart": {
                                        let data;
                                        try {
                                            data = await getStackedVerticalBarData(results, visualization, this.cmsResponse.maxPointLimit);
                                        } catch (err) {
                                            this.log.error(`Error executing getStackedVerticalBarData from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                            try {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, results, visualization, results);
                                            } catch (err) {
                                                this.log.error(`Error executing getValuesData from statistics-insights.controller stacked-vertical-bar-chart ${err}`);
                                                reject(err);
                                            }
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        this.log.debug(`Processed data for chartId  successfully`);
                                        counter.push(1);
                                        break;
                                    }
                                    case "single-horizontal-bar-chart": {
                                        let data;
                                        try {
                                            data = await getSingleHorizontalBar(results, visualization, this.cmsResponse.type);
                                        } catch (err) {
                                            this.log.error(`Error executing getSingleHorizontalBar from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                            try {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, results, visualization);
                                            } catch (err) {
                                                this.log.error(`Error executing getValuesData from statistics-insights.controller single-horizontal-bar-chart ${err}`);
                                                reject(err);
                                            }
                                        }
                                        if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
                                            await getColors(data);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        this.log.debug(`Processed data for chartId successfully`);
                                        counter.push(1);
                                        break;
                                    }
                                    case "sunburst-with-change-chart": {
                                        let data;
                                        try {
                                            data = await getSunBurstSeries(visualization, results);
                                        } catch (err) {
                                            this.log.error(`Error executing getSunBurstSeries from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                            try {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, visualization);
                                            } catch (err) {
                                                this.log.error(`Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`);
                                                reject(err);
                                            }
                                        }
                                        if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
                                            await getColors(data);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "single-circular-bar-chart": {
                                        let data;
                                        try {
                                            data = await getSingleCircularBarSeries(visualization, results);
                                        } catch (err) {
                                            this.log.error(`Error executing getSingleCircularBarSeries from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                            try {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, visualization);
                                            } catch (err) {
                                                this.log.error(`Error executing getValuesData from statistics-insights.controller single-circular-bar-chart ${err}`);
                                                reject(err);
                                            }
                                        }
                                        if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
                                            await getColors(data);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "single-pie-chart": {
                                        let data;
                                        try {
                                            data = await getSinglePieSeries(visualization, results, this.cmsResponse.type);
                                            if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                                try {
                                                    await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, visualization);
                                                } catch (err) {
                                                    this.log.error(`Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`);
                                                    reject(err);
                                                }
                                            }
                                        } catch (err) {
                                            this.log.error(`Error executing getSinglePieSeries from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "single-pie-chart-with-filter": {
                                        let data;
                                        try {
                                            data = await getSinglePieSeriesWithFilter(visualization, results, this.cmsResponse.type);
                                            if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                                try {
                                                    await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, visualization);
                                                } catch (err) {
                                                    this.log.error(`Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`);
                                                    reject(err);
                                                }
                                            }
                                        } catch (err) {
                                            this.log.error(`Error executing getSinglePieSeries from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "single-scatterplot-chart": {
                                        let data;
                                        try {
                                            data = await getScatterPlotSeries(visualization, results);
                                            if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                                try {
                                                    await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data, visualization);
                                                } catch (err) {
                                                    this.log.error(`Error executing getValuesData from statistics-insights.controller sunburst-with-change-chart ${err}`);
                                                    reject(err);
                                                }
                                            }
                                        } catch (err) {
                                            this.log.error(`Error executing getScatterPlotSeries from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "table-view": {
                                        let data;
                                        try {
                                            data = await getTableSeries(visualization, results);
                                        } catch (err) {
                                            this.log.error(`Error executing table view from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        counter.push(1);
                                        break;
                                    }
                                    case "pyramid-chart": {
                                        let data;
                                        try {
                                            data = await getPyramidData(results, visualization, this.cmsResponse.type);
                                        } catch (err) {
                                            this.log.error(`Error executing getPyramidData from statistics-insights.controller ${err}`);
                                            reject(err);
                                        }
                                        if (this.cmsResponse.indicatorValues.valuesMeta && this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                            try {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, results, visualization, results);
                                            } catch (err) {
                                                this.log.error(`Error executing getValuesData from statistics-insights.controller pyramid-chart ${err}`);
                                                reject(err);
                                            }
                                        }
                                        this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                        this.log.debug(`Processed data for chartId  successfully`);
                                        counter.push(1);
                                        break;
                                    }
                                    case "tree-map-with-change-chart": {
                                        try {
                                            const data = await getTreeSeries(visualization, results);
                                            if (this.cmsResponse.indicatorValues.valuesMeta.length > 0 && !newValuesMeta) {
                                                await getValuesData(this.cmsResponse.indicatorValues.valuesMeta, data);
                                            }
                                            if (data.colorCodes && data.colorsRange && data.selectRangeBy) {
                                                await getColors(data);
                                            }
                                            this.graphData.indicatorVisualizations.visualizationsMeta.push(data);
                                            counter.push(1);
                                        } catch (err) {
                                            reject(err);
                                        }
                                        break;
                                    }
                                    default: {
                                        this.log.debug(`Chart type not available`);
                                        counter.push(1);
                                        break;
                                    }
                                }

                                try{
                                    if(this.cmsResponse.type == "coi" || this.cmsResponse.type == "scad"){
                                        await this.getModelMetrics()
                                        this.cmsResponse.indicatorVisualizations.visualizationsMeta.forEach(meta=>{
                                            if (meta.type=='line-chart'){
                                                if (this.cmsResponse.type == 'coi'){
                                                    meta.seriesMeta.forEach(series=>{
                                                        if (series.id.includes('forecast')){
                                                            this.cmsResponse.updated = moment(series.xMax, 'YYYY-MM-DD').format('DD/MM/YYYY');
                                                            this.cmsResponse.updatedDateFromDB = true
                                                            return;
                                                        }
                                                    })
                                                }
                                                else{
                                                    this.cmsResponse.updated = moment(meta.seriesMeta[0].xMax, 'YYYY-MM-DD').format('DD/MM/YYYY');
                                                    this.cmsResponse.updatedDateFromDB = true
                                                }
                                            }
                                        })
                                    }
                                }
                                catch(exp){

                                }
                                
                                if (this.cmsResponse.type == "official_statistics" || this.cmsResponse.type == "innovative_statistics")
                                    this.cmsResponse.type = "scad"
                                if (counter.length === visualizationLen) {
                                    setRedis(cmsCacheKey, JSON.stringify(this.graphData),constants.redis.cmsResponseTTL, req.headers);
                                    return resolve(this.graphData);
                                }
                            });
                            const results = await Promise.allSettled(operations);
                            results.forEach((result, index) => {
                                if (result.status === 'fulfilled') {
                                    // Handle success
                                } else {
                                    // Handle error
                                    this.log.error(`Error processing visualization at index ${index}: ${result.reason}`);
                                    return reject(result.reason)
                                }
                            });
                            
                        }
                        catch(err){
                            
                            reject(err)
                        }
                    }
                    else{
                        this.log.info(`<<<<<Exited microservice-statistics-insights.getStatisticsInsightsById Empty indicatorVisualization from CMS`);
                        reject(new IFPError(404,'IndicatorVisualization not found for node'));
                    }

                } else {
                    this.log.info(`<<<<<Exited microservice-statistics-insights.getStatisticsInsightsById Empty response from CMS`);
                    reject([404, 'Data not found in CMS']);
                }
            } catch (err) {
                
                this.log.error(`<<<<< Exited microservice-statistics-insights.getStatisticsInsightsById with error ${err} `)
                reject(err);
            }
        });

    }

    getOfficialStatisticsOverview = (req) => {
        this.log = this.log = new Logger().getInstance();
        
        return new Promise(async (resolve, reject) => {
            try {

                let nodeIds = req.body.ids;
                let censusNodeDuplicationMap = constants.censusNodeDuplicationMap
                let replacedNodes = {}
                nodeIds = nodeIds.map(node=>{
                    if (Object.keys(censusNodeDuplicationMap).includes(String(node))){
                        replacedNodes[Number(censusNodeDuplicationMap[node])] = node
                        node=Number(censusNodeDuplicationMap[node])
                    }
                    return node
                })

                let type = req.body.type
                let overViewCollection;
                let limitIndicators={};
                let response = {}
                const lang = getLangPrefix(req)
                const accessCheckUrl = constructCmsUrl(process.env.CMS_BASEPATH,lang,constants.cmsGroupUrl.CMS_NODE_ACCESS_CHECK_URL);
                const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

                if (type.includes('_screener')){
                    const dataPromises = nodeIds.map(async (node) => {
                        const data = await getIndicatorMetaData(node);
                        if (data.length)
                            return data[0]
                        // else
                        //     throw new Error(`No overview data found for the indicator ${node}`)
                    });
                    
                    const dataCollection = await Promise.all(dataPromises);
                    const groupByView = {}
                    dataCollection.forEach(dNode =>{
                        if (dNode){
                            if(!groupByView.hasOwnProperty(dNode.SOURCE_TABLE))
                                groupByView[dNode.SOURCE_TABLE] = []
                            groupByView[dNode.SOURCE_TABLE].push(dNode.INDICATOR_ID)
                        }
                    })

                    if (Object.keys(groupByView).length < 1){
                        throw new Error(`No overview data found for the payload`)
                    }
                    
                    overViewCollection = await Promise.all(
                        Object.entries(groupByView).map(async ([viewName, nodes]) =>
                          getNewOverviewData(nodes, type, viewName)
                        )
                      );
                      
                    overViewCollection = overViewCollection.flat();
                }
                else{
                    const accessCheckUrlWithNodes = `${accessCheckUrl}?nids=${nodeIds.join(',')}`
                    const accessData = await getMetaFromCMS(req,cmsLoginUrl, accessCheckUrlWithNodes, req.user.groups)
                    const accessNodes = accessData.accessible_nodes
                    if (accessNodes.length < 1){
                        throw new IFPError(404,`No overview data found for the payload`)
                    }
                    overViewCollection = await getNewOverviewData(accessNodes,type)
                    let limitIndicatorsData = await getLimitIndicators()
                    limitIndicators =  limitIndicatorsData.reduce((acc, obj) => {
                        acc[obj.INDICATOR_ID] = JSON.parse(obj.OVERVIEW_JSON);
                        return acc;
                    }, {});
                }
                
                
                overViewCollection.forEach(node => {
                    let  overView = {}
                    if (Object.keys(limitIndicators).includes(node.INDICATOR_ID))
                        overView = limitIndicators[node.INDICATOR_ID]
                    else{
                        let compareFilters = []
                        if (node.YEARLY) 
                            compareFilters.push('Y/Y')

                        if (node.QUARTERLY) 
                            compareFilters.push('Q/Q')

                        if (node.MONTHLY) 
                            compareFilters.push('M/M')

                        if (! compareFilters.length)
                            compareFilters.push('Y/Y')
                            
                        overView = {
                            "id": "whatsnew",
                            "compareFilters": compareFilters,
                            "valueFormat": "number_1.1-1",
                            "templateFormat": "date_y",
                            "baseDate": node.OBS_DT,
                            "value": node.VALUE?node.VALUE:0,
                            "yearlyCompareValue": node.YEARLY_COMPARE_VALUE?node.YEARLY_COMPARE_VALUE:0,
                            "yearlyChangeValue": node.YEARLY_CHANGE_VALUE?node.YEARLY_CHANGE_VALUE:0,
                            "quarterlyCompareValue": node.QUARTERLY_COMPARE_VALUE?node.QUARTERLY_COMPARE_VALUE:0,
                            "quarterlyChangeValue": node.QUARTERLY_CHANGE_VALUE?node.QUARTERLY_CHANGE_VALUE:0,
                            "monthlyCompareValue": node.MONTHLY_COMPARE_VALUE?node.MONTHLY_COMPARE_VALUE:0,
                            "monthlyChangeValue": node.MONTHLY_CHANGE_VALUE?node.MONTHLY_CHANGE_VALUE:0
                        }
                    }
                    if (Object.keys(replacedNodes).includes(node.NODE_ID))
                        node.NODE_ID = replacedNodes[node.NODE_ID]
                    response[node.NODE_ID] = overView
                })
                resolve(response)
                
            } catch (err) {
                
                this.log.error(`<<<<< Exited microservice-statistics-insights.getOfficialStatisticsOverview with error ${err} `)
                reject(err);
            }
        });
    }

    getStatisticsInsightsWithFilter = async (req) => {
        this.log = new Logger().getInstance();
        try {
            if (req.body.meta && req.body.meta.length > 0) {
                let data = await getAllFilterData(req.body.meta);
                return data;
            }
        } catch (err) {
            if (err?.type == "UNKNOWN_IDENTIFIER") {
                throw new IFPError(400, 'Invalid column in filter');
            }
            this.log.error(`<<<<< Exited microservice-statistics-insights.getStatisticsInsightsWithFilter with error ${err} `)
            throw err;
        }
    }

    visitOfficialStatistics = async (req) => {
        this.log = new Logger().getInstance();
        return new Promise(async (resolve, reject) => {
            try {
                if (req.params.id){
                    setPopularStatData(req.params.id)
                    resolve({"message":"Visit tracked successfully","status":"success"})
                }
                else
                    reject({"message":"Invalid ID","status":"failed"})
            } catch (err) {
                
                this.log.error(`<<<<< Exited microservice-statistics-insights.visitOfficialStatistics with error ${err} `)
                reject(err);
            }
        })
    }
    getPopularOfficialStatistics = async (req) => {
        this.log = new Logger().getInstance();
        return new Promise(async (resolve, reject) => {
            try {
                const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
                const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
                const cmsOfficialStatisticsList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_OFFICIAL_STATISTICS_URL}`;
                let data = await getMetaFromCMS(req,cmsLoginUrl, cmsOfficialStatisticsList, req.user.groups)
                let nodeMap = {}
                let result = []
                if (data.length){
                    data.forEach(d=>{
                        nodeMap[d.id] = d
                    })
                    let popData = await getPopStatsData(Object.keys(nodeMap))
                    popData.forEach(p=>{
                        result.push(nodeMap[p.NODE_ID])
                    })
                }
                resolve(result)
            } catch (err) {
                
                this.log.error(`<<<<< Exited microservice-statistics-insights.getPopularOfficialStatistics with error ${err} `)
                reject(err);
            }
        })
    }

    getForecasts = async (req) => {
        this.log = new Logger().getInstance();
        return new Promise(async (resolve, reject) => {
            try {
                const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
                const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
                const cmsOfficialStatisticsList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_FORECASTS_URL}`;
                let data = await getMetaFromCMS(req,cmsLoginUrl, cmsOfficialStatisticsList, req.user.groups)
                
                let results = []
                data.forEach(d=>{
                    if (d.domain){
                        if (!results[d.domain])
                            results[d.domain] = []
                        results[d.domain].push(d)
                    }
                })

                results = Object.entries(results).map(([domain,nodes])=>{
                    const domainObj = {
                        domain:domain,
                        nodes:nodes
                    }
                    return domainObj
                })
                resolve(results)
            } catch (err) {
                
                this.log.error(`<<<<< Exited microservice-statistics-insights.getForecasts with error ${err} `)
                reject(err);
            }
        })
    }

    getModelMetrics = async (req) => {
        if (!this.cmsResponse.modelMetrics) { return }
        try {
            this.cmsResponse.modelMetrics.metricsData = await inflationController.getMetricsModel();
            this.cmsResponse.modelMetrics.metricsDataTest = await inflationController.getMetricsTrainTest();
        } catch(err) {
            
            this.log.error(`<<<<< Exited microservice-statistics-insights.getModelMetrics with error ${err} `)
        }
    }
}


module.exports = StatisticsInsightsData;
