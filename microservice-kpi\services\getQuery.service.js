const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

const KPI_LOGS_TABLE = "IFP_KPI_LOGS";
const USER_INFO_TABLE = "VW_IFP_USER_INFO";

async function getOverallDomainsQuery(filter) {
  try {
    let binds = {};
    let query = `
            SELECT
                nodeId as nid, 
                COUNT(*) AS count
            FROM
                ${KPI_LOGS_TABLE}
            JOIN
                ${USER_INFO_TABLE}
            ON
                ${KPI_LOGS_TABLE}.userEmail = ${USER_INFO_TABLE}.EMAIL
			WHERE
				nodeId > 0 
        `;

    if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
      let filters = [];
      query += " AND ";

      // Append filter if provided
      if (filter.startDate) {
        filters.push(`endTime >= {startDate: String}`);
        binds.startDate = `${filter.startDate} 00:00:00`;
      }
      if (filter.endDate) {
        filters.push(`endTime <= {endDate: String}`);
        binds.endDate = `${filter.endDate} 23:59:59`;
      }
      if (filter.entityNames?.length > 0) {
        filters.push(
          `${USER_INFO_TABLE}.ENTITY IN {entityName: Array(String)}`
        );
        binds.entityName = filter.entityNames;
      }

      filters.push(
        filter.mobile
          ? `startsWith(sessionType, 'mobile:')`
          : `NOT startsWith(sessionType, 'mobile:')`
      );

      query += filters.join(" AND ");
    }

    query += `
            GROUP BY 
                nodeId
            ORDER BY 
                count DESC;
        `;

    return { query: query, query_params: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-kpi.services.getQuery.service.getOverallDomainsQuery with error ${err} `
    );
    throw err;
  }
}

async function getEntityListQuery() {
  try {
    const query = `SELECT
	DISTINCT ENTITY_ID AS "entity_id",
	iel.NAME AS "name",
  iel.DOMAIN AS "domain",
	UPPER(SUBSTR(iel.DOMAIN, 1, INSTR(DOMAIN, '.') - 1)) AS "entity_domain"
FROM
	IFP_FLOW_USERS_V2 ifuv
LEFT JOIN IFP_ENTITY_LOOKUP iel ON
	iel.ID = ifuv.ENTITY_ID`;
    return { query: query };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-kpi.services.getQuery.service.getEntityListQuery with error ${err} `
    );
    throw err;
  }
}

async function getSelfServiceToolDataQuery(filter) {
  try {
    let binds = {};
    let query = `
            SELECT
                sessionType,
                ROUND(AVG(TIMESTAMPDIFF(SECOND, startTime, endTime)) / 3600, 2) AS avgUsageHours,
                COUNT(*) AS sessionCount
            FROM
                ${KPI_LOGS_TABLE}
            JOIN
                ${USER_INFO_TABLE}
            ON
                ${KPI_LOGS_TABLE}.userEmail = ${USER_INFO_TABLE}.EMAIL
			WHERE
				sessionType LIKE 'self_service__%' 
        `;

    if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
      let filters = [];

      // Append filter if provided
      if (filter.startDate) {
        filters.push(`endTime >= {startDate: String}`);
        binds.startDate = `${filter.startDate} 00:00:00`;
      }
      if (filter.endDate) {
        filters.push(`endTime <= {endDate: String}`);
        binds.endDate = `${filter.endDate} 23:59:59`;
      }
      if (filter.entityNames?.length > 0) {
        filters.push(
          `${USER_INFO_TABLE}.ENTITY IN {entityName: Array(String)}`
        );
        binds.entityName = filter.entityNames;
      }

      filters.push(
        filter.mobile
          ? `startsWith(sessionType, 'mobile:')`
          : `NOT startsWith(sessionType, 'mobile:')`
      );
      // Add additional filters to the WHERE clause
      if (filters.length > 0) {
        query += " AND " + filters.join(" AND ");
      }
    }

    query += `
            GROUP BY 
                sessionType
            ORDER BY 
                sessionCount DESC;
        `;
    return { query: query, query_params: binds };
  } catch (err) {
    log.error(
      `<<<<<< Exited microservice-kpi.services.getQuery.service.getSelfServiceToolDataQuery with error ${err} `
    );
    throw err;
  }
}

async function getselfServiceToolUserDataQuery(filter, limit, offset, isPaginated) {
  let binds = {};
  let query = `
		WITH userDurations AS (
			SELECT
				userEmail,
				SUM(dateDiff('second', startTime, endTime)) AS totalSec
			FROM
				${KPI_LOGS_TABLE}
			WHERE
				sessionType = {sessionType: String}
				${filter.startDate ? `AND endTime >= toDateTime({startDate: String})` : ""}
                ${
                  filter.endDate
                    ? `AND endTime <= toDateTime({endDate: String})`
                    : ""
                }
				${
          filter.mobile
            ? `AND startsWith(sessionType, 'mobile:')`
            : `AND NOT startsWith(sessionType, 'mobile:')`
        }
			GROUP BY
				userEmail
		),
		totalSessionDuration AS (
			SELECT SUM(totalSec) AS value
			FROM userDurations
		)
        SELECT DISTINCT 
            userEmail AS email,
			CONCAT(
				LPAD(toString(floor(totalSec / 3600)), 2, '0'), ':',
				LPAD(toString(floor((totalSec % 3600) / 60)), 2, '0'), ':',
				LPAD(toString(totalSec % 60), 2, '0')
			) AS duration,
			ROUND((totalSec / (SELECT value FROM totalSessionDuration)) * 100, 2) AS percentageShare,
			COUNT(DISTINCT email) OVER() AS total_count
        FROM
            userDurations

		${
      filter.entityNames?.length > 0
        ? `INNER JOIN ${USER_INFO_TABLE} ON ${USER_INFO_TABLE}.EMAIL = userEmail AND ${USER_INFO_TABLE}.ENTITY IN {entityName: Array(String)}`
        : ""
    }
        ORDER BY 
			percentageShare DESC,
			duration DESC
    `;

    if (isPaginated) {
      query += `
        LIMIT {limit: Int}
        OFFSET {offset: Int};
      `;
    }

  binds.sessionType = filter.sessionType;

  if (filter.startDate) binds.startDate = `${filter.startDate} 00:00:00`;
  if (filter.endDate) binds.endDate = `${filter.endDate} 23:59:59`;
  if (filter.entityNames?.length > 0) binds.entityName = filter.entityNames;
  binds.limit = limit;
  binds.offset = offset;

  return { query, query_params: binds };
}

async function getGeoSpatialDataQuery(filter, limit = 10, offset = 0) {
  const sessionType = "geo_spatial";
  let binds = {};
  // Set `gioField` based on the category, either "data1" or "data2"
  const gioField = filter.category === "domain" ? "ikl.data1" : "ikl.data2";

  let query = `	
			SELECT
    ${gioField} AS ${filter.category},
    COUNT(DISTINCT ikl.userEmail) AS user_count,
    CONCAT(
        LPAD(toString(floor(AVG(dateDiff('second', ikl.startTime, ikl.endTime)) / 3600)), 2, '0'),
        ':',
        LPAD(toString(floor((AVG(dateDiff('second', ikl.startTime, ikl.endTime)) % 3600) / 60)), 2, '0'),
        ':',
        LPAD(toString(floor(AVG(dateDiff('second', ikl.startTime, ikl.endTime)) % 60)), 2, '0')
    ) AS duration,
    COUNT(${filter.category}) OVER() AS total_count
FROM
    ${KPI_LOGS_TABLE} ikl
JOIN
    VW_IFP_USER_INFO ui ON ikl.userEmail = ui.EMAIL
WHERE
    ikl.sessionType = {sessionType: String}	`;

  binds.sessionType = sessionType;

  let filters = [];
  if (filter.startDate) {
    filters.push(`ikl.endTime >= toDateTime({startDate: String})`);
    binds.startDate = `${filter.startDate} 00:00:00`;
  }
  if (filter.endDate) {
    filters.push(`ikl.endTime <= toDateTime({endDate: String})`);
    binds.endDate = `${filter.endDate} 23:59:59`;
  }
  if (filter?.entityNames?.length > 0) {
    filters.push(
      `upper(${USER_INFO_TABLE}.ENTITY) IN {entityName: Array(String)}`
    );
    binds.entityName = filter.entityNames;
  }
  if (filter.data1) {
    filters.push(`ikl.data1 = {data1: String}`);
    binds.data1 = filter.data1;
  }
  if (filter.data2) {
    filters.push(`ikl.data2 = {data2: String}`);
    binds.data2 = filter.data2;
  }

  filters.push(
    filter.mobile
      ? `startsWith(sessionType, 'mobile:')`
      : `NOT startsWith(sessionType, 'mobile:')`
  );

  if (filters.length) query += ` AND ` + filters.join(" AND ");

  query += `
        GROUP BY ${gioField}
        ORDER BY user_count, duration DESC
		LIMIT {limit: Int} OFFSET {offset: Int};
    `;

  binds.limit = limit;
  binds.offset = offset;

  return { query, query_params: binds };
}

async function getGeoSpatialUserDataQuery(filter, limit, offset) {
  const sessionType = "geo_spatial";
  let binds = {};

  let query = `
		SELECT
			DISTINCT userEmail AS email,
			COUNT(DISTINCT email) OVER() AS total_count
		FROM
			${KPI_LOGS_TABLE} ikl
		JOIN ${USER_INFO_TABLE} ui ON
			ikl.userEmail = ui.EMAIL
		WHERE
			sessionType = {sessionType: String}
	`;

  binds.sessionType = sessionType;

  let filters = [];
  if (filter.startDate) {
    filters.push(`endTime >= toDateTime({startDate: String})`);
    binds.startDate = `${filter.startDate} 00:00:00`;
  }
  if (filter.endDate) {
    filters.push(`endTime <= toDateTime({endDate: String})`);
    binds.endDate = `${filter.endDate} 23:59:59`;
  }
  if (filter.entityNames?.length) {
    filters.push(`ui.ENTITY IN {entityName: Array(String)}`);
    binds.entityName = filter.entityNames;
  }
  if (filter.domain) {
    filters.push(`data1 = {data1: String}`);
    binds.data1 = filter.domain;
  }
  if (filter.district) {
    filters.push(`data2 = {data2: String}`);
    binds.data2 = filter.district;
  }
  if (filters.length) query += ` AND ` + filters.join(" AND ");

  query += ` ORDER BY userEmail LIMIT {limit: UInt64} OFFSET {offset: UInt64};`;
  binds.limit = limit;
  binds.offset = offset;

  return { query, query_params: binds };
}

// new onedashboard functions
function generateEntityList(entityId) {
  let query = `SELECT
	DISTINCT ENTITY_ID AS "entity_id",
	iel.NAME AS "entity"
FROM
	IFP_FLOW_USERS_V2 ifuv
LEFT JOIN IFP_ENTITY_LOOKUP iel ON
	iel.ID = ifuv.ENTITY_ID`;
  let binds = {};

  // Check if entityId is provided and valid
  if (entityId) {
    const entitySplit = entityId.split(",");
    const entities = entitySplit
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  WHERE ENTITY_ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  return { query, binds };
}

function generateSuperUsersQuery(entityId, status) {
  let binds = {};
  let query = `SELECT
	  NAME AS "name",
	  EMAIL AS "email",
	  DESIGNATION AS "designation",
	  ROLE AS "user_type"
	FROM
	  IFP_FLOW_USERS_V2 ifuv`;

  if (status == "superusers") {
    query += ` WHERE
	  ROLE <> 'USER'`;
  }

  if (entityId) {
    query += ` ${status == "superusers" ? ' AND' : ' WHERE'} ENTITY_ID = :entityId`;
    binds.entityId = entityId;
  }

  query+= ` AND ACTIVATION_FLAG='ACTIVE' AND STATUS !='DELETED'`

  return { query, binds };
}

function generateClassificationUserQuery(entityId) {
  let binds = {};
  let query = `SELECT
	ENTITY_ID ,
	MAX(CLASSIFICATION_ID) AS CLASSIFICATION_ID
FROM
	(
	SELECT
		ENTITY_ID,
		CLASSIFICATION,
		CASE
			WHEN UPPER(CLASSIFICATION) = 'OPEN' THEN 1
			WHEN UPPER(CLASSIFICATION) = 'CONFIDENTIAL' THEN 2
			WHEN UPPER(CLASSIFICATION) = 'SENSITIVE' THEN 3
		END AS CLASSIFICATION_ID
	FROM
		IFP_DISS_ACCESS_POLICY )
`;

  if (entityId) {
    const entitySplit = entityId.split(",");
    const entities = entitySplit
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  WHERE ENTITY_ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  query += ` GROUP BY
	ENTITY_ID
ORDER BY
	ENTITY_ID`;

  return { query, binds };
}

function generateMostNotificationQuery(filter) {
  let binds = {};
  let query = `SELECT 
	  ikl.NODE_ID, 
	  ikl.CONTENT_TYPE, 
	  COUNT(*) AS occurrence_count 
  FROM IFP_USER_NOTIFICATION_MAP ikl
  LEFT JOIN IFP_USER_INFO viui 
	  ON ikl.USER_EMAIL = viui.EMAIL
  WHERE ikl.CONTENT_TYPE != 'Spatial-Analytics'`;

  if (filter.entityNames?.length) {
    const entities = filter.entityNames
      .map((_, index) => `:entityId${index}`)
      .join(", ");
    query += `  AND viui.ENTITY IN (${entities})`;
    filter.entityNames.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  if (filter.startDate && filter.endDate) {
    query += ` AND ikl.INSERT_DT BETWEEN TO_DATE(:startDate, 'YYYY-MM-DD') AND TO_DATE(:endDate, 'YYYY-MM-DD')`;
    binds.startDate = filter.startDate;
    binds.endDate = filter.endDate;
  }

  query += `GROUP BY ikl.NODE_ID, ikl.CONTENT_TYPE
  ORDER BY occurrence_count DESC
  FETCH FIRST 10 ROWS ONLY`;

  return { query, binds };
}

function generateExperimentalIndicatorName(id) {
  let binds = {};
  let query = `SELECT
	INDICATOR_NAME_EN, INDICATOR_ID
FROM
	VW_INDICATOR_MAP vim
WHERE
	INDICATOR_ID IN {ids: Array(String)}`;
  binds.ids = id;
  return { query, binds };
}

module.exports = {
  getOverallDomainsQuery,
  getEntityListQuery,
  getSelfServiceToolDataQuery,
  getselfServiceToolUserDataQuery,
  getGeoSpatialDataQuery,
  getGeoSpatialUserDataQuery,
  generateEntityList,
  generateSuperUsersQuery,
  generateClassificationUserQuery,
  generateMostNotificationQuery,
  generateExperimentalIndicatorName,
};
