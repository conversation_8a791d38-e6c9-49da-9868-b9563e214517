const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

function filterQuery(filterByObj, query) {
    try {
        if (Object.keys(filterByObj).length > 0) {
            Object.entries(filterByObj).forEach(([column, value]) => {
                if (Array.isArray(value)) {
                    let values = value.map(i => `'${i}'`).join(',');
                    if (query.includes('WHERE')) {
                        query = `${query} AND UPPER ( REPLACE(${column},' ','')) IN (${values})`;
                    } else {
                        query = `${query} WHERE UPPER ( REPLACE(${column},' ','')) IN (${values})`;
                    }
                } else {
                    query = `${query} AND UPPER ( REPLACE(${column},' ',''))= '${value.toUpperCase().replace(/ /g, "")}'`
                }
            });
            return query;
        } else {
            return query;
        }
    } catch (err) {
        log.error(`<<<<<< Exited microservice-official-insights.services.getQuery.service.filterQuery with error ${err} `);
        throw err
    }
}

module.exports = {
    filterQuery
}