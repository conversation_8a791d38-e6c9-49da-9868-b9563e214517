const { genarateAccessQuery } = require("./getQuery.service");
const Logger = require("scad-library").logger;
const db = require("../../services/database.service");
const log = new Logger().getInstance();

async function getAccessQuery() {
  try {
    const { query, binds } = genarateAccessQuery();
    const data = await getData(query, binds);
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exited microservice.accesscheck.executeQuery.service.getAccessQuery with error ${err}`
    );
    throw err;
  }
}

async function getData(query, binds = {}) {
  try {
    log.debug(
      `>>>>> microservice.accesscheck.service.executeQuery.service.getData`
    );
    let data = await db.simpleExecute(query, binds);
    log.debug(
      `<<<<< Exit microservice.accesscheck.service.executeQuery.service.getData successfully`
    );
    return data;
  } catch (err) {
    log.error(
      `<<<<< Exit microservice.accesscheck.service.executeQuery.service.getData with error ${err}`
    );
    log.error(`Error Executing Query:- ${query}`);
    throw err;
  }
}

module.exports = {
  getAccessQuery,
};
