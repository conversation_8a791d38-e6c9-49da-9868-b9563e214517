const Logger = require('scad-library').logger;
const moment = require('moment');
require('dotenv').config();

const { getIndicatorData, createCompareAppData, listMyAppsData, getCompareData, addAppToMyAppsData, getCompareNodesInfo } = require('./services/executeQuery');

const { processLineChartData } = require('./services/chart-services/line-chart');
const uuid = require('uuid');

const log = new Logger().getInstance();

const constants = require('../config/constants.json');
const indicatorTemplate = require('./services/chart-services/template.json')
const indicatorTemplateAr = require("./services/chart-services/template_ar.json");

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { getMetaFromCMS } = require('../services/common-service');
const { getDynamicJsonById, getViewNameById } = require('../services/executeQuery.service');
const { IFPError } = require('../utils/error');

async function compareIndicators(req) {
    log.debug(`>>>>>Entered microservice.indicator-compare.controller.getInnovativeInsightsById`);
    try {

        const nodes = req.body.nodes
        const lang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
        
        let response = lang === "AR"
            ? JSON.parse(JSON.stringify(indicatorTemplateAr))
            : JSON.parse(JSON.stringify(indicatorTemplate));

        response.id = nodes.map(node => node.indicatorId).join('_')

        let compareCacheKey = `compareIndicators_${response.indicatorId}_${crypto.createHash('md5').update(JSON.stringify(response)).digest("hex")}`
        const compareCacheResults = await getRedis(compareCacheKey, req.headers);
        if (compareCacheResults) {
            log.info(`<<<<<Cache found for microservice.innovative-insights.controller.getInnovativeInsightsById`);
            return JSON.parse(compareCacheResults);
        }

        //Get nodes information from Indicator Map Table
        let nodesInfo = await getCompareNodesInfo(nodes.map(node => node.indicatorId))
        if (nodesInfo.length<2)
            throw new IFPError(400,'Invalid indicator Ids')

        nodes.forEach(node => {
            let nodeData  = nodesInfo.find(nodeInfo=>nodeInfo.INDICATOR_ID==node.indicatorId)
            node.viewName =nodeData.SOURCE_TABLE
            node.title = nodeData[`INDICATOR_NAME_${lang}`]
            node.unit = lang == 'EN'?nodeData['UNIT']:nodeData[`UNIT_${lang}`]
        });

        const dataPromises = nodes.map(async (node) => {
            const data = await getIndicatorData(node.indicatorId, node.viewName,lang);
            return {
                id: node.indicatorId,
                title: node.title,
                data: data,
            };
        });

        const dataCollection = await Promise.all(dataPromises);

        let visualization = {
            "id": `compare-chart`,
            "type": "line-chart",
            "seriesTitles": {},
            "seriesMeta": [

            ],
            "tooltipTitleFormat": "date_MMM y",
            "tooltipValueFormat": "d3-number",
            "xAxisFormat": "date_y",
            "yAxisFormat": "d3-number",
            "xAxisLabel": "",
            "yAxisLabel": ""
        }


        let genericSeries = {
            "type": "solid",
            "xAccessor": {
                "type": "date",
                "path": "OBS_DT",
                "specifier": "%Y-%m-%d"
            },
            "yAccessor": {
                "type": "value",
                "path": "VALUE"
            },
            "data": [],
            "id": '',
            "dbIndicatorId": '',
            "label": null,
            "color": "#000000"
        }

        for (node of nodes) {
            let series = JSON.parse(JSON.stringify(genericSeries))
            series.id = `series-${node.indicatorId}`
            nodeData = dataCollection.find(collection => collection.id == node.indicatorId)
            series.label = node.unit
            visualization.seriesTitles[series.id] = nodeData.title
            series = processLineChartData(nodeData.data, series);
            visualization.seriesMeta.push(series)
        }

        response.indicatorVisualizations.visualizationsMeta = [visualization];
        response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id

        setRedis(compareCacheKey, JSON.stringify(response), constants.redis.dataResponseTTL, req.headers);
        return response;

    } catch (err) {
        log.error(`<<<<< Exited microservice.innovative-insights.controller.getInnovativeInsightsById with error ${err} `)
        throw err;
    }

}

async function addToMyApps(req) {
    log.debug(`>>>>>Entered microservice.indicator-compare.controller.getInnovativeInsightsById`);
    try {

        const nodes = req.body.nodes

        //Get nodes information from Indicator Map Table
        let nodesInfo = await getCompareNodesInfo(nodes.map(node => node.indicatorId))
        if (nodesInfo.length<2)
            throw new IFPError(400,`Invalid indicator Ids`)

        let compareId = String(uuid.v4());
        let appTitle = req.body.title.replace(/'/g, "''")
        let email = req.user.preferred_username

        let myAppsData = nodes.map(node => {return {
            id: compareId, 
            title: appTitle,
            indicator: node.indicatorId,
            email: email
        }})

        await createCompareAppData(myAppsData)
        await addAppToMyAppsData(myAppsData[0],email)
        return {"message":"Compare app added to my apps","status":true,"compareId":compareId}
    } catch (err) {
        log.error(`<<<<< Exited microservice.innovative-insights.controller.getInnovativeInsightsById with error ${err} `)
        throw err;
    }
}

async function listMyApps(req) {
    log.debug(`>>>>>Entered microservice.indicator-compare.controller.listMyApps`);
    return new Promise(async (resolve, reject) => {

        try {
            let email = req.user.preferred_username
            let data = await listMyAppsData(email)
            resolve(data)
        } catch (err) {
            
            log.error(`<<<<< Exited microservice.innovative-insights.controller.listMyApps with error ${err} `)
            reject(err);
        }
    });
}

async function getCompareApps(req) {
    log.debug(`>>>>>Entered microservice.indicator-compare.controller.getCompareApps`);
    return new Promise(async (resolve, reject) => {

        try {
            let email = req.user.preferred_username
            let id = req.params.id
            const lang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
            const cmsLang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
            const cmsNodeClassificationUrl = `${process.env.CMS_BASEPATH}${cmsLang}${constants.cmsGroupUrl.CMS_NODE_CLASSIFICATION_LIST}`;
            const cmsWhatsNewOptimizedList = `${process.env.CMS_BASEPATH}${cmsLang}${constants.cmsGroupUrl.CMS_WHATS_NEW_OPT_URL}`;
            const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;

            const [compareData, nodeMapCMS,whatsNewData] = await Promise.all([
                getCompareData(id,email),
                getMetaFromCMS(req,cmsLoginUrl, cmsNodeClassificationUrl, req.user.groups),
                getMetaFromCMS(req,cmsLoginUrl, cmsWhatsNewOptimizedList, req.user.groups)
            ])

            if (compareData.length<1)
                throw new IFPError(400,`Please provide a valid compare id`)

            let response = JSON.parse(JSON.stringify(indicatorTemplate))
            const nodes = compareData.map(node => {
                return {
                    indicatorId: node.NODE
                }
            })
            
            response.id = nodes.map(node => node.indicatorId).join('_')

            let compareCacheKey = `compareIndicators_${response.indicatorId}_${crypto.createHash('md5').update(JSON.stringify(response)).digest("hex")}`
            const compareCacheResults = await getRedis(compareCacheKey, req.headers);
            if (compareCacheResults) {
                log.info(`<<<<<Cache found for microservice.innovative-insights.controller.getInnovativeInsightsById`);
                return resolve(JSON.parse(compareCacheResults));
            }

            let nodesInfo = await getCompareNodesInfo(nodes.map(node => node.indicatorId))

            //Checks for access begins
            
            officialNodes = []
            officialScreenerNodes = []
            experimentalScreenerNodes = []
            nodesInfo.forEach(node=>{
                //Checks for Official Indicator
                if (node.INDICATOR_TYPE == 'OFFICIAL' && node.SCREENER == 0)
                    officialNodes.push({id:node.INDICATOR_ID,source:node.SOURCE_TABLE})
                //Checks for Official Screener Indicator
                else if (node.INDICATOR_TYPE == 'OFFICIAL' && node.SCREENER == 1)
                    officialScreenerNodes.push({id:node.INDICATOR_ID,source:node.SOURCE_TABLE})
                //Checks for Experimental Indicator
                else if (node.INDICATOR_TYPE == 'EXPERIMENTAL' && node.SCREENER == 1)
                    experimentalScreenerNodes.push({id:node.INDICATOR_ID,source:node.SOURCE_TABLE})   
                else{
                    throw new IFPError(400,`Invalid Type for indicator`)
                }
            })

            if (officialNodes.length){
                officialNodes = officialNodes.map(o=>o.id)
                let nodeIds = await getDynamicJsonById(officialNodes)
                if (!nodeIds.length)
                    throw new IFPError(403,`You don't have access to this indicator`)

                nodeIds = nodeIds.map(n=>n.NODE_ID)
                let access = nodeIds.every(node=>Object.keys(nodeMapCMS).includes(`${node}`))
                if (!access)
                    throw new IFPError(403,`You don't have access to this indicator`);
            }

            if (experimentalScreenerNodes.length){
                let experimentalLayout = whatsNewData.find(wn=>wn.key=='experimental_statistics')
                let expSources = []
                expSources = await Promise.all(experimentalLayout.indicatorList.map(async exp=>{
                    let viewName = exp.subDomainDetails.screener_configuration.screenerView
                    if (Number(exp.subDomainDetails.screener_configuration.screenerView))
                        viewName = await getViewNameById(exp.subDomainDetails.screener_configuration.screenerView)
                    return viewName
                }))

                let access = experimentalScreenerNodes.every(node=>expSources.includes(node.source))
                if (!access)
                    throw new IFPError(403,`You don't have access to this indicator`)
            }
            
            //Checks for access complete
            nodes.forEach(node => {
                let nodeData  = nodesInfo.find(nodeInfo=>nodeInfo.INDICATOR_ID==node.indicatorId)
                node.viewName =nodeData.SOURCE_TABLE
                node.unit = lang == 'EN'?nodeData['UNIT']:nodeData[`UNIT_${lang}`]
            });

            const dataPromises = nodes.map(async (node) => {
                const data = await getIndicatorData(node.indicatorId, node.viewName,lang);
                return {
                    id: node.indicatorId,
                    title: data[0].INDICATOR_NAME,
                    data: data,
                };
            });

            const dataCollection = await Promise.all(dataPromises);

            let visualization = {
                "id": `compare-chart`,
                "type": "line-chart",
                "seriesTitles": {},
                "seriesMeta": [

                ],
                "tooltipTitleFormat": "date_MMM y",
                "tooltipValueFormat": "d3-number",
                "xAxisFormat": "date_y",
                "yAxisFormat": "d3-number",
                "xAxisLabel": "",
                "yAxisLabel": ""
            }


            let genericSeries = {
                "type": "solid",
                "xAccessor": {
                    "type": "date",
                    "path": "OBS_DT",
                    "specifier": "%Y-%m-%d"
                },
                "yAccessor": {
                    "type": "value",
                    "path": "VALUE"
                },
                "data": [],
                "id": '',
                "dbIndicatorId": '',
                "label": null,
                "color": "#000000"
            }
            response.nodes = nodes.map(node => node.indicatorId)
            for (node of nodes) {
                let series = JSON.parse(JSON.stringify(genericSeries))
                series.id = `series-${node.indicatorId}`
                nodeData = dataCollection.find(collection => collection.id == node.indicatorId)
                series.label = node.unit
                visualization.seriesTitles[series.id] = nodeData.title
                series = processLineChartData(nodeData.data, series);
                visualization.seriesMeta.push(series)
            }

            response.indicatorVisualizations.visualizationsMeta = [visualization];
            response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id

            setRedis(compareCacheKey, JSON.stringify(response), constants.redis.dataResponseTTL, req.headers);
            resolve(response);

        } catch (err) {
            
            log.error(`<<<<< Exited microservice.innovative-insights.controller.getCompareApps with error ${err} `)
            reject(err);
        }
    });

}

module.exports = {
    compareIndicators,
    addToMyApps,
    listMyApps,
    getCompareApps
};
