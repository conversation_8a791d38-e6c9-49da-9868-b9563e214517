const moment = require('moment');
const { IFPError } = require('../../utils/error');

const validateTCAccept = (req,res,next)=>{
    if (req.user.preferred_username != req.body.userId)
        throw new IFPError(400,'Invalid user id')

    const date = moment(req.body.tcAcceptDate, "DD-MMM-YYYY", true);
    if (! date.isValid())
        throw new IFPError(400,'Invalid date: Use format DD-MMM-YYYY')

    next()
}

module.exports = {
    validateTCAccept
}