const Logger = require("scad-library").logger;
const log = new Logger().getInstance();
const mysql = require("mysql2/promise");

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;

let client;
function initialize() {
  try {
    client = mysql.createPool({
      host: process.env.USERONBOARDING_MYSQL_HOST,
      port: process.env.USERONBOARDING_MYSQL_PORT,
      database: process.env.USERONBOARDING_MYSQL_DATABASE,
      user: process.env.USERONBOARDING_MYSQL_USER,
      password: process.env.USERONBOARDING_MYSQL_PASSWORD,
    });
    log.info(`MySQL DB Pool created successfully`);
  } catch (err) {
    log.error(`<PERSON>rror creating MySQL DB Pool ${err}`);
    log.info(`Error creating MySQL DB Pool ${err}`);
  }
}

// async function close() {
//   await oracledb.getPool().close();
// }

async function simpleExecute(statement, params = [], cache = true) {
  const log = new Logger().getInstance();
  try {
    log.debug(`>>>>> Enter services.mysql-database.service.simpleExecute`);
    let retryCount = 0;

    while (retryCount < MAX_RETRIES) {
      try {
        if (params.length)
          log.info(
            `[MySQL] Query to be executed: ${statement} with params : ${JSON.stringify(
              params
            )}`
          );
        else log.info(`[MySQL] Query to be executed: ${statement}`);

        const [result] = await client.query(statement, params);

        log.debug(
          `<<<<< Exit services.mysql-database.service.simpleExecute successfully`
        );

        return result;
      } catch (err) {
        log.error(
          `<<<<< Exit services.mysql-database.service.simpleExecute with error ${err}`
        );
        log.info(
          `<<<<< Exit services.mysql-database.service.simpleExecute with error ${err}`
        );

        if (err.code == "ECONNRESET") {
          log.error(
            `Attempt ${retryCount + 1}: Error executing query - ${err}`
          );
          if (retryCount === MAX_RETRIES - 1) {
            log.error(`Max retry attempts reached. Failing query.`);
            throw err;
          }
          retryCount++;
          log.info(
            `Retrying query (Attempt ${retryCount + 1}) after ${RETRY_DELAY}ms`
          );
          await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
        } else throw err;
      }
    }
  } catch (err) {
    throw err;
  }
}

module.exports = {
  simpleExecute: simpleExecute,
  initialize: initialize,
};
