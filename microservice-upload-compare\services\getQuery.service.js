
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

async function uploadCompareDataQuery() {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO UPL_CMPR_DATA VALUES (TO_DATE(:1, 'yyyy-mm-dd'),:2,:3,TO_DATE(:4, 'yyyy-mm-dd'),:5,:6,TO_DATE(:7, 'yyyy-mm-dd'),:8)`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function postMasterDataQuery(inputData) {
    return new Promise((resolve, reject) => {
        try {
            let query = `INSERT INTO UPL_CMPR_MASTER (LAST_UPLOAD_DATE, USERID, INSERT_DT, INSERT_USER_ID, CMS_NODE) VALUES (TO_DATE('${inputData.last_updated_date}', 'yyyy-mm-dd'),'${inputData.userId}',TO_DATE('${inputData.insertDt}','yyyy-mm-dd'),'${inputData.insertUserId}','${inputData.nodeId}')`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function getDataExistQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `SELECT * FROM UPL_CMPR_MASTER WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

async function deleteExistingDataQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM UPL_CMPR_DATA WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}


async function deleteExistingDataMasterQuery(obj) {
    return new Promise((resolve, reject) => {
        try {
            let query = `DELETE FROM UPL_CMPR_MASTER WHERE USERID = '${obj.userId}' AND CMS_NODE = '${obj.nodeId}'`
            resolve(query);
        } catch (err) {
            
            log.error(`<<<<<< Exited microservice-terms-and-conditions.services.getQuery.service.acceptTermsAndConditionsQuery with error ${err} `);
            reject([424, err]);
        }
    });
}

module.exports = { uploadCompareDataQuery, postMasterDataQuery, getDataExistQuery, deleteExistingDataQuery, deleteExistingDataMasterQuery }