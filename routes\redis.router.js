const express = require('express');
const router = new express.Router();
const redisController = require('../microservice-redis/redis.controller');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();

router.get('/:type', async (req, res, next) => {
    try {
      const data = await redisController.flushRedis(req);
      res.set('Content-Type', 'application/json');
      res.send(data);
      next();
    } catch (err) {
      log.error(`Error clearing redis cache ERROR: ${err}`);
      next(err);
    }
  });

module.exports = router;