const db = require('../../services/database.service');
const clkdb = require('../../services/clk-database.service');
const { getEntityUserCountQuery } = require('./getQuery.service');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getEntityUserCountData(entityId) {
  try {
    let {query,binds} = await getEntityUserCountQuery(entityId)
      
      let data = await getData(query,binds)
      log.debug(`<<<<< Exit microservice-external.services.executeQuery.getEntityUserCount successfully`);
      return data
          
  }
  catch(err){
    log.error(`<<<<< Exited Exit microservice-external.services.executeQuery.getEntityUserCount with error ${err}`);
    throw err;
  }
}

async function getData(query, binds = {}) {
  log.debug(`>>>>> Enter microservice-external.services.executeQuery.getData`);
  try {
    let data = await db.simpleExecute(query, binds)
    return data;
  }
  catch (err) {
    log.error(`Error Executing Query:- ${query}`);
    throw err;

  }
}

async function getClkData(query, binds = {}) {
  log.debug(`>>>>> Enter microservice-external.services.executeQuery.getData`);
  try {
    let data = await clkdb.simpleExecute(query, binds)
    return data;
  }
  catch (err) {
    log.error(`Error Executing Query:- ${query}`);
    throw err;

  }
}


module.exports = {
  getEntityUserCountData
}