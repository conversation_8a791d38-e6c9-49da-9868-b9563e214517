function getTotalEntitiesQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      toInt32(COUNT(DISTINCT ENTITY)) as count
    FROM
      VW_IFP_USER_INFO
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`toDate(USER_CREATED_DT) >= {startDate: Date}`);
      binds.startDate = filter.startDate;
    }
    if (filter.endDate) {
      filters.push(`toDate(USER_CREATED_DT) <= {endDate: Date}`);
      binds.endDate = filter.endDate;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    query += filters.join(" AND ");
  }

  return { query, binds };
}

function getEntitiesCountByMonthQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      formatDateTime(toDate(USER_CREATED_DT), '%b %Y') AS month,
      toInt32(COUNT(DISTINCT ENTITY)) AS count,
      ROW_NUMBER() OVER (ORDER BY min(toDate(USER_CREATED_DT))) AS rank
    FROM
      VW_IFP_USER_INFO
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`toDate(USER_CREATED_DT) >= {startDate: Date}`);
      binds.startDate = filter.startDate;
    }
    if (filter.endDate) {
      filters.push(`toDate(USER_CREATED_DT) <= {endDate: Date}`);
      binds.endDate = filter.endDate;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    query += filters.join(" AND ");
  }

  query += " GROUP BY month ORDER BY min(toDate(USER_CREATED_DT))";

  return { query, binds };
}

function getTopEntitiesByUsageQuery(filter) {
  let binds = {};
  let query = `
    SELECT
      VW_IFP_USER_INFO.ENTITY AS entity,
      AVG(date_diff('second', startTime, endTime)) as duration
    FROM
      IFP_KPI_LOGS ikl
    JOIN VW_IFP_USER_INFO
      ON VW_IFP_USER_INFO.EMAIL = IFP_KPI_LOGS.userEmail
  `;

  if (filter.startDate || filter.endDate || filter.entityNames?.length > 0) {
    query += " WHERE ";
    const filters = [];

    if (filter.startDate) {
      filters.push(`endTime >= {startDate: String}`);
      binds.startDate = `${filter.startDate} 00:00:00`;
    }
    if (filter.endDate) {
      filters.push(`endTime <= {endDate: String}`);
      binds.endDate = `${filter.endDate} 23:59:59`;
    }
    if (filter.entityNames?.length > 0) {
      filters.push(`ENTITY IN {entity: Array(String)}`);
      binds.entity = filter.entityNames;
    }

    filters.push(filter.mobile ? `startsWith(sessionType, 'mobile:')` : `NOT startsWith(sessionType, 'mobile:')`);

    query += filters.join(" AND ");
  }

  query += `
    GROUP BY VW_IFP_USER_INFO.ENTITY
    ORDER BY duration DESC
    LIMIT 5
  `;

  return { query, binds };
}

function generateEntityClassifications(entityId) {
  let binds = {};
  let query = `SELECT DISTINCT CLASSIFICATION AS "name" FROM IFP_DISS_ACCESS_POLICY`

  if (entityId) {
    const entitySplit = entityId.split(',');
    const entities = entitySplit.map((_, index) => `:entityId${index}`).join(", ");
    query += `  WHERE ENTITY_ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  return { query, binds };
}

function generateEntityDomains(entityId, classification) {
  let binds = {};
  let query = `SELECT DISTINCT DOMAIN AS "name" FROM IFP_DISS_ACCESS_POLICY`;

  if (entityId) {
    const entitySplit = entityId.split(',');
    const entities = entitySplit.map((_, index) => `:entityId${index}`).join(", ");
    query += `  WHERE ENTITY_ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  if (classification) {
    query += ` AND CLASSIFICATION=:classification`;
    binds.classification = classification;
  }

  return { query, binds };
}

function generateActiveDomainsQuery(filter) {
  let binds = {
    startDate: filter.startDate,
    endDate: filter.endDate,
    domains: filter.domain
  };

  let query = `WITH 
        months AS (
        SELECT 
        toStartOfMonth(addMonths({startDate:String}::Date, number)) AS month
        FROM 
        numbers(dateDiff('month', {startDate:String}::Date, {endDate:String}::Date) + 1)
        ),
        active_domains AS (
        SELECT 
        toStartOfMonth(startTime) AS month,
        groupUniqArray(splitByChar('@', userEmail)[2]) AS active_domains
        FROM 
        IFP_KPI_LOGS
        WHERE 
        startTime BETWEEN {startDate:String} AND {endDate:String}
        AND has({domains:Array(String)}, splitByChar('@', userEmail)[2])`;


  query += filter.mobile ? ` AND startsWith(sessionType, 'mobile:')` : ` AND NOT startsWith(sessionType, 'mobile:')`


  query += ` GROUP BY 
            month
            )
            SELECT 
            months.month,
            ifNull(active_domains.active_domains, []) AS active_domains
            FROM 
            months
            LEFT JOIN 
            active_domains 
            ON 
            months.month = active_domains.month
            ORDER BY 
            months.month`
  return { query, binds };
}

function generateDownloadReportQuery(filter) {
  let binds = {};
  let query = `SELECT 
      downloadType AS "type",
      groupArray(tuple(name, count)) AS "indicators"
      FROM (
      SELECT    
          ikl.downloadType,
          ikl.data1 AS name,
          COUNT(*) AS count   
      FROM IFP_KPI_LOGS AS ikl   
      LEFT JOIN VW_IFP_USER_INFO AS viui 
          ON ikl.userEmail = viui.EMAIL   
      WHERE ikl.sessionType = 'download'`;

  if (filter.entityNames && filter.entityNames.length > 0) {
    query += ` AND viui.ENTITY IN {entityNames:Array(String)}`;
    binds.entityNames = filter.entityNames;
  }

  if (filter.startDate && filter.endDate) {
    query += ` AND ikl.startTime BETWEEN {startTime:DateTime} AND {endTime:DateTime}`;
    binds.startTime = filter.startDate;
    binds.endTime = filter.endDate;
  }

  query += ` AND ikl.downloadType != ''`;

  query += `
      GROUP BY ikl.downloadType, ikl.data1 
      ORDER BY count DESC 
      LIMIT 10 BY ikl.downloadType   
  ) AS subquery 
  GROUP BY downloadType;`;

  return { query, binds };
}


function generateEntitiesListWithUsersCountQuery(offset, limit, entityId, selectedColumns, isPaginated) {
  let binds = {};
  let query = 'SELECT ';
  if (selectedColumns !== '' && !isPaginated) {
    let columns = selectedColumns.split(',');
    columns.forEach(column => {
      switch (column) {
        case 'entity_id':
          query += 'e.ID AS "entity_id",';
          break;
        case 'entity_name':
          query += 'e.NAME AS "entity_name",';
          break;
        case 'total_users':
          query += 'COUNT(u.ID) AS "total_users",';
          break;
        case 'superusers_count':
          query += 'COUNT(CASE WHEN u.ROLE IN (\'PRIMARY_SUPERUSER\', \'SECONDARY_SUPERUSER\') THEN u.ID END) AS "superusers_count",';
          break;
        case 'onboarded_users':
          query += 'COUNT(CASE WHEN u.STATUS = \'REGISTERED\' AND u.ACTIVATION_FLAG = \'ACTIVE\' THEN u.ID END) AS "onboarded_users",';
          break;
        case 'waiting_to_onboarded_users':
          query += 'COUNT(CASE WHEN u.STATUS != \'REGISTERED\' OR u.ACTIVATION_FLAG != \'ACTIVE\' THEN u.ID END) AS "waiting_to_onboarded_users",';
          break;
        default:
          break;
      }
    })
  } else {
    query += ` 
        e.ID AS "entity_id",
        e.NAME AS "entity_name",
        COUNT(u.ID) AS "total_users",
        COUNT(CASE WHEN u.ROLE IN ('PRIMARY_SUPERUSER', 'SECONDARY_SUPERUSER') THEN u.ID END) AS "superusers_count",
        COUNT(CASE WHEN u.STATUS = 'REGISTERED' AND u.ACTIVATION_FLAG = 'ACTIVE' THEN u.ID END) AS "onboarded_users",
        COUNT(CASE WHEN u.STATUS != 'REGISTERED' OR u.ACTIVATION_FLAG != 'ACTIVE' THEN u.ID END) AS "waiting_to_onboarded_users",`
  }
    
  query += `
      COUNT(*) OVER () AS "total_count"  
      FROM IFP_FLOW_USERS_V2 u
      LEFT JOIN IFP_ENTITY_LOOKUP e 
      ON e.ID = u.ENTITY_ID`;

  if (entityId) {
    const entitySplit = entityId.split(',');
    const entities = entitySplit.map((_, index) => `:entityId${index}`).join(", ");
    query += `  WHERE e.ID IN (${entities})`;
    entitySplit.forEach((entityId, index) => {
      binds[`entityId${index}`] = entityId;
    });
  }

  query += ` GROUP BY e.ID, e.NAME `
  if(query.includes('total_users')){
    query += `ORDER BY "total_users" DESC`;
  }
  
  if (isPaginated) {
    query += `
      OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY`;
}

  return { query, binds };
}

function generateEntityNameQuery(domains) {
  let binds = {}
  let query = `SELECT NAME, SUBSTR(DOMAIN, 1, INSTR(DOMAIN, '.') - 1) AS DOMAIN
       FROM IFP_ENTITY_LOOKUP`;

  if (domains) {
    const entities = domains.map((_, index) => `:domain${index}`).join(", ");
    query += `  WHERE SUBSTR(DOMAIN, 1, INSTR(DOMAIN, '.') - 1) IN  (${entities})`;
    domains.forEach((domain, index) => {
      binds[`domain${index}`] = domain.toLowerCase();
    });
  }
  return { query, binds };
}



module.exports = {
  getTotalEntitiesQuery,
  getEntitiesCountByMonthQuery,
  getTopEntitiesByUsageQuery,
  generateEntityClassifications,
  generateActiveDomainsQuery,
  generateDownloadReportQuery,
  generateEntitiesListWithUsersCountQuery,
  generateEntityDomains,
  generateEntityNameQuery
};
