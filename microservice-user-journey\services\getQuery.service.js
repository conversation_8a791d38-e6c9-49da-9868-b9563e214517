const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const constants = require('../../config/constants.json');


async function getUserJourneyStatusQuery(email) {
  return new Promise((resolve, reject) => {
      try {

          let binds ={
            email:email
          }
         
          let query = `SELECT * FROM IFP_USER_JOURNEY WHERE USER_EMAIL=:email`

          return resolve({ query: query, binds: binds });
          
      } catch (err) {
          
          log.error(`<<<<<< Exited microservice-user-journey.services.getQuery.service.getUserJourneyStatusQuery with error ${err} `);
          reject([424, err]);
      }
  });
}

async function attendUserJourneyQuery(email,status) {
  return new Promise((resolve, reject) => {
      try {

          let binds ={
            email:email,
            status:status
          }
         
          let query = `MERGE INTO IFP_USER_JOURNEY UJ
          USING (SELECT :email AS USER_EMAIL, 1 AS STATUS FROM DUAL) NEW_RECORD
          ON (UJ.USER_EMAIL = NEW_RECORD.USER_EMAIL)
          WHEN MATCHED THEN
            UPDATE SET UJ.STATUS = :status
          WHEN NOT MATCHED THEN
            INSERT (USER_EMAIL, STATUS) VALUES (NEW_RECORD.USER_EMAIL, 1)
          `

          return resolve({ query: query, binds: binds });
          
      } catch (err) {
          
          log.error(`<<<<<< Exited microservice-user-journey.services.getQuery.service.attendUserJourneyQuery with error ${err} `);
          reject([424, err]);
      }
  });
}

module.exports = { 
  getUserJourneyStatusQuery,
  attendUserJourneyQuery
}