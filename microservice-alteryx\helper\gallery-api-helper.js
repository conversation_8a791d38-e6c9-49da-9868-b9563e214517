const crypto = require('crypto');
const querystring = require('querystring');
const { alteryx } = require("../../config/constants.json");

/**
 * Helper class to generated Oauth Params for Alteryx Gallery API.
 */
class AlteryxGalleryAPIOauthHelper {

  constructor() {
    const {
      API_KEY,
      API_SECRET,
    } = alteryx;
    this.apiKey = API_KEY
    this.apiSecret = API_SECRET
    this.params = {
      'oauth_consumer_key': this.apiKey,
      'oauth_nonce': Math.floor(Math.random() * 1e9).toString(),
      'oauth_signature_method': 'HMAC-SHA1',
      'oauth_timestamp': Math.floor(new Date().getTime() / 1000).toString(),
      'oauth_version': '1.0'
    }
  }

  /**
   * Core function to generate the oauth_signature for given Gallery endpoint.
   * @param {*} httpMethod HTTP method of the Gallery API endpoint
   * @param {*} host Alteryx host (w/o protocol), eg: scadssasb.scad.gov.ae
   * @param {*} endpoint Gallery API path (with starting '/'), eg: /gallery/api/workflows/subscriptions
   * @returns 
   */
  generateOauthSignature(httpMethod, host, endpoint) {
    const url = 'https://' + host + endpoint;
    const sortedParams = Object.fromEntries(Object.entries(this.params).sort());
    const normalizedParams = querystring.stringify(sortedParams);
    const baseString = `${httpMethod.toUpperCase()}&${encodeURIComponent(url)}&${encodeURIComponent(normalizedParams)}`;
    const secretBytes = Buffer.from(`${this.apiSecret}&`, 'ascii');
    const baseBytes = Buffer.from(baseString, 'ascii');
    const sig = crypto.createHmac('sha1', secretBytes).update(baseBytes).digest();
    return sig.toString('base64');
  }

  /**
   * Generate the final Oauth Params object.
   * @param {string} httpMethod HTTP method of the Gallery API endpoint, eg: POST
   * @param {string} host Alteryx host (w/o protocol), eg: scadssasb.scad.gov.ae
   * @param {string} endpoint Gallery API path (with starting '/'), eg: /gallery/api/workflows/subscriptions
   * @returns 
   */
  generateAuthorizedParams(httpMethod, host, endpoint) {
    const signature = this.generateOauthSignature(httpMethod, host, endpoint);
    this.params = { ...this.params, oauth_signature: signature }
    return this.params
  }

}

module.exports = AlteryxGalleryAPIOauthHelper
