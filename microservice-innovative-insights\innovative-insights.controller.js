const Logger = require('scad-library').logger;
const moment = require('moment');
require('dotenv').config();
const { getMetaFromCMS } = require('../services/common-service');

const { getInnovativeIndicatorData, getInnovativeFilterData, getInnovativeIndicatorsData,getIndicatorMetaData } = require('./services/executeQuery');

const { processLineChartData } = require('./services/chart-services/line-chart');


const log = new Logger().getInstance();

const constants = require('../config/constants.json');
const indicatorTemplate = require('./services/chart-services/template.json')
const indicatorTemplateAr = require("./services/chart-services/template_ar.json");

const crypto = require('crypto')
const { setRedis, getRedis } = require('../services/redis.service');
const { assignHierarchyValues } = require('./services/helper');
const { getViewNameById } = require('../services/executeQuery.service');
const { IFPError } = require('../utils/error');

async function getInnovativeInsightsById(req) {
    log.debug(`>>>>>Entered microservice.innovative-insights.controller.getInnovativeInsightsById`);
    try {
        
        const lang = req.headers["accept-language"] === 'en' ? '' : `/${req.headers["accept-language"]}`;
        const appLang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
        const cmsDomainsUrl = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_DOMAINS_LIST}`;
        const cmsLoginUrl = `${process.env.CMS_BASEPATH}${constants.cmsUrl.CMS_LOGIN_URL}`;
        const cmsWhatsNewOptimizedList = `${process.env.CMS_BASEPATH}${lang}${constants.cmsGroupUrl.CMS_WHATS_NEW_OPT_URL}`;

        let indicatorId = req.params.id;
        let response = appLang === "AR"
            ? JSON.parse(JSON.stringify(indicatorTemplateAr))
            : JSON.parse(JSON.stringify(indicatorTemplate));

        response.id = indicatorId

        const [domainList,indicatorMetaData,whatsNewData] = await Promise.all([
            getMetaFromCMS(req,cmsLoginUrl, cmsDomainsUrl, req.user.groups),
            getIndicatorMetaData(indicatorId,appLang),
            getMetaFromCMS(req,cmsLoginUrl, cmsWhatsNewOptimizedList, req.user.groups)
        ])

        if (! indicatorMetaData.length)
            throw new IFPError(404,`No such indicator found: ${indicatorId}`)
        
        let meta = indicatorMetaData[0]

        let experimentalLayout = whatsNewData.find(wn=>wn.key=='experimental_statistics')

        let expSources = []
        expSources = await Promise.all(experimentalLayout.indicatorList.map(async exp=>{
            let viewName = exp.subDomainDetails.screener_configuration.screenerView
            if (Number(exp.subDomainDetails.screener_configuration.screenerView))
            viewName = await getViewNameById(exp.subDomainDetails.screener_configuration.screenerView)
            return viewName
        }))

        if (!expSources.includes(meta.SOURCE_TABLE)){
            const errorObj = new IFPError(403,`You don't have access to this indicator: ${indicatorId}`)
            errorObj.access = false
            throw errorObj
        }

        let censusViewMap = constants.censusViewMap
        let indicatorCensusMap = {}
        if (meta.SOURCE_TABLE in censusViewMap)
            indicatorCensusMap = censusViewMap[meta.SOURCE_TABLE]
            
        
        response = assignHierarchyValues(response,meta,domainList.domain)
        
        response.note = indicatorId
        response.viewName = meta.SOURCE_TABLE
        response.content_classification_key = 'experimental_statistics'
        response = {
            ...response,
            ...indicatorCensusMap
        }

        let cmsCacheKey = `cmsMetaInnovativeInsights_${indicatorId}_${crypto.createHash('md5').update(JSON.stringify(response)).digest("hex")}`
        const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
        if (cmsCacheResults) {
            log.info(`<<<<<Cache found for microservice.innovative-insights.controller.getInnovativeInsightsById`);
            return JSON.parse(cmsCacheResults);
        }

        if (response.filterPanel && response.filterPanel.isEnabled) {
            response.filterPanel = await getFilterOptions(response.filterPanel);
        }

        let data = await getInnovativeIndicatorData(indicatorId, meta.SOURCE_TABLE,appLang);
        if (! data.length)
            throw new IFPError(404,`No data found for indicator: ${indicatorId}`)

        let timeUnit = []

        response.overView = {
            "compareFilters": [
            ],
            "valueFormat": "number_1.1-1",
            "templateFormat": "date_y",
            "baseDate": data[0].OBS_DT_LATEST,
            "value": data[0].VALUE_LATEST,
            "yearlyCompareValue": data[0].YEARLY_COMPARE_VALUE,
            "yearlyChangeValue": data[0].YEARLY_CHANGE_VALUE,
            "quarterlyCompareValue": data[0].QUARTERLY_COMPARE_VALUE,
            "quarterlyChangeValue": data[0].QUARTERLY_CHANGE_VALUE,
            "monthlyCompareValue": data[0].MONTHLY_COMPARE_VALUE,
            "monthlyChangeValue": data[0].MONTHLY_COMPARE_VALUE,
        }
        if (data[0].YEARLY){
            response.overView.compareFilters.push("Y/Y")
            timeUnit.push('Yearly')
        }
        if (data[0].QUARTERLY){
            response.overView.compareFilters.push("Q/Q")
            timeUnit.push('Quarterly')
        }
        if (data[0].MONTHLY){
            response.overView.compareFilters.push("M/M")
            timeUnit.push('Monthly')
        }

        if (data) {

            let visualizationMeta = [
                {
                    "id": `line-chart-innovative${indicatorId}`,
                    "type": "line-chart",
                    "seriesMeta": [
                        {
                            "type": "solid",
                            "xAccessor": {
                                "type": "date",
                                "path": "OBS_DT",
                                "specifier": "%Y-%m-%d"
                            },
                            "yAccessor": {
                                "type": "value",
                                "path": "VALUE"
                            },
                            "data":data,
                            "id": `innovative${indicatorId}`,
                            "dbIndicatorId": indicatorId,
                            "label": null,
                            "color": "#000000"
                        }
                    ],
                    "tooltipTitleFormat": "date_MMM y",
                    "tooltipValueFormat": "d3-number",
                    "timeUnit":timeUnit,
                    "xAxisFormat": "date_y",
                    "yAxisFormat": "d3-number",
                    "xAxisLabel": "",
                    "yAxisLabel": data[0].UNIT,
                    "unit": data[0].UNIT
                }
            ]

            response.data_source = data[0].DATA_SOURCE
            response.unit = data[0].UNIT
            response.publication_date = data[0].INSERT_DT  

            let langCode = req.headers["accept-language"]
            let tableFields = [
                {label:( langCode == 'en' ? "INDICATOR ID" : 'معرف المؤشر'),path:"INDICATOR_ID"},
                {label:( langCode == 'en' ? "VALUE": 'قيمة'),path:"VALUE"},
                {label:( langCode == 'en' ? "DATE" : 'تاريخ'),path:"OBS_DT"}
            ]
            response.tableFields = tableFields

            response.domain = data[0].TOPIC
            response.subdomain = data[0].THEME
            response.isMultiDimension = false
            response.component_title = data[0].INDICATOR_NAME
            response.indicatorVisualizations.visualizationsMeta = visualizationMeta;
            response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id
        }


        const indicatorVisualizationsMeta = response.indicatorVisualizations.visualizationsMeta;

        let visualizationLen = response.indicatorVisualizations.visualizationsMeta.length;
        let graphData = response;
        graphData.indicatorVisualizations.visualizationsMeta = [];

        const counter = [];
        let vizFlag = false;
        let vizPromises = indicatorVisualizationsMeta.map(async visualization => {
            visualization.seriesMeta.forEach(series => {
                let chartType = visualization.type
                switch (chartType) {

                    case "line-chart": {
                        try {
                            series = processLineChartData(series.data, series);
                        } catch (err) {
                            
                            log.error(`Error executing processLineChartData from microservice.innovative-insights.controller.getInnovativeInsightsById ${err}`);
                            throw err;
                        }
                        break;
                    }

                    default: {
                        log.debug(`Chart type not available`);
                        counter.push(1);
                        break;
                    }
                }
            })

            graphData.indicatorVisualizations.visualizationsMeta.push(visualization);
            counter.push(1);

            if (response.type == "innovative_statistics")
                response.type = "scad"

            if (counter.length === visualizationLen) {
                delete graphData.viewName
                vizFlag = true;
            }
        });
        await Promise.all(vizPromises)
        if (vizFlag){
            setRedis(cmsCacheKey, JSON.stringify(graphData), constants.redis.cmsResponseTTL, req.headers);
            return graphData;
        }
        else
            throw new IFPError('Error while generating visualization')

    } catch (err) {
        log.error(`<<<<< Exited microservice.innovative-insights.controller.getInnovativeInsightsById with error ${err} `)
        throw err;
    }

}

async function compareInnovativeInsights(req) {
    log.debug(`>>>>>Entered microservice.innovative-insights.controller.getInnovativeInsightsById`);
    return new Promise(async (resolve, reject) => {

        try {
            const counter = [];
            let indicators = req.body.indicators;
            let viewName = req.query.viewName;

            let response = JSON.parse(JSON.stringify(indicatorTemplate))
            indicatorsKey = `${indicators.join('.')}`
            response.id = indicatorsKey

            let cmsCacheKey = `cmsMetaStatisticsInsights_${indicatorsKey}_${crypto.createHash('md5').update(JSON.stringify(response)).digest("hex")}`
            const cmsCacheResults = await getRedis(cmsCacheKey, req.headers);
            if (cmsCacheResults) {
                log.info(`<<<<<Cache found for microservice.innovative-insights.controller.compareInnovativeInsights`);
                return resolve(JSON.parse(cmsCacheResults));
            }

            let indicatorData = {}
            for (const indicator in indicators) {
                let data = await getInnovativeIndicatorData(indicators[indicator], viewName);
                indicatorData[indicators[indicator]] = data
            }

            if (Object.keys(indicatorData).length) {

                let visualizationMeta = [
                    {
                        "id": `line-chart-innovative${indicatorsKey}`,
                        "type": "line-chart",
                        "seriesMeta": [

                        ],
                        "tooltipTitleFormat": "date_MMM y",
                        "tooltipValueFormat": "d3-number",
                        "xAxisFormat": "date_y",
                        "yAxisFormat": "d3-number",
                        "xAxisLabel": "",
                        "yAxisLabel": ""
                    }
                ]

                let genericSeries = {
                    "type": "solid",
                    "xAccessor": {
                        "type": "date",
                        "path": "OBS_DT",
                        "specifier": "%Y-%m-%d"
                    },
                    "yAccessor": {
                        "type": "value",
                        "path": "VALUE"
                    },


                    "label": null,
                    "color": "#000000"
                }

                let seriesMeta = []
                for (const indicator in indicatorData) {
                    let series = JSON.parse(JSON.stringify(genericSeries))
                    series.id = `innovative${indicator}`
                    series.dbIndicatorId = indicator
                    series.data = indicatorData[indicator]
                    seriesMeta.push(series)
                }

                visualizationMeta[0].seriesMeta = seriesMeta

                response.indicatorVisualizations.visualizationsMeta = visualizationMeta;
                response.indicatorVisualizations.visualizationDefault = response.indicatorVisualizations.visualizationsMeta[0].id

            }

            const indicatorVisualizationsMeta = response.indicatorVisualizations.visualizationsMeta;

            let visualizationLen = response.indicatorVisualizations.visualizationsMeta.length;
            let graphData = response;
            graphData.indicatorVisualizations.visualizationsMeta = [];

            indicatorVisualizationsMeta.forEach(async visualization => {
                visualization.seriesMeta.forEach(series => {
                    let chartType = visualization.type
                    switch (chartType) {
                        case "line-chart": {
                            try {
                                series = processLineChartData(series.data, series);
                            } catch (err) {
                                
                                log.error(`Error executing processLineChartData from microservice.innovative-insights.controller.getInnovativeInsightsById ${err}`);
                                reject(err);
                            }
                            break;
                        }

                        default: {
                            log.debug(`Chart type not available`);
                            counter.push(1);
                            break;
                        }
                    }
                })
                graphData.indicatorVisualizations.visualizationsMeta.push(visualization);
                counter.push(1);


                if (response.type == "innovative_statistics")
                    response.type = "scad"
                if (counter.length === visualizationLen) {
                    setRedis(cmsCacheKey, JSON.stringify(graphData), constants.redis.cmsResponseTTL, req.headers);
                    resolve(graphData);
                }
            });



        } catch (err) {
            
            log.error(`<<<<< Exited microservice.innovative-insights.controller.getInnovativeInsightsById with error ${err} `)
            reject(err);
        }
    });

}


async function getInnovativeFilters(req) {
    log.debug(`>>>>>Entered microservice.innovative-insights.controller.getInnovativeInsightsById`);
    return new Promise(async (resolve, reject) => {
        try {
            const lang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;
            let filterIndicator = req.params.id
            let filterMap = {}
            let data = await getInnovativeFilterData(filterIndicator,lang)

            data.forEach(filter => {
                if (!Object.keys(filterMap).includes(filter.DIMENSION))
                    filterMap[filter.DIMENSION] = {items:[]}
                filterMap[filter.DIMENSION]['items'].push({
                    name: filter.VALUE_LABEL,
                    value: filter.VALUE
                })
                filterMap[filter.DIMENSION]['name'] = filter.DIMENSION_LABEL
                if (Number(filter.DEFAULT_SELECTION))
                    filterMap[filter.DIMENSION]['default'] = {
                        name: filter.VALUE_LABEL,
                        value: filter.VALUE
                    }
            })

            let response = Object.entries(filterMap).map(([filter, data]) => {
                const filterObj = {
                    name: data.name,
                    key: filter,
                    default: data.default,
                    items: data.items
                }
                return filterObj;
            })
            resolve(response)
        } catch (err) {
            
            log.error(`<<<<< Exited microservice.innovative-insights.controller.getInnovativeFilters with error ${err} `)
            reject(err);
        }
    });
}


async function getInnovativeIndicators(req) {
    log.debug(`>>>>>Entered microservice.innovative-insights.controller.getInnovativeInsightsById`);
    return new Promise(async (resolve, reject) => {
        try {
            let viewName = req.body.viewName
            let filters = req.body.filters
            let sortBy = req.body.sortBy

            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;

            const lang = req.headers["accept-language"] === 'en' ? 'EN' : `${req.headers["accept-language"].toUpperCase()}`;

            if (Number(viewName)){
                viewName = await getViewNameById(viewName)
            }

            let censusViewMap = constants.censusViewMap
            let indicatorCensusMap = {}
            if (viewName in censusViewMap)
                indicatorCensusMap = censusViewMap[viewName]

            let data = await getInnovativeIndicatorsData(viewName, filters,sortBy, offset, limit,lang)
            let results = []
            data.forEach(element => {
                let tempElement = {
                    "indicatorId": element.INDICATOR_ID,
                    "title": element.INDICATOR_NAME,
                    "compareFilters": [
                    ],
                    "valueFormat": "number_1.1-1",
                    "templateFormat": "date_y",
                    "baseDate": element.OBS_DT_LATEST,
                    "value": element.VALUE_LATEST,
                    "yearlyCompareValue": element.YEARLY_COMPARE_VALUE,
                    "yearlyChangeValue": element.YEARLY_CHANGE_VALUE,
                    "quarterlyCompareValue": element.QUARTERLY_COMPARE_VALUE,
                    "quarterlyChangeValue": element.QUARTERLY_CHANGE_VALUE,
                    "monthlyCompareValue": element.MONTHLY_COMPARE_VALUE,
                    "monthlyChangeValue": element.MONTHLY_COMPARE_VALUE,
                    "unit": element.UNIT
                }
                tempElement = {
                    ...tempElement,
                    ...indicatorCensusMap
                }
                if (element.YEARLY)
                    tempElement.compareFilters.push("Y/Y")
                if (element.QUARTERLY)
                    tempElement.compareFilters.push("Q/Q")
                if (element.MONTHLY)
                    tempElement.compareFilters.push("M/M")

                results.push(tempElement)
            })

            resolve({
                totalCount: data.length ? Number(data[0].TOTAL) : 0,
                page: page,
                limit: limit,
                data: results
            })

        } catch (err) {
            
            log.error(`<<<<< Exited microservice.innovative-insights.controller.getInnovativeFilters with error ${err} `)
            reject(err);
        }
    });
}




module.exports = {
    getInnovativeInsightsById,
    getInnovativeFilters,
    getInnovativeIndicators,
    compareInnovativeInsights
};
