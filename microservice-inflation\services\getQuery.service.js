
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


async function getBaselineForecastDataQuery() {
  try {
   let query = "SELECT * FROM FORECAST_BASELINE_IFP_SCHEMA";
   return query;
  } catch (err) {
    log.error(`<<<<<< Exited microservice-official-insights.services.getQuery.service.getBaselineForecastDataQuery with error ${err} `);
    throw err;
  }
}

async function getCombinationDataQuery(drivers){
  try {
    let binds = {
      oilPrice: drivers.oilPrice,
      governmentSpending: drivers.governmentSpending,
      geoPoliticalIndex: drivers.geoPoliticalIndex,
      singaporeGDP: drivers.singaporeGDP,
      tacIndex: drivers.tacIndex,
      financeFuturePMI: drivers.financeFuturePMI,
      inrReer: drivers.inrReer,
      usDollarPrice: drivers.usDollarPrice,
  }
  let query = `
            SELECT * FROM COMBINATION_DF_NEW_SCHEMA
            WHERE
            "PARAMETER_1_NAME" = 'OIL_PRICE'
            AND "PARAMETER_1_VALUE" = {oilPrice:Double}
            AND "PARAMETER_2_NAME" = 'SINGAPORE_GDP'
            AND "PARAMETER_2_VALUE" = {singaporeGDP:Double}
            AND "PARAMETER_3_NAME" = 'US_DOLLAR_PRICE'
            AND "PARAMETER_3_VALUE" = {usDollarPrice:Double}
            AND "PARAMETER_4_NAME" = 'GOVERNMENT_SPENDING'
            AND "PARAMETER_4_VALUE" = {governmentSpending:Double}
            AND "PARAMETER_5_NAME" = 'GEO_POLITICAL_INDEX'
            AND "PARAMETER_5_VALUE" = {geoPoliticalIndex:Double}
            AND "PARAMETER_6_NAME" = 'TAC_INDEX_AIR_FREIGHT_EU_US'
            AND "PARAMETER_6_VALUE" = {tacIndex:Double}
            AND "PARAMETER_7_NAME" = 'FINANCE_FUTURE_PMI'
            AND "PARAMETER_7_VALUE" = {financeFuturePMI:Double}
            AND "PARAMETER_8_NAME" = 'AED_INR_REER'
            AND "PARAMETER_8_VALUE" = {inrReer:Double} `

  return {'query':query,'binds':binds}
  } catch (err) {
    log.error(`<<<<<< Exited microservice-inflation.services.getQuery.service.getCombinationDataQuery with error ${err} `);
    throw err;
  }
}

async function getForecastCombinationDataQuery(combinationId){
  try {
    let binds = {
      'combinationId': combinationId
    }
    let query = `SELECT * FROM SCENARIO_TABLE_NEW_SCHEMA WHERE "TYPE" = 'FORECAST' AND "PARAMETER_COMBO_ID" = {combinationId:String}`

    return {'query':query,'binds':binds}
  } catch (err) {
    log.error(`<<<<<< Exited microservice-inflation.services.getQuery.service.getCombinationDataQuery with error ${err} `);
    throw err;
  }
}

async function getMetricsModelDataQuery(){
  try {
    let query = `SELECT * FROM METRICS_TABLE`
    return query
  } catch (err) {
    log.error(`<<<<<< Exited microservice-inflation.services.getQuery.service.getMetricsModelDataQuery with error ${err} `);
    throw err;
  }
}

async function getMetricsTrainTestDataQuery(){
  try {
    let query = `SELECT * FROM METRICS_TRAIN_TEST_SPLIT_NEW`
    return query
  } catch (err) {
    log.error(`<<<<<< Exited microservice-inflation.services.getQuery.service.getMetricsTrainTestDataQuery with error ${err} `);
    throw err;
  }
}

module.exports = { 
  getBaselineForecastDataQuery, getCombinationDataQuery, getForecastCombinationDataQuery, getMetricsModelDataQuery, getMetricsTrainTestDataQuery }