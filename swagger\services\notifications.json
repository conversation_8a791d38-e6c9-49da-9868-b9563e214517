{"paths": {"/content-type/notifications/": {"get": {"tags": ["Notifications"], "summary": "Retrive notifications received by a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}, {"name": "status", "in": "query", "required": true, "schema": {"type": "string", "enum": ["READ", "UNREAD"]}, "description": "Read status of the notification"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"APP_TYPE": {"type": "string", "example": "coi", "description": "Type of the application associated with the notification."}, "CONTENT_NAME": {"type": "string", "description": "Name of the content associated with the notification."}, "CONTENT_DESCRIPTION": {"type": "string", "description": "Description of the content associated with the notification."}, "CONTENT_TYPE": {"type": "string", "example": "scad_official_indicator", "description": "Type of the content associated with the notification."}, "INSERT_DATE": {"type": "string", "format": "date-time", "description": "The date and time when the notification was inserted."}, "NODE_ID": {"type": "string", "description": "A unique identifier associated with the notification content."}, "NOTIFICATION_ID": {"type": "string", "description": "A unique identifier for the notification."}, "READ_DATE": {"type": "string", "format": "date", "description": "The date and time when the notification was read, if applicable."}, "READ_STATUS": {"type": "string", "description": "The read status of the notification.", "enum": ["UNREAD", "READ"]}, "USER_EMAIL": {"type": "string", "format": "email", "description": "Email of the user associated with the notification."}}, "required": ["APP_TYPE", "CONTENT_NAME", "CONTENT_DESCRIPTION", "CONTENT_TYPE", "INSERT_DATE", "NODE_ID", "NOTIFICATION_ID", "READ_STATUS", "USER_EMAIL"]}, "description": "An array of notifications."}}, "required": ["data"], "description": "Schema for a response body containing notification details."}}}}}}}, "/content-type/notifications/mappings": {"get": {"tags": ["Notifications"], "summary": "Retrieve notification subscribed objects of a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "patternProperties": {"^[0-9]+$": {"type": "object", "properties": {"isNotification": {"type": "boolean", "description": "Indicates if notifications are enabled for the identifier."}, "isEmail": {"type": "integer", "enum": [0, 1], "description": "Indicates if emails are enabled (1) or disabled (0) for the identifier."}}, "required": ["isNotification", "isEmail"], "additionalProperties": false}}, "additionalProperties": false, "description": "Schema for a response object containing notification and email settings for various identifiers."}}}}}}}, "/content-type/notifications/subscribe": {"post": {"tags": ["Notifications"], "summary": "Subscibe notifications for indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Indicators data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndicatorNotificationMapping"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message indicating the outcome of an operation."}, "status": {"type": "string", "description": "The status of the operation.", "enum": ["success", "failed"]}}, "required": ["message", "status"], "description": "Schema for a response indicating the result of a create mapping operation."}}}}}}}, "/content-type/notifications/subscribe/email": {"post": {"tags": ["Notifications"], "summary": "Subscribe to email notifications for indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Indicators data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndicatorEmailNotificationMapping"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message describing the result of the operation."}, "status": {"type": "string", "description": "The status of the operation.", "enum": ["success", "failed"]}}, "required": ["message", "status"], "description": "Schema for a response indicating the result of an email flag mapping update operation."}}}}}}}, "/content-type/notifications/unsubscribe": {"post": {"tags": ["Notifications"], "summary": "Unsubscibe notifications for indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Indicators data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndicatorNotificationUnsubscribeMapping"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message indicating the outcome of the mapping creation operation."}, "status": {"type": "string", "description": "The status of the mapping creation operation.", "enum": ["success", "failed"]}}, "required": ["message", "status"], "description": "Schema for a response indicating the result of a mapping creation operation."}}}}}}}, "/content-type/notifications/unsubscribe/email": {"post": {"tags": ["Notifications"], "summary": "Unsubscribe to email notifications for indicator", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Indicators Email Notification data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndicatorEmailNotificationUnsubscribeMapping"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"message": {"type": "string", "description": "A message describing the result of the update operation."}, "status": {"type": "string", "description": "The status of the update operation.", "enum": ["success", "failed"]}}, "required": ["message", "status"], "description": "Schema for a response indicating the result of an email flag mapping update operation."}}}}}}}, "/content-type/notifications/read": {"post": {"tags": ["Notifications"], "summary": "Read a notification", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Indicator Notification Read Data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndicatorNotificationRead"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}, "/content-type/notifications/map-list": {"get": {"tags": ["Notifications"], "summary": "", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"IndicatorNotificationMapping": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the item."}, "contentType": {"type": "string", "description": "Type of content, specifying the nature of the item.", "example": "scad_official_indicator"}, "appType": {"type": "string", "description": "Application type, indicating the application or context the item belongs to.", "example": "scad"}, "compare": {"type": "boolean", "description": "Flag indicating whether comparison is enabled or disabled for the item."}}, "required": ["id", "contentType", "appType", "compare"], "description": "Schema for validating the structure and types of a request body for item details."}, "IndicatorEmailNotificationMapping": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the item."}}, "required": ["id"], "description": "Schema for validating the structure of a request body that includes a single identifier."}, "IndicatorNotificationUnsubscribeMapping": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the item."}}, "required": ["id"], "description": "Schema for validating the structure of a request body that includes a single identifier."}, "IndicatorEmailNotificationUnsubscribeMapping": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "integer", "description": "Unique identifier for the item."}}, "required": ["id"], "description": "Schema for validating the structure of a request body that includes a single identifier."}, "IndicatorNotificationRead": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier."}}, "required": ["id"], "description": "Schema for a request body containing a unique identifier."}}}}