const express = require('express');
const router = new express.Router();
const glossaryController = require('../microservice-glossary/glossary.controller');
const { validateGlossaryList } = require('./validators/glossary.validator');
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();


router.post('/', validateGlossaryList, async (req, res, next) => {
  try {
    const data = await glossaryController.getGlossary(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});

router.get('/filters', async (req, res, next) => {
  try {
    const data = await glossaryController.getGlossaryFilters(req);
    res.send(data);
    next();
  } catch (err) {
    
    log.error(`Error fetching data for footer content-type, ERROR: ${err}`);
    next(err);
  }
});


module.exports = router;
