{"paths": {"/content-type/settings/": {"get": {"tags": ["Settings"], "summary": "Retrieves settings for a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"lang": {"type": "string", "description": "Language preference.", "enum": ["en", "ar"]}, "fontSize": {"type": "string", "description": "Font size preference.", "enum": ["sm", "md", "lg", "xl"]}, "cursor": {"type": "string", "description": "Cursor style preference.", "enum": ["type1", "type2", "type3"]}, "theme": {"type": "string", "description": "Theme preference.", "enum": ["light", "dark"]}}, "required": ["lang", "fontSize", "cursor", "theme"], "description": "Schema for a response containing user interface preferences."}}}}}}, "post": {"tags": ["Settings"], "summary": "Update or create settings for a user", "parameters": [{"$ref": "#/components/headers/AcceptLanguageHeader"}], "requestBody": {"description": "Settings data", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SettingsData"}}}}, "responses": {"200": {"description": "Successful response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"SettingsData": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"settings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "The name of the setting.", "enum": ["fontSize", "cursor", "theme", "lang"]}, "value": {"type": "string", "description": "The value of the setting.", "enum": ["sm", "md", "lg", "xl", "type1", "type2", "type3", "light", "dark", "en", "ar", "fr", "es"]}}, "required": ["name", "value"], "description": "A specific user setting and its value."}, "description": "An array of user settings."}}, "required": ["settings"], "description": "Schema for a request body containing an array of user interface settings."}}}}