const express = require('express');
const router = new express.Router();
const {Superuser<PERSON>ontroller, EntityController, ProductEngagementController, UserController, ApprovalController, RoleController} = require('../microservices-users-v2/user.controller')
const Logger = require('scad-library').logger;
const log = new Logger().getInstance();
const passport = require('passport');

router.get('/role', passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new RoleController().getUserRole(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/list', passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new EntityController().listEntity(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/access-policy', passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new EntityController().accessPolicy(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/requests/:type', passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new SuperuserController().getUserAccessRequests(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error fetching entity list, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/superuser/register', async (req, res, next) => {
    try {
        const data = await new SuperuserController().register(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/superuser/verify', async (req, res, next) => {
    try {
        const data = await new SuperuserController().verifyToken(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error verifying invitation token, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/superuser/request/otp', async (req, res, next) => {
    try {
        const data = await new SuperuserController().requestOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/invitations/create',passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new SuperuserController().inviteUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error inviting entity users, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/superuser/manage-access', passport.authenticate('oauth-bearer', { session: false }),async (req, res, next) => {
// router.post('/entity/superuser/manage-access',async (req, res, next) => {
    try {
        const data = await new SuperuserController().approveUserRegistration(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/dg/manage-access', passport.authenticate('oauth-bearer', { session: false }),async (req, res, next) => {
    try {
        const data = await new ApprovalController().approveUserAccess(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/user/verify', async (req, res, next) => {
    try {
        const data = await new UserController().verifyToken(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error verifying invitation token, ERROR: ${err}`);
        next(err);
    }
});

router.post('/entity/user/register', async (req, res, next) => {
    try {
        const data = await new UserController().register(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error inviting entity users, ERROR: ${err}`);
        next(err);
    }
});

router.get('/entity/user/request/otp', async (req, res, next) => {
    try {
        const data = await new UserController().requestOTP(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error registering superuser, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/invitations/create',passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().createInvitations(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error creating invitations for entity, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/user/activate',passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().activateUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error activating user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/user/deactivate',passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().deActivateUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error activating user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/user/deactivate',passport.authenticate('oauth-bearer', { session: false }), async (req, res, next) => {
    try {
        const data = await new ProductEngagementController().deActivateUser(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error activating user, ERROR: ${err}`);
        next(err);
    }
});

router.post('/product-engagement/manage-access', passport.authenticate('oauth-bearer', { session: false }),async (req, res, next) => {
    try {
        const data = await new ApprovalController().approveUserAccess(req);
        res.set('Content-Type', 'application/json');
        res.send(data);
        next();
    } catch (err) {
        log.error(`Error managing user access, ERROR: ${err}`);
        next(err);
    }
});

module.exports = router;
