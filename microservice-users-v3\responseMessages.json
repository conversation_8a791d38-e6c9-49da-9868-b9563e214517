{"SUCCESS": {"USER": {"UPDATE_SUCCESS": "User access updated successfully."}, "APPROVAL_CYCLE_UPDATE": {"APPROVE": "User access approved", "REJECT": "User access rejected", "REVERT": "User access reverted", "REVOKE": "User access revoked", "PENDING_ACCESS_REMOVED": "Pending Sensitive access requests have been removed successfully"}, "INVITE_RESENT": "Invitation resent successfully"}, "ERROR": {"PRIMARY_SUPERUSER_EXISTS": "A Primary Superuser for this entity already exists", "SECONDARY_SUPERUSER_EXISTS": "A Secondary Superuser for this entity already exists", "USER_EXISTS": "User with given details already exists", "PENDING_INVITE_EXISTS": "'Pending' Invite for user already exists.", "UPDATE_DATA_EMPTY": "Data to be updated cannot be empty", "INVITATION_UPDATE_403": "Only pending invitations can be modified.", "ENTITY_REQUIRED": "Entity Not Provided", "COMMON": {"NOT_ALLOWED": "You don't have permission to perform this action at the moment.", "QUERY_PARAM_REQUIRED": "'{query_param_name}' required."}, "INVITATION": {"INVITE_EXISTS": "Invite with given details already exists.", "INVITES_UNSUCCESSFUL_EXISTING_USER": "Some invitees are existing users; remove them and resend invites, or link their EID under the 'Existing Users' tab.", "INVALID_PLATFORM_TYPE": "Platform must be of 'web' or 'all'"}, "USER_GUIDE": {"UNKNOWN_TYPE": "Unrecognized 'type' requested. Must be one of 'confidential' or 'sensitive'", "TYPE_REQUIRED": "'type' must be specified."}}}