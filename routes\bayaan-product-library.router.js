const express = require("express");
const router = new express.Router();
const productLibraryController = require("../microservice-bayaan-product-library/bayaan-product-library.controller");
const Logger = require("scad-library").logger;
const log = new Logger().getInstance();

router.get('/tree-view', async (req, res, next) => {
    try {
      const data = await productLibraryController.productLibraryTreeView(req);
      res.send(data);
      next();
    } catch (err) {
      
      log.error(`Error fetching data for bayaan-product-library content-type, ERROR: ${err}`);
      next(err);
    }
  });

module.exports = router;
